<?xml version="1.0" encoding="utf-8"?>
<navigation xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/nav_graph"
    app:startDestination="@id/mapFragment">

    <!--In the label field of fragment you can give the name that you want to show in the toolbar-->
    <fragment
        android:id="@+id/chargingFragment"
        android:name="com.nxc.evcsolutions.ui.fragment.charging.ChargingFragment"
        android:label="@string/home"
        tools:layout="@layout/fragment_charging">
        <action
            android:id="@+id/action_homeFragment_to_profileFragment"
            app:destination="@id/profileFragment" />
    </fragment>

    <fragment
        android:id="@+id/mapFragment"
        android:name="com.nxc.evcsolutions.ui.fragment.map.MapFragment"
        android:label="@string/map"
        tools:layout="@layout/fragment_map" />

    <fragment
        android:id="@+id/walletFragment"
        android:name="com.nxc.evcsolutions.ui.fragment.wallet.WalletFragment"
        android:label="@string/wallet"
        tools:layout="@layout/fragment_wallet">

        <!--  <action
              android:id="@+id/action_accountsFragment_to_addMoneyFragment"
              app:destination="@id/addMoney" />-->

    </fragment>


    <fragment
        android:id="@+id/profileFragment"
        android:name="com.nxc.evcsolutions.ui.fragment.profile.ProfileFragment"
        android:label="@string/profile"
        tools:layout="@layout/fragment_profile" />

    <fragment
        android:id="@+id/gamesFragment"
        android:name="com.nxc.evcsolutions.ui.fragment.games.GamesFragment"
        android:label="@string/games"
        tools:layout="@layout/fragment_games" />


    <!-- <fragment android:id="@+id/gamesFragment"
     android:name="com.nxc.evcsolutions.ui.fragment.games.GamesFragment"
     android:label="{dynamicTitle}"
     tools:layout="@layout/fragment_games">
     <argument
         android:name="dynamicTitle"
         android:defaultValue="4"
         app:argType="integer" />

     <action
         android:id="@+id/action_gamesFragment_to_helpFragment"
         app:destination="@id/helpFragment" />


 </fragment>-->
    <fragment
        android:id="@+id/settingsFragment"
        android:name="com.nxc.evcsolutions.ui.fragment.SettingsFragment"
        android:label="Settings"
        tools:layout="@layout/fragment_settings" />

    <fragment
        android:id="@+id/complainFragment"
        android:name="com.nxc.evcsolutions.ui.fragment.complain.ComplainFragment"
        android:label="@string/complain"
        tools:layout="@layout/fragment_complain" />

    <fragment
        android:id="@+id/rfidFragment"
        android:name="com.nxc.evcsolutions.ui.fragment.rfidCard.RFIDFragment"
        android:label="@string/order_rfid"
        tools:layout="@layout/fragment_r_f_i_d" />

    <fragment
        android:id="@+id/newsFragment"
        android:name="com.nxc.evcsolutions.ui.fragment.WebFragment"
        android:label="{dynamicTitle}"
        tools:layout="@layout/fragment_web">
        <argument
            android:name="dynamicTitle"
            android:defaultValue="3"
            app:argType="integer" />

    </fragment>
    <fragment
        android:id="@+id/buyChargerFragment"
        android:name="com.nxc.evcsolutions.ui.fragment.WebFragment"
        android:label="{dynamicTitle}"
        tools:layout="@layout/fragment_web">
        <argument
            android:name="dynamicTitle"
            android:defaultValue="4"
            app:argType="integer" />

    </fragment>

    <fragment
        android:id="@+id/helpFragment"
        android:name="com.nxc.evcsolutions.ui.fragment.WebFragment"
        android:label="{dynamicTitle}"
        tools:layout="@layout/fragment_web">
        <argument
            android:name="dynamicTitle"
            android:defaultValue="1"
            app:argType="integer" />

    </fragment>
    <fragment
        android:id="@+id/aboutUsFragment"
        android:name="com.nxc.evcsolutions.ui.fragment.WebFragment"
        android:label="{dynamicTitle}"
        tools:layout="@layout/fragment_web">
        <argument
            android:name="dynamicTitle"
            android:defaultValue="2"
            app:argType="integer" />

    </fragment>

    <fragment
        android:id="@+id/changeLanguage"
        android:name="com.nxc.evcsolutions.ui.fragment.ChangeLanguageFragment"
        android:label="@string/change_language"
        tools:layout="@layout/fragment_change_language" />

    <!-- <fragment
         android:id="@+id/addMoney"
         android:name="com.nxc.evcsolutions.ui.fragment.AddMoneyFragment"
         android:label="@string/add_money"
         tools:layout="@layout/fragment_add_money" />-->

</navigation>