package com.nxc.evcsolutions.ui.fragment;

import com.google.android.gms.maps.model.LatLng;
import com.google.maps.android.clustering.ClusterItem;

public class LocationClusterItem implements ClusterItem {
    private String charge_box_pk;
    private String charge_station_id;
    private String distance;
    private String address;
    private final String csId;
    private final LatLng latLng;

    public LocationClusterItem(String csId, LatLng latLng) {
        this.csId = csId;
        this.latLng = latLng;
    }

    public LocationClusterItem(String charge_box_pk, String charge_station_id, String address,String csName, String distance, LatLng latLng) {
        this.address = address;
        this.csId = csName;
        this.latLng = latLng;
        this.charge_box_pk = charge_box_pk;
        this.charge_station_id = charge_station_id;
        this.distance = distance;
    }

    @Override
    public LatLng getPosition() {  // 1
        return latLng;
    }

    @Override
    public String getTitle() {  // 2
        return "";
    }

    @Override
    public String getSnippet() {
        return "";
    }

    public String getCharge_box_pk() {
        return charge_box_pk;
    }

    public void setCharge_box_pk(String charge_box_pk) {
        this.charge_box_pk = charge_box_pk;
    }

    public LatLng getLatLng() {
        return latLng;
    }


    public String getDistance() {
        return distance;
    }

    public void setDistance(String distance) {
        this.distance = distance;
    }

    public String getCharge_station_id() {
        return charge_station_id;
    }

    public String getUsername() {
        return address;
    }

    public void setUsername(String username) {
        this.address = username;
    }

    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = address;
    }

    public String getCsId() {
        return csId;
    }

}