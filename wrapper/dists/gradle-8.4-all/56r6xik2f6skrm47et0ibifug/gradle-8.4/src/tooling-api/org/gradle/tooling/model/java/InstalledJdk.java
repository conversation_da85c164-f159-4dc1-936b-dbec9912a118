/*
 * Copyright 2016 the original author or authors.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package org.gradle.tooling.model.java;

import org.gradle.api.JavaVersion;

import java.io.File;

/**
 * Represents a Java Development Kit machine installation.
 *
 * @since 2.11
 */
public interface InstalledJdk {
    /**
     * The version of the Java installation.
     *
     * @return The Java version. Never returns {@code null}.
     */
    JavaVersion getJavaVersion();

    /**
     * The home directory of the Java installation.
     *
     * @return The home directory. Never returns {@code null}.
     * */
    File getJavaHome();
}
