/*
 * Copyright 2019 the original author or authors.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package org.gradle.plugins.signing.internal;

import org.gradle.api.file.FileCollection;
import org.gradle.api.file.ProjectLayout;
import org.gradle.plugins.signing.SignOperation;

import javax.inject.Inject;
import java.io.File;
import java.util.List;

public class SignOperationInternal extends SignOperation {
    private final ProjectLayout projectLayout;

    @Inject
    public SignOperationInternal(ProjectLayout projectLayout) {
        this.projectLayout = projectLayout;
    }

    @Override
    protected FileCollection toFileCollection(List<File> files) {
        return projectLayout.files(files);
    }
}
