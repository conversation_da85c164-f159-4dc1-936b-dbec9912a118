/*
 * Copyright 2020 the original author or authors.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package org.gradle.process.internal.worker;

import java.io.Serializable;

/**
 * Handles requests to do work in worker process. Is instantiated in the build process and serialized to the worker process.
 */
public interface RequestHandler<IN, OUT> extends Serializable {
    /**
     * Executes the given request and returns the response. Called in the worker process only.
     */
    OUT run(IN request);
}
