<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc -->
<title>DirectoryReport (Gradle API 8.4)</title>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<link rel="stylesheet" type="text/css" href="../../../../javadoc.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../jquery/jquery-ui.min.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../jquery-ui.overrides.css" title="Style">
<script type="text/javascript" src="../../../../script.js"></script>
<script type="text/javascript" src="../../../../jquery/jszip/dist/jszip.min.js"></script>
<script type="text/javascript" src="../../../../jquery/jszip-utils/dist/jszip-utils.min.js"></script>
<!--[if IE]>
<script type="text/javascript" src="../../../../jquery/jszip-utils/dist/jszip-utils-ie.min.js"></script>
<![endif]-->
<script type="text/javascript" src="../../../../jquery/jquery-3.6.0.min.js"></script>
<script type="text/javascript" src="../../../../jquery/jquery-ui.min.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="DirectoryReport (Gradle API 8.4)";
        }
    }
    catch(err) {
    }
//-->
var data = {"i0":6,"i1":6,"i2":6};
var tabs = {65535:["t0","All Methods"],2:["t2","Instance Methods"],4:["t3","Abstract Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
var pathtoroot = "../../../../";
var useModuleDirectories = false;
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<div class="fixedNav">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../allclasses.html">All&nbsp;Classes</a></li>
</ul>
<ul class="navListSearch">
<li><label for="search">SEARCH:</label>
<input type="text" id="search" value="search" disabled="disabled">
<input type="reset" id="reset" value="reset" disabled="disabled">
</li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li><a href="#nested.class.summary">Nested</a>&nbsp;|&nbsp;</li>
<li><a href="#field.summary">Field</a>&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
</div>
<div class="navPadding">&nbsp;</div>
<script type="text/javascript"><!--
$('.navPadding').css('padding-top', $('.fixedNav').css("height"));
//-->
</script>
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle"><span class="packageLabelInType">Package</span>&nbsp;<a href="package-summary.html">org.gradle.api.reporting</a></div>
<h2 title="Interface DirectoryReport" class="title">Interface DirectoryReport</h2>
</div>
<div class="contentContainer">
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt>All Superinterfaces:</dt>
<dd><code><a href="../../util/Configurable.html" title="interface in org.gradle.util">Configurable</a>&lt;<a href="Report.html" title="interface in org.gradle.api.reporting">Report</a>&gt;</code>, <code><a href="ConfigurableReport.html" title="interface in org.gradle.api.reporting">ConfigurableReport</a></code>, <code><a href="Report.html" title="interface in org.gradle.api.reporting">Report</a></code></dd>
</dl>
<dl>
<dt>All Known Subinterfaces:</dt>
<dd><code><a href="../tasks/testing/JUnitXmlReport.html" title="interface in org.gradle.api.tasks.testing">JUnitXmlReport</a></code></dd>
</dl>
<hr>
<pre>public interface <span class="typeNameLabel">DirectoryReport</span>
extends <a href="ConfigurableReport.html" title="interface in org.gradle.api.reporting">ConfigurableReport</a></pre>
<div class="block">A directory based report to be created.</div>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ======== NESTED CLASS SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="nested.class.summary">
<!--   -->
</a>
<h3>Nested Class Summary</h3>
<ul class="blockList">
<li class="blockList"><a name="nested.classes.inherited.from.class.org.gradle.api.reporting.Report">
<!--   -->
</a>
<h3>Nested classes/interfaces inherited from interface&nbsp;org.gradle.api.reporting.<a href="Report.html" title="interface in org.gradle.api.reporting">Report</a></h3>
<code><a href="Report.OutputType.html" title="enum in org.gradle.api.reporting">Report.OutputType</a></code></li>
</ul>
</li>
</ul>
<!-- =========== FIELD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="field.summary">
<!--   -->
</a>
<h3>Field Summary</h3>
<ul class="blockList">
<li class="blockList"><a name="fields.inherited.from.class.org.gradle.api.reporting.Report">
<!--   -->
</a>
<h3>Fields inherited from interface&nbsp;org.gradle.api.reporting.<a href="Report.html" title="interface in org.gradle.api.reporting">Report</a></h3>
<code><a href="Report.html#NAMER">NAMER</a></code></li>
</ul>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t3" class="tableTab"><span><a href="javascript:show(4);">Abstract Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colSecond" scope="col">Method</th>
<th class="colLast" scope="col">Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>java.io.File</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getEntryPoint--">getEntryPoint</a></span>()</code></th>
<td class="colLast">
<div class="block">Returns the entry point of a directory based Report

 This can be the index.html file in a HTML report</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code><a href="../file/DirectoryProperty.html" title="interface in org.gradle.api.file">DirectoryProperty</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getOutputLocation--">getOutputLocation</a></span>()</code></th>
<td class="colLast">
<div class="block">The location on the filesystem to generate the report to.</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code><a href="Report.OutputType.html" title="enum in org.gradle.api.reporting">Report.OutputType</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getOutputType--">getOutputType</a></span>()</code></th>
<td class="colLast">
<div class="block">Always returns <a href="Report.OutputType.html#DIRECTORY"><code>Report.OutputType.DIRECTORY</code></a></div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.org.gradle.util.Configurable">
<!--   -->
</a>
<h3>Methods inherited from interface&nbsp;org.gradle.util.<a href="../../util/Configurable.html" title="interface in org.gradle.util">Configurable</a></h3>
<code><a href="../../util/Configurable.html#configure-groovy.lang.Closure-">configure</a></code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.org.gradle.api.reporting.ConfigurableReport">
<!--   -->
</a>
<h3>Methods inherited from interface&nbsp;org.gradle.api.reporting.<a href="ConfigurableReport.html" title="interface in org.gradle.api.reporting">ConfigurableReport</a></h3>
<code><a href="ConfigurableReport.html#setDestination-java.io.File-">setDestination</a></code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.org.gradle.api.reporting.Report">
<!--   -->
</a>
<h3>Methods inherited from interface&nbsp;org.gradle.api.reporting.<a href="Report.html" title="interface in org.gradle.api.reporting">Report</a></h3>
<code><a href="Report.html#getDisplayName--">getDisplayName</a>, <a href="Report.html#getName--">getName</a>, <a href="Report.html#getRequired--">getRequired</a></code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="getEntryPoint--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getEntryPoint</h4>
<pre class="methodSignature"><a href="../tasks/Internal.html" title="annotation in org.gradle.api.tasks">@Internal</a>
java.io.File&nbsp;getEntryPoint()</pre>
<div class="block">Returns the entry point of a directory based Report

 This can be the index.html file in a HTML report</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>the entry point of the report or
 <a href="#getOutputLocation--"><code>getOutputLocation()</code></a>
 if no entry point defined</dd>
</dl>
</li>
</ul>
<a name="getOutputLocation--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getOutputLocation</h4>
<pre class="methodSignature"><a href="../tasks/OutputDirectory.html" title="annotation in org.gradle.api.tasks">@OutputDirectory</a>
<a href="../file/DirectoryProperty.html" title="interface in org.gradle.api.file">DirectoryProperty</a>&nbsp;getOutputLocation()</pre>
<div class="block"><span class="descfrmTypeLabel">Description copied from interface:&nbsp;<code><a href="Report.html#getOutputLocation--">Report</a></code></span></div>
<div class="block">The location on the filesystem to generate the report to.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="Report.html#getOutputLocation--">getOutputLocation</a></code>&nbsp;in interface&nbsp;<code><a href="Report.html" title="interface in org.gradle.api.reporting">Report</a></code></dd>
</dl>
</li>
</ul>
<a name="getOutputType--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>getOutputType</h4>
<pre class="methodSignature"><a href="Report.OutputType.html" title="enum in org.gradle.api.reporting">Report.OutputType</a>&nbsp;getOutputType()</pre>
<div class="block">Always returns <a href="Report.OutputType.html#DIRECTORY"><code>Report.OutputType.DIRECTORY</code></a></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="Report.html#getOutputType--">getOutputType</a></code>&nbsp;in interface&nbsp;<code><a href="Report.html" title="interface in org.gradle.api.reporting">Report</a></code></dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd><a href="Report.OutputType.html#DIRECTORY"><code>Report.OutputType.DIRECTORY</code></a></dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../allclasses.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li><a href="#nested.class.summary">Nested</a>&nbsp;|&nbsp;</li>
<li><a href="#field.summary">Field</a>&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
