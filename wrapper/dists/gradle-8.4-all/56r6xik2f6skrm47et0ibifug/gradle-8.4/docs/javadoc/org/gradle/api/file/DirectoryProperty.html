<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc -->
<title>DirectoryProperty (Gradle API 8.4)</title>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<link rel="stylesheet" type="text/css" href="../../../../javadoc.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../jquery/jquery-ui.min.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../jquery-ui.overrides.css" title="Style">
<script type="text/javascript" src="../../../../script.js"></script>
<script type="text/javascript" src="../../../../jquery/jszip/dist/jszip.min.js"></script>
<script type="text/javascript" src="../../../../jquery/jszip-utils/dist/jszip-utils.min.js"></script>
<!--[if IE]>
<script type="text/javascript" src="../../../../jquery/jszip-utils/dist/jszip-utils-ie.min.js"></script>
<![endif]-->
<script type="text/javascript" src="../../../../jquery/jquery-3.6.0.min.js"></script>
<script type="text/javascript" src="../../../../jquery/jquery-ui.min.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="DirectoryProperty (Gradle API 8.4)";
        }
    }
    catch(err) {
    }
//-->
var data = {"i0":6,"i1":6,"i2":6,"i3":6,"i4":6,"i5":6,"i6":6,"i7":6,"i8":6,"i9":6,"i10":6,"i11":6};
var tabs = {65535:["t0","All Methods"],2:["t2","Instance Methods"],4:["t3","Abstract Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
var pathtoroot = "../../../../";
var useModuleDirectories = false;
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<div class="fixedNav">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../allclasses.html">All&nbsp;Classes</a></li>
</ul>
<ul class="navListSearch">
<li><label for="search">SEARCH:</label>
<input type="text" id="search" value="search" disabled="disabled">
<input type="reset" id="reset" value="reset" disabled="disabled">
</li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
</div>
<div class="navPadding">&nbsp;</div>
<script type="text/javascript"><!--
$('.navPadding').css('padding-top', $('.fixedNav').css("height"));
//-->
</script>
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle"><span class="packageLabelInType">Package</span>&nbsp;<a href="package-summary.html">org.gradle.api.file</a></div>
<h2 title="Interface DirectoryProperty" class="title">Interface DirectoryProperty</h2>
</div>
<div class="contentContainer">
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt>All Superinterfaces:</dt>
<dd><code><a href="FileSystemLocationProperty.html" title="interface in org.gradle.api.file">FileSystemLocationProperty</a>&lt;<a href="Directory.html" title="interface in org.gradle.api.file">Directory</a>&gt;</code>, <code><a href="../provider/HasConfigurableValue.html" title="interface in org.gradle.api.provider">HasConfigurableValue</a></code>, <code><a href="../provider/Property.html" title="interface in org.gradle.api.provider">Property</a>&lt;<a href="Directory.html" title="interface in org.gradle.api.file">Directory</a>&gt;</code>, <code><a href="../provider/Provider.html" title="interface in org.gradle.api.provider">Provider</a>&lt;<a href="Directory.html" title="interface in org.gradle.api.file">Directory</a>&gt;</code></dd>
</dl>
<hr>
<pre>public interface <span class="typeNameLabel">DirectoryProperty</span>
extends <a href="FileSystemLocationProperty.html" title="interface in org.gradle.api.file">FileSystemLocationProperty</a>&lt;<a href="Directory.html" title="interface in org.gradle.api.file">Directory</a>&gt;</pre>
<div class="block">Represents some configurable directory location, whose value is mutable.

 <p>
 You can create a <a href="DirectoryProperty.html" title="interface in org.gradle.api.file"><code>DirectoryProperty</code></a> using <a href="../model/ObjectFactory.html#directoryProperty--"><code>ObjectFactory.directoryProperty()</code></a>.
 </p>

 <p><b>Note:</b> This interface is not intended for implementation by build script or plugin authors.</p></div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>4.3</dd>
</dl>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t3" class="tableTab"><span><a href="javascript:show(4);">Abstract Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colSecond" scope="col">Method</th>
<th class="colLast" scope="col">Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code><a href="DirectoryProperty.html" title="interface in org.gradle.api.file">DirectoryProperty</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#convention-org.gradle.api.file.Directory-">convention</a></span>&#8203;(<a href="Directory.html" title="interface in org.gradle.api.file">Directory</a>&nbsp;value)</code></th>
<td class="colLast">
<div class="block">Specifies the value to use as the convention (default value) for this property.</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code><a href="DirectoryProperty.html" title="interface in org.gradle.api.file">DirectoryProperty</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#convention-org.gradle.api.provider.Provider-">convention</a></span>&#8203;(<a href="../provider/Provider.html" title="interface in org.gradle.api.provider">Provider</a>&lt;? extends <a href="Directory.html" title="interface in org.gradle.api.file">Directory</a>&gt;&nbsp;provider)</code></th>
<td class="colLast">
<div class="block">Specifies the provider to be used to query the convention (default value) for this property.</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code><a href="../provider/Provider.html" title="interface in org.gradle.api.provider">Provider</a>&lt;<a href="Directory.html" title="interface in org.gradle.api.file">Directory</a>&gt;</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#dir-java.lang.String-">dir</a></span>&#8203;(java.lang.String&nbsp;path)</code></th>
<td class="colLast">
<div class="block">Returns a <a href="Directory.html" title="interface in org.gradle.api.file"><code>Directory</code></a> whose value is the given path resolved relative to the value of this directory.</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code><a href="../provider/Provider.html" title="interface in org.gradle.api.provider">Provider</a>&lt;<a href="Directory.html" title="interface in org.gradle.api.file">Directory</a>&gt;</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#dir-org.gradle.api.provider.Provider-">dir</a></span>&#8203;(<a href="../provider/Provider.html" title="interface in org.gradle.api.provider">Provider</a>&lt;? extends java.lang.CharSequence&gt;&nbsp;path)</code></th>
<td class="colLast">
<div class="block">Returns a <a href="Directory.html" title="interface in org.gradle.api.file"><code>Directory</code></a> whose value is the given path resolved relative to the value of this directory.</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code><a href="../provider/Provider.html" title="interface in org.gradle.api.provider">Provider</a>&lt;<a href="RegularFile.html" title="interface in org.gradle.api.file">RegularFile</a>&gt;</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#file-java.lang.String-">file</a></span>&#8203;(java.lang.String&nbsp;path)</code></th>
<td class="colLast">
<div class="block">Returns a <a href="RegularFile.html" title="interface in org.gradle.api.file"><code>RegularFile</code></a> whose value is the given path resolved relative to the value of this directory.</div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code><a href="../provider/Provider.html" title="interface in org.gradle.api.provider">Provider</a>&lt;<a href="RegularFile.html" title="interface in org.gradle.api.file">RegularFile</a>&gt;</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#file-org.gradle.api.provider.Provider-">file</a></span>&#8203;(<a href="../provider/Provider.html" title="interface in org.gradle.api.provider">Provider</a>&lt;? extends java.lang.CharSequence&gt;&nbsp;path)</code></th>
<td class="colLast">
<div class="block">Returns a <a href="RegularFile.html" title="interface in org.gradle.api.file"><code>RegularFile</code></a> whose value is the given path resolved relative to the value of this directory.</div>
</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code><a href="DirectoryProperty.html" title="interface in org.gradle.api.file">DirectoryProperty</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#fileProvider-org.gradle.api.provider.Provider-">fileProvider</a></span>&#8203;(<a href="../provider/Provider.html" title="interface in org.gradle.api.provider">Provider</a>&lt;java.io.File&gt;&nbsp;provider)</code></th>
<td class="colLast">
<div class="block">Sets the location of this file, using a <code>File</code> <a href="../provider/Provider.html" title="interface in org.gradle.api.provider"><code>Provider</code></a> instance.</div>
</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code><a href="FileCollection.html" title="interface in org.gradle.api.file">FileCollection</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#files-java.lang.Object...-">files</a></span>&#8203;(java.lang.Object...&nbsp;paths)</code></th>
<td class="colLast">
<div class="block">Returns a <a href="FileCollection.html" title="interface in org.gradle.api.file"><code>FileCollection</code></a> containing the given files,
 whose locations are the given paths resolved relative to this directory,
 as defined by <a href="../Project.html#files-java.lang.Object...-"><code>Project.files(Object...)</code></a>.</div>
</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code><a href="DirectoryProperty.html" title="interface in org.gradle.api.file">DirectoryProperty</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#fileValue-java.io.File-">fileValue</a></span>&#8203;(java.io.File&nbsp;file)</code></th>
<td class="colLast">
<div class="block">Sets the location of this file, using a <code>File</code> instance.</div>
</td>
</tr>
<tr id="i9" class="rowColor">
<td class="colFirst"><code><a href="FileTree.html" title="interface in org.gradle.api.file">FileTree</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getAsFileTree--">getAsFileTree</a></span>()</code></th>
<td class="colLast">
<div class="block">Returns a <a href="FileTree.html" title="interface in org.gradle.api.file"><code>FileTree</code></a> that allows the files and directories contained in this directory to be queried.</div>
</td>
</tr>
<tr id="i10" class="altColor">
<td class="colFirst"><code><a href="DirectoryProperty.html" title="interface in org.gradle.api.file">DirectoryProperty</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#value-org.gradle.api.file.Directory-">value</a></span>&#8203;(<a href="Directory.html" title="interface in org.gradle.api.file">Directory</a>&nbsp;value)</code></th>
<td class="colLast">
<div class="block">Sets the value of the property to the given value, replacing whatever value the property already had.</div>
</td>
</tr>
<tr id="i11" class="rowColor">
<td class="colFirst"><code><a href="DirectoryProperty.html" title="interface in org.gradle.api.file">DirectoryProperty</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#value-org.gradle.api.provider.Provider-">value</a></span>&#8203;(<a href="../provider/Provider.html" title="interface in org.gradle.api.provider">Provider</a>&lt;? extends <a href="Directory.html" title="interface in org.gradle.api.file">Directory</a>&gt;&nbsp;provider)</code></th>
<td class="colLast">
<div class="block">Sets the property to have the same value as the given provider, replacing whatever value the property already had.</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.org.gradle.api.file.FileSystemLocationProperty">
<!--   -->
</a>
<h3>Methods inherited from interface&nbsp;org.gradle.api.file.<a href="FileSystemLocationProperty.html" title="interface in org.gradle.api.file">FileSystemLocationProperty</a></h3>
<code><a href="FileSystemLocationProperty.html#getAsFile--">getAsFile</a>, <a href="FileSystemLocationProperty.html#getLocationOnly--">getLocationOnly</a>, <a href="FileSystemLocationProperty.html#set-java.io.File-">set</a></code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.org.gradle.api.provider.HasConfigurableValue">
<!--   -->
</a>
<h3>Methods inherited from interface&nbsp;org.gradle.api.provider.<a href="../provider/HasConfigurableValue.html" title="interface in org.gradle.api.provider">HasConfigurableValue</a></h3>
<code><a href="../provider/HasConfigurableValue.html#disallowChanges--">disallowChanges</a>, <a href="../provider/HasConfigurableValue.html#disallowUnsafeRead--">disallowUnsafeRead</a>, <a href="../provider/HasConfigurableValue.html#finalizeValueOnRead--">finalizeValueOnRead</a></code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.org.gradle.api.provider.Property">
<!--   -->
</a>
<h3>Methods inherited from interface&nbsp;org.gradle.api.provider.<a href="../provider/Property.html" title="interface in org.gradle.api.provider">Property</a></h3>
<code><a href="../provider/Property.html#finalizeValue--">finalizeValue</a>, <a href="../provider/Property.html#set-org.gradle.api.provider.Provider-">set</a>, <a href="../provider/Property.html#set-T-">set</a></code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.org.gradle.api.provider.Provider">
<!--   -->
</a>
<h3>Methods inherited from interface&nbsp;org.gradle.api.provider.<a href="../provider/Provider.html" title="interface in org.gradle.api.provider">Provider</a></h3>
<code><a href="../provider/Provider.html#filter-java.util.function.Predicate-">filter</a>, <a href="../provider/Provider.html#flatMap-org.gradle.api.Transformer-">flatMap</a>, <a href="../provider/Provider.html#forUseAtConfigurationTime--">forUseAtConfigurationTime</a>, <a href="../provider/Provider.html#get--">get</a>, <a href="../provider/Provider.html#getOrElse-T-">getOrElse</a>, <a href="../provider/Provider.html#getOrNull--">getOrNull</a>, <a href="../provider/Provider.html#isPresent--">isPresent</a>, <a href="../provider/Provider.html#map-org.gradle.api.Transformer-">map</a>, <a href="../provider/Provider.html#orElse-org.gradle.api.provider.Provider-">orElse</a>, <a href="../provider/Provider.html#orElse-T-">orElse</a>, <a href="../provider/Provider.html#zip-org.gradle.api.provider.Provider-java.util.function.BiFunction-">zip</a></code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="getAsFileTree--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getAsFileTree</h4>
<pre class="methodSignature"><a href="FileTree.html" title="interface in org.gradle.api.file">FileTree</a>&nbsp;getAsFileTree()</pre>
<div class="block">Returns a <a href="FileTree.html" title="interface in org.gradle.api.file"><code>FileTree</code></a> that allows the files and directories contained in this directory to be queried.</div>
</li>
</ul>
<a name="value-org.gradle.api.file.Directory-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>value</h4>
<pre class="methodSignature"><a href="DirectoryProperty.html" title="interface in org.gradle.api.file">DirectoryProperty</a>&nbsp;value&#8203;(@Nullable
                        <a href="Directory.html" title="interface in org.gradle.api.file">Directory</a>&nbsp;value)</pre>
<div class="block">Sets the value of the property to the given value, replacing whatever value the property already had.
 This is the same as <a href="../provider/Property.html#set-T-"><code>Property.set(Object)</code></a> but returns this property to allow method chaining.

 <p>
 This method can also be used to discard the value of the property, by passing it <code>null</code>.
 When the value is discarded (or has never been set in the first place), the convention (default value)
 for this property, if specified, will be used to provide the value instead.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../provider/Property.html#value-T-">value</a></code>&nbsp;in interface&nbsp;<code><a href="../provider/Property.html" title="interface in org.gradle.api.provider">Property</a>&lt;<a href="Directory.html" title="interface in org.gradle.api.file">Directory</a>&gt;</code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - The value, can be null.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>this</dd>
</dl>
</li>
</ul>
<a name="value-org.gradle.api.provider.Provider-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>value</h4>
<pre class="methodSignature"><a href="DirectoryProperty.html" title="interface in org.gradle.api.file">DirectoryProperty</a>&nbsp;value&#8203;(<a href="../provider/Provider.html" title="interface in org.gradle.api.provider">Provider</a>&lt;? extends <a href="Directory.html" title="interface in org.gradle.api.file">Directory</a>&gt;&nbsp;provider)</pre>
<div class="block">Sets the property to have the same value as the given provider, replacing whatever value the property already had.
 This property will track the value of the provider and query its value each time the value of the property is queried.
 When the provider has no value, this property will also have no value. This is the same as <a href="../provider/Property.html#set-org.gradle.api.provider.Provider-"><code>Property.set(Provider)</code></a>
 but returns this property to allow method chaining.

 <p>
 This method can NOT be used to discard the value of the property. Specifying a <code>null</code> provider will result
 in an <code>IllegalArgumentException</code> being thrown. When the provider has no value, this property will also have
 no value - regardless of whether or not a convention has been set.
 </p>

 <p>
 When the given provider represents a task output, this property will also carry the task dependency information
 from the provider.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../provider/Property.html#value-org.gradle.api.provider.Provider-">value</a></code>&nbsp;in interface&nbsp;<code><a href="../provider/Property.html" title="interface in org.gradle.api.provider">Property</a>&lt;<a href="Directory.html" title="interface in org.gradle.api.file">Directory</a>&gt;</code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>provider</code> - The provider whose value to use.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>this</dd>
</dl>
</li>
</ul>
<a name="fileValue-java.io.File-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>fileValue</h4>
<pre class="methodSignature"><a href="DirectoryProperty.html" title="interface in org.gradle.api.file">DirectoryProperty</a>&nbsp;fileValue&#8203;(@Nullable
                            java.io.File&nbsp;file)</pre>
<div class="block">Sets the location of this file, using a <code>File</code> instance. <code>File</code> instances with relative paths are resolved relative to the project directory of the project
 that owns this property instance.

 <p>This method is the same as <a href="FileSystemLocationProperty.html#set-java.io.File-"><code>FileSystemLocationProperty.set(File)</code></a> but allows method chaining.</p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="FileSystemLocationProperty.html#fileValue-java.io.File-">fileValue</a></code>&nbsp;in interface&nbsp;<code><a href="FileSystemLocationProperty.html" title="interface in org.gradle.api.file">FileSystemLocationProperty</a>&lt;<a href="Directory.html" title="interface in org.gradle.api.file">Directory</a>&gt;</code></dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>this</dd>
</dl>
</li>
</ul>
<a name="fileProvider-org.gradle.api.provider.Provider-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>fileProvider</h4>
<pre class="methodSignature"><a href="DirectoryProperty.html" title="interface in org.gradle.api.file">DirectoryProperty</a>&nbsp;fileProvider&#8203;(<a href="../provider/Provider.html" title="interface in org.gradle.api.provider">Provider</a>&lt;java.io.File&gt;&nbsp;provider)</pre>
<div class="block">Sets the location of this file, using a <code>File</code> <a href="../provider/Provider.html" title="interface in org.gradle.api.provider"><code>Provider</code></a> instance. <code>File</code> instances with relative paths are resolved relative to the project directory of the project
 that owns this property instance.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="FileSystemLocationProperty.html#fileProvider-org.gradle.api.provider.Provider-">fileProvider</a></code>&nbsp;in interface&nbsp;<code><a href="FileSystemLocationProperty.html" title="interface in org.gradle.api.file">FileSystemLocationProperty</a>&lt;<a href="Directory.html" title="interface in org.gradle.api.file">Directory</a>&gt;</code></dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>this</dd>
</dl>
</li>
</ul>
<a name="convention-org.gradle.api.file.Directory-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>convention</h4>
<pre class="methodSignature"><a href="DirectoryProperty.html" title="interface in org.gradle.api.file">DirectoryProperty</a>&nbsp;convention&#8203;(@Nullable
                             <a href="Directory.html" title="interface in org.gradle.api.file">Directory</a>&nbsp;value)</pre>
<div class="block">Specifies the value to use as the convention (default value) for this property. If the convention is set and
 no explicit value or value provider has been specified, then the convention will be returned as the value of
 the property (when queried).

 <p>
 This method can be used to specify that the property does not have a default value, by passing it
 <code>null</code>.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../provider/Property.html#convention-T-">convention</a></code>&nbsp;in interface&nbsp;<code><a href="../provider/Property.html" title="interface in org.gradle.api.provider">Property</a>&lt;<a href="Directory.html" title="interface in org.gradle.api.file">Directory</a>&gt;</code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - The convention value, or <code>null</code> if the property should have no default value.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>this</dd>
</dl>
</li>
</ul>
<a name="convention-org.gradle.api.provider.Provider-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>convention</h4>
<pre class="methodSignature"><a href="DirectoryProperty.html" title="interface in org.gradle.api.file">DirectoryProperty</a>&nbsp;convention&#8203;(<a href="../provider/Provider.html" title="interface in org.gradle.api.provider">Provider</a>&lt;? extends <a href="Directory.html" title="interface in org.gradle.api.file">Directory</a>&gt;&nbsp;provider)</pre>
<div class="block">Specifies the provider to be used to query the convention (default value) for this property. If a convention
 provider has been set and no explicit value or value provider has been specified, then the convention
 provider's value will be returned as the value of the property (when queried).

 <p>
 The property's convention tracks the convention provider. Whenever the convention's actual value is
 needed, the convention provider will be queried anew.
 </p>

 <p>
 This method can't be used to specify that a property does not have a default value. Passing in a <code>null</code>
 provider will result in an <code>IllegalArgumentException</code> being thrown. When the provider doesn't have
 a value, then the property will behave as if it wouldn't have a convention specified.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../provider/Property.html#convention-org.gradle.api.provider.Provider-">convention</a></code>&nbsp;in interface&nbsp;<code><a href="../provider/Property.html" title="interface in org.gradle.api.provider">Property</a>&lt;<a href="Directory.html" title="interface in org.gradle.api.file">Directory</a>&gt;</code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>provider</code> - The provider of the property's convention value, can't be null.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>this</dd>
</dl>
</li>
</ul>
<a name="dir-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>dir</h4>
<pre class="methodSignature"><a href="../provider/Provider.html" title="interface in org.gradle.api.provider">Provider</a>&lt;<a href="Directory.html" title="interface in org.gradle.api.file">Directory</a>&gt;&nbsp;dir&#8203;(java.lang.String&nbsp;path)</pre>
<div class="block">Returns a <a href="Directory.html" title="interface in org.gradle.api.file"><code>Directory</code></a> whose value is the given path resolved relative to the value of this directory.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>path</code> - The path. Can be absolute.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>The directory.</dd>
</dl>
</li>
</ul>
<a name="dir-org.gradle.api.provider.Provider-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>dir</h4>
<pre class="methodSignature"><a href="../provider/Provider.html" title="interface in org.gradle.api.provider">Provider</a>&lt;<a href="Directory.html" title="interface in org.gradle.api.file">Directory</a>&gt;&nbsp;dir&#8203;(<a href="../provider/Provider.html" title="interface in org.gradle.api.provider">Provider</a>&lt;? extends java.lang.CharSequence&gt;&nbsp;path)</pre>
<div class="block">Returns a <a href="Directory.html" title="interface in org.gradle.api.file"><code>Directory</code></a> whose value is the given path resolved relative to the value of this directory.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>path</code> - The path. Can have a value that is an absolute path.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>The directory.</dd>
</dl>
</li>
</ul>
<a name="file-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>file</h4>
<pre class="methodSignature"><a href="../provider/Provider.html" title="interface in org.gradle.api.provider">Provider</a>&lt;<a href="RegularFile.html" title="interface in org.gradle.api.file">RegularFile</a>&gt;&nbsp;file&#8203;(java.lang.String&nbsp;path)</pre>
<div class="block">Returns a <a href="RegularFile.html" title="interface in org.gradle.api.file"><code>RegularFile</code></a> whose value is the given path resolved relative to the value of this directory.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>path</code> - The path. Can be absolute.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>The file.</dd>
</dl>
</li>
</ul>
<a name="file-org.gradle.api.provider.Provider-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>file</h4>
<pre class="methodSignature"><a href="../provider/Provider.html" title="interface in org.gradle.api.provider">Provider</a>&lt;<a href="RegularFile.html" title="interface in org.gradle.api.file">RegularFile</a>&gt;&nbsp;file&#8203;(<a href="../provider/Provider.html" title="interface in org.gradle.api.provider">Provider</a>&lt;? extends java.lang.CharSequence&gt;&nbsp;path)</pre>
<div class="block">Returns a <a href="RegularFile.html" title="interface in org.gradle.api.file"><code>RegularFile</code></a> whose value is the given path resolved relative to the value of this directory.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>path</code> - The path. Can have a value that is an absolute path.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>The file.</dd>
</dl>
</li>
</ul>
<a name="files-java.lang.Object...-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>files</h4>
<pre class="methodSignature"><a href="FileCollection.html" title="interface in org.gradle.api.file">FileCollection</a>&nbsp;files&#8203;(java.lang.Object...&nbsp;paths)</pre>
<div class="block">Returns a <a href="FileCollection.html" title="interface in org.gradle.api.file"><code>FileCollection</code></a> containing the given files,
 whose locations are the given paths resolved relative to this directory,
 as defined by <a href="../Project.html#files-java.lang.Object...-"><code>Project.files(Object...)</code></a>.

 This method can also be used to create an empty collection, but the collection may not be mutated later.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>paths</code> - The paths to the files. May be empty.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>The file collection.</dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>6.0</dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../allclasses.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
