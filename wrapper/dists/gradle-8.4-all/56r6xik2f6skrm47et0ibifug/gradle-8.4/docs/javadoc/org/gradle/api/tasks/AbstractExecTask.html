<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc -->
<title>AbstractExecTask (Gradle API 8.4)</title>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<link rel="stylesheet" type="text/css" href="../../../../javadoc.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../jquery/jquery-ui.min.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../jquery-ui.overrides.css" title="Style">
<script type="text/javascript" src="../../../../script.js"></script>
<script type="text/javascript" src="../../../../jquery/jszip/dist/jszip.min.js"></script>
<script type="text/javascript" src="../../../../jquery/jszip-utils/dist/jszip-utils.min.js"></script>
<!--[if IE]>
<script type="text/javascript" src="../../../../jquery/jszip-utils/dist/jszip-utils-ie.min.js"></script>
<![endif]-->
<script type="text/javascript" src="../../../../jquery/jquery-3.6.0.min.js"></script>
<script type="text/javascript" src="../../../../jquery/jquery-ui.min.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="AbstractExecTask (Gradle API 8.4)";
        }
    }
    catch(err) {
    }
//-->
var data = {"i0":10,"i1":10,"i2":10,"i3":10,"i4":10,"i5":10,"i6":10,"i7":10,"i8":10,"i9":10,"i10":10,"i11":10,"i12":10,"i13":10,"i14":10,"i15":10,"i16":10,"i17":10,"i18":10,"i19":10,"i20":10,"i21":10,"i22":10,"i23":10,"i24":10,"i25":10,"i26":10,"i27":10,"i28":10,"i29":10,"i30":10,"i31":10,"i32":10,"i33":10,"i34":10,"i35":10,"i36":10};
var tabs = {65535:["t0","All Methods"],2:["t2","Instance Methods"],8:["t4","Concrete Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
var pathtoroot = "../../../../";
var useModuleDirectories = false;
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<div class="fixedNav">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../allclasses.html">All&nbsp;Classes</a></li>
</ul>
<ul class="navListSearch">
<li><label for="search">SEARCH:</label>
<input type="text" id="search" value="search" disabled="disabled">
<input type="reset" id="reset" value="reset" disabled="disabled">
</li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li><a href="#nested.class.summary">Nested</a>&nbsp;|&nbsp;</li>
<li><a href="#field.summary">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
</div>
<div class="navPadding">&nbsp;</div>
<script type="text/javascript"><!--
$('.navPadding').css('padding-top', $('.fixedNav').css("height"));
//-->
</script>
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle"><span class="packageLabelInType">Package</span>&nbsp;<a href="package-summary.html">org.gradle.api.tasks</a></div>
<h2 title="Class AbstractExecTask" class="title">Class AbstractExecTask&lt;T extends AbstractExecTask&gt;</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li>org.gradle.api.internal.AbstractTask</li>
<li>
<ul class="inheritance">
<li><a href="../DefaultTask.html" title="class in org.gradle.api">org.gradle.api.DefaultTask</a></li>
<li>
<ul class="inheritance">
<li>org.gradle.api.internal.ConventionTask</li>
<li>
<ul class="inheritance">
<li>org.gradle.api.tasks.AbstractExecTask&lt;T&gt;</li>
</ul>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt><span class="paramLabel">Type Parameters:</span></dt>
<dd><code>T</code> - The concrete type of the class.</dd>
</dl>
<dl>
<dt>All Implemented Interfaces:</dt>
<dd><code>java.lang.Comparable&lt;<a href="../Task.html" title="interface in org.gradle.api">Task</a>&gt;</code>, <code>org.gradle.api.internal.DynamicObjectAware</code>, <code>org.gradle.api.internal.IConventionAware</code>, <code>org.gradle.api.internal.TaskInternal</code>, <code><a href="../plugins/ExtensionAware.html" title="interface in org.gradle.api.plugins">ExtensionAware</a></code>, <code><a href="../Task.html" title="interface in org.gradle.api">Task</a></code>, <code><a href="../../process/BaseExecSpec.html" title="interface in org.gradle.process">BaseExecSpec</a></code>, <code><a href="../../process/ExecSpec.html" title="interface in org.gradle.process">ExecSpec</a></code>, <code><a href="../../process/ProcessForkOptions.html" title="interface in org.gradle.process">ProcessForkOptions</a></code>, <code><a href="../../util/Configurable.html" title="interface in org.gradle.util">Configurable</a>&lt;<a href="../Task.html" title="interface in org.gradle.api">Task</a>&gt;</code></dd>
</dl>
<dl>
<dt>Direct Known Subclasses:</dt>
<dd><code><a href="Exec.html" title="class in org.gradle.api.tasks">Exec</a></code>, <code><a href="../../nativeplatform/test/tasks/RunTestExecutable.html" title="class in org.gradle.nativeplatform.test.tasks">RunTestExecutable</a></code></dd>
</dl>
<hr>
<pre><a href="../../work/DisableCachingByDefault.html" title="annotation in org.gradle.work">@DisableCachingByDefault</a>(<a href="../../work/DisableCachingByDefault.html#because--">because</a>="Abstract super-class, not to be instantiated directly")
public abstract class <span class="typeNameLabel">AbstractExecTask&lt;T extends AbstractExecTask&gt;</span>
extends org.gradle.api.internal.ConventionTask
implements <a href="../../process/ExecSpec.html" title="interface in org.gradle.process">ExecSpec</a></pre>
<div class="block"><code>AbstractExecTask</code> is the base class for all exec tasks.</div>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ======== NESTED CLASS SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="nested.class.summary">
<!--   -->
</a>
<h3>Nested Class Summary</h3>
<ul class="blockList">
<li class="blockList"><a name="nested.classes.inherited.from.class.org.gradle.api.Task">
<!--   -->
</a>
<h3>Nested classes/interfaces inherited from interface&nbsp;org.gradle.api.<a href="../Task.html" title="interface in org.gradle.api">Task</a></h3>
<code><a href="../Task.Namer.html" title="class in org.gradle.api">Task.Namer</a></code></li>
</ul>
</li>
</ul>
<!-- =========== FIELD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="field.summary">
<!--   -->
</a>
<h3>Field Summary</h3>
<ul class="blockList">
<li class="blockList"><a name="fields.inherited.from.class.org.gradle.api.Task">
<!--   -->
</a>
<h3>Fields inherited from interface&nbsp;org.gradle.api.<a href="../Task.html" title="interface in org.gradle.api">Task</a></h3>
<code><a href="../Task.html#TASK_ACTION">TASK_ACTION</a>, <a href="../Task.html#TASK_CONSTRUCTOR_ARGS">TASK_CONSTRUCTOR_ARGS</a>, <a href="../Task.html#TASK_DEPENDS_ON">TASK_DEPENDS_ON</a>, <a href="../Task.html#TASK_DESCRIPTION">TASK_DESCRIPTION</a>, <a href="../Task.html#TASK_GROUP">TASK_GROUP</a>, <a href="../Task.html#TASK_NAME">TASK_NAME</a>, <a href="../Task.html#TASK_OVERWRITE">TASK_OVERWRITE</a>, <a href="../Task.html#TASK_TYPE">TASK_TYPE</a></code></li>
</ul>
</li>
</ul>
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.summary">
<!--   -->
</a>
<h3>Constructor Summary</h3>
<table class="memberSummary" summary="Constructor Summary table, listing constructors, and an explanation">
<caption><span>Constructors</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Constructor</th>
<th class="colLast" scope="col">Description</th>
</tr>
<tr class="altColor">
<th class="colConstructorName" scope="row"><code><span class="memberNameLink"><a href="#AbstractExecTask-java.lang.Class-">AbstractExecTask</a></span>&#8203;(java.lang.Class&lt;<a href="AbstractExecTask.html" title="type parameter in AbstractExecTask">T</a>&gt;&nbsp;taskType)</code></th>
<td class="colLast">&nbsp;</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colSecond" scope="col">Method</th>
<th class="colLast" scope="col">Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code><a href="AbstractExecTask.html" title="type parameter in AbstractExecTask">T</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#args-java.lang.Iterable-">args</a></span>&#8203;(java.lang.Iterable&lt;?&gt;&nbsp;args)</code></th>
<td class="colLast">
<div class="block">Adds arguments for the command to be executed.</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code><a href="AbstractExecTask.html" title="type parameter in AbstractExecTask">T</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#args-java.lang.Object...-">args</a></span>&#8203;(java.lang.Object...&nbsp;args)</code></th>
<td class="colLast">
<div class="block">Adds arguments for the command to be executed.</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code><a href="AbstractExecTask.html" title="type parameter in AbstractExecTask">T</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#commandLine-java.lang.Iterable-">commandLine</a></span>&#8203;(java.lang.Iterable&lt;?&gt;&nbsp;args)</code></th>
<td class="colLast">
<div class="block">Sets the full command line, including the executable to be executed plus its arguments.</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code><a href="AbstractExecTask.html" title="type parameter in AbstractExecTask">T</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#commandLine-java.lang.Object...-">commandLine</a></span>&#8203;(java.lang.Object...&nbsp;arguments)</code></th>
<td class="colLast">
<div class="block">Sets the full command line, including the executable to be executed plus its arguments.</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code><a href="AbstractExecTask.html" title="type parameter in AbstractExecTask">T</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#copyTo-org.gradle.process.ProcessForkOptions-">copyTo</a></span>&#8203;(<a href="../../process/ProcessForkOptions.html" title="interface in org.gradle.process">ProcessForkOptions</a>&nbsp;target)</code></th>
<td class="colLast">
<div class="block">Copies these options to the given target options.</div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code><a href="AbstractExecTask.html" title="type parameter in AbstractExecTask">T</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#environment-java.lang.String-java.lang.Object-">environment</a></span>&#8203;(java.lang.String&nbsp;name,
           java.lang.Object&nbsp;value)</code></th>
<td class="colLast">
<div class="block">Adds an environment variable to the environment for this process.</div>
</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code><a href="AbstractExecTask.html" title="type parameter in AbstractExecTask">T</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#environment-java.util.Map-">environment</a></span>&#8203;(java.util.Map&lt;java.lang.String,&#8203;?&gt;&nbsp;environmentVariables)</code></th>
<td class="colLast">
<div class="block">Adds some environment variables to the environment for this process.</div>
</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code>protected void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#exec--">exec</a></span>()</code></th>
<td class="colLast">&nbsp;</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code><a href="AbstractExecTask.html" title="type parameter in AbstractExecTask">T</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#executable-java.lang.Object-">executable</a></span>&#8203;(java.lang.Object&nbsp;executable)</code></th>
<td class="colLast">
<div class="block">Sets the name of the executable to use.</div>
</td>
</tr>
<tr id="i9" class="rowColor">
<td class="colFirst"><code>java.util.List&lt;java.lang.String&gt;</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getArgs--">getArgs</a></span>()</code></th>
<td class="colLast">
<div class="block">Returns the arguments for the command to be executed.</div>
</td>
</tr>
<tr id="i10" class="altColor">
<td class="colFirst"><code>java.util.List&lt;<a href="../../process/CommandLineArgumentProvider.html" title="interface in org.gradle.process">CommandLineArgumentProvider</a>&gt;</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getArgumentProviders--">getArgumentProviders</a></span>()</code></th>
<td class="colLast">
<div class="block">Argument providers for the application.</div>
</td>
</tr>
<tr id="i11" class="rowColor">
<td class="colFirst"><code>java.util.List&lt;java.lang.String&gt;</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getCommandLine--">getCommandLine</a></span>()</code></th>
<td class="colLast">
<div class="block">Returns the full command line, including the executable plus its arguments.</div>
</td>
</tr>
<tr id="i12" class="altColor">
<td class="colFirst"><code>java.util.Map&lt;java.lang.String,&#8203;java.lang.Object&gt;</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getEnvironment--">getEnvironment</a></span>()</code></th>
<td class="colLast">
<div class="block">The environment variables to use for the process.</div>
</td>
</tr>
<tr id="i13" class="rowColor">
<td class="colFirst"><code>java.io.OutputStream</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getErrorOutput--">getErrorOutput</a></span>()</code></th>
<td class="colLast">
<div class="block">Returns the output stream to consume standard error from the process executing the command.</div>
</td>
</tr>
<tr id="i14" class="altColor">
<td class="colFirst"><code>protected org.gradle.process.internal.ExecActionFactory</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getExecActionFactory--">getExecActionFactory</a></span>()</code></th>
<td class="colLast">&nbsp;</td>
</tr>
<tr id="i15" class="rowColor">
<td class="colFirst"><code>java.lang.String</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getExecutable--">getExecutable</a></span>()</code></th>
<td class="colLast">
<div class="block">Returns the name of the executable to use.</div>
</td>
</tr>
<tr id="i16" class="altColor">
<td class="colFirst"><code><a href="../provider/Provider.html" title="interface in org.gradle.api.provider">Provider</a>&lt;<a href="../../process/ExecResult.html" title="interface in org.gradle.process">ExecResult</a>&gt;</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getExecutionResult--">getExecutionResult</a></span>()</code></th>
<td class="colLast">
<div class="block">Returns the result for the command run by this task.</div>
</td>
</tr>
<tr id="i17" class="rowColor">
<td class="colFirst"><code>protected <a href="../model/ObjectFactory.html" title="interface in org.gradle.api.model">ObjectFactory</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getObjectFactory--">getObjectFactory</a></span>()</code></th>
<td class="colLast">&nbsp;</td>
</tr>
<tr id="i18" class="altColor">
<td class="colFirst"><code>java.io.InputStream</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getStandardInput--">getStandardInput</a></span>()</code></th>
<td class="colLast">
<div class="block">Returns the standard input stream for the process executing the command.</div>
</td>
</tr>
<tr id="i19" class="rowColor">
<td class="colFirst"><code>java.io.OutputStream</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getStandardOutput--">getStandardOutput</a></span>()</code></th>
<td class="colLast">
<div class="block">Returns the output stream to consume standard output from the process executing the command.</div>
</td>
</tr>
<tr id="i20" class="altColor">
<td class="colFirst"><code>java.io.File</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getWorkingDir--">getWorkingDir</a></span>()</code></th>
<td class="colLast">
<div class="block">Returns the working directory for the process.</div>
</td>
</tr>
<tr id="i21" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#isIgnoreExitValue--">isIgnoreExitValue</a></span>()</code></th>
<td class="colLast">
<div class="block">Tells whether a non-zero exit value is ignored, or an exception thrown.</div>
</td>
</tr>
<tr id="i22" class="altColor">
<td class="colFirst"><code><a href="AbstractExecTask.html" title="type parameter in AbstractExecTask">T</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#setArgs-java.lang.Iterable-">setArgs</a></span>&#8203;(java.lang.Iterable&lt;?&gt;&nbsp;arguments)</code></th>
<td class="colLast">
<div class="block">Sets the arguments for the command to be executed.</div>
</td>
</tr>
<tr id="i23" class="rowColor">
<td class="colFirst"><code><a href="AbstractExecTask.html" title="type parameter in AbstractExecTask">T</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#setArgs-java.util.List-">setArgs</a></span>&#8203;(java.util.List&lt;java.lang.String&gt;&nbsp;arguments)</code></th>
<td class="colLast">
<div class="block">Sets the arguments for the command to be executed.</div>
</td>
</tr>
<tr id="i24" class="altColor">
<td class="colFirst"><code>void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#setCommandLine-java.lang.Iterable-">setCommandLine</a></span>&#8203;(java.lang.Iterable&lt;?&gt;&nbsp;args)</code></th>
<td class="colLast">
<div class="block">Sets the full command line, including the executable to be executed plus its arguments.</div>
</td>
</tr>
<tr id="i25" class="rowColor">
<td class="colFirst"><code>void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#setCommandLine-java.lang.Object...-">setCommandLine</a></span>&#8203;(java.lang.Object...&nbsp;args)</code></th>
<td class="colLast">
<div class="block">Sets the full command line, including the executable to be executed plus its arguments.</div>
</td>
</tr>
<tr id="i26" class="altColor">
<td class="colFirst"><code>void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#setCommandLine-java.util.List-">setCommandLine</a></span>&#8203;(java.util.List&lt;java.lang.String&gt;&nbsp;args)</code></th>
<td class="colLast">
<div class="block">Sets the full command line, including the executable to be executed plus its arguments.</div>
</td>
</tr>
<tr id="i27" class="rowColor">
<td class="colFirst"><code>void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#setEnvironment-java.util.Map-">setEnvironment</a></span>&#8203;(java.util.Map&lt;java.lang.String,&#8203;?&gt;&nbsp;environmentVariables)</code></th>
<td class="colLast">
<div class="block">Sets the environment variable to use for the process.</div>
</td>
</tr>
<tr id="i28" class="altColor">
<td class="colFirst"><code><a href="AbstractExecTask.html" title="type parameter in AbstractExecTask">T</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#setErrorOutput-java.io.OutputStream-">setErrorOutput</a></span>&#8203;(java.io.OutputStream&nbsp;outputStream)</code></th>
<td class="colLast">
<div class="block">Sets the output stream to consume standard error from the process executing the command.</div>
</td>
</tr>
<tr id="i29" class="rowColor">
<td class="colFirst"><code>void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#setExecutable-java.lang.Object-">setExecutable</a></span>&#8203;(java.lang.Object&nbsp;executable)</code></th>
<td class="colLast">
<div class="block">Sets the name of the executable to use.</div>
</td>
</tr>
<tr id="i30" class="altColor">
<td class="colFirst"><code>void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#setExecutable-java.lang.String-">setExecutable</a></span>&#8203;(java.lang.String&nbsp;executable)</code></th>
<td class="colLast">
<div class="block">Sets the name of the executable to use.</div>
</td>
</tr>
<tr id="i31" class="rowColor">
<td class="colFirst"><code><a href="AbstractExecTask.html" title="type parameter in AbstractExecTask">T</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#setIgnoreExitValue-boolean-">setIgnoreExitValue</a></span>&#8203;(boolean&nbsp;ignoreExitValue)</code></th>
<td class="colLast">
<div class="block">Sets whether a non-zero exit value is ignored, or an exception thrown.</div>
</td>
</tr>
<tr id="i32" class="altColor">
<td class="colFirst"><code><a href="AbstractExecTask.html" title="type parameter in AbstractExecTask">T</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#setStandardInput-java.io.InputStream-">setStandardInput</a></span>&#8203;(java.io.InputStream&nbsp;inputStream)</code></th>
<td class="colLast">
<div class="block">Sets the standard input stream for the process executing the command.</div>
</td>
</tr>
<tr id="i33" class="rowColor">
<td class="colFirst"><code><a href="AbstractExecTask.html" title="type parameter in AbstractExecTask">T</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#setStandardOutput-java.io.OutputStream-">setStandardOutput</a></span>&#8203;(java.io.OutputStream&nbsp;outputStream)</code></th>
<td class="colLast">
<div class="block">Sets the output stream to consume standard output from the process executing the command.</div>
</td>
</tr>
<tr id="i34" class="altColor">
<td class="colFirst"><code>void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#setWorkingDir-java.io.File-">setWorkingDir</a></span>&#8203;(java.io.File&nbsp;dir)</code></th>
<td class="colLast">
<div class="block">Sets the working directory for the process.</div>
</td>
</tr>
<tr id="i35" class="rowColor">
<td class="colFirst"><code>void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#setWorkingDir-java.lang.Object-">setWorkingDir</a></span>&#8203;(java.lang.Object&nbsp;dir)</code></th>
<td class="colLast">
<div class="block">Sets the working directory for the process.</div>
</td>
</tr>
<tr id="i36" class="altColor">
<td class="colFirst"><code><a href="AbstractExecTask.html" title="type parameter in AbstractExecTask">T</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#workingDir-java.lang.Object-">workingDir</a></span>&#8203;(java.lang.Object&nbsp;dir)</code></th>
<td class="colLast">
<div class="block">Sets the working directory for the process.</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.org.gradle.api.internal.ConventionTask">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;org.gradle.api.internal.ConventionTask</h3>
<code>conventionMapping, conventionMapping, getConventionMapping</code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.org.gradle.api.DefaultTask">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;org.gradle.api.<a href="../DefaultTask.html" title="class in org.gradle.api">DefaultTask</a></h3>
<code><a href="../DefaultTask.html#compareTo-org.gradle.api.Task-">compareTo</a>, <a href="../DefaultTask.html#configure-groovy.lang.Closure-">configure</a>, <a href="../DefaultTask.html#dependsOn-java.lang.Object...-">dependsOn</a>, <a href="../DefaultTask.html#doFirst-groovy.lang.Closure-">doFirst</a>, <a href="../DefaultTask.html#doFirst-java.lang.String-org.gradle.api.Action-">doFirst</a>, <a href="../DefaultTask.html#doFirst-org.gradle.api.Action-">doFirst</a>, <a href="../DefaultTask.html#doLast-groovy.lang.Closure-">doLast</a>, <a href="../DefaultTask.html#doLast-java.lang.String-org.gradle.api.Action-">doLast</a>, <a href="../DefaultTask.html#doLast-org.gradle.api.Action-">doLast</a>, <a href="../DefaultTask.html#finalizedBy-java.lang.Object...-">finalizedBy</a>, <a href="../DefaultTask.html#getActions--">getActions</a>, <a href="../DefaultTask.html#getAnt--">getAnt</a>, <a href="../DefaultTask.html#getDependsOn--">getDependsOn</a>, <a href="../DefaultTask.html#getDescription--">getDescription</a>, <a href="../DefaultTask.html#getDestroyables--">getDestroyables</a>, <a href="../DefaultTask.html#getDidWork--">getDidWork</a>, <a href="../DefaultTask.html#getEnabled--">getEnabled</a>, <a href="../DefaultTask.html#getExtensions--">getExtensions</a>, <a href="../DefaultTask.html#getFinalizedBy--">getFinalizedBy</a>, <a href="../DefaultTask.html#getGroup--">getGroup</a>, <a href="../DefaultTask.html#getInputs--">getInputs</a>, <a href="../DefaultTask.html#getLocalState--">getLocalState</a>, <a href="../DefaultTask.html#getLogger--">getLogger</a>, <a href="../DefaultTask.html#getLogging--">getLogging</a>, <a href="../DefaultTask.html#getMustRunAfter--">getMustRunAfter</a>, <a href="../DefaultTask.html#getName--">getName</a>, <a href="../DefaultTask.html#getOutputs--">getOutputs</a>, <a href="../DefaultTask.html#getPath--">getPath</a>, <a href="../DefaultTask.html#getProject--">getProject</a>, <a href="../DefaultTask.html#getShouldRunAfter--">getShouldRunAfter</a>, <a href="../DefaultTask.html#getState--">getState</a>, <a href="../DefaultTask.html#getTaskDependencies--">getTaskDependencies</a>, <a href="../DefaultTask.html#getTemporaryDir--">getTemporaryDir</a>, <a href="../DefaultTask.html#getTimeout--">getTimeout</a>, <a href="../DefaultTask.html#hasProperty-java.lang.String-">hasProperty</a>, <a href="../DefaultTask.html#mustRunAfter-java.lang.Object...-">mustRunAfter</a>, <a href="../DefaultTask.html#onlyIf-groovy.lang.Closure-">onlyIf</a>, <a href="../DefaultTask.html#onlyIf-java.lang.String-org.gradle.api.specs.Spec-">onlyIf</a>, <a href="../DefaultTask.html#onlyIf-org.gradle.api.specs.Spec-">onlyIf</a>, <a href="../DefaultTask.html#property-java.lang.String-">property</a>, <a href="../DefaultTask.html#setActions-java.util.List-">setActions</a>, <a href="../DefaultTask.html#setDependsOn-java.lang.Iterable-">setDependsOn</a>, <a href="../DefaultTask.html#setDescription-java.lang.String-">setDescription</a>, <a href="../DefaultTask.html#setDidWork-boolean-">setDidWork</a>, <a href="../DefaultTask.html#setEnabled-boolean-">setEnabled</a>, <a href="../DefaultTask.html#setFinalizedBy-java.lang.Iterable-">setFinalizedBy</a>, <a href="../DefaultTask.html#setGroup-java.lang.String-">setGroup</a>, <a href="../DefaultTask.html#setMustRunAfter-java.lang.Iterable-">setMustRunAfter</a>, <a href="../DefaultTask.html#setOnlyIf-groovy.lang.Closure-">setOnlyIf</a>, <a href="../DefaultTask.html#setOnlyIf-java.lang.String-org.gradle.api.specs.Spec-">setOnlyIf</a>, <a href="../DefaultTask.html#setOnlyIf-org.gradle.api.specs.Spec-">setOnlyIf</a>, <a href="../DefaultTask.html#setProperty-java.lang.String-java.lang.Object-">setProperty</a>, <a href="../DefaultTask.html#setShouldRunAfter-java.lang.Iterable-">setShouldRunAfter</a>, <a href="../DefaultTask.html#shouldRunAfter-java.lang.Object...-">shouldRunAfter</a>, <a href="../DefaultTask.html#usesService-org.gradle.api.provider.Provider-">usesService</a></code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.org.gradle.api.internal.AbstractTask">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;org.gradle.api.internal.AbstractTask</h3>
<code>acceptServiceReferences, appendParallelSafeAction, doNotTrackState, getAsDynamicObject, getConvention, getIdentityPath, getImpliesSubProjects, getLifecycleDependencies, getOnlyIf, getReasonNotToTrackState, getReasonTaskIsIncompatibleWithConfigurationCache, getRequiredServices, getServices, getSharedResources, getStandardOutputCapture, getTaskActions, getTaskIdentity, getTemporaryDirFactory, hasTaskActions, injectIntoNewInstance, isCompatibleWithConfigurationCache, isEnabled, isHasCustomActions, notCompatibleWithConfigurationCache, prependParallelSafeAction, setImpliesSubProjects</code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.Object</h3>
<code>clone, equals, finalize, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.org.gradle.api.Task">
<!--   -->
</a>
<h3>Methods inherited from interface&nbsp;org.gradle.api.<a href="../Task.html" title="interface in org.gradle.api">Task</a></h3>
<code><a href="../Task.html#doNotTrackState-java.lang.String-">doNotTrackState</a>, <a href="../Task.html#getConvention--">getConvention</a>, <a href="../Task.html#notCompatibleWithConfigurationCache-java.lang.String-">notCompatibleWithConfigurationCache</a></code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.detail">
<!--   -->
</a>
<h3>Constructor Detail</h3>
<a name="AbstractExecTask-java.lang.Class-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>AbstractExecTask</h4>
<pre>public&nbsp;AbstractExecTask&#8203;(java.lang.Class&lt;<a href="AbstractExecTask.html" title="type parameter in AbstractExecTask">T</a>&gt;&nbsp;taskType)</pre>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="getObjectFactory--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getObjectFactory</h4>
<pre class="methodSignature">@Inject
protected&nbsp;<a href="../model/ObjectFactory.html" title="interface in org.gradle.api.model">ObjectFactory</a>&nbsp;getObjectFactory()</pre>
</li>
</ul>
<a name="getExecActionFactory--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getExecActionFactory</h4>
<pre class="methodSignature">@Inject
protected&nbsp;org.gradle.process.internal.ExecActionFactory&nbsp;getExecActionFactory()</pre>
</li>
</ul>
<a name="exec--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>exec</h4>
<pre class="methodSignature">protected&nbsp;void&nbsp;exec()</pre>
</li>
</ul>
<a name="commandLine-java.lang.Object...-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>commandLine</h4>
<pre class="methodSignature">public&nbsp;<a href="AbstractExecTask.html" title="type parameter in AbstractExecTask">T</a>&nbsp;commandLine&#8203;(java.lang.Object...&nbsp;arguments)</pre>
<div class="block">Sets the full command line, including the executable to be executed plus its arguments.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../process/ExecSpec.html#commandLine-java.lang.Object...-">commandLine</a></code>&nbsp;in interface&nbsp;<code><a href="../../process/ExecSpec.html" title="interface in org.gradle.process">ExecSpec</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>arguments</code> - the command plus the args to be executed</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>this</dd>
</dl>
</li>
</ul>
<a name="commandLine-java.lang.Iterable-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>commandLine</h4>
<pre class="methodSignature">public&nbsp;<a href="AbstractExecTask.html" title="type parameter in AbstractExecTask">T</a>&nbsp;commandLine&#8203;(java.lang.Iterable&lt;?&gt;&nbsp;args)</pre>
<div class="block">Sets the full command line, including the executable to be executed plus its arguments.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../process/ExecSpec.html#commandLine-java.lang.Iterable-">commandLine</a></code>&nbsp;in interface&nbsp;<code><a href="../../process/ExecSpec.html" title="interface in org.gradle.process">ExecSpec</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>args</code> - the command plus the args to be executed</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>this</dd>
</dl>
</li>
</ul>
<a name="args-java.lang.Object...-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>args</h4>
<pre class="methodSignature">public&nbsp;<a href="AbstractExecTask.html" title="type parameter in AbstractExecTask">T</a>&nbsp;args&#8203;(java.lang.Object...&nbsp;args)</pre>
<div class="block">Adds arguments for the command to be executed.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../process/ExecSpec.html#args-java.lang.Object...-">args</a></code>&nbsp;in interface&nbsp;<code><a href="../../process/ExecSpec.html" title="interface in org.gradle.process">ExecSpec</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>args</code> - args for the command</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>this</dd>
</dl>
</li>
</ul>
<a name="args-java.lang.Iterable-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>args</h4>
<pre class="methodSignature">public&nbsp;<a href="AbstractExecTask.html" title="type parameter in AbstractExecTask">T</a>&nbsp;args&#8203;(java.lang.Iterable&lt;?&gt;&nbsp;args)</pre>
<div class="block">Adds arguments for the command to be executed.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../process/ExecSpec.html#args-java.lang.Iterable-">args</a></code>&nbsp;in interface&nbsp;<code><a href="../../process/ExecSpec.html" title="interface in org.gradle.process">ExecSpec</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>args</code> - args for the command</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>this</dd>
</dl>
</li>
</ul>
<a name="setArgs-java.util.List-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setArgs</h4>
<pre class="methodSignature">public&nbsp;<a href="AbstractExecTask.html" title="type parameter in AbstractExecTask">T</a>&nbsp;setArgs&#8203;(java.util.List&lt;java.lang.String&gt;&nbsp;arguments)</pre>
<div class="block">Sets the arguments for the command to be executed.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../process/ExecSpec.html#setArgs-java.util.List-">setArgs</a></code>&nbsp;in interface&nbsp;<code><a href="../../process/ExecSpec.html" title="interface in org.gradle.process">ExecSpec</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>arguments</code> - args for the command</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>this</dd>
</dl>
</li>
</ul>
<a name="setArgs-java.lang.Iterable-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setArgs</h4>
<pre class="methodSignature">public&nbsp;<a href="AbstractExecTask.html" title="type parameter in AbstractExecTask">T</a>&nbsp;setArgs&#8203;(@Nullable
                 java.lang.Iterable&lt;?&gt;&nbsp;arguments)</pre>
<div class="block">Sets the arguments for the command to be executed.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../process/ExecSpec.html#setArgs-java.lang.Iterable-">setArgs</a></code>&nbsp;in interface&nbsp;<code><a href="../../process/ExecSpec.html" title="interface in org.gradle.process">ExecSpec</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>arguments</code> - args for the command</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>this</dd>
</dl>
</li>
</ul>
<a name="getArgs--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getArgs</h4>
<pre class="methodSignature">@Nullable
<a href="Optional.html" title="annotation in org.gradle.api.tasks">@Optional</a>
<a href="Input.html" title="annotation in org.gradle.api.tasks">@Input</a>
public&nbsp;java.util.List&lt;java.lang.String&gt;&nbsp;getArgs()</pre>
<div class="block">Returns the arguments for the command to be executed. Defaults to an empty list.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../process/ExecSpec.html#getArgs--">getArgs</a></code>&nbsp;in interface&nbsp;<code><a href="../../process/ExecSpec.html" title="interface in org.gradle.process">ExecSpec</a></code></dd>
</dl>
</li>
</ul>
<a name="getArgumentProviders--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getArgumentProviders</h4>
<pre class="methodSignature">public&nbsp;java.util.List&lt;<a href="../../process/CommandLineArgumentProvider.html" title="interface in org.gradle.process">CommandLineArgumentProvider</a>&gt;&nbsp;getArgumentProviders()</pre>
<div class="block">Argument providers for the application.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../process/ExecSpec.html#getArgumentProviders--">getArgumentProviders</a></code>&nbsp;in interface&nbsp;<code><a href="../../process/ExecSpec.html" title="interface in org.gradle.process">ExecSpec</a></code></dd>
</dl>
</li>
</ul>
<a name="getCommandLine--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getCommandLine</h4>
<pre class="methodSignature"><a href="Internal.html" title="annotation in org.gradle.api.tasks">@Internal</a>
public&nbsp;java.util.List&lt;java.lang.String&gt;&nbsp;getCommandLine()</pre>
<div class="block">Returns the full command line, including the executable plus its arguments.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../process/BaseExecSpec.html#getCommandLine--">getCommandLine</a></code>&nbsp;in interface&nbsp;<code><a href="../../process/BaseExecSpec.html" title="interface in org.gradle.process">BaseExecSpec</a></code></dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>The full command line, including the executable plus its arguments</dd>
</dl>
</li>
</ul>
<a name="setCommandLine-java.util.List-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setCommandLine</h4>
<pre class="methodSignature">public&nbsp;void&nbsp;setCommandLine&#8203;(java.util.List&lt;java.lang.String&gt;&nbsp;args)</pre>
<div class="block">Sets the full command line, including the executable to be executed plus its arguments.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../process/ExecSpec.html#setCommandLine-java.util.List-">setCommandLine</a></code>&nbsp;in interface&nbsp;<code><a href="../../process/ExecSpec.html" title="interface in org.gradle.process">ExecSpec</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>args</code> - the command plus the args to be executed</dd>
</dl>
</li>
</ul>
<a name="setCommandLine-java.lang.Iterable-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setCommandLine</h4>
<pre class="methodSignature">public&nbsp;void&nbsp;setCommandLine&#8203;(java.lang.Iterable&lt;?&gt;&nbsp;args)</pre>
<div class="block">Sets the full command line, including the executable to be executed plus its arguments.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../process/ExecSpec.html#setCommandLine-java.lang.Iterable-">setCommandLine</a></code>&nbsp;in interface&nbsp;<code><a href="../../process/ExecSpec.html" title="interface in org.gradle.process">ExecSpec</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>args</code> - the command plus the args to be executed</dd>
</dl>
</li>
</ul>
<a name="setCommandLine-java.lang.Object...-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setCommandLine</h4>
<pre class="methodSignature">public&nbsp;void&nbsp;setCommandLine&#8203;(java.lang.Object...&nbsp;args)</pre>
<div class="block">Sets the full command line, including the executable to be executed plus its arguments.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../process/ExecSpec.html#setCommandLine-java.lang.Object...-">setCommandLine</a></code>&nbsp;in interface&nbsp;<code><a href="../../process/ExecSpec.html" title="interface in org.gradle.process">ExecSpec</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>args</code> - the command plus the args to be executed</dd>
</dl>
</li>
</ul>
<a name="getExecutable--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getExecutable</h4>
<pre class="methodSignature">@Nullable
<a href="Optional.html" title="annotation in org.gradle.api.tasks">@Optional</a>
<a href="Input.html" title="annotation in org.gradle.api.tasks">@Input</a>
public&nbsp;java.lang.String&nbsp;getExecutable()</pre>
<div class="block">Returns the name of the executable to use.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../process/ProcessForkOptions.html#getExecutable--">getExecutable</a></code>&nbsp;in interface&nbsp;<code><a href="../../process/ProcessForkOptions.html" title="interface in org.gradle.process">ProcessForkOptions</a></code></dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>The executable.</dd>
</dl>
</li>
</ul>
<a name="setExecutable-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setExecutable</h4>
<pre class="methodSignature">public&nbsp;void&nbsp;setExecutable&#8203;(@Nullable
                          java.lang.String&nbsp;executable)</pre>
<div class="block">Sets the name of the executable to use.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../process/ProcessForkOptions.html#setExecutable-java.lang.String-">setExecutable</a></code>&nbsp;in interface&nbsp;<code><a href="../../process/ProcessForkOptions.html" title="interface in org.gradle.process">ProcessForkOptions</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>executable</code> - The executable. Must not be null.</dd>
</dl>
</li>
</ul>
<a name="setExecutable-java.lang.Object-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setExecutable</h4>
<pre class="methodSignature">public&nbsp;void&nbsp;setExecutable&#8203;(java.lang.Object&nbsp;executable)</pre>
<div class="block">Sets the name of the executable to use.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../process/ProcessForkOptions.html#setExecutable-java.lang.Object-">setExecutable</a></code>&nbsp;in interface&nbsp;<code><a href="../../process/ProcessForkOptions.html" title="interface in org.gradle.process">ProcessForkOptions</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>executable</code> - The executable. Must not be null.</dd>
</dl>
</li>
</ul>
<a name="executable-java.lang.Object-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>executable</h4>
<pre class="methodSignature">public&nbsp;<a href="AbstractExecTask.html" title="type parameter in AbstractExecTask">T</a>&nbsp;executable&#8203;(java.lang.Object&nbsp;executable)</pre>
<div class="block">Sets the name of the executable to use.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../process/ProcessForkOptions.html#executable-java.lang.Object-">executable</a></code>&nbsp;in interface&nbsp;<code><a href="../../process/ProcessForkOptions.html" title="interface in org.gradle.process">ProcessForkOptions</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>executable</code> - The executable. Must not be null.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>this</dd>
</dl>
</li>
</ul>
<a name="getWorkingDir--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getWorkingDir</h4>
<pre class="methodSignature"><a href="Internal.html" title="annotation in org.gradle.api.tasks">@Internal</a>
public&nbsp;java.io.File&nbsp;getWorkingDir()</pre>
<div class="block">Returns the working directory for the process. Defaults to the project directory.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../process/ProcessForkOptions.html#getWorkingDir--">getWorkingDir</a></code>&nbsp;in interface&nbsp;<code><a href="../../process/ProcessForkOptions.html" title="interface in org.gradle.process">ProcessForkOptions</a></code></dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>The working directory. Never returns null.</dd>
</dl>
</li>
</ul>
<a name="setWorkingDir-java.io.File-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setWorkingDir</h4>
<pre class="methodSignature">public&nbsp;void&nbsp;setWorkingDir&#8203;(java.io.File&nbsp;dir)</pre>
<div class="block">Sets the working directory for the process.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../process/ProcessForkOptions.html#setWorkingDir-java.io.File-">setWorkingDir</a></code>&nbsp;in interface&nbsp;<code><a href="../../process/ProcessForkOptions.html" title="interface in org.gradle.process">ProcessForkOptions</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>dir</code> - The working directory. Must not be null.</dd>
</dl>
</li>
</ul>
<a name="setWorkingDir-java.lang.Object-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setWorkingDir</h4>
<pre class="methodSignature">public&nbsp;void&nbsp;setWorkingDir&#8203;(java.lang.Object&nbsp;dir)</pre>
<div class="block">Sets the working directory for the process. The supplied argument is evaluated as per <a href="../Project.html#file-java.lang.Object-"><code>Project.file(Object)</code></a>.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../process/ProcessForkOptions.html#setWorkingDir-java.lang.Object-">setWorkingDir</a></code>&nbsp;in interface&nbsp;<code><a href="../../process/ProcessForkOptions.html" title="interface in org.gradle.process">ProcessForkOptions</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>dir</code> - The working directory. Must not be null.</dd>
</dl>
</li>
</ul>
<a name="workingDir-java.lang.Object-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>workingDir</h4>
<pre class="methodSignature">public&nbsp;<a href="AbstractExecTask.html" title="type parameter in AbstractExecTask">T</a>&nbsp;workingDir&#8203;(java.lang.Object&nbsp;dir)</pre>
<div class="block">Sets the working directory for the process. The supplied argument is evaluated as per <a href="../Project.html#file-java.lang.Object-"><code>Project.file(Object)</code></a>.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../process/ProcessForkOptions.html#workingDir-java.lang.Object-">workingDir</a></code>&nbsp;in interface&nbsp;<code><a href="../../process/ProcessForkOptions.html" title="interface in org.gradle.process">ProcessForkOptions</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>dir</code> - The working directory. Must not be null.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>this</dd>
</dl>
</li>
</ul>
<a name="getEnvironment--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getEnvironment</h4>
<pre class="methodSignature"><a href="Internal.html" title="annotation in org.gradle.api.tasks">@Internal</a>
public&nbsp;java.util.Map&lt;java.lang.String,&#8203;java.lang.Object&gt;&nbsp;getEnvironment()</pre>
<div class="block">The environment variables to use for the process. Defaults to the environment of this process.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../process/ProcessForkOptions.html#getEnvironment--">getEnvironment</a></code>&nbsp;in interface&nbsp;<code><a href="../../process/ProcessForkOptions.html" title="interface in org.gradle.process">ProcessForkOptions</a></code></dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>The environment. Returns an empty map when there are no environment variables.</dd>
</dl>
</li>
</ul>
<a name="setEnvironment-java.util.Map-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setEnvironment</h4>
<pre class="methodSignature">public&nbsp;void&nbsp;setEnvironment&#8203;(java.util.Map&lt;java.lang.String,&#8203;?&gt;&nbsp;environmentVariables)</pre>
<div class="block">Sets the environment variable to use for the process.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../process/ProcessForkOptions.html#setEnvironment-java.util.Map-">setEnvironment</a></code>&nbsp;in interface&nbsp;<code><a href="../../process/ProcessForkOptions.html" title="interface in org.gradle.process">ProcessForkOptions</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>environmentVariables</code> - The environment variables. Must not be null.</dd>
</dl>
</li>
</ul>
<a name="environment-java.lang.String-java.lang.Object-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>environment</h4>
<pre class="methodSignature">public&nbsp;<a href="AbstractExecTask.html" title="type parameter in AbstractExecTask">T</a>&nbsp;environment&#8203;(java.lang.String&nbsp;name,
                     java.lang.Object&nbsp;value)</pre>
<div class="block">Adds an environment variable to the environment for this process.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../process/ProcessForkOptions.html#environment-java.lang.String-java.lang.Object-">environment</a></code>&nbsp;in interface&nbsp;<code><a href="../../process/ProcessForkOptions.html" title="interface in org.gradle.process">ProcessForkOptions</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>name</code> - The name of the variable.</dd>
<dd><code>value</code> - The value for the variable. Must not be null.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>this</dd>
</dl>
</li>
</ul>
<a name="environment-java.util.Map-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>environment</h4>
<pre class="methodSignature">public&nbsp;<a href="AbstractExecTask.html" title="type parameter in AbstractExecTask">T</a>&nbsp;environment&#8203;(java.util.Map&lt;java.lang.String,&#8203;?&gt;&nbsp;environmentVariables)</pre>
<div class="block">Adds some environment variables to the environment for this process.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../process/ProcessForkOptions.html#environment-java.util.Map-">environment</a></code>&nbsp;in interface&nbsp;<code><a href="../../process/ProcessForkOptions.html" title="interface in org.gradle.process">ProcessForkOptions</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>environmentVariables</code> - The environment variables. Must not be null.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>this</dd>
</dl>
</li>
</ul>
<a name="copyTo-org.gradle.process.ProcessForkOptions-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>copyTo</h4>
<pre class="methodSignature">public&nbsp;<a href="AbstractExecTask.html" title="type parameter in AbstractExecTask">T</a>&nbsp;copyTo&#8203;(<a href="../../process/ProcessForkOptions.html" title="interface in org.gradle.process">ProcessForkOptions</a>&nbsp;target)</pre>
<div class="block">Copies these options to the given target options.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../process/ProcessForkOptions.html#copyTo-org.gradle.process.ProcessForkOptions-">copyTo</a></code>&nbsp;in interface&nbsp;<code><a href="../../process/ProcessForkOptions.html" title="interface in org.gradle.process">ProcessForkOptions</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>target</code> - The target options</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>this</dd>
</dl>
</li>
</ul>
<a name="setStandardInput-java.io.InputStream-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setStandardInput</h4>
<pre class="methodSignature">public&nbsp;<a href="AbstractExecTask.html" title="type parameter in AbstractExecTask">T</a>&nbsp;setStandardInput&#8203;(java.io.InputStream&nbsp;inputStream)</pre>
<div class="block">Sets the standard input stream for the process executing the command. The stream is closed after the process
 completes.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../process/BaseExecSpec.html#setStandardInput-java.io.InputStream-">setStandardInput</a></code>&nbsp;in interface&nbsp;<code><a href="../../process/BaseExecSpec.html" title="interface in org.gradle.process">BaseExecSpec</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>inputStream</code> - The standard input stream for the process. Must not be null.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>this</dd>
</dl>
</li>
</ul>
<a name="getStandardInput--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getStandardInput</h4>
<pre class="methodSignature"><a href="Internal.html" title="annotation in org.gradle.api.tasks">@Internal</a>
public&nbsp;java.io.InputStream&nbsp;getStandardInput()</pre>
<div class="block">Returns the standard input stream for the process executing the command. The stream is closed after the process
 completes. Defaults to an empty stream.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../process/BaseExecSpec.html#getStandardInput--">getStandardInput</a></code>&nbsp;in interface&nbsp;<code><a href="../../process/BaseExecSpec.html" title="interface in org.gradle.process">BaseExecSpec</a></code></dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>The standard input stream.</dd>
</dl>
</li>
</ul>
<a name="setStandardOutput-java.io.OutputStream-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setStandardOutput</h4>
<pre class="methodSignature">public&nbsp;<a href="AbstractExecTask.html" title="type parameter in AbstractExecTask">T</a>&nbsp;setStandardOutput&#8203;(java.io.OutputStream&nbsp;outputStream)</pre>
<div class="block">Sets the output stream to consume standard output from the process executing the command. The stream is closed
 after the process completes.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../process/BaseExecSpec.html#setStandardOutput-java.io.OutputStream-">setStandardOutput</a></code>&nbsp;in interface&nbsp;<code><a href="../../process/BaseExecSpec.html" title="interface in org.gradle.process">BaseExecSpec</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>outputStream</code> - The standard output stream for the process. Must not be null.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>this</dd>
</dl>
</li>
</ul>
<a name="getStandardOutput--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getStandardOutput</h4>
<pre class="methodSignature"><a href="Internal.html" title="annotation in org.gradle.api.tasks">@Internal</a>
public&nbsp;java.io.OutputStream&nbsp;getStandardOutput()</pre>
<div class="block">Returns the output stream to consume standard output from the process executing the command. Defaults to <code>
 System.out</code>.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../process/BaseExecSpec.html#getStandardOutput--">getStandardOutput</a></code>&nbsp;in interface&nbsp;<code><a href="../../process/BaseExecSpec.html" title="interface in org.gradle.process">BaseExecSpec</a></code></dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>The output stream</dd>
</dl>
</li>
</ul>
<a name="setErrorOutput-java.io.OutputStream-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setErrorOutput</h4>
<pre class="methodSignature">public&nbsp;<a href="AbstractExecTask.html" title="type parameter in AbstractExecTask">T</a>&nbsp;setErrorOutput&#8203;(java.io.OutputStream&nbsp;outputStream)</pre>
<div class="block">Sets the output stream to consume standard error from the process executing the command. The stream is closed
 after the process completes.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../process/BaseExecSpec.html#setErrorOutput-java.io.OutputStream-">setErrorOutput</a></code>&nbsp;in interface&nbsp;<code><a href="../../process/BaseExecSpec.html" title="interface in org.gradle.process">BaseExecSpec</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>outputStream</code> - The standard output error stream for the process. Must not be null.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>this</dd>
</dl>
</li>
</ul>
<a name="getErrorOutput--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getErrorOutput</h4>
<pre class="methodSignature"><a href="Internal.html" title="annotation in org.gradle.api.tasks">@Internal</a>
public&nbsp;java.io.OutputStream&nbsp;getErrorOutput()</pre>
<div class="block">Returns the output stream to consume standard error from the process executing the command. Default to <code>
 System.err</code>.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../process/BaseExecSpec.html#getErrorOutput--">getErrorOutput</a></code>&nbsp;in interface&nbsp;<code><a href="../../process/BaseExecSpec.html" title="interface in org.gradle.process">BaseExecSpec</a></code></dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>The error output stream.</dd>
</dl>
</li>
</ul>
<a name="setIgnoreExitValue-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setIgnoreExitValue</h4>
<pre class="methodSignature">public&nbsp;<a href="AbstractExecTask.html" title="type parameter in AbstractExecTask">T</a>&nbsp;setIgnoreExitValue&#8203;(boolean&nbsp;ignoreExitValue)</pre>
<div class="block">Sets whether a non-zero exit value is ignored, or an exception thrown.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../process/BaseExecSpec.html#setIgnoreExitValue-boolean-">setIgnoreExitValue</a></code>&nbsp;in interface&nbsp;<code><a href="../../process/BaseExecSpec.html" title="interface in org.gradle.process">BaseExecSpec</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>ignoreExitValue</code> - whether a non-zero exit value is ignored, or an exception thrown</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>this</dd>
</dl>
</li>
</ul>
<a name="isIgnoreExitValue--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isIgnoreExitValue</h4>
<pre class="methodSignature"><a href="Input.html" title="annotation in org.gradle.api.tasks">@Input</a>
public&nbsp;boolean&nbsp;isIgnoreExitValue()</pre>
<div class="block">Tells whether a non-zero exit value is ignored, or an exception thrown. Defaults to <code>false</code>.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../process/BaseExecSpec.html#isIgnoreExitValue--">isIgnoreExitValue</a></code>&nbsp;in interface&nbsp;<code><a href="../../process/BaseExecSpec.html" title="interface in org.gradle.process">BaseExecSpec</a></code></dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>whether a non-zero exit value is ignored, or an exception thrown</dd>
</dl>
</li>
</ul>
<a name="getExecutionResult--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>getExecutionResult</h4>
<pre class="methodSignature"><a href="Internal.html" title="annotation in org.gradle.api.tasks">@Internal</a>
public&nbsp;<a href="../provider/Provider.html" title="interface in org.gradle.api.provider">Provider</a>&lt;<a href="../../process/ExecResult.html" title="interface in org.gradle.process">ExecResult</a>&gt;&nbsp;getExecutionResult()</pre>
<div class="block">Returns the result for the command run by this task. The provider has no value if this task has not been executed yet.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>A provider of the result.</dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>6.1</dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../allclasses.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li><a href="#nested.class.summary">Nested</a>&nbsp;|&nbsp;</li>
<li><a href="#field.summary">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
