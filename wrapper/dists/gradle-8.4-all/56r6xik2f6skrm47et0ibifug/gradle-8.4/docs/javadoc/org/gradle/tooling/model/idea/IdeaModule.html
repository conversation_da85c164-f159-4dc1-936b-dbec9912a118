<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc -->
<title>IdeaModule (Gradle API 8.4)</title>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<link rel="stylesheet" type="text/css" href="../../../../../javadoc.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../../jquery/jquery-ui.min.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../../jquery-ui.overrides.css" title="Style">
<script type="text/javascript" src="../../../../../script.js"></script>
<script type="text/javascript" src="../../../../../jquery/jszip/dist/jszip.min.js"></script>
<script type="text/javascript" src="../../../../../jquery/jszip-utils/dist/jszip-utils.min.js"></script>
<!--[if IE]>
<script type="text/javascript" src="../../../../../jquery/jszip-utils/dist/jszip-utils-ie.min.js"></script>
<![endif]-->
<script type="text/javascript" src="../../../../../jquery/jquery-3.6.0.min.js"></script>
<script type="text/javascript" src="../../../../../jquery/jquery-ui.min.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="IdeaModule (Gradle API 8.4)";
        }
    }
    catch(err) {
    }
//-->
var data = {"i0":6,"i1":6,"i2":6,"i3":6,"i4":6,"i5":6,"i6":6,"i7":6};
var tabs = {65535:["t0","All Methods"],2:["t2","Instance Methods"],4:["t3","Abstract Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
var pathtoroot = "../../../../../";
var useModuleDirectories = false;
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<div class="fixedNav">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../index-all.html">Index</a></li>
<li><a href="../../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../../allclasses.html">All&nbsp;Classes</a></li>
</ul>
<ul class="navListSearch">
<li><label for="search">SEARCH:</label>
<input type="text" id="search" value="search" disabled="disabled">
<input type="reset" id="reset" value="reset" disabled="disabled">
</li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
</div>
<div class="navPadding">&nbsp;</div>
<script type="text/javascript"><!--
$('.navPadding').css('padding-top', $('.fixedNav').css("height"));
//-->
</script>
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle"><span class="packageLabelInType">Package</span>&nbsp;<a href="package-summary.html">org.gradle.tooling.model.idea</a></div>
<h2 title="Interface IdeaModule" class="title">Interface IdeaModule</h2>
</div>
<div class="contentContainer">
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt>All Superinterfaces:</dt>
<dd><code><a href="../Element.html" title="interface in org.gradle.tooling.model">Element</a></code>, <code><a href="../HasGradleProject.html" title="interface in org.gradle.tooling.model">HasGradleProject</a></code>, <code><a href="../HierarchicalElement.html" title="interface in org.gradle.tooling.model">HierarchicalElement</a></code>, <code><a href="../Model.html" title="interface in org.gradle.tooling.model">Model</a></code>, <code><a href="../ProjectModel.html" title="interface in org.gradle.tooling.model">ProjectModel</a></code></dd>
</dl>
<hr>
<pre>public interface <span class="typeNameLabel">IdeaModule</span>
extends <a href="../HierarchicalElement.html" title="interface in org.gradle.tooling.model">HierarchicalElement</a>, <a href="../HasGradleProject.html" title="interface in org.gradle.tooling.model">HasGradleProject</a></pre>
<div class="block">Represents information about the IDEA module.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>1.0-milestone-5</dd>
</dl>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t3" class="tableTab"><span><a href="javascript:show(4);">Abstract Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colSecond" scope="col">Method</th>
<th class="colLast" scope="col">Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code><a href="IdeaCompilerOutput.html" title="interface in org.gradle.tooling.model.idea">IdeaCompilerOutput</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getCompilerOutput--">getCompilerOutput</a></span>()</code></th>
<td class="colLast">
<div class="block">Returns information about idea compiler output (output dirs, inheritance of output dir, etc.)</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code><a href="../DomainObjectSet.html" title="interface in org.gradle.tooling.model">DomainObjectSet</a>&lt;? extends <a href="IdeaContentRoot.html" title="interface in org.gradle.tooling.model.idea">IdeaContentRoot</a>&gt;</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getContentRoots--">getContentRoots</a></span>()</code></th>
<td class="colLast">
<div class="block">All content roots.</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code><a href="../DomainObjectSet.html" title="interface in org.gradle.tooling.model">DomainObjectSet</a>&lt;? extends <a href="IdeaDependency.html" title="interface in org.gradle.tooling.model.idea">IdeaDependency</a>&gt;</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getDependencies--">getDependencies</a></span>()</code></th>
<td class="colLast">
<div class="block">dependencies of this module (i.e.</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code><a href="../GradleProject.html" title="interface in org.gradle.tooling.model">GradleProject</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getGradleProject--">getGradleProject</a></span>()</code></th>
<td class="colLast">
<div class="block">The gradle project that is associated with this module.</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code><a href="IdeaJavaLanguageSettings.html" title="interface in org.gradle.tooling.model.idea">IdeaJavaLanguageSettings</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getJavaLanguageSettings--">getJavaLanguageSettings</a></span>()</code></th>
<td class="colLast">
<div class="block">Returns the Java language settings for this element, or <code>null</code> if this element is not a Java element.</div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code>java.lang.String</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getJdkName--">getJdkName</a></span>()</code></th>
<td class="colLast">
<div class="block">Returns the name of the JDK.</div>
</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code><a href="IdeaProject.html" title="interface in org.gradle.tooling.model.idea">IdeaProject</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getParent--">getParent</a></span>()</code></th>
<td class="colLast">
<div class="block">Returns the project of this module.</div>
</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code><a href="IdeaProject.html" title="interface in org.gradle.tooling.model.idea">IdeaProject</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getProject--">getProject</a></span>()</code></th>
<td class="colLast">
<div class="block">Returns the project of this module.</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.org.gradle.tooling.model.Element">
<!--   -->
</a>
<h3>Methods inherited from interface&nbsp;org.gradle.tooling.model.<a href="../Element.html" title="interface in org.gradle.tooling.model">Element</a></h3>
<code><a href="../Element.html#getDescription--">getDescription</a>, <a href="../Element.html#getName--">getName</a></code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.org.gradle.tooling.model.HasGradleProject">
<!--   -->
</a>
<h3>Methods inherited from interface&nbsp;org.gradle.tooling.model.<a href="../HasGradleProject.html" title="interface in org.gradle.tooling.model">HasGradleProject</a></h3>
<code><a href="../HasGradleProject.html#getProjectIdentifier--">getProjectIdentifier</a></code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.org.gradle.tooling.model.HierarchicalElement">
<!--   -->
</a>
<h3>Methods inherited from interface&nbsp;org.gradle.tooling.model.<a href="../HierarchicalElement.html" title="interface in org.gradle.tooling.model">HierarchicalElement</a></h3>
<code><a href="../HierarchicalElement.html#getChildren--">getChildren</a></code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="getJavaLanguageSettings--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getJavaLanguageSettings</h4>
<pre class="methodSignature">@Nullable
<a href="IdeaJavaLanguageSettings.html" title="interface in org.gradle.tooling.model.idea">IdeaJavaLanguageSettings</a>&nbsp;getJavaLanguageSettings()
                                          throws <a href="../UnsupportedMethodException.html" title="class in org.gradle.tooling.model">UnsupportedMethodException</a></pre>
<div class="block">Returns the Java language settings for this element, or <code>null</code> if this element is not a Java element.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>The Java language settings.</dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code><a href="../UnsupportedMethodException.html" title="class in org.gradle.tooling.model">UnsupportedMethodException</a></code> - For Gradle versions older than 2.11, where this method is not supported.</dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>2.11</dd>
</dl>
</li>
</ul>
<a name="getJdkName--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getJdkName</h4>
<pre class="methodSignature">java.lang.String&nbsp;getJdkName()
                     throws <a href="../UnsupportedMethodException.html" title="class in org.gradle.tooling.model">UnsupportedMethodException</a></pre>
<div class="block">Returns the name of the JDK.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>The name of the JDK.</dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code><a href="../UnsupportedMethodException.html" title="class in org.gradle.tooling.model">UnsupportedMethodException</a></code></dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>3.4</dd>
</dl>
</li>
</ul>
<a name="getContentRoots--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getContentRoots</h4>
<pre class="methodSignature"><a href="../DomainObjectSet.html" title="interface in org.gradle.tooling.model">DomainObjectSet</a>&lt;? extends <a href="IdeaContentRoot.html" title="interface in org.gradle.tooling.model.idea">IdeaContentRoot</a>&gt;&nbsp;getContentRoots()</pre>
<div class="block">All content roots. Most idea modules have a single content root.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>content roots</dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>1.0-milestone-5</dd>
</dl>
</li>
</ul>
<a name="getGradleProject--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getGradleProject</h4>
<pre class="methodSignature"><a href="../GradleProject.html" title="interface in org.gradle.tooling.model">GradleProject</a>&nbsp;getGradleProject()</pre>
<div class="block">The gradle project that is associated with this module.
 Typically, a single module corresponds to a single gradle project.
 <p>
 See <a href="../HasGradleProject.html" title="interface in org.gradle.tooling.model"><code>HasGradleProject</code></a></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../HasGradleProject.html#getGradleProject--">getGradleProject</a></code>&nbsp;in interface&nbsp;<code><a href="../HasGradleProject.html" title="interface in org.gradle.tooling.model">HasGradleProject</a></code></dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>associated gradle project</dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>1.0-milestone-5</dd>
</dl>
</li>
</ul>
<a name="getParent--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getParent</h4>
<pre class="methodSignature"><a href="IdeaProject.html" title="interface in org.gradle.tooling.model.idea">IdeaProject</a>&nbsp;getParent()</pre>
<div class="block">Returns the project of this module.
 Alias to <a href="#getProject--"><code>getProject()</code></a></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../HierarchicalElement.html#getParent--">getParent</a></code>&nbsp;in interface&nbsp;<code><a href="../HierarchicalElement.html" title="interface in org.gradle.tooling.model">HierarchicalElement</a></code></dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>idea project</dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>1.0-milestone-5</dd>
</dl>
</li>
</ul>
<a name="getProject--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getProject</h4>
<pre class="methodSignature"><a href="IdeaProject.html" title="interface in org.gradle.tooling.model.idea">IdeaProject</a>&nbsp;getProject()</pre>
<div class="block">Returns the project of this module.
 Alias to <a href="#getParent--"><code>getParent()</code></a></div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>idea project</dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>1.0-milestone-5</dd>
</dl>
</li>
</ul>
<a name="getCompilerOutput--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getCompilerOutput</h4>
<pre class="methodSignature"><a href="IdeaCompilerOutput.html" title="interface in org.gradle.tooling.model.idea">IdeaCompilerOutput</a>&nbsp;getCompilerOutput()</pre>
<div class="block">Returns information about idea compiler output (output dirs, inheritance of output dir, etc.)</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>1.0-milestone-5</dd>
</dl>
</li>
</ul>
<a name="getDependencies--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>getDependencies</h4>
<pre class="methodSignature"><a href="../DomainObjectSet.html" title="interface in org.gradle.tooling.model">DomainObjectSet</a>&lt;? extends <a href="IdeaDependency.html" title="interface in org.gradle.tooling.model.idea">IdeaDependency</a>&gt;&nbsp;getDependencies()</pre>
<div class="block">dependencies of this module (i.e. module dependencies, library dependencies, etc.)</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>dependencies</dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>1.0-milestone-5</dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../index-all.html">Index</a></li>
<li><a href="../../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../../allclasses.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
