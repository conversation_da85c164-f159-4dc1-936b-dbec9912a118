<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc -->
<title>ObjectiveCSourceSet (Gradle API 8.4)</title>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<link rel="stylesheet" type="text/css" href="../../../../javadoc.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../jquery/jquery-ui.min.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../jquery-ui.overrides.css" title="Style">
<script type="text/javascript" src="../../../../script.js"></script>
<script type="text/javascript" src="../../../../jquery/jszip/dist/jszip.min.js"></script>
<script type="text/javascript" src="../../../../jquery/jszip-utils/dist/jszip-utils.min.js"></script>
<!--[if IE]>
<script type="text/javascript" src="../../../../jquery/jszip-utils/dist/jszip-utils-ie.min.js"></script>
<![endif]-->
<script type="text/javascript" src="../../../../jquery/jquery-3.6.0.min.js"></script>
<script type="text/javascript" src="../../../../jquery/jquery-ui.min.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="ObjectiveCSourceSet (Gradle API 8.4)";
        }
    }
    catch(err) {
    }
//-->
var pathtoroot = "../../../../";
var useModuleDirectories = false;
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<div class="fixedNav">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../allclasses.html">All&nbsp;Classes</a></li>
</ul>
<ul class="navListSearch">
<li><label for="search">SEARCH:</label>
<input type="text" id="search" value="search" disabled="disabled">
<input type="reset" id="reset" value="reset" disabled="disabled">
</li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li><a href="#nested.class.summary">Nested</a>&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li>Method</li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
</div>
<div class="navPadding">&nbsp;</div>
<script type="text/javascript"><!--
$('.navPadding').css('padding-top', $('.fixedNav').css("height"));
//-->
</script>
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle"><span class="packageLabelInType">Package</span>&nbsp;<a href="package-summary.html">org.gradle.language.objectivec</a></div>
<h2 title="Interface ObjectiveCSourceSet" class="title">Interface ObjectiveCSourceSet</h2>
</div>
<div class="contentContainer">
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt>All Superinterfaces:</dt>
<dd><code><a href="../../api/Buildable.html" title="interface in org.gradle.api">Buildable</a></code>, <code><a href="../../api/BuildableComponentSpec.html" title="interface in org.gradle.api">BuildableComponentSpec</a></code>, <code><a href="../../platform/base/ComponentSpec.html" title="interface in org.gradle.platform.base">ComponentSpec</a></code>, <code><a href="../nativeplatform/DependentSourceSet.html" title="interface in org.gradle.language.nativeplatform">DependentSourceSet</a></code>, <code><a href="../nativeplatform/HeaderExportingSourceSet.html" title="interface in org.gradle.language.nativeplatform">HeaderExportingSourceSet</a></code>, <code><a href="../base/LanguageSourceSet.html" title="interface in org.gradle.language.base">LanguageSourceSet</a></code>, <code><a href="../../model/ModelElement.html" title="interface in org.gradle.model">ModelElement</a></code>, <code><a href="../../api/Named.html" title="interface in org.gradle.api">Named</a></code></dd>
</dl>
<hr>
<pre><a href="../../api/Incubating.html" title="annotation in org.gradle.api">@Incubating</a>
public interface <span class="typeNameLabel">ObjectiveCSourceSet</span>
extends <a href="../nativeplatform/HeaderExportingSourceSet.html" title="interface in org.gradle.language.nativeplatform">HeaderExportingSourceSet</a>, <a href="../base/LanguageSourceSet.html" title="interface in org.gradle.language.base">LanguageSourceSet</a>, <a href="../nativeplatform/DependentSourceSet.html" title="interface in org.gradle.language.nativeplatform">DependentSourceSet</a></pre>
<div class="block">A set of Objective-C source files.

 <p>An ObjectiveC source set contains a set of source files, together with an optional set of exported header files.</p>

 <pre class='autoTested'>
 plugins {
     id 'objective-c'
 }

 model {
     components {
         main(NativeLibrarySpec) {
             sources {
                 objc {
                     source {
                         srcDirs "src/main/objectiveC", "src/shared/objectiveC"
                         include "**/*.m"
                     }
                     exportedHeaders {
                         srcDirs "src/main/include"
                     }
                 }
             }
         }
     }
 }
 </pre></div>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ======== NESTED CLASS SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="nested.class.summary">
<!--   -->
</a>
<h3>Nested Class Summary</h3>
<ul class="blockList">
<li class="blockList"><a name="nested.classes.inherited.from.class.org.gradle.api.Named">
<!--   -->
</a>
<h3>Nested classes/interfaces inherited from interface&nbsp;org.gradle.api.<a href="../../api/Named.html" title="interface in org.gradle.api">Named</a></h3>
<code><a href="../../api/Named.Namer.html" title="class in org.gradle.api">Named.Namer</a></code></li>
</ul>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.org.gradle.api.Buildable">
<!--   -->
</a>
<h3>Methods inherited from interface&nbsp;org.gradle.api.<a href="../../api/Buildable.html" title="interface in org.gradle.api">Buildable</a></h3>
<code><a href="../../api/Buildable.html#getBuildDependencies--">getBuildDependencies</a></code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.org.gradle.api.BuildableComponentSpec">
<!--   -->
</a>
<h3>Methods inherited from interface&nbsp;org.gradle.api.<a href="../../api/BuildableComponentSpec.html" title="interface in org.gradle.api">BuildableComponentSpec</a></h3>
<code><a href="../../api/BuildableComponentSpec.html#builtBy-java.lang.Object...-">builtBy</a>, <a href="../../api/BuildableComponentSpec.html#getBuildTask--">getBuildTask</a>, <a href="../../api/BuildableComponentSpec.html#hasBuildDependencies--">hasBuildDependencies</a>, <a href="../../api/BuildableComponentSpec.html#setBuildTask-org.gradle.api.Task-">setBuildTask</a></code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.org.gradle.platform.base.ComponentSpec">
<!--   -->
</a>
<h3>Methods inherited from interface&nbsp;org.gradle.platform.base.<a href="../../platform/base/ComponentSpec.html" title="interface in org.gradle.platform.base">ComponentSpec</a></h3>
<code><a href="../../platform/base/ComponentSpec.html#getProjectPath--">getProjectPath</a></code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.org.gradle.language.nativeplatform.DependentSourceSet">
<!--   -->
</a>
<h3>Methods inherited from interface&nbsp;org.gradle.language.nativeplatform.<a href="../nativeplatform/DependentSourceSet.html" title="interface in org.gradle.language.nativeplatform">DependentSourceSet</a></h3>
<code><a href="../nativeplatform/DependentSourceSet.html#getLibs--">getLibs</a>, <a href="../nativeplatform/DependentSourceSet.html#getPreCompiledHeader--">getPreCompiledHeader</a>, <a href="../nativeplatform/DependentSourceSet.html#lib-java.lang.Object-">lib</a>, <a href="../nativeplatform/DependentSourceSet.html#setPreCompiledHeader-java.lang.String-">setPreCompiledHeader</a></code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.org.gradle.language.nativeplatform.HeaderExportingSourceSet">
<!--   -->
</a>
<h3>Methods inherited from interface&nbsp;org.gradle.language.nativeplatform.<a href="../nativeplatform/HeaderExportingSourceSet.html" title="interface in org.gradle.language.nativeplatform">HeaderExportingSourceSet</a></h3>
<code><a href="../nativeplatform/HeaderExportingSourceSet.html#getExportedHeaders--">getExportedHeaders</a>, <a href="../nativeplatform/HeaderExportingSourceSet.html#getImplicitHeaders--">getImplicitHeaders</a></code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.org.gradle.language.base.LanguageSourceSet">
<!--   -->
</a>
<h3>Methods inherited from interface&nbsp;org.gradle.language.base.<a href="../base/LanguageSourceSet.html" title="interface in org.gradle.language.base">LanguageSourceSet</a></h3>
<code><a href="../base/LanguageSourceSet.html#generatedBy-org.gradle.api.Task-">generatedBy</a>, <a href="../base/LanguageSourceSet.html#getParentName--">getParentName</a>, <a href="../base/LanguageSourceSet.html#getSource--">getSource</a></code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.org.gradle.model.ModelElement">
<!--   -->
</a>
<h3>Methods inherited from interface&nbsp;org.gradle.model.<a href="../../model/ModelElement.html" title="interface in org.gradle.model">ModelElement</a></h3>
<code><a href="../../model/ModelElement.html#getDisplayName--">getDisplayName</a>, <a href="../../model/ModelElement.html#getName--">getName</a></code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../allclasses.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li><a href="#nested.class.summary">Nested</a>&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li>Method</li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
