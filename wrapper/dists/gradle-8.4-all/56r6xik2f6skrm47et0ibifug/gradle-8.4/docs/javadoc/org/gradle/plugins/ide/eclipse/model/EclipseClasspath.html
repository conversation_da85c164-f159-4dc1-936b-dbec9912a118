<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc -->
<title>EclipseClasspath (Gradle API 8.4)</title>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<link rel="stylesheet" type="text/css" href="../../../../../../javadoc.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../../../jquery/jquery-ui.min.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../../../jquery-ui.overrides.css" title="Style">
<script type="text/javascript" src="../../../../../../script.js"></script>
<script type="text/javascript" src="../../../../../../jquery/jszip/dist/jszip.min.js"></script>
<script type="text/javascript" src="../../../../../../jquery/jszip-utils/dist/jszip-utils.min.js"></script>
<!--[if IE]>
<script type="text/javascript" src="../../../../../../jquery/jszip-utils/dist/jszip-utils-ie.min.js"></script>
<![endif]-->
<script type="text/javascript" src="../../../../../../jquery/jquery-3.6.0.min.js"></script>
<script type="text/javascript" src="../../../../../../jquery/jquery-ui.min.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="EclipseClasspath (Gradle API 8.4)";
        }
    }
    catch(err) {
    }
//-->
var data = {"i0":10,"i1":10,"i2":10,"i3":6,"i4":10,"i5":10,"i6":10,"i7":10,"i8":10,"i9":10,"i10":10,"i11":10,"i12":10,"i13":10,"i14":10,"i15":10,"i16":10,"i17":10,"i18":10,"i19":10,"i20":10,"i21":10,"i22":10,"i23":10,"i24":10,"i25":10,"i26":10,"i27":10,"i28":10,"i29":10,"i30":10,"i31":10,"i32":10};
var tabs = {65535:["t0","All Methods"],2:["t2","Instance Methods"],4:["t3","Abstract Methods"],8:["t4","Concrete Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
var pathtoroot = "../../../../../../";
var useModuleDirectories = false;
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<div class="fixedNav">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../../index-all.html">Index</a></li>
<li><a href="../../../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../../../allclasses.html">All&nbsp;Classes</a></li>
</ul>
<ul class="navListSearch">
<li><label for="search">SEARCH:</label>
<input type="text" id="search" value="search" disabled="disabled">
<input type="reset" id="reset" value="reset" disabled="disabled">
</li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
</div>
<div class="navPadding">&nbsp;</div>
<script type="text/javascript"><!--
$('.navPadding').css('padding-top', $('.fixedNav').css("height"));
//-->
</script>
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle"><span class="packageLabelInType">Package</span>&nbsp;<a href="package-summary.html">org.gradle.plugins.ide.eclipse.model</a></div>
<h2 title="Class EclipseClasspath" class="title">Class EclipseClasspath</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li>org.gradle.plugins.ide.eclipse.model.EclipseClasspath</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<hr>
<pre>public abstract class <span class="typeNameLabel">EclipseClasspath</span>
extends java.lang.Object</pre>
<div class="block">The build path settings for the generated Eclipse project. Used by the
 <a href="../GenerateEclipseClasspath.html" title="class in org.gradle.plugins.ide.eclipse"><code>GenerateEclipseClasspath</code></a> task to generate an Eclipse .classpath file.
 <p>
 The following example demonstrates the various configuration options.
 Keep in mind that all properties have sensible defaults; only configure them explicitly
 if the defaults don't match your needs.

 <pre class='autoTested'>
 plugins {
     id 'java'
     id 'eclipse'
 }

 configurations {
   provided
   someBoringConfig
 }

 eclipse {
   //if you want parts of paths in resulting file to be replaced by variables (files):
   pathVariables 'GRADLE_HOME': file('/best/software/gradle'), 'TOMCAT_HOME': file('../tomcat')

   classpath {
     //you can tweak the classpath of the Eclipse project by adding extra configurations:
     plusConfigurations += [ configurations.provided ]

     //you can also remove configurations from the classpath:
     minusConfigurations += [ configurations.someBoringConfig ]

     //if you want to append extra containers:
     containers 'someFriendlyContainer', 'andYetAnotherContainer'

     //customizing the classes output directory:
     defaultOutputDir = file('build-eclipse')

     //default settings for downloading sources and Javadoc:
     downloadSources = true
     downloadJavadoc = false

     //if you want to expose test classes to dependent projects
     containsTestFixtures = true

     //customizing which Eclipse source directories should be marked as test
     testSourceSets = [sourceSets.test]

     //customizing which dependencies should be marked as test on the project's classpath
     testConfigurations = [configurations.testCompileClasspath, configurations.testRuntimeClasspath]
   }
 }
 </pre>

 For tackling edge cases, users can perform advanced configuration on the resulting XML file.
 It is also possible to affect the way that the Eclipse plugin merges the existing configuration
 via beforeMerged and whenMerged closures.
 <p>
 The beforeMerged and whenMerged closures receive a <a href="Classpath.html" title="class in org.gradle.plugins.ide.eclipse.model"><code>Classpath</code></a> object.
 <p>
 Examples of advanced configuration:

 <pre class='autoTested'>
 plugins {
     id 'java'
     id 'eclipse'
 }

 eclipse {
   classpath {
     file {
       //if you want to mess with the resulting XML in whatever way you fancy
       withXml {
         def node = it.asNode()
         node.appendNode('xml', 'is what I love')
       }

       //closure executed after .classpath content is loaded from existing file
       //but before gradle build information is merged
       beforeMerged { classpath -&gt;
         //you can tinker with the <a href="Classpath.html" title="class in org.gradle.plugins.ide.eclipse.model"><code>Classpath</code></a> here
       }

       //closure executed after .classpath content is loaded from existing file
       //and after gradle build information is merged
       whenMerged { classpath -&gt;
         //you can tinker with the <a href="Classpath.html" title="class in org.gradle.plugins.ide.eclipse.model"><code>Classpath</code></a> here
       }
     }
   }
 }
 </pre></div>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.summary">
<!--   -->
</a>
<h3>Constructor Summary</h3>
<table class="memberSummary" summary="Constructor Summary table, listing constructors, and an explanation">
<caption><span>Constructors</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Constructor</th>
<th class="colLast" scope="col">Description</th>
</tr>
<tr class="altColor">
<th class="colConstructorName" scope="row"><code><span class="memberNameLink"><a href="#EclipseClasspath-org.gradle.api.Project-">EclipseClasspath</a></span>&#8203;(<a href="../../../../api/Project.html" title="interface in org.gradle.api">Project</a>&nbsp;project)</code></th>
<td class="colLast">&nbsp;</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t3" class="tableTab"><span><a href="javascript:show(4);">Abstract Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colSecond" scope="col">Method</th>
<th class="colLast" scope="col">Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#containers-java.lang.String...-">containers</a></span>&#8203;(java.lang.String...&nbsp;containers)</code></th>
<td class="colLast">
<div class="block">Further classpath containers to be added.</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#file-groovy.lang.Closure-">file</a></span>&#8203;(<a href="https://docs.groovy-lang.org/docs/groovy-3.0.17/html/gapi/groovy/lang/Closure.html?is-external=true" title="class or interface in groovy.lang" class="externalLink">Closure</a>&nbsp;closure)</code></th>
<td class="colLast">
<div class="block">Enables advanced configuration like tinkering with the output XML or affecting the way
 that the contents of an existing .classpath file is merged with Gradle build information.</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#file-org.gradle.api.Action-">file</a></span>&#8203;(<a href="../../../../api/Action.html" title="interface in org.gradle.api">Action</a>&lt;? super <a href="../../api/XmlFileContentMerger.html" title="class in org.gradle.plugins.ide.api">XmlFileContentMerger</a>&gt;&nbsp;action)</code></th>
<td class="colLast">
<div class="block">Enables advanced configuration like tinkering with the output XML or affecting the way
 that the contents of an existing .classpath file is merged with Gradle build information.</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>abstract <a href="../../../../api/provider/Property.html" title="interface in org.gradle.api.provider">Property</a>&lt;java.io.File&gt;</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getBaseSourceOutputDir--">getBaseSourceOutputDir</a></span>()</code></th>
<td class="colLast">
<div class="block">The base output directory for source sets.</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code>java.util.List&lt;java.io.File&gt;</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getClassFolders--">getClassFolders</a></span>()</code></th>
<td class="colLast">&nbsp;</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code>java.util.Set&lt;java.lang.String&gt;</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getContainers--">getContainers</a></span>()</code></th>
<td class="colLast">
<div class="block">The classpath containers to be added.</div>
</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code><a href="../../../../api/provider/Property.html" title="interface in org.gradle.api.provider">Property</a>&lt;java.lang.Boolean&gt;</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getContainsTestFixtures--">getContainsTestFixtures</a></span>()</code></th>
<td class="colLast">
<div class="block">Returns <code>true</code> if the classpath contains test fixture classes that should be visible
 through incoming project dependencies.</div>
</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code>java.io.File</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getDefaultOutputDir--">getDefaultOutputDir</a></span>()</code></th>
<td class="colLast">
<div class="block">The default output directory where Eclipse puts compiled classes.</div>
</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code><a href="../../api/XmlFileContentMerger.html" title="class in org.gradle.plugins.ide.api">XmlFileContentMerger</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getFile--">getFile</a></span>()</code></th>
<td class="colLast">
<div class="block">See <a href="#file-org.gradle.api.Action-"><code>file(Action)</code></a>.</div>
</td>
</tr>
<tr id="i9" class="rowColor">
<td class="colFirst"><code>org.gradle.plugins.ide.eclipse.model.internal.FileReferenceFactory</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getFileReferenceFactory--">getFileReferenceFactory</a></span>()</code></th>
<td class="colLast">&nbsp;</td>
</tr>
<tr id="i10" class="altColor">
<td class="colFirst"><code>java.util.Collection&lt;<a href="../../../../api/artifacts/Configuration.html" title="interface in org.gradle.api.artifacts">Configuration</a>&gt;</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getMinusConfigurations--">getMinusConfigurations</a></span>()</code></th>
<td class="colLast">
<div class="block">The configurations whose files are to be excluded from the classpath entries.</div>
</td>
</tr>
<tr id="i11" class="rowColor">
<td class="colFirst"><code>java.util.Map&lt;java.lang.String,&#8203;java.io.File&gt;</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getPathVariables--">getPathVariables</a></span>()</code></th>
<td class="colLast">&nbsp;</td>
</tr>
<tr id="i12" class="altColor">
<td class="colFirst"><code>java.util.Collection&lt;<a href="../../../../api/artifacts/Configuration.html" title="interface in org.gradle.api.artifacts">Configuration</a>&gt;</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getPlusConfigurations--">getPlusConfigurations</a></span>()</code></th>
<td class="colLast">
<div class="block">The configurations whose files are to be added as classpath entries.</div>
</td>
</tr>
<tr id="i13" class="rowColor">
<td class="colFirst"><code><a href="../../../../api/Project.html" title="interface in org.gradle.api">Project</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getProject--">getProject</a></span>()</code></th>
<td class="colLast">&nbsp;</td>
</tr>
<tr id="i14" class="altColor">
<td class="colFirst"><code>java.lang.Iterable&lt;<a href="../../../../api/tasks/SourceSet.html" title="interface in org.gradle.api.tasks">SourceSet</a>&gt;</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getSourceSets--">getSourceSets</a></span>()</code></th>
<td class="colLast">
<div class="block">The source sets to be added.</div>
</td>
</tr>
<tr id="i15" class="rowColor">
<td class="colFirst"><code><a href="../../../../api/provider/SetProperty.html" title="interface in org.gradle.api.provider">SetProperty</a>&lt;<a href="../../../../api/artifacts/Configuration.html" title="interface in org.gradle.api.artifacts">Configuration</a>&gt;</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getTestConfigurations--">getTestConfigurations</a></span>()</code></th>
<td class="colLast">
<div class="block">Returns the test configurations.</div>
</td>
</tr>
<tr id="i16" class="altColor">
<td class="colFirst"><code><a href="../../../../api/provider/SetProperty.html" title="interface in org.gradle.api.provider">SetProperty</a>&lt;<a href="../../../../api/tasks/SourceSet.html" title="interface in org.gradle.api.tasks">SourceSet</a>&gt;</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getTestSourceSets--">getTestSourceSets</a></span>()</code></th>
<td class="colLast">
<div class="block">Returns the test source sets.</div>
</td>
</tr>
<tr id="i17" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#isDownloadJavadoc--">isDownloadJavadoc</a></span>()</code></th>
<td class="colLast">
<div class="block">Whether to download and associate Javadoc Jars with the dependency Jars.</div>
</td>
</tr>
<tr id="i18" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#isDownloadSources--">isDownloadSources</a></span>()</code></th>
<td class="colLast">
<div class="block">Whether to download and associate source Jars with the dependency Jars.</div>
</td>
</tr>
<tr id="i19" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#isProjectDependenciesOnly--">isProjectDependenciesOnly</a></span>()</code></th>
<td class="colLast">&nbsp;</td>
</tr>
<tr id="i20" class="altColor">
<td class="colFirst"><code>void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#mergeXmlClasspath-org.gradle.plugins.ide.eclipse.model.Classpath-">mergeXmlClasspath</a></span>&#8203;(<a href="Classpath.html" title="class in org.gradle.plugins.ide.eclipse.model">Classpath</a>&nbsp;xmlClasspath)</code></th>
<td class="colLast">&nbsp;</td>
</tr>
<tr id="i21" class="rowColor">
<td class="colFirst"><code>java.util.List&lt;<a href="ClasspathEntry.html" title="interface in org.gradle.plugins.ide.eclipse.model">ClasspathEntry</a>&gt;</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#resolveDependencies--">resolveDependencies</a></span>()</code></th>
<td class="colLast">
<div class="block">Calculates, resolves and returns dependency entries of this classpath.</div>
</td>
</tr>
<tr id="i22" class="altColor">
<td class="colFirst"><code>void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#setClassFolders-java.util.List-">setClassFolders</a></span>&#8203;(java.util.List&lt;java.io.File&gt;&nbsp;classFolders)</code></th>
<td class="colLast">&nbsp;</td>
</tr>
<tr id="i23" class="rowColor">
<td class="colFirst"><code>void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#setContainers-java.util.Set-">setContainers</a></span>&#8203;(java.util.Set&lt;java.lang.String&gt;&nbsp;containers)</code></th>
<td class="colLast">&nbsp;</td>
</tr>
<tr id="i24" class="altColor">
<td class="colFirst"><code>void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#setDefaultOutputDir-java.io.File-">setDefaultOutputDir</a></span>&#8203;(java.io.File&nbsp;defaultOutputDir)</code></th>
<td class="colLast">&nbsp;</td>
</tr>
<tr id="i25" class="rowColor">
<td class="colFirst"><code>void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#setDownloadJavadoc-boolean-">setDownloadJavadoc</a></span>&#8203;(boolean&nbsp;downloadJavadoc)</code></th>
<td class="colLast">&nbsp;</td>
</tr>
<tr id="i26" class="altColor">
<td class="colFirst"><code>void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#setDownloadSources-boolean-">setDownloadSources</a></span>&#8203;(boolean&nbsp;downloadSources)</code></th>
<td class="colLast">&nbsp;</td>
</tr>
<tr id="i27" class="rowColor">
<td class="colFirst"><code>void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#setFile-org.gradle.plugins.ide.api.XmlFileContentMerger-">setFile</a></span>&#8203;(<a href="../../api/XmlFileContentMerger.html" title="class in org.gradle.plugins.ide.api">XmlFileContentMerger</a>&nbsp;file)</code></th>
<td class="colLast">&nbsp;</td>
</tr>
<tr id="i28" class="altColor">
<td class="colFirst"><code>void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#setMinusConfigurations-java.util.Collection-">setMinusConfigurations</a></span>&#8203;(java.util.Collection&lt;<a href="../../../../api/artifacts/Configuration.html" title="interface in org.gradle.api.artifacts">Configuration</a>&gt;&nbsp;minusConfigurations)</code></th>
<td class="colLast">&nbsp;</td>
</tr>
<tr id="i29" class="rowColor">
<td class="colFirst"><code>void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#setPathVariables-java.util.Map-">setPathVariables</a></span>&#8203;(java.util.Map&lt;java.lang.String,&#8203;java.io.File&gt;&nbsp;pathVariables)</code></th>
<td class="colLast">&nbsp;</td>
</tr>
<tr id="i30" class="altColor">
<td class="colFirst"><code>void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#setPlusConfigurations-java.util.Collection-">setPlusConfigurations</a></span>&#8203;(java.util.Collection&lt;<a href="../../../../api/artifacts/Configuration.html" title="interface in org.gradle.api.artifacts">Configuration</a>&gt;&nbsp;plusConfigurations)</code></th>
<td class="colLast">&nbsp;</td>
</tr>
<tr id="i31" class="rowColor">
<td class="colFirst"><code>void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#setProjectDependenciesOnly-boolean-">setProjectDependenciesOnly</a></span>&#8203;(boolean&nbsp;projectDependenciesOnly)</code></th>
<td class="colLast">&nbsp;</td>
</tr>
<tr id="i32" class="altColor">
<td class="colFirst"><code>void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#setSourceSets-java.lang.Iterable-">setSourceSets</a></span>&#8203;(java.lang.Iterable&lt;<a href="../../../../api/tasks/SourceSet.html" title="interface in org.gradle.api.tasks">SourceSet</a>&gt;&nbsp;sourceSets)</code></th>
<td class="colLast">&nbsp;</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.Object</h3>
<code>clone, equals, finalize, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.detail">
<!--   -->
</a>
<h3>Constructor Detail</h3>
<a name="EclipseClasspath-org.gradle.api.Project-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>EclipseClasspath</h4>
<pre>@Inject
public&nbsp;EclipseClasspath&#8203;(<a href="../../../../api/Project.html" title="interface in org.gradle.api">Project</a>&nbsp;project)</pre>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="getSourceSets--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getSourceSets</h4>
<pre class="methodSignature">public&nbsp;java.lang.Iterable&lt;<a href="../../../../api/tasks/SourceSet.html" title="interface in org.gradle.api.tasks">SourceSet</a>&gt;&nbsp;getSourceSets()</pre>
<div class="block">The source sets to be added.
 <p>
 See <a href="EclipseClasspath.html" title="class in org.gradle.plugins.ide.eclipse.model"><code>EclipseClasspath</code></a> for an example.</div>
</li>
</ul>
<a name="setSourceSets-java.lang.Iterable-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setSourceSets</h4>
<pre class="methodSignature">public&nbsp;void&nbsp;setSourceSets&#8203;(java.lang.Iterable&lt;<a href="../../../../api/tasks/SourceSet.html" title="interface in org.gradle.api.tasks">SourceSet</a>&gt;&nbsp;sourceSets)</pre>
</li>
</ul>
<a name="getPlusConfigurations--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getPlusConfigurations</h4>
<pre class="methodSignature">public&nbsp;java.util.Collection&lt;<a href="../../../../api/artifacts/Configuration.html" title="interface in org.gradle.api.artifacts">Configuration</a>&gt;&nbsp;getPlusConfigurations()</pre>
<div class="block">The configurations whose files are to be added as classpath entries.
 <p>
 See <a href="EclipseClasspath.html" title="class in org.gradle.plugins.ide.eclipse.model"><code>EclipseClasspath</code></a> for an example.</div>
</li>
</ul>
<a name="setPlusConfigurations-java.util.Collection-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setPlusConfigurations</h4>
<pre class="methodSignature">public&nbsp;void&nbsp;setPlusConfigurations&#8203;(java.util.Collection&lt;<a href="../../../../api/artifacts/Configuration.html" title="interface in org.gradle.api.artifacts">Configuration</a>&gt;&nbsp;plusConfigurations)</pre>
</li>
</ul>
<a name="getMinusConfigurations--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getMinusConfigurations</h4>
<pre class="methodSignature">public&nbsp;java.util.Collection&lt;<a href="../../../../api/artifacts/Configuration.html" title="interface in org.gradle.api.artifacts">Configuration</a>&gt;&nbsp;getMinusConfigurations()</pre>
<div class="block">The configurations whose files are to be excluded from the classpath entries.
 <p>
 See <a href="EclipseClasspath.html" title="class in org.gradle.plugins.ide.eclipse.model"><code>EclipseClasspath</code></a> for an example.</div>
</li>
</ul>
<a name="setMinusConfigurations-java.util.Collection-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setMinusConfigurations</h4>
<pre class="methodSignature">public&nbsp;void&nbsp;setMinusConfigurations&#8203;(java.util.Collection&lt;<a href="../../../../api/artifacts/Configuration.html" title="interface in org.gradle.api.artifacts">Configuration</a>&gt;&nbsp;minusConfigurations)</pre>
</li>
</ul>
<a name="getContainers--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getContainers</h4>
<pre class="methodSignature">public&nbsp;java.util.Set&lt;java.lang.String&gt;&nbsp;getContainers()</pre>
<div class="block">The classpath containers to be added.
 <p>
 See <a href="EclipseClasspath.html" title="class in org.gradle.plugins.ide.eclipse.model"><code>EclipseClasspath</code></a> for an example.</div>
</li>
</ul>
<a name="setContainers-java.util.Set-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setContainers</h4>
<pre class="methodSignature">public&nbsp;void&nbsp;setContainers&#8203;(java.util.Set&lt;java.lang.String&gt;&nbsp;containers)</pre>
</li>
</ul>
<a name="getDefaultOutputDir--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getDefaultOutputDir</h4>
<pre class="methodSignature">public&nbsp;java.io.File&nbsp;getDefaultOutputDir()</pre>
<div class="block">The default output directory where Eclipse puts compiled classes.
 <p>
 See <a href="EclipseClasspath.html" title="class in org.gradle.plugins.ide.eclipse.model"><code>EclipseClasspath</code></a> for an example.</div>
</li>
</ul>
<a name="setDefaultOutputDir-java.io.File-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setDefaultOutputDir</h4>
<pre class="methodSignature">public&nbsp;void&nbsp;setDefaultOutputDir&#8203;(java.io.File&nbsp;defaultOutputDir)</pre>
</li>
</ul>
<a name="getBaseSourceOutputDir--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getBaseSourceOutputDir</h4>
<pre class="methodSignature"><a href="../../../../api/Incubating.html" title="annotation in org.gradle.api">@Incubating</a>
public abstract&nbsp;<a href="../../../../api/provider/Property.html" title="interface in org.gradle.api.provider">Property</a>&lt;java.io.File&gt;&nbsp;getBaseSourceOutputDir()</pre>
<div class="block">The base output directory for source sets.
 <p>
 See <a href="EclipseClasspath.html" title="class in org.gradle.plugins.ide.eclipse.model"><code>EclipseClasspath</code></a> for an example.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>8.1</dd>
</dl>
</li>
</ul>
<a name="isDownloadSources--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isDownloadSources</h4>
<pre class="methodSignature">public&nbsp;boolean&nbsp;isDownloadSources()</pre>
<div class="block">Whether to download and associate source Jars with the dependency Jars. Defaults to true.
 <p>
 See <a href="EclipseClasspath.html" title="class in org.gradle.plugins.ide.eclipse.model"><code>EclipseClasspath</code></a> for an example.</div>
</li>
</ul>
<a name="setDownloadSources-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setDownloadSources</h4>
<pre class="methodSignature">public&nbsp;void&nbsp;setDownloadSources&#8203;(boolean&nbsp;downloadSources)</pre>
</li>
</ul>
<a name="isDownloadJavadoc--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isDownloadJavadoc</h4>
<pre class="methodSignature">public&nbsp;boolean&nbsp;isDownloadJavadoc()</pre>
<div class="block">Whether to download and associate Javadoc Jars with the dependency Jars. Defaults to false.
 <p>
 See <a href="EclipseClasspath.html" title="class in org.gradle.plugins.ide.eclipse.model"><code>EclipseClasspath</code></a> for an example.</div>
</li>
</ul>
<a name="setDownloadJavadoc-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setDownloadJavadoc</h4>
<pre class="methodSignature">public&nbsp;void&nbsp;setDownloadJavadoc&#8203;(boolean&nbsp;downloadJavadoc)</pre>
</li>
</ul>
<a name="getFile--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getFile</h4>
<pre class="methodSignature">public&nbsp;<a href="../../api/XmlFileContentMerger.html" title="class in org.gradle.plugins.ide.api">XmlFileContentMerger</a>&nbsp;getFile()</pre>
<div class="block">See <a href="#file-org.gradle.api.Action-"><code>file(Action)</code></a>.</div>
</li>
</ul>
<a name="setFile-org.gradle.plugins.ide.api.XmlFileContentMerger-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setFile</h4>
<pre class="methodSignature">public&nbsp;void&nbsp;setFile&#8203;(<a href="../../api/XmlFileContentMerger.html" title="class in org.gradle.plugins.ide.api">XmlFileContentMerger</a>&nbsp;file)</pre>
</li>
</ul>
<a name="getPathVariables--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getPathVariables</h4>
<pre class="methodSignature">public&nbsp;java.util.Map&lt;java.lang.String,&#8203;java.io.File&gt;&nbsp;getPathVariables()</pre>
</li>
</ul>
<a name="setPathVariables-java.util.Map-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setPathVariables</h4>
<pre class="methodSignature">public&nbsp;void&nbsp;setPathVariables&#8203;(java.util.Map&lt;java.lang.String,&#8203;java.io.File&gt;&nbsp;pathVariables)</pre>
</li>
</ul>
<a name="isProjectDependenciesOnly--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isProjectDependenciesOnly</h4>
<pre class="methodSignature">public&nbsp;boolean&nbsp;isProjectDependenciesOnly()</pre>
</li>
</ul>
<a name="setProjectDependenciesOnly-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setProjectDependenciesOnly</h4>
<pre class="methodSignature">public&nbsp;void&nbsp;setProjectDependenciesOnly&#8203;(boolean&nbsp;projectDependenciesOnly)</pre>
</li>
</ul>
<a name="getClassFolders--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getClassFolders</h4>
<pre class="methodSignature">public&nbsp;java.util.List&lt;java.io.File&gt;&nbsp;getClassFolders()</pre>
</li>
</ul>
<a name="setClassFolders-java.util.List-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setClassFolders</h4>
<pre class="methodSignature">public&nbsp;void&nbsp;setClassFolders&#8203;(java.util.List&lt;java.io.File&gt;&nbsp;classFolders)</pre>
</li>
</ul>
<a name="getProject--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getProject</h4>
<pre class="methodSignature">public&nbsp;<a href="../../../../api/Project.html" title="interface in org.gradle.api">Project</a>&nbsp;getProject()</pre>
</li>
</ul>
<a name="containers-java.lang.String...-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>containers</h4>
<pre class="methodSignature">public&nbsp;void&nbsp;containers&#8203;(java.lang.String...&nbsp;containers)</pre>
<div class="block">Further classpath containers to be added.
 <p>
 See <a href="EclipseClasspath.html" title="class in org.gradle.plugins.ide.eclipse.model"><code>EclipseClasspath</code></a> for an example.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>containers</code> - the classpath containers to be added</dd>
</dl>
</li>
</ul>
<a name="file-groovy.lang.Closure-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>file</h4>
<pre class="methodSignature">public&nbsp;void&nbsp;file&#8203;(<a href="https://docs.groovy-lang.org/docs/groovy-3.0.17/html/gapi/groovy/lang/DelegatesTo.html?is-external=true" title="class or interface in groovy.lang" class="externalLink">@DelegatesTo</a>(<a href="../../api/XmlFileContentMerger.html" title="class in org.gradle.plugins.ide.api">XmlFileContentMerger.class</a>)
                 <a href="https://docs.groovy-lang.org/docs/groovy-3.0.17/html/gapi/groovy/lang/Closure.html?is-external=true" title="class or interface in groovy.lang" class="externalLink">Closure</a>&nbsp;closure)</pre>
<div class="block">Enables advanced configuration like tinkering with the output XML or affecting the way
 that the contents of an existing .classpath file is merged with Gradle build information.
 The object passed to the whenMerged{} and beforeMerged{} closures is of type <a href="Classpath.html" title="class in org.gradle.plugins.ide.eclipse.model"><code>Classpath</code></a>.
 <p>
 See <a href="EclipseProject.html" title="class in org.gradle.plugins.ide.eclipse.model"><code>EclipseProject</code></a> for an example.</div>
</li>
</ul>
<a name="file-org.gradle.api.Action-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>file</h4>
<pre class="methodSignature">public&nbsp;void&nbsp;file&#8203;(<a href="../../../../api/Action.html" title="interface in org.gradle.api">Action</a>&lt;? super <a href="../../api/XmlFileContentMerger.html" title="class in org.gradle.plugins.ide.api">XmlFileContentMerger</a>&gt;&nbsp;action)</pre>
<div class="block">Enables advanced configuration like tinkering with the output XML or affecting the way
 that the contents of an existing .classpath file is merged with Gradle build information.
 The object passed to the whenMerged{} and beforeMerged{} closures is of type <a href="Classpath.html" title="class in org.gradle.plugins.ide.eclipse.model"><code>Classpath</code></a>.
 <p>
 See <a href="EclipseProject.html" title="class in org.gradle.plugins.ide.eclipse.model"><code>EclipseProject</code></a> for an example.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>3.5</dd>
</dl>
</li>
</ul>
<a name="resolveDependencies--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>resolveDependencies</h4>
<pre class="methodSignature">public&nbsp;java.util.List&lt;<a href="ClasspathEntry.html" title="interface in org.gradle.plugins.ide.eclipse.model">ClasspathEntry</a>&gt;&nbsp;resolveDependencies()</pre>
<div class="block">Calculates, resolves and returns dependency entries of this classpath.</div>
</li>
</ul>
<a name="mergeXmlClasspath-org.gradle.plugins.ide.eclipse.model.Classpath-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>mergeXmlClasspath</h4>
<pre class="methodSignature">public&nbsp;void&nbsp;mergeXmlClasspath&#8203;(<a href="Classpath.html" title="class in org.gradle.plugins.ide.eclipse.model">Classpath</a>&nbsp;xmlClasspath)</pre>
</li>
</ul>
<a name="getFileReferenceFactory--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getFileReferenceFactory</h4>
<pre class="methodSignature">public&nbsp;org.gradle.plugins.ide.eclipse.model.internal.FileReferenceFactory&nbsp;getFileReferenceFactory()</pre>
</li>
</ul>
<a name="getContainsTestFixtures--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getContainsTestFixtures</h4>
<pre class="methodSignature">public&nbsp;<a href="../../../../api/provider/Property.html" title="interface in org.gradle.api.provider">Property</a>&lt;java.lang.Boolean&gt;&nbsp;getContainsTestFixtures()</pre>
<div class="block">Returns <code>true</code> if the classpath contains test fixture classes that should be visible
 through incoming project dependencies.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>6.8</dd>
</dl>
</li>
</ul>
<a name="getTestSourceSets--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getTestSourceSets</h4>
<pre class="methodSignature"><a href="../../../../api/Incubating.html" title="annotation in org.gradle.api">@Incubating</a>
public&nbsp;<a href="../../../../api/provider/SetProperty.html" title="interface in org.gradle.api.provider">SetProperty</a>&lt;<a href="../../../../api/tasks/SourceSet.html" title="interface in org.gradle.api.tasks">SourceSet</a>&gt;&nbsp;getTestSourceSets()</pre>
<div class="block">Returns the test source sets.
 <p>
 The source directories in the returned source sets are marked with the 'test' classpath attribute on the Eclipse classpath.
 <p>
 The default value contains the following elements:
 <ul>
     <li>All source sets with names containing the 'test' substring (case ignored)</li>
     <li>All source sets defined via the jvm-test-suite DSL</li>
 </ul></div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>7.5</dd>
</dl>
</li>
</ul>
<a name="getTestConfigurations--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>getTestConfigurations</h4>
<pre class="methodSignature"><a href="../../../../api/Incubating.html" title="annotation in org.gradle.api">@Incubating</a>
public&nbsp;<a href="../../../../api/provider/SetProperty.html" title="interface in org.gradle.api.provider">SetProperty</a>&lt;<a href="../../../../api/artifacts/Configuration.html" title="interface in org.gradle.api.artifacts">Configuration</a>&gt;&nbsp;getTestConfigurations()</pre>
<div class="block">Returns the test configurations.
 <p>
 All resolved dependencies that appear only in the returned dependency configurations are marked with the 'test' classpath attribute on the Eclipse classpath.
 <p>
 The default value contains the following elements:
 <ul>
     <li>The compile and runtime configurations of the <a href="#testSourceSets"><code>testSourceSets</code></a>, including the jvm-test-suite source sets</li>
     <li>Other configurations with names containing the 'test' substring (case ignored)</li>
 </ul>
 <p>
 Note, that this property should contain resolvable configurations only.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>7.5</dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../../index-all.html">Index</a></li>
<li><a href="../../../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../../../allclasses.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
