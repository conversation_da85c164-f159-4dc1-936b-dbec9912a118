<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc -->
<title>org.gradle.language.cpp (Gradle API 8.4)</title>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<link rel="stylesheet" type="text/css" href="../../../../javadoc.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../jquery/jquery-ui.min.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../jquery-ui.overrides.css" title="Style">
<script type="text/javascript" src="../../../../script.js"></script>
<script type="text/javascript" src="../../../../jquery/jszip/dist/jszip.min.js"></script>
<script type="text/javascript" src="../../../../jquery/jszip-utils/dist/jszip-utils.min.js"></script>
<!--[if IE]>
<script type="text/javascript" src="../../../../jquery/jszip-utils/dist/jszip-utils-ie.min.js"></script>
<![endif]-->
<script type="text/javascript" src="../../../../jquery/jquery-3.6.0.min.js"></script>
<script type="text/javascript" src="../../../../jquery/jquery-ui.min.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="org.gradle.language.cpp (Gradle API 8.4)";
        }
    }
    catch(err) {
    }
//-->
var pathtoroot = "../../../../";
var useModuleDirectories = false;
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<div class="fixedNav">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../index.html">Overview</a></li>
<li class="navBarCell1Rev">Package</li>
<li>Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../allclasses.html">All&nbsp;Classes</a></li>
</ul>
<ul class="navListSearch">
<li><label for="search">SEARCH:</label>
<input type="text" id="search" value="search" disabled="disabled">
<input type="reset" id="reset" value="reset" disabled="disabled">
</li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
</div>
<div class="navPadding">&nbsp;</div>
<script type="text/javascript"><!--
$('.navPadding').css('padding-top', $('.fixedNav').css("height"));
//-->
</script>
<div class="header">
<h1 title="Package" class="title">Package&nbsp;org.gradle.language.cpp</h1>
</div>
<div class="contentContainer"><a name="package.description">
<!--   -->
</a>
<div class="block">Model classes for building from C++ language sources.</div>
<ul class="blockList">
<li class="blockList">
<table class="typeSummary" summary="Interface Summary table, listing interfaces, and an explanation">
<caption><span>Interface Summary</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Interface</th>
<th class="colLast" scope="col">Description</th>
</tr>
<tbody>
<tr class="altColor">
<th class="colFirst" scope="row"><a href="CppApplication.html" title="interface in org.gradle.language.cpp">CppApplication</a></th>
<td class="colLast">
<div class="block">Configuration for a C++ application, defining the source files that make up the application plus other settings.</div>
</td>
</tr>
<tr class="rowColor">
<th class="colFirst" scope="row"><a href="CppBinary.html" title="interface in org.gradle.language.cpp">CppBinary</a></th>
<td class="colLast">
<div class="block">A binary built from C++ source and linked from the resulting object files.</div>
</td>
</tr>
<tr class="altColor">
<th class="colFirst" scope="row"><a href="CppComponent.html" title="interface in org.gradle.language.cpp">CppComponent</a></th>
<td class="colLast">
<div class="block">Configuration for a C++ component, such as a library or executable, defining the source files and private header directories that make up the component.</div>
</td>
</tr>
<tr class="rowColor">
<th class="colFirst" scope="row"><a href="CppExecutable.html" title="interface in org.gradle.language.cpp">CppExecutable</a></th>
<td class="colLast">
<div class="block">An executable built from C++ source.</div>
</td>
</tr>
<tr class="altColor">
<th class="colFirst" scope="row"><a href="CppLibrary.html" title="interface in org.gradle.language.cpp">CppLibrary</a></th>
<td class="colLast">
<div class="block">Configuration for a C++ library, defining the source files and header directories that make up the library plus other settings.</div>
</td>
</tr>
<tr class="rowColor">
<th class="colFirst" scope="row"><a href="CppPlatform.html" title="interface in org.gradle.language.cpp">CppPlatform</a></th>
<td class="colLast">
<div class="block">A target platform for building C++ binaries.</div>
</td>
</tr>
<tr class="altColor">
<th class="colFirst" scope="row"><a href="CppSharedLibrary.html" title="interface in org.gradle.language.cpp">CppSharedLibrary</a></th>
<td class="colLast">
<div class="block">A shared library built from C++ source.</div>
</td>
</tr>
<tr class="rowColor">
<th class="colFirst" scope="row"><a href="CppSourceSet.html" title="interface in org.gradle.language.cpp">CppSourceSet</a></th>
<td class="colLast">
<div class="block">A set of C++ source files.</div>
</td>
</tr>
<tr class="altColor">
<th class="colFirst" scope="row"><a href="CppStaticLibrary.html" title="interface in org.gradle.language.cpp">CppStaticLibrary</a></th>
<td class="colLast">
<div class="block">A static library built from C++ source.</div>
</td>
</tr>
<tr class="rowColor">
<th class="colFirst" scope="row"><a href="ProductionCppComponent.html" title="interface in org.gradle.language.cpp">ProductionCppComponent</a></th>
<td class="colLast">
<div class="block">Represents a C++ component that is the main product of a project.</div>
</td>
</tr>
</tbody>
</table>
</li>
</ul>
</div>
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../index.html">Overview</a></li>
<li class="navBarCell1Rev">Package</li>
<li>Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../allclasses.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
