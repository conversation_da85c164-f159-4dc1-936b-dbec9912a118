<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc -->
<title>GoogleTestTestSuiteBinarySpec (Gradle API 8.4)</title>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<link rel="stylesheet" type="text/css" href="../../../../../javadoc.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../../jquery/jquery-ui.min.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../../jquery-ui.overrides.css" title="Style">
<script type="text/javascript" src="../../../../../script.js"></script>
<script type="text/javascript" src="../../../../../jquery/jszip/dist/jszip.min.js"></script>
<script type="text/javascript" src="../../../../../jquery/jszip-utils/dist/jszip-utils.min.js"></script>
<!--[if IE]>
<script type="text/javascript" src="../../../../../jquery/jszip-utils/dist/jszip-utils-ie.min.js"></script>
<![endif]-->
<script type="text/javascript" src="../../../../../jquery/jquery-3.6.0.min.js"></script>
<script type="text/javascript" src="../../../../../jquery/jquery-ui.min.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="GoogleTestTestSuiteBinarySpec (Gradle API 8.4)";
        }
    }
    catch(err) {
    }
//-->
var data = {"i0":6,"i1":6};
var tabs = {65535:["t0","All Methods"],2:["t2","Instance Methods"],4:["t3","Abstract Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
var pathtoroot = "../../../../../";
var useModuleDirectories = false;
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<div class="fixedNav">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../index-all.html">Index</a></li>
<li><a href="../../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../../allclasses.html">All&nbsp;Classes</a></li>
</ul>
<ul class="navListSearch">
<li><label for="search">SEARCH:</label>
<input type="text" id="search" value="search" disabled="disabled">
<input type="reset" id="reset" value="reset" disabled="disabled">
</li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li><a href="#nested.class.summary">Nested</a>&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
</div>
<div class="navPadding">&nbsp;</div>
<script type="text/javascript"><!--
$('.navPadding').css('padding-top', $('.fixedNav').css("height"));
//-->
</script>
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle"><span class="packageLabelInType">Package</span>&nbsp;<a href="package-summary.html">org.gradle.nativeplatform.test.googletest</a></div>
<h2 title="Interface GoogleTestTestSuiteBinarySpec" class="title">Interface GoogleTestTestSuiteBinarySpec</h2>
</div>
<div class="contentContainer">
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt>All Superinterfaces:</dt>
<dd><code><a href="../../../platform/base/Binary.html" title="interface in org.gradle.platform.base">Binary</a></code>, <code><a href="../../../platform/base/BinarySpec.html" title="interface in org.gradle.platform.base">BinarySpec</a></code>, <code><a href="../../../api/Buildable.html" title="interface in org.gradle.api">Buildable</a></code>, <code><a href="../../../api/BuildableComponentSpec.html" title="interface in org.gradle.api">BuildableComponentSpec</a></code>, <code><a href="../../../api/CheckableComponentSpec.html" title="interface in org.gradle.api">CheckableComponentSpec</a></code>, <code><a href="../../../platform/base/ComponentSpec.html" title="interface in org.gradle.platform.base">ComponentSpec</a></code>, <code><a href="../../../model/ModelElement.html" title="interface in org.gradle.model">ModelElement</a></code>, <code><a href="../../../api/Named.html" title="interface in org.gradle.api">Named</a></code>, <code><a href="../../NativeBinarySpec.html" title="interface in org.gradle.nativeplatform">NativeBinarySpec</a></code>, <code><a href="../NativeTestSuiteBinarySpec.html" title="interface in org.gradle.nativeplatform.test">NativeTestSuiteBinarySpec</a></code>, <code><a href="../../../testing/base/TestSuiteBinarySpec.html" title="interface in org.gradle.testing.base">TestSuiteBinarySpec</a></code></dd>
</dl>
<hr>
<pre><a href="../../../api/Incubating.html" title="annotation in org.gradle.api">@Incubating</a>
public interface <span class="typeNameLabel">GoogleTestTestSuiteBinarySpec</span>
extends <a href="../NativeTestSuiteBinarySpec.html" title="interface in org.gradle.nativeplatform.test">NativeTestSuiteBinarySpec</a></pre>
<div class="block">An executable which run a Google Test test suite.</div>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ======== NESTED CLASS SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="nested.class.summary">
<!--   -->
</a>
<h3>Nested Class Summary</h3>
<ul class="blockList">
<li class="blockList"><a name="nested.classes.inherited.from.class.org.gradle.api.Named">
<!--   -->
</a>
<h3>Nested classes/interfaces inherited from interface&nbsp;org.gradle.api.<a href="../../../api/Named.html" title="interface in org.gradle.api">Named</a></h3>
<code><a href="../../../api/Named.Namer.html" title="class in org.gradle.api">Named.Namer</a></code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="nested.classes.inherited.from.class.org.gradle.nativeplatform.test.NativeTestSuiteBinarySpec">
<!--   -->
</a>
<h3>Nested classes/interfaces inherited from interface&nbsp;org.gradle.nativeplatform.test.<a href="../NativeTestSuiteBinarySpec.html" title="interface in org.gradle.nativeplatform.test">NativeTestSuiteBinarySpec</a></h3>
<code><a href="../NativeTestSuiteBinarySpec.TasksCollection.html" title="interface in org.gradle.nativeplatform.test">NativeTestSuiteBinarySpec.TasksCollection</a></code></li>
</ul>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t3" class="tableTab"><span><a href="javascript:show(4);">Abstract Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colSecond" scope="col">Method</th>
<th class="colLast" scope="col">Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code><a href="GoogleTestTestSuiteSpec.html" title="interface in org.gradle.nativeplatform.test.googletest">GoogleTestTestSuiteSpec</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getComponent--">getComponent</a></span>()</code></th>
<td class="colLast">
<div class="block">The component that this binary was built from.</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code><a href="GoogleTestTestSuiteSpec.html" title="interface in org.gradle.nativeplatform.test.googletest">GoogleTestTestSuiteSpec</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getTestSuite--">getTestSuite</a></span>()</code></th>
<td class="colLast">
<div class="block">Returns the test suite that this binary belongs to.</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.org.gradle.platform.base.Binary">
<!--   -->
</a>
<h3>Methods inherited from interface&nbsp;org.gradle.platform.base.<a href="../../../platform/base/Binary.html" title="interface in org.gradle.platform.base">Binary</a></h3>
<code><a href="../../../platform/base/Binary.html#getDisplayName--">getDisplayName</a></code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.org.gradle.platform.base.BinarySpec">
<!--   -->
</a>
<h3>Methods inherited from interface&nbsp;org.gradle.platform.base.<a href="../../../platform/base/BinarySpec.html" title="interface in org.gradle.platform.base">BinarySpec</a></h3>
<code><a href="../../../platform/base/BinarySpec.html#getInputs--">getInputs</a>, <a href="../../../platform/base/BinarySpec.html#getSources--">getSources</a>, <a href="../../../platform/base/BinarySpec.html#isBuildable--">isBuildable</a></code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.org.gradle.api.Buildable">
<!--   -->
</a>
<h3>Methods inherited from interface&nbsp;org.gradle.api.<a href="../../../api/Buildable.html" title="interface in org.gradle.api">Buildable</a></h3>
<code><a href="../../../api/Buildable.html#getBuildDependencies--">getBuildDependencies</a></code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.org.gradle.api.BuildableComponentSpec">
<!--   -->
</a>
<h3>Methods inherited from interface&nbsp;org.gradle.api.<a href="../../../api/BuildableComponentSpec.html" title="interface in org.gradle.api">BuildableComponentSpec</a></h3>
<code><a href="../../../api/BuildableComponentSpec.html#builtBy-java.lang.Object...-">builtBy</a>, <a href="../../../api/BuildableComponentSpec.html#getBuildTask--">getBuildTask</a>, <a href="../../../api/BuildableComponentSpec.html#hasBuildDependencies--">hasBuildDependencies</a>, <a href="../../../api/BuildableComponentSpec.html#setBuildTask-org.gradle.api.Task-">setBuildTask</a></code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.org.gradle.api.CheckableComponentSpec">
<!--   -->
</a>
<h3>Methods inherited from interface&nbsp;org.gradle.api.<a href="../../../api/CheckableComponentSpec.html" title="interface in org.gradle.api">CheckableComponentSpec</a></h3>
<code><a href="../../../api/CheckableComponentSpec.html#checkedBy-java.lang.Object...-">checkedBy</a>, <a href="../../../api/CheckableComponentSpec.html#getCheckTask--">getCheckTask</a>, <a href="../../../api/CheckableComponentSpec.html#setCheckTask-org.gradle.api.Task-">setCheckTask</a></code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.org.gradle.platform.base.ComponentSpec">
<!--   -->
</a>
<h3>Methods inherited from interface&nbsp;org.gradle.platform.base.<a href="../../../platform/base/ComponentSpec.html" title="interface in org.gradle.platform.base">ComponentSpec</a></h3>
<code><a href="../../../platform/base/ComponentSpec.html#getProjectPath--">getProjectPath</a></code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.org.gradle.model.ModelElement">
<!--   -->
</a>
<h3>Methods inherited from interface&nbsp;org.gradle.model.<a href="../../../model/ModelElement.html" title="interface in org.gradle.model">ModelElement</a></h3>
<code><a href="../../../model/ModelElement.html#getDisplayName--">getDisplayName</a>, <a href="../../../model/ModelElement.html#getName--">getName</a></code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.org.gradle.nativeplatform.NativeBinarySpec">
<!--   -->
</a>
<h3>Methods inherited from interface&nbsp;org.gradle.nativeplatform.<a href="../../NativeBinarySpec.html" title="interface in org.gradle.nativeplatform">NativeBinarySpec</a></h3>
<code><a href="../../NativeBinarySpec.html#getAssembler--">getAssembler</a>, <a href="../../NativeBinarySpec.html#getBuildType--">getBuildType</a>, <a href="../../NativeBinarySpec.html#getcCompiler--">getcCompiler</a>, <a href="../../NativeBinarySpec.html#getCppCompiler--">getCppCompiler</a>, <a href="../../NativeBinarySpec.html#getFlavor--">getFlavor</a>, <a href="../../NativeBinarySpec.html#getLibs--">getLibs</a>, <a href="../../NativeBinarySpec.html#getLinker--">getLinker</a>, <a href="../../NativeBinarySpec.html#getObjcCompiler--">getObjcCompiler</a>, <a href="../../NativeBinarySpec.html#getObjcppCompiler--">getObjcppCompiler</a>, <a href="../../NativeBinarySpec.html#getRcCompiler--">getRcCompiler</a>, <a href="../../NativeBinarySpec.html#getStaticLibArchiver--">getStaticLibArchiver</a>, <a href="../../NativeBinarySpec.html#getTargetPlatform--">getTargetPlatform</a>, <a href="../../NativeBinarySpec.html#getToolChain--">getToolChain</a>, <a href="../../NativeBinarySpec.html#lib-java.lang.Object-">lib</a></code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.org.gradle.nativeplatform.test.NativeTestSuiteBinarySpec">
<!--   -->
</a>
<h3>Methods inherited from interface&nbsp;org.gradle.nativeplatform.test.<a href="../NativeTestSuiteBinarySpec.html" title="interface in org.gradle.nativeplatform.test">NativeTestSuiteBinarySpec</a></h3>
<code><a href="../NativeTestSuiteBinarySpec.html#getExecutable--">getExecutable</a>, <a href="../NativeTestSuiteBinarySpec.html#getExecutableFile--">getExecutableFile</a>, <a href="../NativeTestSuiteBinarySpec.html#getInstallation--">getInstallation</a>, <a href="../NativeTestSuiteBinarySpec.html#getTasks--">getTasks</a>, <a href="../NativeTestSuiteBinarySpec.html#getTestedBinary--">getTestedBinary</a></code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="getComponent--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getComponent</h4>
<pre class="methodSignature"><a href="GoogleTestTestSuiteSpec.html" title="interface in org.gradle.nativeplatform.test.googletest">GoogleTestTestSuiteSpec</a>&nbsp;getComponent()</pre>
<div class="block">The component that this binary was built from.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../NativeBinarySpec.html#getComponent--">getComponent</a></code>&nbsp;in interface&nbsp;<code><a href="../../NativeBinarySpec.html" title="interface in org.gradle.nativeplatform">NativeBinarySpec</a></code></dd>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../NativeTestSuiteBinarySpec.html#getComponent--">getComponent</a></code>&nbsp;in interface&nbsp;<code><a href="../NativeTestSuiteBinarySpec.html" title="interface in org.gradle.nativeplatform.test">NativeTestSuiteBinarySpec</a></code></dd>
</dl>
</li>
</ul>
<a name="getTestSuite--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>getTestSuite</h4>
<pre class="methodSignature"><a href="GoogleTestTestSuiteSpec.html" title="interface in org.gradle.nativeplatform.test.googletest">GoogleTestTestSuiteSpec</a>&nbsp;getTestSuite()</pre>
<div class="block">Returns the test suite that this binary belongs to.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../NativeTestSuiteBinarySpec.html#getTestSuite--">getTestSuite</a></code>&nbsp;in interface&nbsp;<code><a href="../NativeTestSuiteBinarySpec.html" title="interface in org.gradle.nativeplatform.test">NativeTestSuiteBinarySpec</a></code></dd>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../testing/base/TestSuiteBinarySpec.html#getTestSuite--">getTestSuite</a></code>&nbsp;in interface&nbsp;<code><a href="../../../testing/base/TestSuiteBinarySpec.html" title="interface in org.gradle.testing.base">TestSuiteBinarySpec</a></code></dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../index-all.html">Index</a></li>
<li><a href="../../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../../allclasses.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li><a href="#nested.class.summary">Nested</a>&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
