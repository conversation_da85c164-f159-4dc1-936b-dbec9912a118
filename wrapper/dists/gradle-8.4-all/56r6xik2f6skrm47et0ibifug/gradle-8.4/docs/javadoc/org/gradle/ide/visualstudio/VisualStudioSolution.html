<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc -->
<title>VisualStudioSolution (Gradle API 8.4)</title>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<link rel="stylesheet" type="text/css" href="../../../../javadoc.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../jquery/jquery-ui.min.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../jquery-ui.overrides.css" title="Style">
<script type="text/javascript" src="../../../../script.js"></script>
<script type="text/javascript" src="../../../../jquery/jszip/dist/jszip.min.js"></script>
<script type="text/javascript" src="../../../../jquery/jszip-utils/dist/jszip-utils.min.js"></script>
<!--[if IE]>
<script type="text/javascript" src="../../../../jquery/jszip-utils/dist/jszip-utils-ie.min.js"></script>
<![endif]-->
<script type="text/javascript" src="../../../../jquery/jquery-3.6.0.min.js"></script>
<script type="text/javascript" src="../../../../jquery/jquery-ui.min.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="VisualStudioSolution (Gradle API 8.4)";
        }
    }
    catch(err) {
    }
//-->
var data = {"i0":6,"i1":6,"i2":6,"i3":6,"i4":6};
var tabs = {65535:["t0","All Methods"],2:["t2","Instance Methods"],4:["t3","Abstract Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
var pathtoroot = "../../../../";
var useModuleDirectories = false;
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<div class="fixedNav">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../allclasses.html">All&nbsp;Classes</a></li>
</ul>
<ul class="navListSearch">
<li><label for="search">SEARCH:</label>
<input type="text" id="search" value="search" disabled="disabled">
<input type="reset" id="reset" value="reset" disabled="disabled">
</li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li><a href="#nested.class.summary">Nested</a>&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
</div>
<div class="navPadding">&nbsp;</div>
<script type="text/javascript"><!--
$('.navPadding').css('padding-top', $('.fixedNav').css("height"));
//-->
</script>
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle"><span class="packageLabelInType">Package</span>&nbsp;<a href="package-summary.html">org.gradle.ide.visualstudio</a></div>
<h2 title="Interface VisualStudioSolution" class="title">Interface VisualStudioSolution</h2>
</div>
<div class="contentContainer">
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt>All Superinterfaces:</dt>
<dd><code><a href="../../api/Buildable.html" title="interface in org.gradle.api">Buildable</a></code>, <code><a href="../../api/Describable.html" title="interface in org.gradle.api">Describable</a></code>, <code><a href="../../plugins/ide/IdeWorkspace.html" title="interface in org.gradle.plugins.ide">IdeWorkspace</a></code>, <code><a href="../../api/Named.html" title="interface in org.gradle.api">Named</a></code></dd>
</dl>
<hr>
<pre><a href="../../api/Incubating.html" title="annotation in org.gradle.api">@Incubating</a>
public interface <span class="typeNameLabel">VisualStudioSolution</span>
extends <a href="../../api/Named.html" title="interface in org.gradle.api">Named</a>, <a href="../../api/Buildable.html" title="interface in org.gradle.api">Buildable</a>, <a href="../../plugins/ide/IdeWorkspace.html" title="interface in org.gradle.plugins.ide">IdeWorkspace</a></pre>
<div class="block">A visual studio solution, representing one or more native binaries in a build.
 <p>

 The content and location of the generate solution file can be modified by the supplied methods:

 <pre class='autoTested'>
  plugins {
      id 'visual-studio'
  }

  model {
      visualStudio {
          solution {
              solutionFile.location = "vs/${name}.sln"
              solutionFile.withContent { TextProvider content -&gt;
                  content.asBuilder().insert(0, "# GENERATED FILE: DO NOT EDIT\n")
                  content.text = content.text.replaceAll("HideSolutionNode = FALSE", "HideSolutionNode = TRUE")
              }
          }
      }
  }
 </pre></div>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ======== NESTED CLASS SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="nested.class.summary">
<!--   -->
</a>
<h3>Nested Class Summary</h3>
<ul class="blockList">
<li class="blockList"><a name="nested.classes.inherited.from.class.org.gradle.api.Named">
<!--   -->
</a>
<h3>Nested classes/interfaces inherited from interface&nbsp;org.gradle.api.<a href="../../api/Named.html" title="interface in org.gradle.api">Named</a></h3>
<code><a href="../../api/Named.Namer.html" title="class in org.gradle.api">Named.Namer</a></code></li>
</ul>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t3" class="tableTab"><span><a href="javascript:show(4);">Abstract Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colSecond" scope="col">Method</th>
<th class="colLast" scope="col">Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code><a href="../../api/tasks/TaskDependency.html" title="interface in org.gradle.api.tasks">TaskDependency</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getBuildDependencies--">getBuildDependencies</a></span>()</code></th>
<td class="colLast">
<div class="block">Returns a dependency which contains the tasks which build this artifact.</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>java.lang.String</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getDisplayName--">getDisplayName</a></span>()</code></th>
<td class="colLast">
<div class="block">Returns the display name of this object.</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code><a href="../../api/provider/Provider.html" title="interface in org.gradle.api.provider">Provider</a>&lt;<a href="../../api/file/RegularFile.html" title="interface in org.gradle.api.file">RegularFile</a>&gt;</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getLocation--">getLocation</a></span>()</code></th>
<td class="colLast">
<div class="block">Returns the location of the generated solution file.</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>java.lang.String</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getName--">getName</a></span>()</code></th>
<td class="colLast">
<div class="block">The object's name.</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code><a href="TextConfigFile.html" title="interface in org.gradle.ide.visualstudio">TextConfigFile</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getSolutionFile--">getSolutionFile</a></span>()</code></th>
<td class="colLast">
<div class="block">Configuration for the generated solution file.</div>
</td>
</tr>
</table>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="getSolutionFile--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getSolutionFile</h4>
<pre class="methodSignature"><a href="../../api/tasks/Internal.html" title="annotation in org.gradle.api.tasks">@Internal</a>
<a href="../../api/Incubating.html" title="annotation in org.gradle.api">@Incubating</a>
<a href="TextConfigFile.html" title="interface in org.gradle.ide.visualstudio">TextConfigFile</a>&nbsp;getSolutionFile()</pre>
<div class="block">Configuration for the generated solution file.</div>
</li>
</ul>
<a name="getLocation--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getLocation</h4>
<pre class="methodSignature"><a href="../../api/tasks/Internal.html" title="annotation in org.gradle.api.tasks">@Internal</a>
<a href="../../api/provider/Provider.html" title="interface in org.gradle.api.provider">Provider</a>&lt;<a href="../../api/file/RegularFile.html" title="interface in org.gradle.api.file">RegularFile</a>&gt;&nbsp;getLocation()</pre>
<div class="block">Returns the location of the generated solution file.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../plugins/ide/IdeWorkspace.html#getLocation--">getLocation</a></code>&nbsp;in interface&nbsp;<code><a href="../../plugins/ide/IdeWorkspace.html" title="interface in org.gradle.plugins.ide">IdeWorkspace</a></code></dd>
</dl>
</li>
</ul>
<a name="getBuildDependencies--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getBuildDependencies</h4>
<pre class="methodSignature"><a href="../../api/tasks/Internal.html" title="annotation in org.gradle.api.tasks">@Internal</a>
<a href="../../api/Incubating.html" title="annotation in org.gradle.api">@Incubating</a>
<a href="../../api/tasks/TaskDependency.html" title="interface in org.gradle.api.tasks">TaskDependency</a>&nbsp;getBuildDependencies()</pre>
<div class="block"><span class="descfrmTypeLabel">Description copied from interface:&nbsp;<code><a href="../../api/Buildable.html#getBuildDependencies--">Buildable</a></code></span></div>
<div class="block">Returns a dependency which contains the tasks which build this artifact. All <code>Buildable</code> implementations
 must ensure that the returned dependency object is live, so that it tracks changes to the dependencies of this
 buildable.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../api/Buildable.html#getBuildDependencies--">getBuildDependencies</a></code>&nbsp;in interface&nbsp;<code><a href="../../api/Buildable.html" title="interface in org.gradle.api">Buildable</a></code></dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>The dependency. Never returns null. Returns an empty dependency when this artifact is not built by any
         tasks.</dd>
</dl>
</li>
</ul>
<a name="getName--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getName</h4>
<pre class="methodSignature"><a href="../../api/tasks/Internal.html" title="annotation in org.gradle.api.tasks">@Internal</a>
java.lang.String&nbsp;getName()</pre>
<div class="block"><span class="descfrmTypeLabel">Description copied from interface:&nbsp;<code><a href="../../api/Named.html#getName--">Named</a></code></span></div>
<div class="block">The object's name.
 <p>
 Must be constant for the life of the object.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../api/Named.html#getName--">getName</a></code>&nbsp;in interface&nbsp;<code><a href="../../api/Named.html" title="interface in org.gradle.api">Named</a></code></dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>The name. Never null.</dd>
</dl>
</li>
</ul>
<a name="getDisplayName--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>getDisplayName</h4>
<pre class="methodSignature"><a href="../../api/tasks/Internal.html" title="annotation in org.gradle.api.tasks">@Internal</a>
java.lang.String&nbsp;getDisplayName()</pre>
<div class="block"><span class="descfrmTypeLabel">Description copied from interface:&nbsp;<code><a href="../../api/Describable.html#getDisplayName--">Describable</a></code></span></div>
<div class="block">Returns the display name of this object. It is strongly encouraged to compute it
 lazily, and cache the value if it is expensive.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../api/Describable.html#getDisplayName--">getDisplayName</a></code>&nbsp;in interface&nbsp;<code><a href="../../api/Describable.html" title="interface in org.gradle.api">Describable</a></code></dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>the display name</dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../allclasses.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li><a href="#nested.class.summary">Nested</a>&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
