<!DOCTYPE html>
<html>
<head>
    <meta name="viewport" content="width=device-width, initial-scale=1" charset="UTF-8">
    <title>org.gradle.tooling.model</title>
    <link href="../../images/logo-icon.svg" rel="icon" type="image/svg">
    <script>var pathToRoot = "../../";</script>
    <script>const storage = localStorage.getItem("dokka-dark-mode")
    if (storage == null) {
        const osDarkSchemePreferred = window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches
        if (osDarkSchemePreferred === true) {
            document.getElementsByTagName("html")[0].classList.add("theme-dark")
        }
    } else {
        const savedDarkMode = JSON.parse(storage)
        if(savedDarkMode === true) {
            document.getElementsByTagName("html")[0].classList.add("theme-dark")
        }
    }
    </script>
<script type="text/javascript" src="../../scripts/sourceset_dependencies.js" async="async"></script>
<link href="../../styles/style.css" rel="Stylesheet">
<link href="../../styles/jetbrains-mono.css" rel="Stylesheet">
<link href="../../styles/main.css" rel="Stylesheet">
<link href="../../styles/prism.css" rel="Stylesheet">
<link href="../../styles/logo-styles.css" rel="Stylesheet">
<script type="text/javascript" src="../../scripts/clipboard.js" async="async"></script>
<script type="text/javascript" src="../../scripts/navigation-loader.js" async="async"></script>
<script type="text/javascript" src="../../scripts/platform-content-handler.js" async="async"></script>
<script type="text/javascript" src="../../scripts/main.js" defer="defer"></script>
<script type="text/javascript" src="../../scripts/prism.js" async="async"></script>
<script type="text/javascript" src="../../scripts/symbol-parameters-wrapper_deferred.js" defer="defer"></script>
<link href="../../images/gradle-logo.svg">
<link href="../../styles/gradle.css" rel="Stylesheet">
</head>
<body>
<div class="navigation-wrapper" id="navigation-wrapper">
    <div id="leftToggler"><span class="icon-toggler"></span></div>
    <div class="library-name">
            <a href="../../index.html">
                    <span>Kotlin DSL Reference for Gradle</span>
            </a>
    </div>
    <div>
8.4    </div>
    <div class="pull-right d-flex">
        <div class="filter-section" id="filter-section">
                <button class="platform-tag platform-selector jvm-like" data-active="" data-filter=":docs/java_api">API</button>
        </div>
        <button id="theme-toggle-button"><span id="theme-toggle"></span></button>
        <div id="searchBar"></div>
    </div>
</div>
<div id="container">
    <div id="leftColumn">
        <div id="sideMenu"></div>
    </div>
    <div id="main">
<div class="main-content" data-page-type="package" id="content" pageIds="Kotlin DSL Reference for Gradle::org.gradle.tooling.model////PointingToDeclaration//-1793262594">
  <div class="breadcrumbs"><a href="../../index.html">Kotlin DSL Reference for Gradle</a><span class="delimiter">/</span><span class="current">org.gradle.tooling.model</span></div>
  <div class="cover ">
    <h1 class="cover"><span><span>Package-level</span></span> <span><span>declarations</span></span></h1>
    <div class="platform-hinted UnderCoverText with-platform-tabs" data-platform-hinted="data-platform-hinted">
      <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api" data-active="" data-toggle=":docs/java_api">API</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/java_api"><p class="paragraph">The general-purpose tooling model types, provided by the tooling API.</p></div>    </div>
  </div>
  <div class="tabbedcontent">
    <div class="tabs-section" tabs-section="tabs-section"><button class="section-tab" data-active="" data-togglable="TYPE">Types</button></div>
    <div class="tabs-section-body">
      <div data-togglable="TYPE">
        <h2 class="">Types</h2>
        <div class="table"><a data-name="-1729747690%2FClasslikes%2F-1793262594" anchor-label="BuildableElement" id="-1729747690%2FClasslikes%2F-1793262594" data-filterable-set=":docs/java_api"></a>
          <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="-buildable-element/index.html"><span>Buildable</span><wbr></wbr><span><span>Element</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="-1729747690%2FClasslikes%2F-1793262594"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
                    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api" data-active="" data-toggle=":docs/java_api">API</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/java_api"><div class="symbol monospace"><span class="token keyword">interface </span><a href="-buildable-element/index.html">BuildableElement</a> : <a href="-element/index.html">Element</a></div><div class="brief ">Represents an element which has Gradle tasks associated with it.</div></div>                  </div>
                </div>
              </div>
            </div>
          </div>
<a data-name="-1485492287%2FClasslikes%2F-1793262594" anchor-label="BuildIdentifier" id="-1485492287%2FClasslikes%2F-1793262594" data-filterable-set=":docs/java_api"></a>
          <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="-build-identifier/index.html"><span>Build</span><wbr></wbr><span><span>Identifier</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="-1485492287%2FClasslikes%2F-1793262594"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
                    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api" data-active="" data-toggle=":docs/java_api">API</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/java_api"><div class="symbol monospace"><span class="token keyword">interface </span><a href="-build-identifier/index.html">BuildIdentifier</a> : <a href="-model/index.html">Model</a></div><div class="brief ">Identifies a Gradle build.</div></div>                  </div>
                </div>
              </div>
            </div>
          </div>
<a data-name="1296546159%2FClasslikes%2F-1793262594" anchor-label="BuildModel" id="1296546159%2FClasslikes%2F-1793262594" data-filterable-set=":docs/java_api"></a>
          <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="-build-model/index.html"><span>Build</span><wbr></wbr><span><span>Model</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="1296546159%2FClasslikes%2F-1793262594"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
                    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api" data-active="" data-toggle=":docs/java_api">API</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/java_api"><div class="symbol monospace"><span class="token keyword">interface </span><a href="-build-model/index.html">BuildModel</a></div><div class="brief ">Represents a model that is associated with or represents some Gradle build.</div></div>                  </div>
                </div>
              </div>
            </div>
          </div>
<a data-name="-1882124452%2FClasslikes%2F-1793262594" anchor-label="ComponentSelector" id="-1882124452%2FClasslikes%2F-1793262594" data-filterable-set=":docs/java_api"></a>
          <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="-component-selector/index.html"><span>Component</span><wbr></wbr><span><span>Selector</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="-1882124452%2FClasslikes%2F-1793262594"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
                    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api" data-active="" data-toggle=":docs/java_api">API</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/java_api"><div class="symbol monospace"><span class="token keyword">interface </span><a href="-component-selector/index.html">ComponentSelector</a></div><div class="brief ">Represents a component selector.</div></div>                  </div>
                </div>
              </div>
            </div>
          </div>
<a data-name="1369378175%2FClasslikes%2F-1793262594" anchor-label="Dependency" id="1369378175%2FClasslikes%2F-1793262594" data-filterable-set=":docs/java_api"></a>
          <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="-dependency/index.html"><span><span>Dependency</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="1369378175%2FClasslikes%2F-1793262594"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
                    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api" data-active="" data-toggle=":docs/java_api">API</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/java_api"><div class="symbol monospace"><span class="token keyword">interface </span><a href="-dependency/index.html">Dependency</a></div><div class="brief ">Represents an artifact dependency.</div></div>                  </div>
                </div>
              </div>
            </div>
          </div>
<a data-name="668396089%2FClasslikes%2F-1793262594" anchor-label="DomainObjectSet" id="668396089%2FClasslikes%2F-1793262594" data-filterable-set=":docs/java_api"></a>
          <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="-domain-object-set/index.html"><span>Domain</span><wbr></wbr><span>Object</span><wbr></wbr><span><span>Set</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="668396089%2FClasslikes%2F-1793262594"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
                    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api" data-active="" data-toggle=":docs/java_api">API</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/java_api"><div class="symbol monospace"><span class="token keyword">interface </span><a href="-domain-object-set/index.html">DomainObjectSet</a><span class="token operator">&lt;</span><span class="token keyword"></span><a href="-domain-object-set/index.html">T</a><span class="token operator">&gt;</span> : <a href="https://docs.oracle.com/en/java/javase/11/docs/api/java.base/java/util/Set.html">Set</a><span class="token operator">&lt;</span><a href="https://docs.oracle.com/en/java/javase/11/docs/api/java.base/java/util/Set.html">E</a><span class="token operator">&gt; </span></div><div class="brief ">A set of domain objects of type T.</div></div>                  </div>
                </div>
              </div>
            </div>
          </div>
<a data-name="-268595940%2FClasslikes%2F-1793262594" anchor-label="Element" id="-268595940%2FClasslikes%2F-1793262594" data-filterable-set=":docs/java_api"></a>
          <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="-element/index.html"><span><span>Element</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="-268595940%2FClasslikes%2F-1793262594"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
                    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api" data-active="" data-toggle=":docs/java_api">API</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/java_api"><div class="symbol monospace"><span class="token keyword">interface </span><a href="-element/index.html">Element</a> : <a href="-model/index.html">Model</a></div><div class="brief ">Described model element.</div></div>                  </div>
                </div>
              </div>
            </div>
          </div>
<a data-name="1925790900%2FClasslikes%2F-1793262594" anchor-label="ExternalDependency" id="1925790900%2FClasslikes%2F-1793262594" data-filterable-set=":docs/java_api"></a>
          <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="-external-dependency/index.html"><span>External</span><wbr></wbr><span><span>Dependency</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="1925790900%2FClasslikes%2F-1793262594"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
                    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api" data-active="" data-toggle=":docs/java_api">API</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/java_api"><div class="symbol monospace"><span class="token keyword">interface </span><a href="-external-dependency/index.html">ExternalDependency</a> : <a href="-dependency/index.html">Dependency</a></div><div class="brief ">Represents an external artifact dependency.</div></div>                  </div>
                </div>
              </div>
            </div>
          </div>
<a data-name="-89886221%2FClasslikes%2F-1793262594" anchor-label="GradleModuleVersion" id="-89886221%2FClasslikes%2F-1793262594" data-filterable-set=":docs/java_api"></a>
          <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="-gradle-module-version/index.html"><span>Gradle</span><wbr></wbr><span>Module</span><wbr></wbr><span><span>Version</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="-89886221%2FClasslikes%2F-1793262594"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
                    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api" data-active="" data-toggle=":docs/java_api">API</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/java_api"><div class="symbol monospace"><span class="token keyword">interface </span><a href="-gradle-module-version/index.html">GradleModuleVersion</a></div><div class="brief ">Informs about a module version, i.e.</div></div>                  </div>
                </div>
              </div>
            </div>
          </div>
<a data-name="-1862990138%2FClasslikes%2F-1793262594" anchor-label="GradleProject" id="-1862990138%2FClasslikes%2F-1793262594" data-filterable-set=":docs/java_api"></a>
          <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="-gradle-project/index.html"><span>Gradle</span><wbr></wbr><span><span>Project</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="-1862990138%2FClasslikes%2F-1793262594"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
                    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api" data-active="" data-toggle=":docs/java_api">API</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/java_api"><div class="symbol monospace"><span class="token keyword">interface </span><a href="-gradle-project/index.html">GradleProject</a> : <a href="-hierarchical-element/index.html">HierarchicalElement</a>, <a href="-buildable-element/index.html">BuildableElement</a>, <a href="-project-model/index.html">ProjectModel</a></div><div class="brief ">Represents a Gradle project.</div></div>                  </div>
                </div>
              </div>
            </div>
          </div>
<a data-name="303041918%2FClasslikes%2F-1793262594" anchor-label="GradleTask" id="303041918%2FClasslikes%2F-1793262594" data-filterable-set=":docs/java_api"></a>
          <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="-gradle-task/index.html"><span>Gradle</span><wbr></wbr><span><span>Task</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="303041918%2FClasslikes%2F-1793262594"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
                    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api" data-active="" data-toggle=":docs/java_api">API</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/java_api"><div class="symbol monospace"><span class="token keyword">interface </span><a href="-gradle-task/index.html">GradleTask</a> : <a href="-task/index.html">Task</a></div><div class="brief ">Represents a task which is executable by Gradle.</div></div>                  </div>
                </div>
              </div>
            </div>
          </div>
<a data-name="740502098%2FClasslikes%2F-1793262594" anchor-label="HasGradleProject" id="740502098%2FClasslikes%2F-1793262594" data-filterable-set=":docs/java_api"></a>
          <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="-has-gradle-project/index.html"><span>Has</span><wbr></wbr><span>Gradle</span><wbr></wbr><span><span>Project</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="740502098%2FClasslikes%2F-1793262594"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
                    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api" data-active="" data-toggle=":docs/java_api">API</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/java_api"><div class="symbol monospace"><span class="token keyword">interface </span><a href="-has-gradle-project/index.html">HasGradleProject</a> : <a href="-project-model/index.html">ProjectModel</a></div><div class="brief ">An element that is associated with a Gradle project.</div></div>                  </div>
                </div>
              </div>
            </div>
          </div>
<a data-name="913328709%2FClasslikes%2F-1793262594" anchor-label="HierarchicalElement" id="913328709%2FClasslikes%2F-1793262594" data-filterable-set=":docs/java_api"></a>
          <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="-hierarchical-element/index.html"><span>Hierarchical</span><wbr></wbr><span><span>Element</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="913328709%2FClasslikes%2F-1793262594"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
                    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api" data-active="" data-toggle=":docs/java_api">API</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/java_api"><div class="symbol monospace"><span class="token keyword">interface </span><a href="-hierarchical-element/index.html">HierarchicalElement</a> : <a href="-element/index.html">Element</a></div><div class="brief ">Represents an element which belongs to some hierarchy.</div></div>                  </div>
                </div>
              </div>
            </div>
          </div>
<a data-name="-548132995%2FClasslikes%2F-1793262594" anchor-label="Launchable" id="-548132995%2FClasslikes%2F-1793262594" data-filterable-set=":docs/java_api"></a>
          <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="-launchable/index.html"><span><span>Launchable</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="-548132995%2FClasslikes%2F-1793262594"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
                    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api" data-active="" data-toggle=":docs/java_api">API</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/java_api"><div class="symbol monospace"><span class="token keyword">interface </span><a href="-launchable/index.html">Launchable</a> : <a href="-project-model/index.html">ProjectModel</a></div><div class="brief ">Represents an object that can be used to launch a Gradle build, such as a task.</div></div>                  </div>
                </div>
              </div>
            </div>
          </div>
<a data-name="203111247%2FClasslikes%2F-1793262594" anchor-label="Model" id="203111247%2FClasslikes%2F-1793262594" data-filterable-set=":docs/java_api"></a>
          <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="-model/index.html"><span><span>Model</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="203111247%2FClasslikes%2F-1793262594"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
                    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api" data-active="" data-toggle=":docs/java_api">API</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/java_api"><div class="symbol monospace"><span class="token keyword">interface </span><a href="-model/index.html">Model</a></div><div class="brief ">A model that is buildable by the Tooling API.</div></div>                  </div>
                </div>
              </div>
            </div>
          </div>
<a data-name="-1185494764%2FClasslikes%2F-1793262594" anchor-label="ProjectDependency" id="-1185494764%2FClasslikes%2F-1793262594" data-filterable-set=":docs/java_api"></a>
          <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="-project-dependency/index.html"><span>Project</span><wbr></wbr><span><span>Dependency</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="-1185494764%2FClasslikes%2F-1793262594"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
                    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api" data-active="" data-toggle=":docs/java_api">API</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/java_api"><div class="symbol monospace"><span class="token keyword">interface </span><a href="-project-dependency/index.html">ProjectDependency</a> : <a href="-dependency/index.html">Dependency</a></div><div class="brief ">Represents a dependency on another project.</div></div>                  </div>
                </div>
              </div>
            </div>
          </div>
<a data-name="1000314230%2FClasslikes%2F-1793262594" anchor-label="ProjectIdentifier" id="1000314230%2FClasslikes%2F-1793262594" data-filterable-set=":docs/java_api"></a>
          <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="-project-identifier/index.html"><span>Project</span><wbr></wbr><span><span>Identifier</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="1000314230%2FClasslikes%2F-1793262594"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
                    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api" data-active="" data-toggle=":docs/java_api">API</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/java_api"><div class="symbol monospace"><span class="token keyword">interface </span><a href="-project-identifier/index.html">ProjectIdentifier</a> : <a href="-model/index.html">Model</a></div><div class="brief ">Identifies a Gradle project.</div></div>                  </div>
                </div>
              </div>
            </div>
          </div>
<a data-name="-1761093990%2FClasslikes%2F-1793262594" anchor-label="ProjectModel" id="-1761093990%2FClasslikes%2F-1793262594" data-filterable-set=":docs/java_api"></a>
          <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="-project-model/index.html"><span>Project</span><wbr></wbr><span><span>Model</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="-1761093990%2FClasslikes%2F-1793262594"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
                    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api" data-active="" data-toggle=":docs/java_api">API</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/java_api"><div class="symbol monospace"><span class="token keyword">interface </span><a href="-project-model/index.html">ProjectModel</a></div><div class="brief ">Represents a model that is associated with some Gradle project.</div></div>                  </div>
                </div>
              </div>
            </div>
          </div>
<a data-name="-1619270330%2FClasslikes%2F-1793262594" anchor-label="SourceDirectory" id="-1619270330%2FClasslikes%2F-1793262594" data-filterable-set=":docs/java_api"></a>
          <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="-source-directory/index.html"><span>Source</span><wbr></wbr><span><span>Directory</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="-1619270330%2FClasslikes%2F-1793262594"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
                    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api" data-active="" data-toggle=":docs/java_api">API</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/java_api"><div class="symbol monospace"><span class="token keyword">interface </span><a href="-source-directory/index.html">SourceDirectory</a></div><div class="brief ">Represents a source directory.</div></div>                  </div>
                </div>
              </div>
            </div>
          </div>
<a data-name="1708236613%2FClasslikes%2F-1793262594" anchor-label="Task" id="1708236613%2FClasslikes%2F-1793262594" data-filterable-set=":docs/java_api"></a>
          <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="-task/index.html"><span><span>Task</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="1708236613%2FClasslikes%2F-1793262594"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
                    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api" data-active="" data-toggle=":docs/java_api">API</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/java_api"><div class="symbol monospace"><span class="token keyword">interface </span><a href="-task/index.html">Task</a> : <a href="-launchable/index.html">Launchable</a></div><div class="brief ">Represents a task which is executable by Gradle.</div></div>                  </div>
                </div>
              </div>
            </div>
          </div>
<a data-name="195731494%2FClasslikes%2F-1793262594" anchor-label="TaskSelector" id="195731494%2FClasslikes%2F-1793262594" data-filterable-set=":docs/java_api"></a>
          <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="-task-selector/index.html"><span>Task</span><wbr></wbr><span><span>Selector</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="195731494%2FClasslikes%2F-1793262594"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
                    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api" data-active="" data-toggle=":docs/java_api">API</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/java_api"><div class="symbol monospace"><span class="token keyword">interface </span><a href="-task-selector/index.html">TaskSelector</a> : <a href="-launchable/index.html">Launchable</a></div><div class="brief ">Represents a <a href="-launchable/index.html">Launchable</a> that uses task name to select tasks executed from a given project and its sub-projects.</div></div>                  </div>
                </div>
              </div>
            </div>
          </div>
<a data-name="1130179857%2FClasslikes%2F-1793262594" anchor-label="UnsupportedMethodException" id="1130179857%2FClasslikes%2F-1793262594" data-filterable-set=":docs/java_api"></a>
          <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="-unsupported-method-exception/index.html"><span>Unsupported</span><wbr></wbr><span>Method</span><wbr></wbr><span><span>Exception</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="1130179857%2FClasslikes%2F-1793262594"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
                    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api" data-active="" data-toggle=":docs/java_api">API</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/java_api"><div class="symbol monospace"><span class="token keyword">open </span><span class="token keyword">class </span><a href="-unsupported-method-exception/index.html">UnsupportedMethodException</a> : <a href="https://docs.oracle.com/en/java/javase/11/docs/api/java.base/java/lang/UnsupportedOperationException.html">UnsupportedOperationException</a></div><div class="brief ">Thrown when the tooling API client attempts to use a method that does not exist in the version of Gradle that the tooling API is connected to.</div></div>                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
      <div class="footer">
        <span class="go-to-top-icon"><a href="#content" id="go-to-top-link"></a></span><span>Gradle Kotlin DSL Reference</span><span
                class="pull-right"><span>Generated by </span><a
                href="https://github.com/Kotlin/dokka"><span>dokka</span><span class="padded-icon"></span></a></span>
      </div>
    </div>
</div>
</body>
</html>
