<!DOCTYPE html>
<html>
<head>
    <meta name="viewport" content="width=device-width, initial-scale=1" charset="UTF-8">
    <title>JavaToolchainService</title>
    <link href="../../../images/logo-icon.svg" rel="icon" type="image/svg">
    <script>var pathToRoot = "../../../";</script>
    <script>const storage = localStorage.getItem("dokka-dark-mode")
    if (storage == null) {
        const osDarkSchemePreferred = window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches
        if (osDarkSchemePreferred === true) {
            document.getElementsByTagName("html")[0].classList.add("theme-dark")
        }
    } else {
        const savedDarkMode = JSON.parse(storage)
        if(savedDarkMode === true) {
            document.getElementsByTagName("html")[0].classList.add("theme-dark")
        }
    }
    </script>
<script type="text/javascript" src="../../../scripts/sourceset_dependencies.js" async="async"></script>
<link href="../../../styles/style.css" rel="Stylesheet">
<link href="../../../styles/jetbrains-mono.css" rel="Stylesheet">
<link href="../../../styles/main.css" rel="Stylesheet">
<link href="../../../styles/prism.css" rel="Stylesheet">
<link href="../../../styles/logo-styles.css" rel="Stylesheet">
<script type="text/javascript" src="../../../scripts/clipboard.js" async="async"></script>
<script type="text/javascript" src="../../../scripts/navigation-loader.js" async="async"></script>
<script type="text/javascript" src="../../../scripts/platform-content-handler.js" async="async"></script>
<script type="text/javascript" src="../../../scripts/main.js" defer="defer"></script>
<script type="text/javascript" src="../../../scripts/prism.js" async="async"></script>
<script type="text/javascript" src="../../../scripts/symbol-parameters-wrapper_deferred.js" defer="defer"></script>
<link href="../../../images/gradle-logo.svg">
<link href="../../../styles/gradle.css" rel="Stylesheet">
</head>
<body>
<div class="navigation-wrapper" id="navigation-wrapper">
    <div id="leftToggler"><span class="icon-toggler"></span></div>
    <div class="library-name">
            <a href="../../../index.html">
                    <span>Kotlin DSL Reference for Gradle</span>
            </a>
    </div>
    <div>
8.4    </div>
    <div class="pull-right d-flex">
        <div class="filter-section" id="filter-section">
                <button class="platform-tag platform-selector jvm-like" data-active="" data-filter=":docs/java_api">API</button>
        </div>
        <button id="theme-toggle-button"><span id="theme-toggle"></span></button>
        <div id="searchBar"></div>
    </div>
</div>
<div id="container">
    <div id="leftColumn">
        <div id="sideMenu"></div>
    </div>
    <div id="main">
<div class="main-content" data-page-type="classlike" id="content" pageIds="Kotlin DSL Reference for Gradle::org.gradle.jvm.toolchain/JavaToolchainService///PointingToDeclaration//-**********">
  <div class="breadcrumbs"><a href="../../../index.html">Kotlin DSL Reference for Gradle</a><span class="delimiter">/</span><a href="../index.html">org.gradle.jvm.toolchain</a><span class="delimiter">/</span><span class="current">JavaToolchainService</span></div>
  <div class="cover ">
    <h1 class="cover"><span>Java</span><wbr></wbr><span>Toolchain</span><wbr></wbr><span><span>Service</span></span></h1>
    <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
      <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api" data-active="" data-toggle=":docs/java_api">API</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/java_api"><div class="symbol monospace"><span class="token keyword">interface </span><a href="index.html">JavaToolchainService</a><span class="clearfix"><span class="floating-right">(<a href="https://github.com/gradle/gradle/blob/e9251e572c9bd1d01e503a0dfdf43aedaeecdc3f/platforms/jvm/toolchains-jvm/src/main/java/org/gradle/jvm/toolchain/JavaToolchainService.java#L29">source</a>)</span></span></div><p class="paragraph">Allows to query for toolchain managed tools, like <a href="../-java-compiler/index.html">JavaCompiler</a>, <a href="../-java-launcher/index.html">JavaLauncher</a> and <a href="../-javadoc-tool/index.html">JavadocTool</a>. </p><p class="paragraph"> An instance of this service is available for injection into tasks, plugins and other types.</p></div>    </div>
  </div>
  <div class="tabbedcontent">
    <div class="tabs-section" tabs-section="tabs-section"><button class="section-tab" data-active="" data-togglable="CONSTRUCTOR,TYPE,PROPERTY,FUNCTION">Members</button></div>
    <div class="tabs-section-body">
      <div data-togglable="FUNCTION">
        <h2 class="">Functions</h2>
        <div class="table"><a data-name="-1705191277%2FFunctions%2F-**********" anchor-label="compilerFor" id="-1705191277%2FFunctions%2F-**********" data-filterable-set=":docs/java_api"></a>
          <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="compiler-for.html"><span>compiler</span><wbr></wbr><span><span>For</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="-1705191277%2FFunctions%2F-**********"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
                    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api" data-active="" data-toggle=":docs/java_api">API</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/java_api"><div class="symbol monospace"><span class="token keyword">abstract </span><span class="token keyword"></span><span class="token keyword">fun </span><a href="compiler-for.html"><span class="token function">compilerFor</span></a><span class="token punctuation">(</span><span class="parameters "><span class="parameter ">config<span class="token operator">: </span><a href="../../org.gradle.api/-action/index.html">Action</a><span class="token operator">&lt;</span><span class="token keyword">out </span><a href="https://kotlinlang.org/api/latest/jvm/stdlib/kotlin/-any/index.html">Any</a><span class="token operator">&gt;</span></span></span><span class="token punctuation">)</span><span class="token operator">: </span><a href="../../org.gradle.api.provider/-provider/index.html">Provider</a><span class="token operator">&lt;</span><a href="../-java-compiler/index.html">JavaCompiler</a><span class="token operator">&gt;</span></div><div class="brief ">Obtain a <a href="../-java-compiler/index.html">JavaCompiler</a> matching the <a href="../-java-toolchain-spec/index.html">JavaToolchainSpec</a>, as configured by the provided action.</div><div class="symbol monospace"><span class="token keyword">abstract </span><span class="token keyword"></span><span class="token keyword">fun </span><a href="compiler-for.html"><span class="token function">compilerFor</span></a><span class="token punctuation">(</span><span class="parameters "><span class="parameter ">spec<span class="token operator">: </span><a href="../-java-toolchain-spec/index.html">JavaToolchainSpec</a></span></span><span class="token punctuation">)</span><span class="token operator">: </span><a href="../../org.gradle.api.provider/-provider/index.html">Provider</a><span class="token operator">&lt;</span><a href="../-java-compiler/index.html">JavaCompiler</a><span class="token operator">&gt;</span></div><div class="brief ">Obtain a <a href="../-java-compiler/index.html">JavaCompiler</a> matching the <a href="../-java-toolchain-spec/index.html">JavaToolchainSpec</a>.</div></div>                  </div>
                </div>
              </div>
            </div>
          </div>
<a data-name="-141165591%2FFunctions%2F-**********" anchor-label="javadocToolFor" id="-141165591%2FFunctions%2F-**********" data-filterable-set=":docs/java_api"></a>
          <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="javadoc-tool-for.html"><span>javadoc</span><wbr></wbr><span>Tool</span><wbr></wbr><span><span>For</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="-141165591%2FFunctions%2F-**********"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
                    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api" data-active="" data-toggle=":docs/java_api">API</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/java_api"><div class="symbol monospace"><span class="token keyword">abstract </span><span class="token keyword"></span><span class="token keyword">fun </span><a href="javadoc-tool-for.html"><span class="token function">javadocToolFor</span></a><span class="token punctuation">(</span><span class="parameters "><span class="parameter ">config<span class="token operator">: </span><a href="../../org.gradle.api/-action/index.html">Action</a><span class="token operator">&lt;</span><span class="token keyword">out </span><a href="https://kotlinlang.org/api/latest/jvm/stdlib/kotlin/-any/index.html">Any</a><span class="token operator">&gt;</span></span></span><span class="token punctuation">)</span><span class="token operator">: </span><a href="../../org.gradle.api.provider/-provider/index.html">Provider</a><span class="token operator">&lt;</span><a href="../-javadoc-tool/index.html">JavadocTool</a><span class="token operator">&gt;</span></div><div class="brief ">Obtain a <a href="../-javadoc-tool/index.html">JavadocTool</a> matching the <a href="../-java-toolchain-spec/index.html">JavaToolchainSpec</a>, as configured by the provided action.</div><div class="symbol monospace"><span class="token keyword">abstract </span><span class="token keyword"></span><span class="token keyword">fun </span><a href="javadoc-tool-for.html"><span class="token function">javadocToolFor</span></a><span class="token punctuation">(</span><span class="parameters "><span class="parameter ">spec<span class="token operator">: </span><a href="../-java-toolchain-spec/index.html">JavaToolchainSpec</a></span></span><span class="token punctuation">)</span><span class="token operator">: </span><a href="../../org.gradle.api.provider/-provider/index.html">Provider</a><span class="token operator">&lt;</span><a href="../-javadoc-tool/index.html">JavadocTool</a><span class="token operator">&gt;</span></div><div class="brief ">Obtain a <a href="../-javadoc-tool/index.html">JavadocTool</a> matching the <a href="../-java-toolchain-spec/index.html">JavaToolchainSpec</a>.</div></div>                  </div>
                </div>
              </div>
            </div>
          </div>
<a data-name="**********%2FFunctions%2F-**********" anchor-label="launcherFor" id="**********%2FFunctions%2F-**********" data-filterable-set=":docs/java_api"></a>
          <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="launcher-for.html"><span>launcher</span><wbr></wbr><span><span>For</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="**********%2FFunctions%2F-**********"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
                    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api" data-active="" data-toggle=":docs/java_api">API</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/java_api"><div class="symbol monospace"><span class="token keyword">abstract </span><span class="token keyword"></span><span class="token keyword">fun </span><a href="launcher-for.html"><span class="token function">launcherFor</span></a><span class="token punctuation">(</span><span class="parameters "><span class="parameter ">config<span class="token operator">: </span><a href="../../org.gradle.api/-action/index.html">Action</a><span class="token operator">&lt;</span><span class="token keyword">out </span><a href="https://kotlinlang.org/api/latest/jvm/stdlib/kotlin/-any/index.html">Any</a><span class="token operator">&gt;</span></span></span><span class="token punctuation">)</span><span class="token operator">: </span><a href="../../org.gradle.api.provider/-provider/index.html">Provider</a><span class="token operator">&lt;</span><a href="../-java-launcher/index.html">JavaLauncher</a><span class="token operator">&gt;</span></div><div class="brief ">Obtain a <a href="../-java-launcher/index.html">JavaLauncher</a> matching the <a href="../-java-toolchain-spec/index.html">JavaToolchainSpec</a>, as configured by the provided action.</div><div class="symbol monospace"><span class="token keyword">abstract </span><span class="token keyword"></span><span class="token keyword">fun </span><a href="launcher-for.html"><span class="token function">launcherFor</span></a><span class="token punctuation">(</span><span class="parameters "><span class="parameter ">spec<span class="token operator">: </span><a href="../-java-toolchain-spec/index.html">JavaToolchainSpec</a></span></span><span class="token punctuation">)</span><span class="token operator">: </span><a href="../../org.gradle.api.provider/-provider/index.html">Provider</a><span class="token operator">&lt;</span><a href="../-java-launcher/index.html">JavaLauncher</a><span class="token operator">&gt;</span></div><div class="brief ">Obtain a <a href="../-java-launcher/index.html">JavaLauncher</a> matching the <a href="../-java-toolchain-spec/index.html">JavaToolchainSpec</a>.</div></div>                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
      <div class="footer">
        <span class="go-to-top-icon"><a href="#content" id="go-to-top-link"></a></span><span>Gradle Kotlin DSL Reference</span><span
                class="pull-right"><span>Generated by </span><a
                href="https://github.com/Kotlin/dokka"><span>dokka</span><span class="padded-icon"></span></a></span>
      </div>
    </div>
</div>
</body>
</html>
