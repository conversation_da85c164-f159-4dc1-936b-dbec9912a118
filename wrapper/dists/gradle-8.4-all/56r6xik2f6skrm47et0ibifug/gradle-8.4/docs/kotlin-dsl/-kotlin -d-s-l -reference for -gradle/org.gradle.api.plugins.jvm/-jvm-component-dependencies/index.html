<!DOCTYPE html>
<html>
<head>
    <meta name="viewport" content="width=device-width, initial-scale=1" charset="UTF-8">
    <title>JvmComponentDependencies</title>
    <link href="../../../images/logo-icon.svg" rel="icon" type="image/svg">
    <script>var pathToRoot = "../../../";</script>
    <script>const storage = localStorage.getItem("dokka-dark-mode")
    if (storage == null) {
        const osDarkSchemePreferred = window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches
        if (osDarkSchemePreferred === true) {
            document.getElementsByTagName("html")[0].classList.add("theme-dark")
        }
    } else {
        const savedDarkMode = JSON.parse(storage)
        if(savedDarkMode === true) {
            document.getElementsByTagName("html")[0].classList.add("theme-dark")
        }
    }
    </script>
<script type="text/javascript" src="../../../scripts/sourceset_dependencies.js" async="async"></script>
<link href="../../../styles/style.css" rel="Stylesheet">
<link href="../../../styles/jetbrains-mono.css" rel="Stylesheet">
<link href="../../../styles/main.css" rel="Stylesheet">
<link href="../../../styles/prism.css" rel="Stylesheet">
<link href="../../../styles/logo-styles.css" rel="Stylesheet">
<script type="text/javascript" src="../../../scripts/clipboard.js" async="async"></script>
<script type="text/javascript" src="../../../scripts/navigation-loader.js" async="async"></script>
<script type="text/javascript" src="../../../scripts/platform-content-handler.js" async="async"></script>
<script type="text/javascript" src="../../../scripts/main.js" defer="defer"></script>
<script type="text/javascript" src="../../../scripts/prism.js" async="async"></script>
<script type="text/javascript" src="../../../scripts/symbol-parameters-wrapper_deferred.js" defer="defer"></script>
<link href="../../../images/gradle-logo.svg">
<link href="../../../styles/gradle.css" rel="Stylesheet">
</head>
<body>
<div class="navigation-wrapper" id="navigation-wrapper">
    <div id="leftToggler"><span class="icon-toggler"></span></div>
    <div class="library-name">
            <a href="../../../index.html">
                    <span>Kotlin DSL Reference for Gradle</span>
            </a>
    </div>
    <div>
8.4    </div>
    <div class="pull-right d-flex">
        <div class="filter-section" id="filter-section">
                <button class="platform-tag platform-selector jvm-like" data-active="" data-filter=":docs/java_api">API</button>
                <button class="platform-tag platform-selector jvm-like" data-active="" data-filter=":docs/kotlin_dsl">DSL</button>
        </div>
        <button id="theme-toggle-button"><span id="theme-toggle"></span></button>
        <div id="searchBar"></div>
    </div>
</div>
<div id="container">
    <div id="leftColumn">
        <div id="sideMenu"></div>
    </div>
    <div id="main">
<div class="main-content" data-page-type="classlike" id="content" pageIds="Kotlin DSL Reference for Gradle::org.gradle.api.plugins.jvm/JvmComponentDependencies///PointingToDeclaration//-1793262594">
  <div class="breadcrumbs"><a href="../../../index.html">Kotlin DSL Reference for Gradle</a><span class="delimiter">/</span><a href="../index.html">org.gradle.api.plugins.jvm</a><span class="delimiter">/</span><span class="current">JvmComponentDependencies</span></div>
  <div class="cover ">
    <h1 class="cover"><span>Jvm</span><wbr></wbr><span>Component</span><wbr></wbr><span><span>Dependencies</span></span></h1>
    <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
      <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api" data-active="" data-toggle=":docs/java_api">API</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/java_api"><div class="symbol monospace"><div class="block"><div class="block"><span class="token annotation builtin">@</span><a href="../../org.gradle.api/-incubating/index.html"><span class="token annotation builtin">Incubating</span></a></div></div><span class="token keyword">interface </span><a href="index.html">JvmComponentDependencies</a> : <a href="../-platform-dependency-modifiers/index.html">PlatformDependencyModifiers</a>, <a href="../-test-fixtures-dependency-modifiers/index.html">TestFixturesDependencyModifiers</a>, <a href="../../org.gradle.api.artifacts.dsl/-gradle-dependencies/index.html">GradleDependencies</a><span class="clearfix"><span class="floating-right">(<a href="https://github.com/gradle/gradle/blob/e9251e572c9bd1d01e503a0dfdf43aedaeecdc3f/subprojects/plugins/src/main/java/org/gradle/api/plugins/jvm/JvmComponentDependencies.java#L41">source</a>)</span></span></div><p class="paragraph">This DSL element is used to add dependencies to a component, like <a href="../-jvm-test-suite/index.html">JvmTestSuite</a>. </p><ul><li><code class="lang-kotlin">implementation</code> dependencies are used at compilation and runtime.</li><li><code class="lang-kotlin">compileOnly</code> dependencies are used only at compilation and are not available at runtime.</li><li><code class="lang-kotlin">runtimeOnly</code> dependencies are not available at compilation and are used only at runtime.</li><li><code class="lang-kotlin">annotationProcessor</code> dependencies are used only at compilation for the annotation processor classpath</li></ul><h4 class="">See also</h4><div class="table"><div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api"><div class="main-subrow keyValue "><div class=""><span class="inline-flex"><div><a href="../../org.gradle.api.artifacts.dsl/-dependency-handler/index.html"><span>Dependency</span><wbr></wbr><span><span>Handler</span></span></a></div></span></div><div><div class="title"><p class="paragraph">For more information.</p></div></div></div></div></div></div>    </div>
  </div>
  <div class="tabbedcontent">
    <div class="tabs-section" tabs-section="tabs-section"><button class="section-tab" data-active="" data-togglable="CONSTRUCTOR,TYPE,PROPERTY,FUNCTION">Members</button><button class="section-tab" data-togglable="CONSTRUCTOR,TYPE,PROPERTY,FUNCTION,EXTENSION_PROPERTY,EXTENSION_FUNCTION">Members &amp; Extensions</button></div>
    <div class="tabs-section-body">
      <div data-togglable="FUNCTION">
        <h2 class="">Functions</h2>
        <div class="table"><a data-name="-772648589%2FFunctions%2F-1793262594" anchor-label="getAnnotationProcessor" id="-772648589%2FFunctions%2F-1793262594" data-filterable-set=":docs/java_api"></a>
          <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="get-annotation-processor.html"><span>get</span><wbr></wbr><span>Annotation</span><wbr></wbr><span><span>Processor</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="-772648589%2FFunctions%2F-1793262594"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
                    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api" data-active="" data-toggle=":docs/java_api">API</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/java_api"><div class="symbol monospace"><span class="token keyword">abstract </span><span class="token keyword"></span><span class="token keyword">fun </span><a href="get-annotation-processor.html"><span class="token function">getAnnotationProcessor</span></a><span class="token punctuation">(</span><span class="token punctuation">)</span><span class="token operator">: </span><a href="../../org.gradle.api.artifacts.dsl/-dependency-adder/index.html">DependencyAdder</a></div><div class="brief ">Returns a <a href="../../org.gradle.api.artifacts.dsl/-dependency-adder/index.html">DependencyAdder</a> to add to the set of annotation processor dependencies.</div></div>                  </div>
                </div>
              </div>
            </div>
          </div>
<a data-name="-2072345705%2FFunctions%2F-1793262594" anchor-label="getCompileOnly" id="-2072345705%2FFunctions%2F-1793262594" data-filterable-set=":docs/java_api"></a>
          <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="get-compile-only.html"><span>get</span><wbr></wbr><span>Compile</span><wbr></wbr><span><span>Only</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="-2072345705%2FFunctions%2F-1793262594"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
                    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api" data-active="" data-toggle=":docs/java_api">API</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/java_api"><div class="symbol monospace"><span class="token keyword">abstract </span><span class="token keyword"></span><span class="token keyword">fun </span><a href="get-compile-only.html"><span class="token function">getCompileOnly</span></a><span class="token punctuation">(</span><span class="token punctuation">)</span><span class="token operator">: </span><a href="../../org.gradle.api.artifacts.dsl/-dependency-adder/index.html">DependencyAdder</a></div><div class="brief ">Returns a <a href="../../org.gradle.api.artifacts.dsl/-dependency-adder/index.html">DependencyAdder</a> to add to the set of compile-only dependencies.</div></div>                  </div>
                </div>
              </div>
            </div>
          </div>
<a data-name="1824655070%2FFunctions%2F-1793262594" anchor-label="getDependencyFactory" id="1824655070%2FFunctions%2F-1793262594" data-filterable-set=":docs/java_api"></a>
          <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="../../org.gradle.api.artifacts.dsl/-dependencies/get-dependency-factory.html"><span>get</span><wbr></wbr><span>Dependency</span><wbr></wbr><span><span>Factory</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="1824655070%2FFunctions%2F-1793262594"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
                    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api" data-active="" data-toggle=":docs/java_api">API</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/java_api"><div class="symbol monospace"><span class="token keyword">abstract </span><span class="token keyword"></span><span class="token keyword">fun </span><a href="../../org.gradle.api.artifacts.dsl/-dependencies/get-dependency-factory.html"><span class="token function">getDependencyFactory</span></a><span class="token punctuation">(</span><span class="token punctuation">)</span><span class="token operator">: </span><a href="../../org.gradle.api.artifacts.dsl/-dependency-factory/index.html">DependencyFactory</a></div></div>                  </div>
                </div>
              </div>
            </div>
          </div>
<a data-name="-1549657808%2FFunctions%2F-1793262594" anchor-label="getEnforcedPlatform" id="-1549657808%2FFunctions%2F-1793262594" data-filterable-set=":docs/java_api"></a>
          <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="../-platform-dependency-modifiers/get-enforced-platform.html"><span>get</span><wbr></wbr><span>Enforced</span><wbr></wbr><span><span>Platform</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="-1549657808%2FFunctions%2F-1793262594"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
                    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api" data-active="" data-toggle=":docs/java_api">API</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/java_api"><div class="symbol monospace"><span class="token keyword">abstract </span><span class="token keyword"></span><span class="token keyword">fun </span><a href="../-platform-dependency-modifiers/get-enforced-platform.html"><span class="token function">getEnforcedPlatform</span></a><span class="token punctuation">(</span><span class="token punctuation">)</span><span class="token operator">: </span><a href="../-platform-dependency-modifiers/-enforced-platform-dependency-modifier/index.html">PlatformDependencyModifiers.EnforcedPlatformDependencyModifier</a></div></div>                  </div>
                </div>
              </div>
            </div>
          </div>
<a data-name="-1721432526%2FFunctions%2F-1793262594" anchor-label="getImplementation" id="-1721432526%2FFunctions%2F-1793262594" data-filterable-set=":docs/java_api"></a>
          <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="get-implementation.html"><span>get</span><wbr></wbr><span><span>Implementation</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="-1721432526%2FFunctions%2F-1793262594"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
                    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api" data-active="" data-toggle=":docs/java_api">API</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/java_api"><div class="symbol monospace"><span class="token keyword">abstract </span><span class="token keyword"></span><span class="token keyword">fun </span><a href="get-implementation.html"><span class="token function">getImplementation</span></a><span class="token punctuation">(</span><span class="token punctuation">)</span><span class="token operator">: </span><a href="../../org.gradle.api.artifacts.dsl/-dependency-adder/index.html">DependencyAdder</a></div><div class="brief ">Returns a <a href="../../org.gradle.api.artifacts.dsl/-dependency-adder/index.html">DependencyAdder</a> to add to the set of implementation dependencies.</div></div>                  </div>
                </div>
              </div>
            </div>
          </div>
<a data-name="-2103982382%2FFunctions%2F-1793262594" anchor-label="getObjectFactory" id="-2103982382%2FFunctions%2F-1793262594" data-filterable-set=":docs/java_api"></a>
          <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="../../org.gradle.api.artifacts.dsl/-dependencies/get-object-factory.html"><span>get</span><wbr></wbr><span>Object</span><wbr></wbr><span><span>Factory</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="-2103982382%2FFunctions%2F-1793262594"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
                    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api" data-active="" data-toggle=":docs/java_api">API</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/java_api"><div class="symbol monospace"><span class="token keyword">abstract </span><span class="token keyword"></span><span class="token keyword">fun </span><a href="../../org.gradle.api.artifacts.dsl/-dependencies/get-object-factory.html"><span class="token function">getObjectFactory</span></a><span class="token punctuation">(</span><span class="token punctuation">)</span><span class="token operator">: </span><a href="../../org.gradle.api.model/-object-factory/index.html">ObjectFactory</a></div></div>                  </div>
                </div>
              </div>
            </div>
          </div>
<a data-name="-1538974254%2FFunctions%2F-1793262594" anchor-label="getPlatform" id="-1538974254%2FFunctions%2F-1793262594" data-filterable-set=":docs/java_api"></a>
          <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="../-platform-dependency-modifiers/get-platform.html"><span>get</span><wbr></wbr><span><span>Platform</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="-1538974254%2FFunctions%2F-1793262594"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
                    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api" data-active="" data-toggle=":docs/java_api">API</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/java_api"><div class="symbol monospace"><span class="token keyword">abstract </span><span class="token keyword"></span><span class="token keyword">fun </span><a href="../-platform-dependency-modifiers/get-platform.html"><span class="token function">getPlatform</span></a><span class="token punctuation">(</span><span class="token punctuation">)</span><span class="token operator">: </span><a href="../-platform-dependency-modifiers/-platform-dependency-modifier/index.html">PlatformDependencyModifiers.PlatformDependencyModifier</a></div></div>                  </div>
                </div>
              </div>
            </div>
          </div>
<a data-name="-1914634172%2FFunctions%2F-1793262594" anchor-label="getProject" id="-1914634172%2FFunctions%2F-1793262594" data-filterable-set=":docs/java_api"></a>
          <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="../../org.gradle.api.artifacts.dsl/-dependencies/get-project.html"><span>get</span><wbr></wbr><span><span>Project</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="-1914634172%2FFunctions%2F-1793262594"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
                    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api" data-active="" data-toggle=":docs/java_api">API</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/java_api"><div class="symbol monospace"><span class="token keyword">abstract </span><span class="token keyword"></span><span class="token keyword">fun </span><a href="../../org.gradle.api.artifacts.dsl/-dependencies/get-project.html"><span class="token function">getProject</span></a><span class="token punctuation">(</span><span class="token punctuation">)</span><span class="token operator">: </span><a href="../../org.gradle.api/-project/index.html">Project</a></div></div>                  </div>
                </div>
              </div>
            </div>
          </div>
<a data-name="111856658%2FFunctions%2F-1793262594" anchor-label="getRuntimeOnly" id="111856658%2FFunctions%2F-1793262594" data-filterable-set=":docs/java_api"></a>
          <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="get-runtime-only.html"><span>get</span><wbr></wbr><span>Runtime</span><wbr></wbr><span><span>Only</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="111856658%2FFunctions%2F-1793262594"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
                    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api" data-active="" data-toggle=":docs/java_api">API</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/java_api"><div class="symbol monospace"><span class="token keyword">abstract </span><span class="token keyword"></span><span class="token keyword">fun </span><a href="get-runtime-only.html"><span class="token function">getRuntimeOnly</span></a><span class="token punctuation">(</span><span class="token punctuation">)</span><span class="token operator">: </span><a href="../../org.gradle.api.artifacts.dsl/-dependency-adder/index.html">DependencyAdder</a></div><div class="brief ">Returns a <a href="../../org.gradle.api.artifacts.dsl/-dependency-adder/index.html">DependencyAdder</a> to add to the set of runtime-only dependencies.</div></div>                  </div>
                </div>
              </div>
            </div>
          </div>
<a data-name="1500967922%2FFunctions%2F-1793262594" anchor-label="getTestFixtures" id="1500967922%2FFunctions%2F-1793262594" data-filterable-set=":docs/java_api"></a>
          <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="../-test-fixtures-dependency-modifiers/get-test-fixtures.html"><span>get</span><wbr></wbr><span>Test</span><wbr></wbr><span><span>Fixtures</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="1500967922%2FFunctions%2F-1793262594"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
                    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api" data-active="" data-toggle=":docs/java_api">API</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/java_api"><div class="symbol monospace"><span class="token keyword">abstract </span><span class="token keyword"></span><span class="token keyword">fun </span><a href="../-test-fixtures-dependency-modifiers/get-test-fixtures.html"><span class="token function">getTestFixtures</span></a><span class="token punctuation">(</span><span class="token punctuation">)</span><span class="token operator">: </span><a href="../-test-fixtures-dependency-modifiers/-test-fixtures-dependency-modifier/index.html">TestFixturesDependencyModifiers.TestFixturesDependencyModifier</a></div></div>                  </div>
                </div>
              </div>
            </div>
          </div>
<a data-name="-810301383%2FFunctions%2F-1793262594" anchor-label="gradleApi" id="-810301383%2FFunctions%2F-1793262594" data-filterable-set=":docs/java_api"></a>
          <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="../../org.gradle.api.artifacts.dsl/-gradle-dependencies/gradle-api.html"><span>gradle</span><wbr></wbr><span><span>Api</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="-810301383%2FFunctions%2F-1793262594"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
                    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api" data-active="" data-toggle=":docs/java_api">API</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/java_api"><div class="symbol monospace"><span class="token keyword">open </span><span class="token keyword"></span><span class="token keyword">fun </span><a href="../../org.gradle.api.artifacts.dsl/-gradle-dependencies/gradle-api.html"><span class="token function">gradleApi</span></a><span class="token punctuation">(</span><span class="token punctuation">)</span><span class="token operator">: </span><a href="../../org.gradle.api.artifacts/-dependency/index.html">Dependency</a></div></div>                  </div>
                </div>
              </div>
            </div>
          </div>
<a data-name="1582448047%2FFunctions%2F-1793262594" anchor-label="gradleTestKit" id="1582448047%2FFunctions%2F-1793262594" data-filterable-set=":docs/java_api"></a>
          <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="../../org.gradle.api.artifacts.dsl/-gradle-dependencies/gradle-test-kit.html"><span>gradle</span><wbr></wbr><span>Test</span><wbr></wbr><span><span>Kit</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="1582448047%2FFunctions%2F-1793262594"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
                    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api" data-active="" data-toggle=":docs/java_api">API</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/java_api"><div class="symbol monospace"><span class="token keyword">open </span><span class="token keyword"></span><span class="token keyword">fun </span><a href="../../org.gradle.api.artifacts.dsl/-gradle-dependencies/gradle-test-kit.html"><span class="token function">gradleTestKit</span></a><span class="token punctuation">(</span><span class="token punctuation">)</span><span class="token operator">: </span><a href="../../org.gradle.api.artifacts/-dependency/index.html">Dependency</a></div></div>                  </div>
                </div>
              </div>
            </div>
          </div>
<a data-name="1327002675%2FFunctions%2F-1793262594" anchor-label="localGroovy" id="1327002675%2FFunctions%2F-1793262594" data-filterable-set=":docs/java_api"></a>
          <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="../../org.gradle.api.artifacts.dsl/-gradle-dependencies/local-groovy.html"><span>local</span><wbr></wbr><span><span>Groovy</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="1327002675%2FFunctions%2F-1793262594"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
                    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api" data-active="" data-toggle=":docs/java_api">API</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/java_api"><div class="symbol monospace"><span class="token keyword">open </span><span class="token keyword"></span><span class="token keyword">fun </span><a href="../../org.gradle.api.artifacts.dsl/-gradle-dependencies/local-groovy.html"><span class="token function">localGroovy</span></a><span class="token punctuation">(</span><span class="token punctuation">)</span><span class="token operator">: </span><a href="../../org.gradle.api.artifacts/-dependency/index.html">Dependency</a></div></div>                  </div>
                </div>
              </div>
            </div>
          </div>
<a data-name="125899120%2FFunctions%2F-1793262594" anchor-label="module" id="125899120%2FFunctions%2F-1793262594" data-filterable-set=":docs/java_api"></a>
          <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="../../org.gradle.api.artifacts.dsl/-dependencies/module.html"><span><span>module</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="125899120%2FFunctions%2F-1793262594"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
                    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api" data-active="" data-toggle=":docs/java_api">API</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/java_api"><div class="symbol monospace"><span class="token keyword">open </span><span class="token keyword"></span><span class="token keyword">fun </span><a href="../../org.gradle.api.artifacts.dsl/-dependencies/module.html"><span class="token function">module</span></a><span class="token punctuation">(</span><span class="parameters "><span class="parameter ">dependencyNotation<span class="token operator">: </span><a href="https://docs.oracle.com/en/java/javase/11/docs/api/java.base/java/lang/CharSequence.html">CharSequence</a></span></span><span class="token punctuation">)</span><span class="token operator">: </span><a href="../../org.gradle.api.artifacts/-external-module-dependency/index.html">ExternalModuleDependency</a></div></div>                  </div>
                </div>
              </div>
            </div>
          </div>
<a data-name="1177489002%2FFunctions%2F-1867656071" anchor-label="module" id="1177489002%2FFunctions%2F-1867656071" data-filterable-set=":docs/kotlin_dsl"></a>
          <div class="table-row" data-togglable="EXTENSION_FUNCTION" data-filterable-current=":docs/kotlin_dsl" data-filterable-set=":docs/kotlin_dsl">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="../../org.gradle.kotlin.dsl/module.html"><span><span>module</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="1177489002%2FFunctions%2F-1867656071"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
                    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/kotlin_dsl" data-filterable-set=":docs/kotlin_dsl" data-active="" data-toggle=":docs/kotlin_dsl">DSL</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/kotlin_dsl"><div class="symbol monospace"><span class="token keyword"></span><span class="token keyword">fun </span><a href="../../org.gradle.api.artifacts.dsl/-dependencies/index.html#-644536520%2FMain%2F-1867656071">Dependencies</a><span class="token punctuation">.</span><a href="../../org.gradle.kotlin.dsl/module.html"><span class="token function">module</span></a><span class="token punctuation">(</span><span class="parameters "><span class="parameter ">group<span class="token operator">: </span><a href="https://kotlinlang.org/api/latest/jvm/stdlib/kotlin/-string/index.html">String</a><span class="token operator">?</span><span class="token punctuation">, </span></span><span class="parameter ">name<span class="token operator">: </span><a href="https://kotlinlang.org/api/latest/jvm/stdlib/kotlin/-string/index.html">String</a><span class="token punctuation">, </span></span><span class="parameter ">version<span class="token operator">: </span><a href="https://kotlinlang.org/api/latest/jvm/stdlib/kotlin/-string/index.html">String</a><span class="token operator">?</span></span></span><span class="token punctuation">)</span><span class="token operator">: </span><span data-unresolved-link="org.gradle.api.artifacts/ExternalModuleDependency///PointingToDeclaration/">ExternalModuleDependency</span></div><div class="brief "><p class="paragraph">Creates a dependency based on the group, name and version (GAV) coordinates.</p></div></div>                  </div>
                </div>
              </div>
            </div>
          </div>
<a data-name="1258597109%2FFunctions%2F-1793262594" anchor-label="project" id="1258597109%2FFunctions%2F-1793262594" data-filterable-set=":docs/java_api"></a>
          <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="../../org.gradle.api.artifacts.dsl/-dependencies/project.html"><span><span>project</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="1258597109%2FFunctions%2F-1793262594"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
                    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api" data-active="" data-toggle=":docs/java_api">API</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/java_api"><div class="symbol monospace"><span class="token keyword">open </span><span class="token keyword"></span><span class="token keyword">fun </span><a href="../../org.gradle.api.artifacts.dsl/-dependencies/project.html"><span class="token function">project</span></a><span class="token punctuation">(</span><span class="parameters "><span class="parameter ">projectPath<span class="token operator">: </span><a href="https://docs.oracle.com/en/java/javase/11/docs/api/java.base/java/lang/String.html">String</a></span></span><span class="token punctuation">)</span><span class="token operator">: </span><a href="../../org.gradle.api.artifacts/-project-dependency/index.html">ProjectDependency</a></div></div>                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
      <div class="footer">
        <span class="go-to-top-icon"><a href="#content" id="go-to-top-link"></a></span><span>Gradle Kotlin DSL Reference</span><span
                class="pull-right"><span>Generated by </span><a
                href="https://github.com/Kotlin/dokka"><span>dokka</span><span class="padded-icon"></span></a></span>
      </div>
    </div>
</div>
</body>
</html>
