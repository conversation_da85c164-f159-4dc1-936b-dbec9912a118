<!DOCTYPE html>
<html>
<head>
    <meta name="viewport" content="width=device-width, initial-scale=1" charset="UTF-8">
    <title>org.gradle.api.artifacts.transform</title>
    <link href="../../images/logo-icon.svg" rel="icon" type="image/svg">
    <script>var pathToRoot = "../../";</script>
    <script>const storage = localStorage.getItem("dokka-dark-mode")
    if (storage == null) {
        const osDarkSchemePreferred = window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches
        if (osDarkSchemePreferred === true) {
            document.getElementsByTagName("html")[0].classList.add("theme-dark")
        }
    } else {
        const savedDarkMode = JSON.parse(storage)
        if(savedDarkMode === true) {
            document.getElementsByTagName("html")[0].classList.add("theme-dark")
        }
    }
    </script>
<script type="text/javascript" src="../../scripts/sourceset_dependencies.js" async="async"></script>
<link href="../../styles/style.css" rel="Stylesheet">
<link href="../../styles/jetbrains-mono.css" rel="Stylesheet">
<link href="../../styles/main.css" rel="Stylesheet">
<link href="../../styles/prism.css" rel="Stylesheet">
<link href="../../styles/logo-styles.css" rel="Stylesheet">
<script type="text/javascript" src="../../scripts/clipboard.js" async="async"></script>
<script type="text/javascript" src="../../scripts/navigation-loader.js" async="async"></script>
<script type="text/javascript" src="../../scripts/platform-content-handler.js" async="async"></script>
<script type="text/javascript" src="../../scripts/main.js" defer="defer"></script>
<script type="text/javascript" src="../../scripts/prism.js" async="async"></script>
<script type="text/javascript" src="../../scripts/symbol-parameters-wrapper_deferred.js" defer="defer"></script>
<link href="../../images/gradle-logo.svg">
<link href="../../styles/gradle.css" rel="Stylesheet">
</head>
<body>
<div class="navigation-wrapper" id="navigation-wrapper">
    <div id="leftToggler"><span class="icon-toggler"></span></div>
    <div class="library-name">
            <a href="../../index.html">
                    <span>Kotlin DSL Reference for Gradle</span>
            </a>
    </div>
    <div>
8.4    </div>
    <div class="pull-right d-flex">
        <div class="filter-section" id="filter-section">
                <button class="platform-tag platform-selector jvm-like" data-active="" data-filter=":docs/java_api">API</button>
        </div>
        <button id="theme-toggle-button"><span id="theme-toggle"></span></button>
        <div id="searchBar"></div>
    </div>
</div>
<div id="container">
    <div id="leftColumn">
        <div id="sideMenu"></div>
    </div>
    <div id="main">
<div class="main-content" data-page-type="package" id="content" pageIds="Kotlin DSL Reference for Gradle::org.gradle.api.artifacts.transform////PointingToDeclaration//-**********">
  <div class="breadcrumbs"><a href="../../index.html">Kotlin DSL Reference for Gradle</a><span class="delimiter">/</span><span class="current">org.gradle.api.artifacts.transform</span></div>
  <div class="cover ">
    <h1 class="cover"><span><span>Package-level</span></span> <span><span>declarations</span></span></h1>
    <div class="platform-hinted UnderCoverText with-platform-tabs" data-platform-hinted="data-platform-hinted">
      <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api" data-active="" data-toggle=":docs/java_api">API</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/java_api"><p class="paragraph">Provides classes, interfaces and annotations for registering and implementing artifact transforms. </p><p class="paragraph"> To register an artifact transform, use <a href="../org.gradle.api.artifacts.dsl/-dependency-handler/register-transform.html">registerTransform</a>. This allows you to register a <a href="-transform-action/index.html">org.gradle.api.artifacts.transform.TransformAction</a> using <a href="-transform-spec/index.html">org.gradle.api.artifacts.transform.TransformSpec</a>. </p><p class="paragraph">If you want to create a new artifact transform action, have a look at <a href="-transform-action/index.html">org.gradle.api.artifacts.transform.TransformAction</a>.</p></div>    </div>
  </div>
  <div class="tabbedcontent">
    <div class="tabs-section" tabs-section="tabs-section"><button class="section-tab" data-active="" data-togglable="TYPE">Types</button></div>
    <div class="tabs-section-body">
      <div data-togglable="TYPE">
        <h2 class="">Types</h2>
        <div class="table"><a data-name="-750266112%2FClasslikes%2F-**********" anchor-label="CacheableTransform" id="-750266112%2FClasslikes%2F-**********" data-filterable-set=":docs/java_api"></a>
          <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="-cacheable-transform/index.html"><span>Cacheable</span><wbr></wbr><span><span>Transform</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="-750266112%2FClasslikes%2F-**********"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
                    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api" data-active="" data-toggle=":docs/java_api">API</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/java_api"><div class="symbol monospace"><div class="block"><div class="block"><span class="token annotation builtin">@</span><a href="https://docs.oracle.com/en/java/javase/11/docs/api/java.base/java/lang/annotation/Retention.html"><span class="token annotation builtin">Retention</span></a><span class="token punctuation">(</span><span>value<span class="token operator"> = </span><a href="https://docs.oracle.com/en/java/javase/11/docs/api/java.base/java/lang/annotation/RetentionPolicy.html">RetentionPolicy.RUNTIME</a></span><wbr></wbr><span class="token punctuation">)</span></div><div class="block"><span class="token annotation builtin">@</span><a href="https://docs.oracle.com/en/java/javase/11/docs/api/java.base/java/lang/annotation/Target.html"><span class="token annotation builtin">Target</span></a><span class="token punctuation">(</span><span>value<span class="token operator"> = </span><span class="token punctuation">[</span><span><a href="https://docs.oracle.com/en/java/javase/11/docs/api/java.base/java/lang/annotation/ElementType.html">ElementType.TYPE</a></span><wbr></wbr><span class="token punctuation">]</span></span><wbr></wbr><span class="token punctuation">)</span></div></div><span class="token keyword">annotation class </span><a href="-cacheable-transform/index.html">CacheableTransform</a></div><div class="brief ">Attaching this annotation to a <a href="-transform-action/index.html">TransformAction</a> type it indicates that the build cache should be used for artifact transforms of this type.</div></div>                  </div>
                </div>
              </div>
            </div>
          </div>
<a data-name="1748044502%2FClasslikes%2F-**********" anchor-label="InputArtifact" id="1748044502%2FClasslikes%2F-**********" data-filterable-set=":docs/java_api"></a>
          <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="-input-artifact/index.html"><span>Input</span><wbr></wbr><span><span>Artifact</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="1748044502%2FClasslikes%2F-**********"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
                    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api" data-active="" data-toggle=":docs/java_api">API</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/java_api"><div class="symbol monospace"><div class="block"><div class="block"><span class="token annotation builtin">@</span><a href="https://docs.oracle.com/en/java/javase/11/docs/api/java.base/java/lang/annotation/Retention.html"><span class="token annotation builtin">Retention</span></a><span class="token punctuation">(</span><span>value<span class="token operator"> = </span><a href="https://docs.oracle.com/en/java/javase/11/docs/api/java.base/java/lang/annotation/RetentionPolicy.html">RetentionPolicy.RUNTIME</a></span><wbr></wbr><span class="token punctuation">)</span></div><div class="block"><span class="token annotation builtin">@</span><a href="https://docs.oracle.com/en/java/javase/11/docs/api/java.base/java/lang/annotation/Target.html"><span class="token annotation builtin">Target</span></a><span class="token punctuation">(</span><span>value<span class="token operator"> = </span><span class="token punctuation">[</span><span><a href="https://docs.oracle.com/en/java/javase/11/docs/api/java.base/java/lang/annotation/ElementType.html">ElementType.METHOD</a></span><wbr></wbr><span class="token punctuation">]</span></span><wbr></wbr><span class="token punctuation">)</span></div><div class="block"><span class="token annotation builtin">@</span><a href="https://docs.oracle.com/en/java/javase/11/docs/api/java.base/java/lang/annotation/Documented.html"><span class="token annotation builtin">Documented</span></a></div><div class="block"><span class="token annotation builtin">@</span><a href="../org.gradle.api.reflect/-injection-point-qualifier/index.html"><span class="token annotation builtin">InjectionPointQualifier</span></a><span class="token punctuation">(</span><span>supportedProviderTypes<span class="token operator"> = </span><span class="token punctuation">[</span><span><a href="../org.gradle.api.file/-file-system-location/index.html">FileSystemLocation::class</a></span><wbr></wbr><span class="token punctuation">]</span></span><wbr></wbr><span class="token punctuation">)</span></div></div><span class="token keyword">annotation class </span><a href="-input-artifact/index.html">InputArtifact</a></div><div class="brief ">Attach this annotation to an abstract getter that should receive the input artifact for an artifact transform.</div></div>                  </div>
                </div>
              </div>
            </div>
          </div>
<a data-name="**********%2FClasslikes%2F-**********" anchor-label="InputArtifactDependencies" id="**********%2FClasslikes%2F-**********" data-filterable-set=":docs/java_api"></a>
          <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="-input-artifact-dependencies/index.html"><span>Input</span><wbr></wbr><span>Artifact</span><wbr></wbr><span><span>Dependencies</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="**********%2FClasslikes%2F-**********"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
                    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api" data-active="" data-toggle=":docs/java_api">API</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/java_api"><div class="symbol monospace"><div class="block"><div class="block"><span class="token annotation builtin">@</span><a href="https://docs.oracle.com/en/java/javase/11/docs/api/java.base/java/lang/annotation/Retention.html"><span class="token annotation builtin">Retention</span></a><span class="token punctuation">(</span><span>value<span class="token operator"> = </span><a href="https://docs.oracle.com/en/java/javase/11/docs/api/java.base/java/lang/annotation/RetentionPolicy.html">RetentionPolicy.RUNTIME</a></span><wbr></wbr><span class="token punctuation">)</span></div><div class="block"><span class="token annotation builtin">@</span><a href="https://docs.oracle.com/en/java/javase/11/docs/api/java.base/java/lang/annotation/Target.html"><span class="token annotation builtin">Target</span></a><span class="token punctuation">(</span><span>value<span class="token operator"> = </span><span class="token punctuation">[</span><span><a href="https://docs.oracle.com/en/java/javase/11/docs/api/java.base/java/lang/annotation/ElementType.html">ElementType.METHOD</a></span><wbr></wbr><span class="token punctuation">]</span></span><wbr></wbr><span class="token punctuation">)</span></div><div class="block"><span class="token annotation builtin">@</span><a href="https://docs.oracle.com/en/java/javase/11/docs/api/java.base/java/lang/annotation/Documented.html"><span class="token annotation builtin">Documented</span></a></div><div class="block"><span class="token annotation builtin">@</span><a href="../org.gradle.api.reflect/-injection-point-qualifier/index.html"><span class="token annotation builtin">InjectionPointQualifier</span></a><span class="token punctuation">(</span><span>supportedTypes<span class="token operator"> = </span><a href="../org.gradle.api.file/-file-collection/index.html">FileCollection::class</a></span><wbr></wbr><span class="token punctuation">)</span></div></div><span class="token keyword">annotation class </span><a href="-input-artifact-dependencies/index.html">InputArtifactDependencies</a></div><div class="brief ">Attach this annotation to an abstract getter that should receive the artifact dependencies of the <a href="-input-artifact/index.html">InputArtifact</a> of an artifact transform.</div></div>                  </div>
                </div>
              </div>
            </div>
          </div>
<a data-name="1938533456%2FClasslikes%2F-**********" anchor-label="TransformAction" id="1938533456%2FClasslikes%2F-**********" data-filterable-set=":docs/java_api"></a>
          <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="-transform-action/index.html"><span>Transform</span><wbr></wbr><span><span>Action</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="1938533456%2FClasslikes%2F-**********"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
                    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api" data-active="" data-toggle=":docs/java_api">API</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/java_api"><div class="symbol monospace"><span class="token keyword">interface </span><a href="-transform-action/index.html">TransformAction</a><span class="token operator">&lt;</span><span class="token keyword"></span><a href="-transform-action/index.html">T</a><span class="token operator"> : </span><a href="-transform-parameters/index.html">TransformParameters</a><span class="token operator">?</span><span class="token operator">&gt;</span></div><div class="brief ">Interface for artifact transform actions.</div></div>                  </div>
                </div>
              </div>
            </div>
          </div>
<a data-name="34700778%2FClasslikes%2F-**********" anchor-label="TransformOutputs" id="34700778%2FClasslikes%2F-**********" data-filterable-set=":docs/java_api"></a>
          <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="-transform-outputs/index.html"><span>Transform</span><wbr></wbr><span><span>Outputs</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="34700778%2FClasslikes%2F-**********"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
                    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api" data-active="" data-toggle=":docs/java_api">API</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/java_api"><div class="symbol monospace"><span class="token keyword">interface </span><a href="-transform-outputs/index.html">TransformOutputs</a></div><div class="brief ">The outputs of the artifact transform.</div></div>                  </div>
                </div>
              </div>
            </div>
          </div>
<a data-name="-1385600868%2FClasslikes%2F-**********" anchor-label="TransformParameters" id="-1385600868%2FClasslikes%2F-**********" data-filterable-set=":docs/java_api"></a>
          <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="-transform-parameters/index.html"><span>Transform</span><wbr></wbr><span><span>Parameters</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="-1385600868%2FClasslikes%2F-**********"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
                    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api" data-active="" data-toggle=":docs/java_api">API</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/java_api"><div class="symbol monospace"><span class="token keyword">interface </span><a href="-transform-parameters/index.html">TransformParameters</a></div><div class="brief ">Marker interface for parameter objects to <a href="-transform-action/index.html">TransformAction</a>s.</div></div>                  </div>
                </div>
              </div>
            </div>
          </div>
<a data-name="-677420117%2FClasslikes%2F-**********" anchor-label="TransformSpec" id="-677420117%2FClasslikes%2F-**********" data-filterable-set=":docs/java_api"></a>
          <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="-transform-spec/index.html"><span>Transform</span><wbr></wbr><span><span>Spec</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="-677420117%2FClasslikes%2F-**********"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
                    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api" data-active="" data-toggle=":docs/java_api">API</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/java_api"><div class="symbol monospace"><span class="token keyword">interface </span><a href="-transform-spec/index.html">TransformSpec</a><span class="token operator">&lt;</span><span class="token keyword"></span><a href="-transform-spec/index.html">T</a><span class="token operator"> : </span><a href="-transform-parameters/index.html">TransformParameters</a><span class="token operator">?</span><span class="token operator">&gt;</span></div><div class="brief ">Base configuration for artifact transform registrations.</div></div>                  </div>
                </div>
              </div>
            </div>
          </div>
<a data-name="-1320130768%2FClasslikes%2F-**********" anchor-label="VariantTransformConfigurationException" id="-1320130768%2FClasslikes%2F-**********" data-filterable-set=":docs/java_api"></a>
          <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="-variant-transform-configuration-exception/index.html"><span>Variant</span><wbr></wbr><span>Transform</span><wbr></wbr><span>Configuration</span><wbr></wbr><span><span>Exception</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="-1320130768%2FClasslikes%2F-**********"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
                    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api" data-active="" data-toggle=":docs/java_api">API</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/java_api"><div class="symbol monospace"><span class="token keyword">open </span><span class="token keyword">class </span><a href="-variant-transform-configuration-exception/index.html">VariantTransformConfigurationException</a> : <a href="../org.gradle.api/-gradle-exception/index.html">GradleException</a></div><div class="brief ">An exception to report a problem during a transform execution.</div></div>                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
      <div class="footer">
        <span class="go-to-top-icon"><a href="#content" id="go-to-top-link"></a></span><span>Gradle Kotlin DSL Reference</span><span
                class="pull-right"><span>Generated by </span><a
                href="https://github.com/Kotlin/dokka"><span>dokka</span><span class="padded-icon"></span></a></span>
      </div>
    </div>
</div>
</body>
</html>
