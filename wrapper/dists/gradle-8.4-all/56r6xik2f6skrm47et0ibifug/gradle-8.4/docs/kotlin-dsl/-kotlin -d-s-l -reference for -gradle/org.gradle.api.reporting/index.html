<!DOCTYPE html>
<html>
<head>
    <meta name="viewport" content="width=device-width, initial-scale=1" charset="UTF-8">
    <title>org.gradle.api.reporting</title>
    <link href="../../images/logo-icon.svg" rel="icon" type="image/svg">
    <script>var pathToRoot = "../../";</script>
    <script>const storage = localStorage.getItem("dokka-dark-mode")
    if (storage == null) {
        const osDarkSchemePreferred = window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches
        if (osDarkSchemePreferred === true) {
            document.getElementsByTagName("html")[0].classList.add("theme-dark")
        }
    } else {
        const savedDarkMode = JSON.parse(storage)
        if(savedDarkMode === true) {
            document.getElementsByTagName("html")[0].classList.add("theme-dark")
        }
    }
    </script>
<script type="text/javascript" src="../../scripts/sourceset_dependencies.js" async="async"></script>
<link href="../../styles/style.css" rel="Stylesheet">
<link href="../../styles/jetbrains-mono.css" rel="Stylesheet">
<link href="../../styles/main.css" rel="Stylesheet">
<link href="../../styles/prism.css" rel="Stylesheet">
<link href="../../styles/logo-styles.css" rel="Stylesheet">
<script type="text/javascript" src="../../scripts/clipboard.js" async="async"></script>
<script type="text/javascript" src="../../scripts/navigation-loader.js" async="async"></script>
<script type="text/javascript" src="../../scripts/platform-content-handler.js" async="async"></script>
<script type="text/javascript" src="../../scripts/main.js" defer="defer"></script>
<script type="text/javascript" src="../../scripts/prism.js" async="async"></script>
<script type="text/javascript" src="../../scripts/symbol-parameters-wrapper_deferred.js" defer="defer"></script>
<link href="../../images/gradle-logo.svg">
<link href="../../styles/gradle.css" rel="Stylesheet">
</head>
<body>
<div class="navigation-wrapper" id="navigation-wrapper">
    <div id="leftToggler"><span class="icon-toggler"></span></div>
    <div class="library-name">
            <a href="../../index.html">
                    <span>Kotlin DSL Reference for Gradle</span>
            </a>
    </div>
    <div>
8.4    </div>
    <div class="pull-right d-flex">
        <div class="filter-section" id="filter-section">
                <button class="platform-tag platform-selector jvm-like" data-active="" data-filter=":docs/java_api">API</button>
        </div>
        <button id="theme-toggle-button"><span id="theme-toggle"></span></button>
        <div id="searchBar"></div>
    </div>
</div>
<div id="container">
    <div id="leftColumn">
        <div id="sideMenu"></div>
    </div>
    <div id="main">
<div class="main-content" data-page-type="package" id="content" pageIds="Kotlin DSL Reference for Gradle::org.gradle.api.reporting////PointingToDeclaration//-1793262594">
  <div class="breadcrumbs"><a href="../../index.html">Kotlin DSL Reference for Gradle</a><span class="delimiter">/</span><span class="current">org.gradle.api.reporting</span></div>
  <div class="cover ">
    <h1 class="cover"><span><span>Package-level</span></span> <span><span>declarations</span></span></h1>
    <div class="platform-hinted UnderCoverText with-platform-tabs" data-platform-hinted="data-platform-hinted">
      <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api" data-active="" data-toggle=":docs/java_api">API</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/java_api"><p class="paragraph">Classes for reporting</p></div>    </div>
  </div>
  <div class="tabbedcontent">
    <div class="tabs-section" tabs-section="tabs-section"><button class="section-tab" data-active="" data-togglable="TYPE">Types</button></div>
    <div class="tabs-section-body">
      <div data-togglable="TYPE">
        <h2 class="">Types</h2>
        <div class="table"><a data-name="-1988838678%2FClasslikes%2F-1793262594" anchor-label="BuildDashboardReports" id="-1988838678%2FClasslikes%2F-1793262594" data-filterable-set=":docs/java_api"></a>
          <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="-build-dashboard-reports/index.html"><span>Build</span><wbr></wbr><span>Dashboard</span><wbr></wbr><span><span>Reports</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="-1988838678%2FClasslikes%2F-1793262594"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
                    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api" data-active="" data-toggle=":docs/java_api">API</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/java_api"><div class="symbol monospace"><span class="token keyword">interface </span><a href="-build-dashboard-reports/index.html">BuildDashboardReports</a> : <a href="-report-container/index.html">ReportContainer</a><span class="token operator">&lt;</span><a href="-report-container/index.html">T</a><span class="token operator">&gt; </span></div><div class="brief ">The reporting configuration for the <a href="-generate-build-dashboard/index.html">GenerateBuildDashboard</a> task.</div></div>                  </div>
                </div>
              </div>
            </div>
          </div>
<a data-name="-1623071598%2FClasslikes%2F-1793262594" anchor-label="ConfigurableReport" id="-1623071598%2FClasslikes%2F-1793262594" data-filterable-set=":docs/java_api"></a>
          <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="-configurable-report/index.html"><span>Configurable</span><wbr></wbr><span><span>Report</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="-1623071598%2FClasslikes%2F-1793262594"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
                    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api" data-active="" data-toggle=":docs/java_api">API</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/java_api"><div class="symbol monospace"><span class="token keyword">interface </span><a href="-configurable-report/index.html">ConfigurableReport</a> : <a href="-report/index.html">Report</a></div><div class="brief ">A file based report to be created with a configurable destination.</div></div>                  </div>
                </div>
              </div>
            </div>
          </div>
<a data-name="1469401220%2FClasslikes%2F-1793262594" anchor-label="CustomizableHtmlReport" id="1469401220%2FClasslikes%2F-1793262594" data-filterable-set=":docs/java_api"></a>
          <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="-customizable-html-report/index.html"><span>Customizable</span><wbr></wbr><span>Html</span><wbr></wbr><span><span>Report</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="1469401220%2FClasslikes%2F-1793262594"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
                    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api" data-active="" data-toggle=":docs/java_api">API</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/java_api"><div class="symbol monospace"><span class="token keyword">interface </span><a href="-customizable-html-report/index.html">CustomizableHtmlReport</a> : <a href="-single-file-report/index.html">SingleFileReport</a></div><div class="brief ">A HTML Report whose generation can be customized with a XSLT stylesheet.</div></div>                  </div>
                </div>
              </div>
            </div>
          </div>
<a data-name="-858389886%2FClasslikes%2F-1793262594" anchor-label="DirectoryReport" id="-858389886%2FClasslikes%2F-1793262594" data-filterable-set=":docs/java_api"></a>
          <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="-directory-report/index.html"><span>Directory</span><wbr></wbr><span><span>Report</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="-858389886%2FClasslikes%2F-1793262594"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
                    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api" data-active="" data-toggle=":docs/java_api">API</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/java_api"><div class="symbol monospace"><span class="token keyword">interface </span><a href="-directory-report/index.html">DirectoryReport</a> : <a href="-configurable-report/index.html">ConfigurableReport</a></div><div class="brief ">A directory based report to be created.</div></div>                  </div>
                </div>
              </div>
            </div>
          </div>
<a data-name="719161988%2FClasslikes%2F-1793262594" anchor-label="GenerateBuildDashboard" id="719161988%2FClasslikes%2F-1793262594" data-filterable-set=":docs/java_api"></a>
          <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="-generate-build-dashboard/index.html"><span>Generate</span><wbr></wbr><span>Build</span><wbr></wbr><span><span>Dashboard</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="719161988%2FClasslikes%2F-1793262594"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
                    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api" data-active="" data-toggle=":docs/java_api">API</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/java_api"><div class="symbol monospace"><div class="block"><div class="block"><span class="token annotation builtin">@</span><a href="../org.gradle.work/-disable-caching-by-default/index.html"><span class="token annotation builtin">DisableCachingByDefault</span></a><span class="token punctuation">(</span><span>because<span class="token operator"> = </span><span class="breakable-word"><span class="token string">&quot;Not made cacheable, yet&quot;</span></span></span><wbr></wbr><span class="token punctuation">)</span></div></div><span class="token keyword">abstract </span><span class="token keyword">class </span><a href="-generate-build-dashboard/index.html">GenerateBuildDashboard</a> : <a href="../org.gradle.api/-default-task/index.html">DefaultTask</a>, <a href="-reporting/index.html">Reporting</a><span class="token operator">&lt;</span><a href="-reporting/index.html">T</a><span class="token operator">&gt; </span></div><div class="brief ">Generates build dashboard report.</div></div>                  </div>
                </div>
              </div>
            </div>
          </div>
<a data-name="-1474588661%2FClasslikes%2F-1793262594" anchor-label="Report" id="-1474588661%2FClasslikes%2F-1793262594" data-filterable-set=":docs/java_api"></a>
          <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="-report/index.html"><span><span>Report</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="-1474588661%2FClasslikes%2F-1793262594"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
                    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api" data-active="" data-toggle=":docs/java_api">API</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/java_api"><div class="symbol monospace"><span class="token keyword">interface </span><a href="-report/index.html">Report</a> : <a href="../org.gradle.util/-configurable/index.html">Configurable</a><span class="token operator">&lt;</span><a href="../org.gradle.util/-configurable/index.html">T</a><span class="token operator">&gt; </span></div><div class="brief ">A file based report to be created.</div></div>                  </div>
                </div>
              </div>
            </div>
          </div>
<a data-name="778708054%2FClasslikes%2F-1793262594" anchor-label="ReportContainer" id="778708054%2FClasslikes%2F-1793262594" data-filterable-set=":docs/java_api"></a>
          <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="-report-container/index.html"><span>Report</span><wbr></wbr><span><span>Container</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="778708054%2FClasslikes%2F-1793262594"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
                    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api" data-active="" data-toggle=":docs/java_api">API</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/java_api"><div class="symbol monospace"><span class="token keyword">interface </span><a href="-report-container/index.html">ReportContainer</a><span class="token operator">&lt;</span><span class="token keyword"></span><a href="-report-container/index.html">T</a><span class="token operator"> : </span><a href="-report/index.html">Report</a><span class="token operator">?</span><span class="token operator">&gt;</span> : <a href="../org.gradle.api/-named-domain-object-set/index.html">NamedDomainObjectSet</a><span class="token operator">&lt;</span><a href="../org.gradle.api/-named-domain-object-set/index.html">T</a><span class="token operator">&gt; </span>, <a href="../org.gradle.util/-configurable/index.html">Configurable</a><span class="token operator">&lt;</span><a href="../org.gradle.util/-configurable/index.html">T</a><span class="token operator">&gt; </span></div><div class="brief ">A container of <a href="-report/index.html">Report</a> objects, that represent potential reports.</div></div>                  </div>
                </div>
              </div>
            </div>
          </div>
<a data-name="-2123939723%2FClasslikes%2F-1793262594" anchor-label="Reporting" id="-2123939723%2FClasslikes%2F-1793262594" data-filterable-set=":docs/java_api"></a>
          <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="-reporting/index.html"><span><span>Reporting</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="-2123939723%2FClasslikes%2F-1793262594"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
                    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api" data-active="" data-toggle=":docs/java_api">API</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/java_api"><div class="symbol monospace"><span class="token keyword">interface </span><a href="-reporting/index.html">Reporting</a><span class="token operator">&lt;</span><span class="token keyword"></span><a href="-reporting/index.html">T</a><span class="token operator"> : </span><a href="-report-container/index.html">ReportContainer</a><span class="token operator">?</span><span class="token operator">&gt;</span></div><div class="brief ">An object that provides reporting options.</div></div>                  </div>
                </div>
              </div>
            </div>
          </div>
<a data-name="1133776174%2FClasslikes%2F-1793262594" anchor-label="ReportingExtension" id="1133776174%2FClasslikes%2F-1793262594" data-filterable-set=":docs/java_api"></a>
          <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="-reporting-extension/index.html"><span>Reporting</span><wbr></wbr><span><span>Extension</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="1133776174%2FClasslikes%2F-1793262594"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
                    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api" data-active="" data-toggle=":docs/java_api">API</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/java_api"><div class="symbol monospace"><span class="token keyword">abstract </span><span class="token keyword">class </span><a href="-reporting-extension/index.html">ReportingExtension</a></div><div class="brief ">A project extension named &quot;reporting&quot; that provides basic reporting settings and utilities.</div></div>                  </div>
                </div>
              </div>
            </div>
          </div>
<a data-name="1798841232%2FClasslikes%2F-1793262594" anchor-label="ReportSpec" id="1798841232%2FClasslikes%2F-1793262594" data-filterable-set=":docs/java_api"></a>
          <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="-report-spec/index.html"><span>Report</span><wbr></wbr><span><span>Spec</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="1798841232%2FClasslikes%2F-1793262594"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
                    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api" data-active="" data-toggle=":docs/java_api">API</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/java_api"><div class="symbol monospace"><div class="block"><div class="block"><span class="token annotation builtin">@</span><a href="../org.gradle.api/-incubating/index.html"><span class="token annotation builtin">Incubating</span></a></div></div><span class="token keyword">interface </span><a href="-report-spec/index.html">ReportSpec</a> : <a href="../org.gradle.api/-named/index.html">Named</a></div><div class="brief ">Common parent for aggregation report types</div></div>                  </div>
                </div>
              </div>
            </div>
          </div>
<a data-name="-260577273%2FClasslikes%2F-1793262594" anchor-label="SingleFileReport" id="-260577273%2FClasslikes%2F-1793262594" data-filterable-set=":docs/java_api"></a>
          <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="-single-file-report/index.html"><span>Single</span><wbr></wbr><span>File</span><wbr></wbr><span><span>Report</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="-260577273%2FClasslikes%2F-1793262594"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
                    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api" data-active="" data-toggle=":docs/java_api">API</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/java_api"><div class="symbol monospace"><span class="token keyword">interface </span><a href="-single-file-report/index.html">SingleFileReport</a> : <a href="-configurable-report/index.html">ConfigurableReport</a></div><div class="brief ">A report that is a single file.</div></div>                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
      <div class="footer">
        <span class="go-to-top-icon"><a href="#content" id="go-to-top-link"></a></span><span>Gradle Kotlin DSL Reference</span><span
                class="pull-right"><span>Generated by </span><a
                href="https://github.com/Kotlin/dokka"><span>dokka</span><span class="padded-icon"></span></a></span>
      </div>
    </div>
</div>
</body>
</html>
