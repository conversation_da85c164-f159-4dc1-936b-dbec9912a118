/*
 * Copyright 2019 the original author or authors.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package org.gradle.workers;

import org.gradle.api.file.ConfigurableFileCollection;

/**
 * A worker spec providing the requirements of an isolated classpath.
 *
 * @since 5.6
 */
public interface ClassLoaderWorkerSpec extends WorkerSpec {
    /**
     * Gets the classpath associated with the worker.
     *
     * @return the classpath associated with the worker
     */
    ConfigurableFileCollection getClasspath();
}
