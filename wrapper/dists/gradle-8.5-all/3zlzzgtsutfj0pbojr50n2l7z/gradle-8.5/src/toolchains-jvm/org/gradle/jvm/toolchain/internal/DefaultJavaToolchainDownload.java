/*
 * Copyright 2022 the original author or authors.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package org.gradle.jvm.toolchain.internal;

import org.gradle.jvm.toolchain.JavaToolchainDownload;

import java.net.URI;

public class DefaultJavaToolchainDownload implements JavaToolchainDownload {

    public static DefaultJavaToolchainDownload fromUri(URI uri) {
        return new DefaultJavaToolchainDownload(uri);
    }

    private final URI uri;

    private DefaultJavaToolchainDownload(URI uri) {
        this.uri = uri;
    }

    @Override
    public URI getUri() {
        return uri;
    }
}
