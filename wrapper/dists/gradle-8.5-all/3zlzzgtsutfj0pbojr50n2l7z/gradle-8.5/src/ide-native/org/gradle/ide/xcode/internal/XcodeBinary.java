/*
 * Copyright 2018 the original author or authors.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package org.gradle.ide.xcode.internal;

import org.gradle.api.file.FileSystemLocation;
import org.gradle.api.provider.Provider;

public class XcodeBinary {
    private final String buildConfigurationName;
    private final Provider<? extends FileSystemLocation> outputFile;
    private final String architectureName;

    public XcodeBinary(String buildConfigurationName, Provider<? extends FileSystemLocation> outputFile, String architectureName) {
        this.buildConfigurationName = buildConfigurationName;
        this.outputFile = outputFile;
        this.architectureName = architectureName;
    }

    public String getBuildConfigurationName() {
        return buildConfigurationName;
    }

    public Provider<? extends FileSystemLocation> getOutputFile() {
        return outputFile;
    }

    public String getArchitectureName() {
        return architectureName;
    }
}
