/*
 * Copyright 2020 the original author or authors.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package org.gradle.internal.snapshot;

/**
 * Ways to continue visiting a snapshot hierarchy after an entry has been visited.
 *
 * @see java.nio.file.FileVisitResult
 */
public enum SnapshotVisitResult {

    /**
     * Continue visiting. When returned after visiting a directory,
     * the entries in the directory will be visited next.
     */
    CONTINUE,

    /**
     * Terminate visiting immediately.
     */
    TERMINATE,

    /**
     * If returned from visiting a directory, the directories entries will not be visited;
     * otherwise works as {@link #CONTINUE}.
     */
    SKIP_SUBTREE
}
