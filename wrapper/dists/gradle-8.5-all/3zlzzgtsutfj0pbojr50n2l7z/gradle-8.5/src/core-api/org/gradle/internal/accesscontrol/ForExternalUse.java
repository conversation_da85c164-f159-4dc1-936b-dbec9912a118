/*
 * Copyright 2022 the original author or authors.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package org.gradle.internal.accesscontrol;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * Designates a public API declaration that should not be used internally in the Gradle codebase.
 * This is due to the contract of this public API that is stricter than what is expected in the Gradle internals.
 * <p>
 * For example, the implementation of such an API might add validations checking third-party
 * plugins or build scripts for compliance with some limitations imposed by Gradle. In the internal
 * code, one does not expect such limitations.
 * <p>
 * This annotations should only be applied to Gradle public API declarations.
 *
 * @see AllowUsingApiForExternalUse
 */
@Retention(RetentionPolicy.CLASS)
@Target({ElementType.TYPE, ElementType.METHOD, ElementType.FIELD})
public @interface ForExternalUse {
}
