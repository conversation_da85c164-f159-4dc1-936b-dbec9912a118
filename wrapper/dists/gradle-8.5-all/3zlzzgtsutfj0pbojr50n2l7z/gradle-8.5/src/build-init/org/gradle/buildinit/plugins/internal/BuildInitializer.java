/*
 * Copyright 2016 the original author or authors.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package org.gradle.buildinit.plugins.internal;

import org.gradle.buildinit.plugins.internal.modifiers.BuildInitDsl;
import org.gradle.buildinit.plugins.internal.modifiers.BuildInitTestFramework;
import org.gradle.buildinit.plugins.internal.modifiers.ComponentType;
import org.gradle.buildinit.plugins.internal.modifiers.Language;
import org.gradle.buildinit.plugins.internal.modifiers.ModularizationOption;

import java.util.Optional;
import java.util.Set;

/**
 * Initializes a Gradle build.
 */
public interface BuildInitializer {
    String getId();

    /**
     * What kind of component does this initializer create?
     */
    ComponentType getComponentType();

    /**
     * Which language does the production source files created by this initializer use?
     */
    Language getLanguage();

    boolean supportsJavaTargets();

    /**
     * Can this type of project be split-up into multiple subprojects?
     */
    Set<ModularizationOption> getModularizationOptions();

    /**
     * Returns the set of DSLs supported for this type of project.
     */
    Set<BuildInitDsl> getDsls();

    /**
     * The preferred DSL to use for this type of project.
     */
    BuildInitDsl getDefaultDsl();

    /**
     * Does a project name make sense for this type of project?
     */
    boolean supportsProjectName();

    /**
     * Does a source package name make sense for this type of project?
     */
    boolean supportsPackage();

    /**
     * Returns the set of test frameworks supported for this type of project.
     */
    Set<BuildInitTestFramework> getTestFrameworks();

    /**
     * Returns {@link BuildInitTestFramework#NONE} when no tests generated by default.
     */
    BuildInitTestFramework getDefaultTestFramework();

    /**
     * Returns a collection of further reading related to this type of build (may be empty).
     */
    Optional<String> getFurtherReading(InitSettings settings);
    /**
     * Generates content for the given build.
     */
    void generate(InitSettings settings);
}
