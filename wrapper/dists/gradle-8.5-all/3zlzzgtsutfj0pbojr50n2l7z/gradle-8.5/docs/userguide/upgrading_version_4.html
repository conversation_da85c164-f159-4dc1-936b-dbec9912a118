<!DOCTYPE html>
<html lang="en-US">
<head>
<meta charset="utf-8">
<meta http-equiv="X-UA-Compatible" content="IE=edge">
<meta name="viewport" content="width=device-width, initial-scale=1.0">
<meta name="generator" content="Asciidoctor 2.0.12">
<title>Upgrading your build from Gradle 4.x to 5.0</title>
<style>
/*
 * Copyright 2018 the original author or authors.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
 /* Custom Admonition Blocks */
:root {
    --caution-color: #e40046;
    --caution-on-color: #fff;
    --important-color: #802392;
    --important-on-color: #fff;
    --note-color: #2d7dd2;
    --note-on-color: #fff;
    --tip-color: #43b929;
    --tip-on-color: #fff;
    --warning-color: #f70;
    --warning-on-color: #fff;
    --admonition-background: #fafafa;
    --doc-icon-filter: invert(14.5%);
    --rem-base: 18;
}

/* Lato (normal, regular) */
@font-face {
    font-family: Lato;
    font-weight: 400;
    font-style: normal;
    src: url("https://assets.gradle.com/lato/fonts/lato-normal/lato-normal.woff2") format("woff2"),
    url("https://assets.gradle.com/lato/fonts/lato-normal/lato-normal.woff") format("woff");
}
/* Lato (normal, italic) */
@font-face {
    font-display: swap;
    font-family: Lato;
    font-weight: 400;
    font-style: italic;
    src: url("https://assets.gradle.com/lato/fonts/lato-normal-italic/lato-normal-italic.woff2") format("woff2"),
    url("https://assets.gradle.com/lato/fonts/lato-normal-italic/lato-normal-italic.woff") format("woff");
}
/* Lato (bold, regular) */
@font-face {
    font-display: swap;
    font-family: Lato;
    font-weight: 500;
    font-style: normal;
    src: url("https://assets.gradle.com/lato/fonts/lato-semibold/lato-semibold.woff2") format("woff2"),
    url("https://assets.gradle.com/lato/fonts/lato-semibold/lato-semibold.woff") format("woff");
}
/* Lato (bold, regular) */
@font-face {
    font-display: swap;
    font-family: Lato;
    font-weight: 800;
    font-style: normal;
    src: url("https://assets.gradle.com/lato/fonts/lato-heavy/lato-heavy.woff2") format("woff2"),
    url("https://assets.gradle.com/lato/fonts/lato-heavy/lato-heavy.woff") format("woff");
}


/* BEGIN asciidoc.css */

/*! normalize.css v2.1.2 | MIT License | git.io/normalize */
/* ========================================================================== HTML5 display definitions ========================================================================== */
/** Correct `block` display not defined in IE 8/9. */
article, aside, details, figcaption, figure, footer, header, hgroup, main, nav, section, summary { display: block; }

/** Correct `inline-block` display not defined in IE 8/9. */
audio, canvas, video { display: inline-block; }

/** Prevent modern browsers from displaying `audio` without controls. Remove excess height in iOS 5 devices. */
audio:not([controls]) { display: none; height: 0; }

/** Address `[hidden]` styling not present in IE 8/9. Hide the `template` element in IE, Safari, and Firefox < 22. */
[hidden], template { display: none; }

script { display: none !important; }

/* ========================================================================== Base ========================================================================== */
/** 1. Set default font family to sans-serif. 2. Prevent iOS text size adjust after orientation change, without disabling user zoom. */
html { font-family: sans-serif; /* 1 */ -ms-text-size-adjust: 100%; /* 2 */ -webkit-text-size-adjust: 100%; /* 2 */ }

/** Remove default margin. */
body { margin: 0; }

/* ========================================================================== Links ========================================================================== */
/** Remove the gray background color from active links in IE 10. */
a { background: transparent; }

/** Address `outline` inconsistency between Chrome and other browsers. */
a:focus { outline: thin dotted; }

/** Improve readability when focused and also mouse hovered in all browsers. */
a:active, a:hover { outline: 0; }

/* ========================================================================== Typography ========================================================================== */
/** Address variable `h1` font-size and margin within `section` and `article` contexts in Firefox 4+, Safari 5, and Chrome. */
h1 { font-size: 2em; margin: 0.67em 0; }

/** Address styling not present in IE 8/9, Safari 5, and Chrome. */
abbr[title] { border-bottom: 1px dotted; }

/** Address style set to `bolder` in Firefox 4+, Safari 5, and Chrome. */
b, strong { font-weight: bold; }

/** Address styling not present in Safari 5 and Chrome. */
dfn { font-style: italic; }

/** Address differences between Firefox and other browsers. */
hr { -moz-box-sizing: content-box; box-sizing: content-box; height: 0; }

/** Address styling not present in IE 8/9. */
mark { background: #ff0; color: #000; }

/** Correct font family set oddly in Safari 5 and Chrome. */
code, kbd, pre, samp { font-family: monospace, serif; font-size: 1em; }

/** Improve readability of pre-formatted text in all browsers. */
pre { white-space: pre-wrap; }

/** Set consistent quote types. */
q { quotes: "\201C" "\201D" "\2018" "\2019"; }

/** Address inconsistent and variable font size in all browsers. */
small { font-size: 80%; }

/** Prevent `sub` and `sup` affecting `line-height` in all browsers. */
sub, sup { font-size: 75%; line-height: 0; position: relative; vertical-align: baseline; }

sup { top: -0.5em; }

sub { bottom: -0.25em; }

/* ========================================================================== Embedded content ========================================================================== */
/** Remove border when inside `a` element in IE 8/9. */
img { border: 0; }

/** Correct overflow displayed oddly in IE 9. */
svg:not(:root) { overflow: hidden; }

/* ========================================================================== Figures ========================================================================== */
/** Address margin not present in IE 8/9 and Safari 5. */
figure { margin: 0; }

/* ========================================================================== Forms ========================================================================== */
/** Define consistent border, margin, and padding. */
fieldset { border: 1px solid #c0c0c0; margin: 0 2px; padding: 0.35em 0.625em 0.75em; }

/** 1. Correct `color` not being inherited in IE 8/9. 2. Remove padding so people aren't caught out if they zero out fieldsets. */
legend { border: 0; /* 1 */ padding: 0; /* 2 */ }

/** 1. Correct font family not being inherited in all browsers. 2. Correct font size not being inherited in all browsers. 3. Address margins set differently in Firefox 4+, Safari 5, and Chrome. */
button, input, select, textarea { font-family: inherit; /* 1 */ font-size: 100%; /* 2 */ margin: 0; /* 3 */ }

/** Address Firefox 4+ setting `line-height` on `input` using `!important` in the UA stylesheet. */
button, input { line-height: normal; }

/** Address inconsistent `text-transform` inheritance for `button` and `select`. All other form control elements do not inherit `text-transform` values. Correct `button` style inheritance in Chrome, Safari 5+, and IE 8+. Correct `select` style inheritance in Firefox 4+ and Opera. */
button, select { text-transform: none; }

/** 1. Avoid the WebKit bug in Android 4.0.* where (2) destroys native `audio` and `video` controls. 2. Correct inability to style clickable `input` types in iOS. 3. Improve usability and consistency of cursor style between image-type `input` and others. */
button, html input[type="button"], input[type="reset"], input[type="submit"] { -webkit-appearance: button; /* 2 */ cursor: pointer; /* 3 */ }

/** Re-set default cursor for disabled elements. */
button[disabled], html input[disabled] { cursor: default; }

/** 1. Address box sizing set to `content-box` in IE 8/9. 2. Remove excess padding in IE 8/9. */
input[type="checkbox"], input[type="radio"] { box-sizing: border-box; /* 1 */ padding: 0; /* 2 */ }

/** 1. Address `appearance` set to `searchfield` in Safari 5 and Chrome. 2. Address `box-sizing` set to `border-box` in Safari 5 and Chrome (include `-moz` to future-proof). */
input[type="search"] { -webkit-appearance: textfield; /* 1 */ -moz-box-sizing: content-box; -webkit-box-sizing: content-box; /* 2 */ box-sizing: content-box; }

/** Remove inner padding and search cancel button in Safari 5 and Chrome on OS X. */
input[type="search"]::-webkit-search-cancel-button, input[type="search"]::-webkit-search-decoration { -webkit-appearance: none; }

/** Remove inner padding and border in Firefox 4+. */
button::-moz-focus-inner, input::-moz-focus-inner { border: 0; padding: 0; }

/** 1. Remove default vertical scrollbar in IE 8/9. 2. Improve readability and alignment in all browsers. */
textarea { overflow: auto; /* 1 */ vertical-align: top; /* 2 */ }

/* ========================================================================== Tables ========================================================================== */
/** Remove most spacing between table cells. */
table { border-collapse: collapse; border-spacing: 0; }

meta.foundation-mq-small { font-family: "only screen and (min-width: 768px)"; width: 768px; }

meta.foundation-mq-medium { font-family: "only screen and (min-width:1280px)"; width: 1280px; }

meta.foundation-mq-large { font-family: "only screen and (min-width:1440px)"; width: 1440px; }

*, *:before, *:after { -moz-box-sizing: border-box; -webkit-box-sizing: border-box; box-sizing: border-box; }

html, body { font-size: 100%; }

body { background: white; color: rgba(0, 0, 0, 0.8); padding: 0; margin: 0; font-family: "Noto Serif", "DejaVu Serif", serif; font-weight: normal; font-style: normal; line-height: 1; position: relative; cursor: auto; }

a:hover { cursor: pointer; }

img, object, embed { max-width: 100%; height: auto; }

object, embed { height: 100%; }

img { -ms-interpolation-mode: bicubic; }

#map_canvas img, #map_canvas embed, #map_canvas object, .map_canvas img, .map_canvas embed, .map_canvas object { max-width: none !important; }

.left { float: left !important; }

.right { float: right !important; }

.text-left { text-align: left !important; }

.text-right { text-align: right !important; }

.text-center { text-align: center !important; }

.text-justify { text-align: justify !important; }

.hide { display: none; }

.antialiased { -webkit-font-smoothing: antialiased; }

img { display: inline-block; vertical-align: middle; }

textarea { height: auto; min-height: 50px; }

select { width: 100%; }

object, svg { display: inline-block; vertical-align: middle; }

.center { margin-left: auto; margin-right: auto; }

.spread { width: 100%; }

p.lead, .paragraph.lead > p, #preamble > .sectionbody > .paragraph:first-of-type p { font-size: 1.21875em; line-height: 1.6; }

.subheader, .admonitionblock td.content > .title, .audioblock > .title, .exampleblock > .title, .imageblock > .title, .listingblock > .title, .literalblock > .title, .stemblock > .title, .openblock > .title, .paragraph > .title, .quoteblock > .title, table.tableblock > .title, .verseblock > .title, .videoblock > .title, .dlist > .title, .olist > .title, .ulist > .title, .qlist > .title, .hdlist > .title { line-height: 1.45; color: #7a2518; font-weight: normal; margin-top: 0; margin-bottom: 0.25em; }

/* Typography resets */
div, dl, dt, dd, ul, ol, li, h1, h2, h3, #toctitle, .sidebarblock > .content > .title, h4, h5, h6, pre, form, p, blockquote, th, td { margin: 0; padding: 0; direction: ltr; }

/* Default Link Styles */
a { color: #2156a5; text-decoration: underline; line-height: inherit; }
a:hover, a:focus { color: #1d4b8f; }
a img { border: none; }

/* Default paragraph styles */
p { font-family: inherit; font-weight: normal; font-size: 1em; line-height: 1.6; margin-bottom: 1.25em; text-rendering: optimizeLegibility; }
p aside { font-size: 0.875em; line-height: 1.35; font-style: italic; }

/* Default header styles */
h1, h2, h3, #toctitle, .sidebarblock > .content > .title, h4, h5, h6 { font-family: "Open Sans", "DejaVu Sans", sans-serif; font-weight: 300; font-style: normal; color: #ba3925; text-rendering: optimizeLegibility; margin-top: 1em; margin-bottom: 0.5em; line-height: 1.0125em; }
h1 small, h2 small, h3 small, #toctitle small, .sidebarblock > .content > .title small, h4 small, h5 small, h6 small { font-size: 60%; color: #e99b8f; line-height: 0; }

h1 { font-size: 2.125em; }

h2 { font-size: 1.6875em; }

h3, #toctitle, .sidebarblock > .content > .title { font-size: 1.375em; }

h4 { font-size: 1.125em; }

h5 { font-size: 1.125em; }

h6 { font-size: 1em; }

hr { border: solid #ddddd8; border-width: 1px 0 0; clear: both; margin: 1.25em 0 1.1875em; height: 0; }

/* Helpful Typography Defaults */
em, i { font-style: italic; line-height: inherit; }

strong, b { font-weight: bold; line-height: inherit; }

small { font-size: 60%; line-height: inherit; }

code { font-family: "Droid Sans Mono", "DejaVu Sans Mono", monospace; font-weight: normal; color: rgba(0, 0, 0, 0.9); }

a code { color: #021274; }

/* Lists */
ul, ol, dl { font-size: 1em; line-height: 1.6; margin-bottom: 1.25em; list-style-position: outside; font-family: inherit; }

ul, ol { margin-left: 1.5em; }
ul.no-bullet, ol.no-bullet { margin-left: 1.5em; }

/* Unordered Lists */
ul li ul, ul li ol { margin-left: 1.25em; margin-bottom: 0; font-size: 1em; /* Override nested font-size change */ }
ul.square li ul, ul.circle li ul, ul.disc li ul { list-style: inherit; }
ul.square { list-style-type: square; }
ul.circle { list-style-type: circle; }
ul.disc { list-style-type: disc; }
ul.no-bullet { list-style: none; }

/* Ordered Lists */
ol li ul, ol li ol { margin-left: 1.25em; margin-bottom: 0; }

/* Definition Lists */
dl dt { margin-bottom: 0.3125em; font-weight: bold; }
dl dd { margin-bottom: 1.25em; }

/* Abbreviations */
abbr, acronym { text-transform: uppercase; font-size: 90%; color: rgba(0, 0, 0, 0.8); border-bottom: 1px dotted #dddddd; cursor: help; }

abbr { text-transform: none; }

/* Blockquotes */
blockquote { margin: 0 0 1.25em; padding: 0.5625em 1.25em 0 1.1875em; border-left: 1px solid #dddddd; }
blockquote cite { display: block; font-size: 0.9375em; color: rgba(0, 0, 0, 0.6); }
blockquote cite:before { content: "\2014 \0020"; }
blockquote cite a, blockquote cite a:visited { color: rgba(0, 0, 0, 0.6); }

blockquote, blockquote p { line-height: 1.6; color: rgba(0, 0, 0, 0.85); }

/* Microformats */
.vcard { display: inline-block; margin: 0 0 1.25em 0; border: 1px solid #dddddd; padding: 0.625em 0.75em; }
.vcard li { margin: 0; display: block; }
.vcard .fn { font-weight: bold; font-size: 0.9375em; }

.vevent .summary { font-weight: bold; }
.vevent abbr { cursor: auto; text-decoration: none; font-weight: bold; border: none; padding: 0 0.0625em; }

@media only screen and (min-width: 768px) { h1, h2, h3, #toctitle, .sidebarblock > .content > .title, h4, h5, h6 { line-height: 1.2; }
    h1 { font-size: 2.75em; }
    h2 { font-size: 2.3125em; }
    h3, #toctitle, .sidebarblock > .content > .title { font-size: 1.6875em; }
    h4 { font-size: 1.4375em; } }
/* Tables */
table { background: white; margin-bottom: 1.25em; border: solid 1px #dedede; }
table thead, table tfoot { background: #f7f8f7; font-weight: bold; }
table thead tr th, table thead tr td, table tfoot tr th, table tfoot tr td { padding: 0.5em 0.625em 0.625em; font-size: inherit; color: rgba(0, 0, 0, 0.8); text-align: left; }
table tr th, table tr td { padding: 0.5625em 0.625em; font-size: inherit; color: rgba(0, 0, 0, 0.8); }
table tr.even, table tr.alt, table tr:nth-of-type(even) { background: #f8f8f7; }
table thead tr th, table tfoot tr th, table tbody tr td, table tr td, table tfoot tr td { display: table-cell; line-height: 1.6; }

body { tab-size: 4; }

h1, h2, h3, #toctitle, .sidebarblock > .content > .title, h4, h5, h6 { line-height: 1.2; word-spacing: -0.05em; }
h1 strong, h2 strong, h3 strong, #toctitle strong, .sidebarblock > .content > .title strong, h4 strong, h5 strong, h6 strong { font-weight: 400; }

.clearfix:before, .clearfix:after, .float-group:before, .float-group:after { content: " "; display: table; }
.clearfix:after, .float-group:after { clear: both; }

*:not(pre) > code { font-size: 0.9375em; font-style: normal !important; letter-spacing: 0; padding: 0.1em 0.5ex; word-spacing: -0.15em; background-color: #f7f7f8; -webkit-border-radius: 4px; border-radius: 4px; line-height: 1.45; text-rendering: optimizeSpeed; word-wrap: break-word; }
*:not(pre) > code.nobreak { word-wrap: normal; }
*:not(pre) > code.nowrap { white-space: nowrap; }

pre, pre > code { line-height: 1.45; color: rgba(0, 0, 0, 0.9); font-family: "Droid Sans Mono", "DejaVu Sans Mono", "Monospace", monospace; font-weight: normal; text-rendering: optimizeSpeed; }

em em { font-style: normal; }

strong strong { font-weight: normal; }

.keyseq { color: rgba(51, 51, 51, 0.8); }

kbd { font-family: "Droid Sans Mono", "DejaVu Sans Mono", monospace; display: inline-block; color: rgba(0, 0, 0, 0.8); font-size: 0.65em; line-height: 1.45; background-color: #f7f7f7; border: 1px solid #ccc; -webkit-border-radius: 3px; border-radius: 3px; -webkit-box-shadow: 0 1px 0 rgba(0, 0, 0, 0.2), 0 0 0 0.1em white inset; box-shadow: 0 1px 0 rgba(0, 0, 0, 0.2), 0 0 0 0.1em white inset; margin: 0 0.15em; padding: 0.2em 0.5em; vertical-align: middle; position: relative; top: -0.1em; white-space: nowrap; }

.keyseq kbd:first-child { margin-left: 0; }

.keyseq kbd:last-child { margin-right: 0; }

.menuseq, .menu { color: rgba(0, 0, 0, 0.8); }

b.button:before, b.button:after { position: relative; top: -1px; font-weight: normal; }

b.button:before { content: "["; padding: 0 3px 0 2px; }

b.button:after { content: "]"; padding: 0 2px 0 3px; }

p a > code:hover { color: rgba(0, 0, 0, 0.9); }

#header, #content, #footnotes, #footer { width: 100%; margin-left: auto; margin-right: auto; margin-top: 0; margin-bottom: 0; max-width: 62.5em; *zoom: 1; position: relative; padding-left: 0.9375em; padding-right: 0.9375em; }
#header:before, #header:after, #content:before, #content:after, #footnotes:before, #footnotes:after, #footer:before, #footer:after { content: " "; display: table; }
#header:after, #content:after, #footnotes:after, #footer:after { clear: both; }

#content { margin-top: 1.25em; }

#content:before { content: none; }

#header > h1:first-child { color: rgba(0, 0, 0, 0.85); margin-top: 2.25rem; margin-bottom: 0; }
#header > h1:first-child + #toc { margin-top: 8px; border-top: 1px solid #ddddd8; }
#header > h1:only-child, body.toc2 #header > h1:nth-last-child(2) { border-bottom: 1px solid #ddddd8; padding-bottom: 8px; }
#header .details { border-bottom: 1px solid #ddddd8; line-height: 1.45; padding-top: 0.25em; padding-bottom: 0.25em; padding-left: 0.25em; color: rgba(0, 0, 0, 0.6); display: -ms-flexbox; display: -webkit-flex; display: flex; -ms-flex-flow: row wrap; -webkit-flex-flow: row wrap; flex-flow: row wrap; }
#header .details span:first-child { margin-left: -0.125em; }
#header .details span.email a { color: rgba(0, 0, 0, 0.85); }
#header .details br { display: none; }
#header .details br + span:before { content: "\00a0\2013\00a0"; }
#header .details br + span.author:before { content: "\00a0\22c5\00a0"; color: rgba(0, 0, 0, 0.85); }
#header .details br + span#revremark:before { content: "\00a0|\00a0"; }
#header #revnumber { text-transform: capitalize; }
#header #revnumber:after { content: "\00a0"; }

#content > h1:first-child:not([class]) { color: rgba(0, 0, 0, 0.85); border-bottom: 1px solid #ddddd8; padding-bottom: 8px; margin-top: 0; padding-top: 1rem; margin-bottom: 1.25rem; }

#toc { border-bottom: 1px solid #efefed; padding-bottom: 0.5em; }
#toc > ul { margin-left: 0.125em; }
#toc ul.sectlevel0 > li > a { font-style: italic; }
#toc ul.sectlevel0 ul.sectlevel1 { margin: 0.5em 0; }
#toc ul { font-family: "Open Sans", "DejaVu Sans", sans-serif; list-style-type: none; }
#toc li { line-height: 1.3334; margin-top: 0.3334em; }
#toc a { text-decoration: none; }
#toc a:active { text-decoration: underline;}

#toctitle { color: #7a2518; font-size: 1.2em; }

@media only screen and (min-width: 768px) { #toctitle { font-size: 1.375em; }
    body.toc2 { padding-left: 15em; padding-right: 0; }
    #toc.toc2 { margin-top: 0 !important; background-color: #f8f8f7; position: fixed; width: 15em; left: 0; top: 0; border-right: 1px solid #efefed; border-top-width: 0 !important; border-bottom-width: 0 !important; z-index: 1000; padding: 1.25em 1em; height: 100%; overflow: auto; }
    #toc.toc2 #toctitle { margin-top: 0; margin-bottom: 0.8rem; font-size: 1.2em; }
    #toc.toc2 > ul { font-size: 0.9em; margin-bottom: 0; }
    #toc.toc2 ul ul { margin-left: 0; padding-left: 1em; }
    #toc.toc2 ul.sectlevel0 ul.sectlevel1 { padding-left: 0; margin-top: 0.5em; margin-bottom: 0.5em; }
    body.toc2.toc-right { padding-left: 0; padding-right: 15em; }
    body.toc2.toc-right #toc.toc2 { border-right-width: 0; border-left: 1px solid #efefed; left: auto; right: 0; } }
@media only screen and (min-width: 1280px) { body.toc2 { padding-left: 20em; padding-right: 0; }
    #toc.toc2 { width: 20em; }
    #toc.toc2 #toctitle { font-size: 1.375em; }
    #toc.toc2 > ul { font-size: 0.95em; }
    #toc.toc2 ul ul { padding-left: 1.25em; }
    body.toc2.toc-right { padding-left: 0; padding-right: 20em; } }
#content #toc { border-style: solid; border-width: 1px; border-color: #e0e0dc; margin-bottom: 1.25em; padding: 1.25em; background: #f8f8f7; -webkit-border-radius: 4px; border-radius: 4px; }
#content #toc > :first-child { margin-top: 0; }
#content #toc > :last-child { margin-bottom: 0; }

#footer { max-width: 100%; background-color: rgba(0, 0, 0, 0.8); padding: 1.25em; }

#footer-text { color: rgba(255, 255, 255, 0.8); line-height: 1.44; }

.sect1 { padding-bottom: 0.625em; }

@media only screen and (min-width: 768px) { .sect1 { padding-bottom: 1.25em; } }
.sect1 + .sect1 { border-top: 1px solid #efefed; }

#content h1 > a.anchor, h2 > a.anchor, h3 > a.anchor, #toctitle > a.anchor, .sidebarblock > .content > .title > a.anchor, h4 > a.anchor, h5 > a.anchor, h6 > a.anchor { position: absolute; z-index: 1001; width: 1.5ex; margin-left: -1.5ex; display: block; text-decoration: none !important; visibility: hidden; text-align: center; font-weight: normal; }
#content h1 > a.anchor:before, h2 > a.anchor:before, h3 > a.anchor:before, #toctitle > a.anchor:before, .sidebarblock > .content > .title > a.anchor:before, h4 > a.anchor:before, h5 > a.anchor:before, h6 > a.anchor:before { content: "\00A7"; font-size: 0.85em; display: block; padding-top: 0.1em; }
#content h1:hover > a.anchor, #content h1 > a.anchor:hover, h2:hover > a.anchor, h2 > a.anchor:hover, h3:hover > a.anchor, #toctitle:hover > a.anchor, .sidebarblock > .content > .title:hover > a.anchor, h3 > a.anchor:hover, #toctitle > a.anchor:hover, .sidebarblock > .content > .title > a.anchor:hover, h4:hover > a.anchor, h4 > a.anchor:hover, h5:hover > a.anchor, h5 > a.anchor:hover, h6:hover > a.anchor, h6 > a.anchor:hover { visibility: visible; }
#content h1 > a.link, h2 > a.link, h3 > a.link, #toctitle > a.link, .sidebarblock > .content > .title > a.link, h4 > a.link, h5 > a.link, h6 > a.link { color: #ba3925; text-decoration: none; }
#content h1 > a.link:hover, h2 > a.link:hover, h3 > a.link:hover, #toctitle > a.link:hover, .sidebarblock > .content > .title > a.link:hover, h4 > a.link:hover, h5 > a.link:hover, h6 > a.link:hover { color: #a53221; }

.audioblock, .imageblock, .literalblock, .listingblock, .stemblock, .videoblock { margin-bottom: 1.25em; }

.admonitionblock td.content > .title, .audioblock > .title, .exampleblock > .title, .imageblock > .title, .listingblock > .title, .literalblock > .title, .stemblock > .title, .openblock > .title, .paragraph > .title, .quoteblock > .title, table.tableblock > .title, .verseblock > .title, .videoblock > .title, .dlist > .title, .olist > .title, .ulist > .title, .qlist > .title, .hdlist > .title { text-rendering: optimizeLegibility; text-align: left; font-family: "Noto Serif", "DejaVu Serif", serif; font-size: 1rem; font-style: italic; }

table.tableblock > caption.title { white-space: nowrap; overflow: visible; max-width: 0; }

.paragraph.lead > p, #preamble > .sectionbody > .paragraph:first-of-type p { color: rgba(0, 0, 0, 0.85); }

table.tableblock #preamble > .sectionbody > .paragraph:first-of-type p { font-size: inherit; }

/* Custom Admonition Blocks - Icons from https://github.com/primer/octicons */
.admonitionblock {
    margin: 1.4rem 0 0
}

.admonitionblock i {
    font-family: inherit;
}

.admonitionblock i.fa {
    background: no-repeat 50%/1em 1em;
    display: inline-block;
    filter: var(--doc-icon-filter);
    font-style: normal;
    height: 1em;
    -webkit-hyphens: none;
    hyphens: none;
    vertical-align: -.125em;
    width: 1em
}

.admonitionblock p,
.admonitionblock td.content {
    font-size: 1rem;
}

.admonitionblock td.content>.title+*,
.admonitionblock td.content>:not(.title):first-child {
    margin-top: 0
}

.admonitionblock pre {
    font-size: calc(15/var(--rem-base)*1rem)
}

.admonitionblock > table {
    position: relative;
    table-layout: fixed;
    border: none;
    width: 100%
}

.admonitionblock td.content {
    word-wrap: anywhere;
    background: var(--admonition-background);
    padding: 1rem 1rem 1rem;
    width: 100%;
    border-radius: 4px;
}

.admonitionblock td.icon {
    background: linear-gradient(90deg,rgba(0,0,0,.2) 0,rgba(0,0,0,.2)) no-repeat 0 /2.075em 100%;
    border-radius: .5em;
    font-size: calc(15/var(--rem-base)*1rem);
    left: 0;
    line-height: 1;
    padding: .25em .075em;
    position: absolute;
    top: 0;
    transform: translate(-.5rem,-50%)
}

.admonitionblock td.icon i {
    align-items: center;
    background-position-x: .5em;
    display: inline-flex;
    filter: invert(100%);
    padding-left: 2em;
    vertical-align: initial;
    width: auto
}

.admonitionblock td.icon i::after {
    content: attr(title);
    filter: invert(100%);
    font-style: normal;
    font-weight: bold;
    margin: -.05em;
    padding: 0 .5em;
    text-transform: uppercase
}

.admonitionblock.caution td.icon {
    background-color: var(--caution-color);
    color: var(--caution-on-color)
}

.admonitionblock.caution td.icon i {
    background-image: url(./img/octicons-16.svg#view-flame)
}

.admonitionblock.important td.icon {
    background-color: var(--important-color);
    color: var(--important-on-color)
}

.admonitionblock.important td.icon i {
    background-image: url(./img/octicons-16.svg#view-stop)
}

.admonitionblock.note td.icon {
    background-color: var(--note-color);
    color: var(--note-on-color)
}

.admonitionblock.note td.icon i {
    background-image: url(./img/octicons-16.svg#view-info)
}

.admonitionblock.tip td.icon {
    background-color: var(--tip-color);
    color: var(--tip-on-color)
}

.admonitionblock.tip td.icon i {
    background-image: url(./img/octicons-16.svg#view-light-bulb)
}

.admonitionblock.warning td.icon {
    background-color: var(--warning-color);
    color: var(--warning-on-color)
}

.admonitionblock.warning td.icon i {
    background-image: url(./img/octicons-16.svg#view-alert)
}

/* Custom collapsible block */
details summary {
  width: 100%;
  padding: 1rem 0;
  border-top: 1px solid gray;
  position: relative;
  cursor: pointer;
  list-style: none;
}

details summary:after {
  content: "+";
  color: black;
  position: absolute;
  font-size: 1.75rem;
  line-height: 0;
  margin-top: 0.3rem;
  right: 0;
  font-weight: 400;
  transform-origin: center;
  transition: 200ms linear;
}

details[open] summary:after {
  transform: rotate(45deg);
  font-size: 2rem;
}

details summary {
  outline: 0;
}

details p {
  font-size: 0.95rem;
  margin: 0 0 1rem;
  padding-top: 1rem;
}

.exampleblock > .content { border-style: solid; border-width: 1px; border-color: #e6e6e6; margin-bottom: 1.25em; padding: 1.25em; background: white; -webkit-border-radius: 4px; border-radius: 4px; }
.exampleblock > .content > :first-child { margin-top: 0; }
.exampleblock > .content > :last-child { margin-bottom: 0; }

.sidebarblock { border-style: solid; border-width: 1px; border-color: #e0e0dc; margin-bottom: 1.25em; padding: 1.25em; background: #f8f8f7; -webkit-border-radius: 4px; border-radius: 4px; }
.sidebarblock > :first-child { margin-top: 0; }
.sidebarblock > :last-child { margin-bottom: 0; }
.sidebarblock > .content > .title { color: #7a2518; margin-top: 0; text-align: center; }

.exampleblock > .content > :last-child > :last-child, .exampleblock > .content .olist > ol > li:last-child > :last-child, .exampleblock > .content .ulist > ul > li:last-child > :last-child, .exampleblock > .content .qlist > ol > li:last-child > :last-child, .sidebarblock > .content > :last-child > :last-child, .sidebarblock > .content .olist > ol > li:last-child > :last-child, .sidebarblock > .content .ulist > ul > li:last-child > :last-child, .sidebarblock > .content .qlist > ol > li:last-child > :last-child { margin-bottom: 0; }

.literalblock pre, .listingblock pre:not(.highlight), .listingblock pre[class="highlight"], .listingblock pre[class^="highlight "], .listingblock pre.CodeRay, .listingblock pre.prettyprint { background: #f7f7f8; }
.sidebarblock .literalblock pre, .sidebarblock .listingblock pre:not(.highlight), .sidebarblock .listingblock pre[class="highlight"], .sidebarblock .listingblock pre[class^="highlight "], .sidebarblock .listingblock pre.CodeRay, .sidebarblock .listingblock pre.prettyprint { background: #f2f1f1; }

.literalblock pre, .literalblock pre[class], .listingblock pre, .listingblock pre[class] { -webkit-border-radius: 4px; border-radius: 4px; word-wrap: break-word; padding: 1em; font-size: 0.8125em; }
.literalblock pre.nowrap, .literalblock pre[class].nowrap, .listingblock pre.nowrap, .listingblock pre[class].nowrap { overflow-x: auto; white-space: pre; word-wrap: normal; }
@media only screen and (min-width: 768px) { .literalblock pre, .literalblock pre[class], .listingblock pre, .listingblock pre[class] { font-size: 0.90625em; } }
@media only screen and (min-width: 1280px) { .literalblock pre, .literalblock pre[class], .listingblock pre, .listingblock pre[class] { font-size: 1em; } }

.literalblock.output pre { color: #f7f7f8; background-color: rgba(0, 0, 0, 0.9); }

.listingblock pre.highlightjs { padding: 0; }
.listingblock pre.highlightjs > code { padding: 1em; -webkit-border-radius: 4px; border-radius: 4px; }

.listingblock pre.prettyprint { border-width: 0; }

.listingblock > .content { position: relative; }

.listingblock code[data-lang]:before { display: none; content: attr(data-lang); position: absolute; font-size: 0.75em; top: 0.425rem; right: 0.5rem; line-height: 1; text-transform: uppercase; color: #999; }

.listingblock:hover code[data-lang]:before { display: block; }

.listingblock.terminal pre .command:before { content: attr(data-prompt); padding-right: 0.5em; color: #999; }

.listingblock.terminal pre .command:not([data-prompt]):before { content: "$"; }

table.pyhltable { border-collapse: separate; border: 0; margin-bottom: 0; background: none; }

table.pyhltable td { vertical-align: top; padding-top: 0; padding-bottom: 0; line-height: 1.45; }

table.pyhltable td.code { padding-left: .75em; padding-right: 0; }

pre.pygments .lineno, table.pyhltable td:not(.code) { color: #999; padding-left: 0; padding-right: .5em; border-right: 1px solid #ddddd8; }

pre.pygments .lineno { display: inline-block; margin-right: .25em; }

table.pyhltable .linenodiv { background: none !important; padding-right: 0 !important; }

.quoteblock { margin: 0 1em 1.25em 1.5em; display: table; }
.quoteblock > .title { margin-left: -1.5em; margin-bottom: 0.75em; }
.quoteblock blockquote, .quoteblock blockquote p { color: rgba(0, 0, 0, 0.85); font-size: 1.15rem; line-height: 1.75; word-spacing: 0.1em; letter-spacing: 0; font-style: italic; text-align: justify; }
.quoteblock blockquote { margin: 0; padding: 0; border: 0; }
.quoteblock blockquote:before { content: "\201c"; float: left; font-size: 2.75em; font-weight: bold; line-height: 0.6em; margin-left: -0.6em; color: #7a2518; text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1); }
.quoteblock blockquote > .paragraph:last-child p { margin-bottom: 0; }
.quoteblock .attribution { margin-top: 0.5em; margin-right: 0.5ex; text-align: right; }
.quoteblock .quoteblock { margin-left: 0; margin-right: 0; padding: 0.5em 0; border-left: 3px solid rgba(0, 0, 0, 0.6); }
.quoteblock .quoteblock blockquote { padding: 0 0 0 0.75em; }
.quoteblock .quoteblock blockquote:before { display: none; }

.verseblock { margin: 0 1em 1.25em 1em; }
.verseblock pre { font-family: "Open Sans", "DejaVu Sans", sans; font-size: 1.15rem; color: rgba(0, 0, 0, 0.85); font-weight: 300; text-rendering: optimizeLegibility; }
.verseblock pre strong { font-weight: 400; }
.verseblock .attribution { margin-top: 1.25rem; margin-left: 0.5ex; }

.quoteblock .attribution, .verseblock .attribution { font-size: 0.9375em; line-height: 1.45; font-style: italic; }
.quoteblock .attribution br, .verseblock .attribution br { display: none; }
.quoteblock .attribution cite, .verseblock .attribution cite { display: block; letter-spacing: -0.025em; color: rgba(0, 0, 0, 0.6); }

.quoteblock.abstract { margin: 0 0 1.25em 0; display: block; }
.quoteblock.abstract blockquote, .quoteblock.abstract blockquote p { text-align: left; word-spacing: 0; }
.quoteblock.abstract blockquote:before, .quoteblock.abstract blockquote p:first-of-type:before { display: none; }

table.tableblock { max-width: 100%; border-collapse: separate; }
table.tableblock td > .paragraph:last-child p > p:last-child, table.tableblock th > p:last-child, table.tableblock td > p:last-child { margin-bottom: 0; }

table.tableblock, th.tableblock, td.tableblock { border: 0 solid #dedede; }

table.grid-all th.tableblock, table.grid-all td.tableblock { border-width: 0 1px 1px 0; }

table.grid-all tfoot > tr > th.tableblock, table.grid-all tfoot > tr > td.tableblock { border-width: 1px 1px 0 0; }

table.grid-cols th.tableblock, table.grid-cols td.tableblock { border-width: 0 1px 0 0; }

table.grid-all * > tr > .tableblock:last-child, table.grid-cols * > tr > .tableblock:last-child { border-right-width: 0; }

table.grid-rows th.tableblock, table.grid-rows td.tableblock { border-width: 0 0 1px 0; }

table.grid-all tbody > tr:last-child > th.tableblock, table.grid-all tbody > tr:last-child > td.tableblock, table.grid-all thead:last-child > tr > th.tableblock, table.grid-rows tbody > tr:last-child > th.tableblock, table.grid-rows tbody > tr:last-child > td.tableblock, table.grid-rows thead:last-child > tr > th.tableblock { border-bottom-width: 0; }

table.grid-rows tfoot > tr > th.tableblock, table.grid-rows tfoot > tr > td.tableblock { border-width: 1px 0 0 0; }

table.frame-all { border-width: 1px; }

table.frame-sides { border-width: 0 1px; }

table.frame-topbot { border-width: 1px 0; }

th.halign-left, td.halign-left { text-align: left; }

th.halign-right, td.halign-right { text-align: right; }

th.halign-center, td.halign-center { text-align: center; }

th.valign-top, td.valign-top { vertical-align: top; }

th.valign-bottom, td.valign-bottom { vertical-align: bottom; }

th.valign-middle, td.valign-middle { vertical-align: middle; }

table thead th, table tfoot th { font-weight: bold; }

tbody tr th { display: table-cell; line-height: 1.6; background: #f7f8f7; }

tbody tr th, tbody tr th p, tfoot tr th, tfoot tr th p { color: rgba(0, 0, 0, 0.8); font-weight: bold; }

p.tableblock > code:only-child { background: none; padding: 0; }

p.tableblock { font-size: 1em; }

td > div.verse { white-space: pre; }

ol { margin-left: 1.75em; }

ul li ol { margin-left: 1.5em; }

dl dd { margin-left: 1.125em; }

dl dd:last-child, dl dd:last-child > :last-child { margin-bottom: 0; }

ol > li p, ul > li p, ul dd, ol dd, .olist .olist, .ulist .ulist, .ulist .olist, .olist .ulist { margin-bottom: 0.625em; }

ul.unstyled, ol.unnumbered, ul.checklist, ul.none { list-style-type: none; }

ul.unstyled, ol.unnumbered, ul.checklist { margin-left: 0.625em; }

ul.checklist li > p:first-child > .fa-square-o:first-child, ul.checklist li > p:first-child > .fa-check-square-o:first-child { width: 1em; font-size: 0.85em; }

ul.checklist li > p:first-child > input[type="checkbox"]:first-child { width: 1em; position: relative; top: 1px; }

ul.inline { margin: 0 auto 0.625em auto; margin-left: -1.375em; margin-right: 0; padding: 0; list-style: none; overflow: hidden; }
ul.inline > li { list-style: none; float: left; margin-left: 1.375em; display: block; }
ul.inline > li > * { display: block; }

.unstyled dl dt { font-weight: normal; font-style: normal; }

ol.arabic { list-style-type: decimal; }

ol.decimal { list-style-type: decimal-leading-zero; }

ol.loweralpha { list-style-type: lower-alpha; }

ol.upperalpha { list-style-type: upper-alpha; }

ol.lowerroman { list-style-type: lower-roman; }

ol.upperroman { list-style-type: upper-roman; }

ol.lowergreek { list-style-type: lower-greek; }

.hdlist > table, .colist > table { border: 0; background: none; }
.hdlist > table > tbody > tr, .colist > table > tbody > tr { background: none; }

td.hdlist1, td.hdlist2 { vertical-align: top; padding: 0 0.625em; }

td.hdlist1 { font-weight: bold; padding-bottom: 1.25em; }

.literalblock + .colist { margin-top: -0.5em; }

.colist > table tr > td:first-of-type { padding: 0 0.75em; line-height: 1; }
.colist > table tr > td:first-of-type img { max-width: initial; }
.colist > table tr > td:last-of-type { padding: 0.25em 0; }

.thumb, .th { line-height: 0; display: inline-block; border: solid 4px white; -webkit-box-shadow: 0 0 0 1px #dddddd; box-shadow: 0 0 0 1px #dddddd; }

.imageblock.left, .imageblock[style*="float: left"] { margin: 0.25em 0.625em 1.25em 0; }
.imageblock.right, .imageblock[style*="float: right"] { margin: 0.25em 0 1.25em 0.625em; }
.imageblock > .title { margin-bottom: 0; }
.imageblock.thumb, .imageblock.th { border-width: 6px; }
.imageblock.thumb > .title, .imageblock.th > .title { padding: 0 0.125em; }

.image.left, .image.right { margin-top: 0.25em; margin-bottom: 0.25em; display: inline-block; line-height: 0; }
.image.left { margin-right: 0.625em; }
.image.right { margin-left: 0.625em; }

a.image { text-decoration: none; display: inline-block; }
a.image object { pointer-events: none; }

sup.footnote, sup.footnoteref { font-size: 0.875em; position: static; vertical-align: super; }
sup.footnote a, sup.footnoteref a { text-decoration: none; }
sup.footnote a:active, sup.footnoteref a:active { text-decoration: underline; }

#footnotes { padding-top: 0.75em; padding-bottom: 0.75em; margin-bottom: 0.625em; }
#footnotes hr { width: 20%; min-width: 6.25em; margin: -0.25em 0 0.75em 0; border-width: 1px 0 0 0; }
#footnotes .footnote { padding: 0 0.375em 0 0.225em; line-height: 1.3334; font-size: 0.875em; margin-left: 1.2em; text-indent: -1.05em; margin-bottom: 0.2em; }
#footnotes .footnote a:first-of-type { font-weight: bold; text-decoration: none; }
#footnotes .footnote:last-of-type { margin-bottom: 0; }
#content #footnotes { margin-top: -0.625em; margin-bottom: 0; padding: 0.75em 0; }

.gist .file-data > table { border: 0; background: #fff; width: 100%; margin-bottom: 0; }
.gist .file-data > table td.line-data { width: 99%; }

div.unbreakable { page-break-inside: avoid; }

.big { font-size: larger; }

.small { font-size: smaller; }

.underline { text-decoration: underline; }

.overline { text-decoration: overline; }

.line-through { text-decoration: line-through; }

.aqua { color: #00bfbf; }

.aqua-background { background-color: #00fafa; }

.black { color: black; }

.black-background { background-color: black; }

.blue { color: #0000bf; }

.blue-background { background-color: #0000fa; }

.fuchsia { color: #bf00bf; }

.fuchsia-background { background-color: #fa00fa; }

.gray { color: #606060; }

.gray-background { background-color: #7d7d7d; }

.green { color: #006000; }

.green-background { background-color: #007d00; }

.lime { color: #00bf00; }

.lime-background { background-color: #00fa00; }

.maroon { color: #600000; }

.maroon-background { background-color: #7d0000; }

.navy { color: #000060; }

.navy-background { background-color: #00007d; }

.olive { color: #606000; }

.olive-background { background-color: #7d7d00; }

.purple { color: #600060; }

.purple-background { background-color: #7d007d; }

.red { color: #bf0000; }

.red-background { background-color: #fa0000; }

.silver { color: #909090; }

.silver-background { background-color: #bcbcbc; }

.teal { color: #006060; }

.teal-background { background-color: #007d7d; }

.white { color: #bfbfbf; }

.white-background { background-color: #fafafa; }

.yellow { color: #bfbf00; }

.yellow-background { background-color: #fafa00; }

span.icon > .fa { cursor: default; }

.conum[data-value] { display: inline-block; color: #fff !important; background-color: rgba(0, 0, 0, 0.8); -webkit-border-radius: 100px; border-radius: 100px; text-align: center; font-size: 0.75em; width: 1.67em; height: 1.67em; line-height: 1.67em; font-family: "Open Sans", "DejaVu Sans", sans-serif; font-style: normal; font-weight: bold; }
.conum[data-value] * { color: #fff !important; }
.conum[data-value] + b { display: none; }
.conum[data-value]:after { content: attr(data-value); }
pre .conum[data-value] { position: relative; top: -0.125em; }

b.conum * { color: inherit !important; }

.conum:not([data-value]):empty { display: none; }

dt, th.tableblock, td.content, div.footnote { text-rendering: optimizeLegibility; }

h1, h2, p, td.content, span.alt { letter-spacing: -0.01em; }

p strong, td.content strong, div.footnote strong { letter-spacing: -0.005em; }

p, blockquote, dt, td.content, span.alt { font-size: 1.0625rem; }

p { margin-bottom: 1.25rem; }

.sidebarblock p, .sidebarblock dt, .sidebarblock td.content, p.tableblock { font-size: 1em; }

.exampleblock > .content { background-color: #fffef7; border-color: #e0e0dc; -webkit-box-shadow: 0 1px 4px #e0e0dc; box-shadow: 0 1px 4px #e0e0dc; }

.print-only { display: none !important; }

@media print { @page { margin: 1.25cm 0.75cm; }
    * { -webkit-box-shadow: none !important; box-shadow: none !important; text-shadow: none !important; }
    a { color: inherit !important; text-decoration: underline !important; }
    a.bare, a[href^="#"], a[href^="mailto:"] { text-decoration: none !important; }
    a[href^="http:"]:not(.bare):after, a[href^="https:"]:not(.bare):after { content: "(" attr(href) ")"; display: inline-block; font-size: 0.875em; padding-left: 0.25em; }
    abbr[title]:after { content: " (" attr(title) ")"; }
    pre, blockquote, tr, img, object, svg { page-break-inside: avoid; }
    thead { display: table-header-group; }
    svg { max-width: 100%; }
    p, blockquote, dt, td.content { font-size: 1em; orphans: 3; widows: 3; }
    h2, h3, #toctitle, .sidebarblock > .content > .title, #toctitle, .sidebarblock > .content > .title { page-break-after: avoid; }
    #toc, .sidebarblock, .exampleblock > .content { background: none !important; }
    #toc { border-bottom: 1px solid #ddddd8 !important; padding-bottom: 0 !important; }
    .sect1 { padding-bottom: 0 !important; }
    .sect1 + .sect1 { border: 0 !important; }
    #header > h1:first-child { margin-top: 1.25rem; }
    body.book #header { text-align: center; }
    body.book #header > h1:first-child { border: 0 !important; margin: 2.5em 0 1em 0; }
    body.book #header .details { border: 0 !important; display: block; padding: 0 !important; }
    body.book #header .details span:first-child { margin-left: 0 !important; }
    body.book #header .details br { display: block; }
    body.book #header .details br + span:before { content: none !important; }
    body.book #toc { border: 0 !important; text-align: left !important; padding: 0 !important; margin: 0 !important; }
    body.book #toc, body.book #preamble, body.book h1.sect0, body.book .sect1 > h2 { page-break-before: always; }
    .listingblock code[data-lang]:before { display: block; }
    #footer { background: none !important; padding: 0 0.9375em; }
    #footer-text { color: rgba(0, 0, 0, 0.6) !important; font-size: 0.9em; }
    .hide-on-print { display: none !important; }
    .print-only { display: block !important; }
    .hide-for-print { display: none !important; }
    .show-for-print { display: inherit !important; } }

/* END asciidoc.css */

html,
body {
    margin: 0;
    padding: 0;
}

html {
    font-size: 16px;
    font-weight: 400;
    line-height: 1.5;
}

body {
    color: #02303A;
    background-color: #fff;
    font-family: "Lato", "Helvetica Neue", Arial, sans-serif;
    line-height: 1.5;
    -webkit-text-size-adjust: 100%;
    -ms-text-size-adjust: 100%;
}

/* Links */
a {
    color: #1DA2BD;
    text-decoration: none;
}

a:hover,
a:focus {
    text-decoration: underline;
}

#content a[href^='../dsl/'],
#content a[href^='../kotlin-dsl/'],
#content a[href^='../javadoc/'] {
    font-family: 'Inconsolata', monospace;
    font-style: normal;
    border-bottom: 1px dotted rgba(29, 162, 189, 0.5);
    padding: 0 1px;
}

#content a[href^='../dsl/']:hover,
#content a[href^='../dsl/']:focus,
#content a[href^='../kotlin-dsl/']:hover,
#content a[href^='../kotlin-dsl/']:focus,
#content a[href^='../javadoc/']:hover,
#content a[href^='../javadoc/']:focus {
    text-decoration: none;
}

/* Copy */

p {
    font-size: 1rem;
}

pre,
pre > code,
code {
    font-family: 'Inconsolata', monospace;
}

h1,
h2,
h3,
h4,
h5,
h6,
#toctitle,
.sidebarblock > .content > .title {
    font-family: inherit;
    font-weight: 500;
    color: inherit;
}

h1 {
    font-size: 2rem;
}

h2 {
    font-size: 1.5rem;
}

h3 {
    font-size: 1.125rem;
}

h4 {
    font-size: 1.0625rem;
}

h5, h6 {
    font-size: 1rem;
}

b, strong {
    font-weight: 500;
}

dl {
    margin: 0 0 1.25rem 1.5rem;
}

.dlist dt code {
    color: #02303A;
    font-size: 1em;
    font-weight: bold;
}

.dlist p {
    margin-bottom: 0.625rem;
}

.sr-only {
    border: 0;
    clip: rect(0, 0, 0, 0);
    height: 1px;
    margin: -1px;
    overflow: hidden;
    padding: 0;
    position: absolute;
    width: 1px;
}

/* Layout */
.main-content > .appendix,
.main-content > .book,
.main-content > .chapter,
.main-content > .footer {
    background-color: white;
    border-radius: 5px;
    max-width: 45rem;
    padding: 1.5rem;
}

@media screen and (max-width: 45em) {
    .footer {
        max-width: 100%;
    }

    .main-content > .appendix,
    .main-content > .book,
    .main-content > .chapter {
        margin-top: 0;
        margin-bottom: 0;
    }
}

/* Override asciidoc styles */
#header {
    position: static;
}

#header, #content {
    padding: 0;
}

#header .details {
    /* TODO: Pretty sure there's a way to avoid Asciidoc generating details */
    display: none;
}

p {
    color: #02303A;
}

h1,
h2,
p,
p strong,
td.content,
td.content strong,
div.footnote strong,
span.alt {
    letter-spacing: normal;
}

.subheader,
.admonitionblock td.content > .title,
.audioblock > .title,
.exampleblock > .title,
.imageblock > .title,
.listingblock > .title,
.literalblock > .title,
.stemblock > .title,
.openblock > .title,
.paragraph > .title,
.quoteblock > .title,
table.tableblock > .title,
.verseblock > .title,
.videoblock > .title,
.dlist > .title,
.olist > .title,
.ulist > .title,
.qlist > .title,
.hdlist > .title {
    color: inherit;
    font-family: inherit;
}

.listingblock .title,
.listingblock .title code {
    font-style: normal;
    font-weight: bold;
}

.imageblock,
.videoblock {
    padding: 0.25em;
}

p.lead,
.paragraph.lead > p,
#preamble > .sectionbody > .paragraph:first-of-type p {
    font-size: 1.0625rem;
}

.paragraph.lead > p,
#preamble > .sectionbody > .paragraph:first-of-type p {
    color: inherit;
}

.sect1 {
    padding-bottom: 0;
}

.sect1 + .sect1 {
    border: 0 none;
}

.verseblock pre {
    font-family: "Lato", Arial, sans-serif;
}

td.hdlist1 {
    padding-bottom: 0.625rem;
}

td.hdlist2 p {
    margin-bottom: 0.625rem;
}

body.book #header > h1 {
    border: 0;
}

#header > h1:first-child {
    margin-top: 0;
}

#content a.link {
    color: #02303A;
}

.highlight .com {
    color: #777;
}

.listingblock pre.highlightjs > code {
    overflow-x: auto;
}

.listingblock pre.highlight {
    overflow-x: auto;
}

.listingblock pre.highlight > code {
    white-space: pre;
}

.conum[data-value] {
    font-family: "Lato", Arial, sans-serif;
}

.colist > table tr > td:first-of-type {
    padding-top: 0.25em;
    padding-bottom: 0.25em;
    line-height: 1.4;
    vertical-align: baseline;
}

/*
 * Samples
 */
.exampleblock > .content {
    background-color: inherit;
    border: 0 none;
    box-shadow: none;
    padding: 0;
    padding-bottom: 0.7rem;
    margin-bottom: 0;
}

.exampleblock > .content .title {
    background-color: #f7f7f8;
    border-top: 1px solid #ccc;
    font-family: 'Inconsolata', monospace;
    margin: 0;
    padding: 1em 1em 0;
}

.exampleblock > .title > a {
    text-decoration: none;
    color: rgba(0, 0, 0, 0.8);
}

.exampleblock .listingblock {
    margin: 0;
}

/*
 * Ensure that blocks of code do not wrap by applying the behavior of `[listing%nowrap]` by default.
 *
 * These styles are copied from a CSS ruleset in asciidoctor.css that has the same group of
 * selectors except that they end with `.nowrap`.
 */
.literalblock pre,
.literalblock pre[class],
.listingblock pre,
.listingblock pre[class] {
    overflow-x: auto;
    white-space: pre;
    word-wrap: normal;
}

/*
 * This CSS ruleset solves: https://github.com/gradle/guides/issues/113#issuecomment-314826749.
 */
.literalblock pre::after,
.literalblock pre[class]::after,
.listingblock pre::after,
.listingblock pre[class]::after {
    content: "";
}

.quoteblock blockquote,
.quoteblock blockquote p {
    text-align: left;
    text-align: start;
}

div.screenshot {
    box-shadow: 0 0 20px 1px rgba(0, 0, 0, 0.2);
    margin-left: auto;
    margin-right: auto;
    width: 90%;
}

.inset {
    box-shadow: 0 0 1px 1px rgba(0, 0, 0, 0.1);
    padding: 1em;
}

.image.inline-icon img {
    vertical-align: sub;
}

/* TOC */
#header > h1:first-child + #toc {
    background: none;
    border: 0 none;
    margin-top: 0;
}

#toc,
#content #toc {
    border: 0 none;
}

#toc > ul {
    margin-left: 0;
    font-family: inherit;
}

#toc > ul > li {
    line-height: 1.25;
    margin-top: 0;
    padding-bottom: 0.5rem;
}

#toc > ul > li:last-of-type {
    padding-bottom: 0;
}

#toc a {
    font-style: normal;
}

#toc a:hover,
#toc a:focus,
#toc a:hover code,
#toc a:focus code {
    color: #1DA2BD;
}

#toc a:active {
    text-decoration: none;
}

/* Site header specific styles */
.hamburger {
    background-color: transparent;
    background-image: none;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    margin-left: auto;
    padding: 11px 10px;
}

.hamburger:focus {
    outline: 0;
}

.hamburger__bar {
    display: block;
    width: 22px;
    height: 2px;
    background-color: black;
    border-radius: 1px;
}

.hamburger__bar + .hamburger__bar {
    margin-top: 4px;
}

.site-header {
    background-color: white;
}

/* Override javadoc styles */
.site-header div {
    font-family: 'Lato', Arial, sans-serif;
}

.site-header__navigation-header a {
    align-self: center;
    border-bottom: 0 none;
    height: 36px;
}

.site-header .site-header-version {
    align-self: center;
    color: #1da2bd;
    font-size: 20px;
    padding-left: 1px;
    margin-top: 22px;
}

.site-header__navigation {
    display: flex;
    flex-direction: column;
}

.site-header__navigation-header {
    display: flex;
    flex: 0 0 auto;
    margin-left: 12px;
}

.site-header__navigation-collapsible {
    flex: 1 1 auto;
    height: 210px;
    overflow: visible;
    transition: height 0.3s ease;
}

.site-header__navigation-items {
    display: flex;
    flex-direction: column;
    flex-wrap: wrap;
    align-items: flex-start;
    max-height: 210px; /* This matches the collapsible height above */
    margin: 0 20px;
    padding-top: 12px;
    padding-left: 0;
    list-style-type: none;
}

.site-header__navigation-item {
    flex: 0 1 auto;
    font-size: 16px;
    width: 250px;
}

.site-header__navigation-item .site-header__navigation-link {
    position: relative;
    display: inline-block;
    cursor: pointer;
    width: 100%;
    padding: 5px;
    line-height: 20px;
    border: 0 none;
    color: #02303A;
    text-decoration: none;
    transition: none;
    -o-transition: none;
    -moz-transition: none;
    -webkit-transition: none;
}

.site-header__navigation-item .site-header__navigation-link:hover {
    color: #1DA2BD;
}

.site-header__navigation-item .site-header__navigation-link.active {
    font-weight: 500;
}

/* Navigation submenu styles */
.site-header__navigation-submenu-section {
    position: relative;
}

.site-header__navigation-submenu-section .site-header__down-arrow {
    width: 8px;
    height: 8px;
    margin-left: 2px;
    margin-top: 0;
}

.site-header__navigation-submenu-section .site-header__navigation-link:hover path {
    fill: none;
}

.site-header__navigation-submenu-section .site-header__navigation-submenu .site-header__navigation-submenu-item-link:hover {
    color: #1DA2BD;
}

.site-header__navigation-submenu-section .site-header__navigation-submenu {
    display: none;
    width: 170px;
    background-color: white;
    top: 40px;
    left: 7px; /* NOTE: This must match the padding of .site-header__navigation-link */
    padding: 3px 10px 6px 10px;
    z-index: 100;
}

.site-header__navigation-submenu-section .site-header__navigation-submenu .site-header__navigation-submenu-item-link {
    width: 100%;
    color: #02303A;
    white-space: nowrap;
    display: inline-block;
    padding-top: 3px;
    border: 0 none;
    transition: none;
    -o-transition: none;
    -moz-transition: none;
    -webkit-transition: none;
}

.site-header__navigation-submenu-section .site-header__navigation-submenu .site-header__navigation-submenu-item-link .site-header__navigation-submenu-item-link-text {
    display: inline-block;
    font-size: 16px;
}

.site-header__navigation-submenu-section.open .site-header__navigation-submenu {
    display: block;
}

/* Top navigation mobile styles */
@media (max-width: 1023px) {
    .site-header__navigation-collapsible--collapse {
        height: 0;
        overflow-y: hidden;
    }

    .site-header__navigation-submenu-section .site-header__navigation-submenu {
        padding: 0 1rem 0.5rem 1.5rem;
        display: block;
        top: 30px !important;
        left: 0 !important;
    }

    .site-header__navigation-item,
    .site-header__navigation-submenu-section .site-header__navigation-submenu .site-header__navigation-submenu-item-link .site-header__navigation-submenu-item-link-text {
        font-size: 18px;
    }

    .site-header {
        padding: 5px 12px;
    }

    .site-header-version {
        display: none;
    }

    .site-footer__navigation {
        flex-direction: column;
    }

    .site-footer__links {
        flex-wrap: wrap;
    }

    .site-footer__link-group {
        margin-bottom: 1rem;
    }
}

/* Top navigation desktop styles */
@media (min-width: 1024px) {
    .site-header {
        -webkit-box-shadow: 0 2px 4px 0 rgba(0, 0, 0, .15);
        -moz-box-shadow: 0 2px 4px 0 rgba(0, 0, 0, .15);
        box-shadow: 0 2px 4px 0 rgba(0, 0, 0, .15);
        z-index: 1;
    }

    /*
      Pushes the section headings to just below the top nav bar when a user
      navigates directly to section anchors.
     */
    #content h2[id], #content h3[id], #content h4[id], #content h5[id] {
        padding-top: 60px;
    }

    #content h2[id] {
        /* Little extra room above h2s */
        margin-top: -1em;
    }

    #content h3[id], #content h4[id], #content h5[id] {
        margin-top: -60px;
    }

    .site-header__navigation {
        flex-direction: row;
    }

    .site-header__navigation-button {
        display: none;
    }

    .site-header__navigation-items {
        flex-direction: row;
        align-items: center;
        float: right;
        width: auto;
        padding-top: 0;
    }

    .site-header__navigation-item {
        width: auto;
    }

    .site-header__navigation-item .site-header__navigation-link {
        padding: 15px 18px;
    }

    .site-header__navigation-item:last-of-type .site-header__navigation-link {
        padding-right: 0;
    }

    .site-header__navigation-link--button {
        padding: 6px 12px;
    }

    .site-header__navigation-collapsible {
        height: auto;
    }

    .site-header__navigation-submenu-section .site-header__navigation-submenu {
        position: absolute;
        border: 1px solid #9a9a9a;
        border-radius: 3px;
    }

    .site-header__navigation-submenu-section:hover .site-header__navigation-submenu {
        display: block;
    }

    /*
      Pushes the section headings to just below the top nav bar when a user
      navigates directly to section anchors. It doesn't work if you try
      to apply the padding and margin to the `h` elements directly.
     */
    .chapter a[name],
    .chapter .anchor {
        padding-top: 60px;
        margin-top: -60px;
        text-decoration: none;
        border: none;
        display: inline-block;
    }
}

/* Side Navigation styles */
/* Docs Navigation */
.docs-navigation {
    padding-left: 5px;
    padding: 20px 20px 20px 26px;
    background: #f8f8f7;
    border-right: 1px solid #e7e7e9;
    overflow: scroll;
}

.docs-navigation .search-container {
    display: none;
    margin-bottom: 1rem;
}

.docs-navigation .search-input {
    height: 30px;
    line-height: 30px;
    box-sizing: border-box;
    border: 1px solid #e3e3e3;
    border-radius: 3px;
    color: #666;
    outline: none;
    margin-right: 10px;
    transition: border-color 0.2s ease;
    background: white url('data:image/svg+xml;utf8,<svg height="20px" version="1.1" viewBox="0 0 32 32" width="20px" xmlns="http://www.w3.org/2000/svg"><g fill="#cccccc" fill-rule="evenodd" stroke="none" stroke-width="1"><path d="M19.4271164,21.4271164 C18.0372495,22.4174803 16.3366522,23 14.5,23 C9.80557939,23 6,19.1944206 6,14.5 C6,9.80557939 9.80557939,6 14.5,6 C19.1944206,6 23,9.80557939 23,14.5 C23,16.3366522 22.4174803,18.0372495 21.4271164,19.4271164 L27.0119176,25.0119176 C27.5621186,25.5621186 27.5575313,26.4424687 27.0117185,26.9882815 L26.9882815,27.0117185 C26.4438648,27.5561352 25.5576204,27.5576204 25.0119176,27.0119176 L19.4271164,21.4271164 L19.4271164,21.4271164 Z M14.5,21 C18.0898511,21 21,18.0898511 21,14.5 C21,10.9101489 18.0898511,8 14.5,8 C10.9101489,8 8,10.9101489 8,14.5 C8,18.0898511 10.9101489,21 14.5,21 L14.5,21 Z"/></g></svg>') no-repeat 8px 5px;
    background-size: 20px;
    vertical-align: middle !important;
    width: 100%;
    padding-left: 10px;
}

.docs-navigation .search-input:focus {
    border-color: #1BA8CB;
}

.docs-navigation .search-input::placeholder {
    color: #ccc;
}

/* Docsearch overrides */
.docs-navigation .algolia-autocomplete {
    display: block !important;
    flex: 1;
}

.docs-navigation .algolia-autocomplete .ds-dropdown-menu {
    width: 100px;
    min-width: 0 !important;
    max-width: none !important;
    padding: .75rem 0 !important;
    background-color: #fff !important;
    background-clip: padding-box;
    border: 1px solid #e3e3e3 !important;
    box-shadow: 0 .5rem 1rem rgba(0, 0, 0, .175) !important;
    position: fixed !important;
    top: 103px;
    left: 25px;
}

.docs-navigation .algolia-autocomplete .ds-dropdown-menu [class^="ds-dataset-"] {
    padding: 0 !important;
    overflow: visible !important;
    background-color: transparent !important;
    border: 0 !important;
    position: inherit !important;
    top: 112px;
    left: 25px;
}

.docs-navigation .algolia-autocomplete .ds-dropdown-menu .ds-suggestions {
    margin-top: 0 !important;
}

.docs-navigation .algolia-autocomplete .algolia-docsearch-suggestion {
    padding: 0 15px !important;
}

.docs-navigation .algolia-autocomplete .algolia-docsearch-suggestion--category-header {
    font-weight: 800 !important;
    color: #1BA8CB !important;
}

.docs-navigation .algolia-autocomplete .algolia-docsearch-suggestion--wrapper {}

.docs-navigation .algolia-autocomplete .algolia-docsearch-suggestion--subcategory-column {}

.docs-navigation .algolia-autocomplete .algolia-docsearch-suggestion--content {
    padding: 0 !important;
}

.docs-navigation .algolia-autocomplete .algolia-docsearch-suggestion--title {
    display: block !important;
    color: #02303A;
    padding: .25rem 1rem !important;
    margin-bottom: 0 !important;
    font-weight: 500 !important;
}

.docs-navigation .algolia-autocomplete .algolia-docsearch-suggestion--text {
    padding: 0 1rem .5rem !important;
    margin-top: -.25rem !important;
    font-size: 0.9em !important;
    font-weight: 400 !important;
    line-height: 1.25 !important;
}

.docs-navigation .algolia-autocomplete .algolia-docsearch-footer {
    float: none !important;
    width: auto !important;
    height: auto !important;
    padding: .75rem 1rem 0;
    font-size: .875rem !important;
    line-height: 1 !important;
    color: #767676 !important;
    border-top: 1px solid #e3e3e3 !important;
    text-align: right;
}

.docs-navigation .algolia-autocomplete .algolia-docsearch-footer a {
    font-size: .875rem;
    text-decoration: underline;
}

.docs-navigation .algolia-autocomplete .algolia-docsearch-footer--logo {
    display: inline !important;
    overflow: visible !important;
    color: inherit !important;
    text-indent: 0 !important;
    background: none !important;
}

.docs-navigation .algolia-autocomplete .algolia-docsearch-suggestion--highlight {
    background-color: rgba(143, 187, 237, .1) !important;
}

.docs-navigation .algolia-autocomplete .ds-suggestion .ds-cursor .algolia-docsearch-suggestion--content {
    background-color: rgba(208, 189, 236, .15) !important;
}

.docs-navigation a {
    color: #02303A;
    display: block;
    font-size: .95rem;
    position: relative;
}

.docs-navigation a:focus {
    outline: none;
}

.docs-navigation a:hover {
    color: #35c1e4;
    text-decoration: none;
}

.docs-navigation a code {
    color: #02303A;
    overflow-wrap: break-word;
    padding: 0;
    word-break: break-all;
}

.docs-navigation a.active {
    color: #06A0CE;
    outline: 0;
    border: none;
    -moz-outline-style: none;
}

.docs-navigation a.active:hover {
    color: #35c1e4;
    text-decoration: underline;
}

.docs-navigation ul {
    list-style-type: none;
    margin: 0;
    padding: 0;
}

.docs-navigation li > ul > li a {
    font-size: 14px;
    color: #7d7d7d;
}

.docs-navigation ul:last-of-type {
    margin-bottom: 0;
}

.docs-navigation li {
    margin-top: 0.3334em;
    line-height: 1.3334;
}

.docs-navigation li:last-of-type {
    margin-bottom: 0;
}

.docs-navigation .nav-dropdown:before {
    content: '\2023';
    font-size: 28px;
    position: absolute;
    margin-left: -14px;
    margin-top: -8px;
}

.docs-navigation .nav-dropdown.expanded:before {
    transform: rotate(90deg);
}

.docs-navigation > ul ul,
.docs-navigation > ul ul ul {
    display: none;
    height: 0;
    margin-left: 1rem;
}

.docs-navigation > ul ul:target,
.docs-navigation > ul ul:target ul,
.docs-navigation > ul .nav-dropdown.expanded ~ ul {
    display: block;
    height: auto;
}

.docs-navigation h3 {
    font-size: .95rem;
    font-weight: 600;
    line-height: 1.5;
    margin: 1.5em 0 0;
}

.docs-navigation .docs-home-link {
    position: relative;
}

@media screen and (min-width: 45rem) {
    .main-content {
        display: flex;
    }
}

/* User guide navigation appears for desktops */
@media screen and (min-width: 64rem) {
    .docs-navigation {
        flex: 0 0 auto;
        width: 13.75rem;
    }

    .main-content > .appendix,
    .main-content > .book,
    .main-content > .chapter {
        flex: 0 0 auto;
        margin: 0 auto;
    }
}

/**
 * For mobile devices, we show navigation at bottom of page.
 *
 * This is the simplest solution to this issue.
 */
@media not screen and (min-width: 64rem) {
    /* Repeat the class twice to prioritize our mobile classes! */

    .content.content {
        /* Make the height equal to
           the real height of content */
        overflow: visible;
    }

    .main-content {
        /* Main content show first */
        flex-direction: column-reverse;
    }

    .docs-navigation.docs-navigation {
        /* Fill the main container */
        width: 100%;

        /* Don't clip the navigation container */
        overflow: visible;
    }
}

/* Userguide Meta */
.chapter-meta {
    float: right;
    text-align: right;
}

.chapter-meta .edit-link {
    color: #999;
    font-size: 0.9em;
    padding-right: 3px;
}

.chapter-meta .edit-link svg {
    margin-right: 1px;
}

/* Clever use of RTL to fill in all stars to left of hover position */
.rating {
    direction: rtl;
}

.rating > .star {
    cursor: pointer;
    display: table-cell;
    padding: 3px;
}

.rating > .star:hover > svg > g,
.rating > .star:hover ~ .star > svg > g,
.rating > .star.selected > svg > g,
.rating > .star.selected ~ .star > svg > g {
    fill: #999;
}

/* Footer styles */
.site-footer {
}

.site-footer__navigation {
    display: flex;
    padding: 30px 12px;
    max-width: 62.5rem;
    margin: 0 auto;
    padding-left: 5rem;
}

@media not screen and (min-width: 64rem) {
    .site-footer__navigation {
        /* same to nav.docs-navigation for mobiles */
        padding: 20px 20px 20px 26px;
    }
}

.site-footer__links {
    display: flex;
    flex: 1 1 auto;
}

.site-footer__links .site-footer__links-list {
    list-style-type: none;
    margin: 0;
}

.site-footer__links .site-footer__links-list a {
    color: #ddd;
}

.site-footer__link-group {
    flex: 1 1 auto;
    flex-basis: 175px;
}

.site-footer__link-group header {
    color: #fff;
}

.site-footer__subscribe-newsletter .newsletter-form__header h5 {
    color: #fff;
    margin-top: 0;
}

.site-footer__subscribe-newsletter p {
    font-size: 0.875rem;
    margin: 2px 0 0 2px;
    opacity: 0.7;
}

.site-footer__subscribe-newsletter .disclaimer {
    color: #ddd;
    font-size: 0.75rem;
    opacity: 0.55;
}

.site-footer__subscribe-newsletter .newsletter-form {
    padding-top: 6px;
    display: flex;
    justify-content: flex-start;
}

.site-footer__subscribe-newsletter .email,
.site-footer__subscribe-newsletter .submit {
    height: 40px;
}

.site-footer__subscribe-newsletter .email {
    line-height: 40px;
    width: 250px;
    color: #1DA2BD;
    font-size: 16px;
    padding-left: 20px;
    border-top-left-radius: 4px;
    border-bottom-left-radius: 4px;
    border-style: none;
}

.site-footer__subscribe-newsletter .submit {
    font-family: inherit;
    font-size: inherit;
    line-height: inherit;
    width: 100px;
    background-color: #1BA8CB;
    color: white;
    font-weight: 500;
    border-top-right-radius: 4px;
    border-bottom-right-radius: 4px;
    border-style: none;
    cursor: pointer;
    transition: all .3s ease;
}

/* Secondary footer (below) */
.site-footer-secondary {
    background-color: white;
    border-top: 1px solid #e7e7e9;
    width: 100%;
    z-index: 1;
}

.site-footer-secondary__contents {
    display: flex;
    align-items: center;
    justify-content: space-between;
    max-width: 75rem;
    margin-left: auto;
    margin-right: auto;
    font-size: 0.875rem;
    padding: 0.5rem 0.75rem;
}

/*
 * 1. Value is the largest computed width among 'site-footer__copy' and 'site-footer__links'.
 */
.site-footer__copy,
.site-footer__secondary-links {
    flex-grow: 0;
    flex-basis: 280px;
    /* 1. */
}

/*
 * 1. 'flex-shrink: 1' is applied to the element with the smallest computed width among
 *    'site-footer__copy' and 'site-footer__links'.
 */
.site-footer__copy {
    flex-shrink: 1;
    /* 1. */
}

.site-footer__logo {
    flex: 0 0 auto;
    margin-right: 10px;
    margin-left: 10px;
}

.site-footer__logo svg {
    width: 35px;
    height: 35px;
}

/*
 * 1. 'flex-shrink: 0' is applied to the element with the largest computed width among
 *    'site-footer__copy' and 'site-footer__links'.
 */
.site-footer__secondary-links {
    flex-shrink: 0;
    /* 1 */
    text-align: right;
    white-space: nowrap;
}

.site-footer-secondary a {
    color: #999;
}

.site-footer-secondary__links a:not(:last-child) {
    padding-right: 10px;
}

.site-footer-secondary__links a:not(:first-child) {
    padding-left: 10px;
}

@media all and (max-width: 29.99rem) {
    .site-footer__rights,
    .site-footer-secondary__links {
        display: none;
    }

    .site-footer__logo {
        order: 1;
        text-align: left;
    }

    .site-footer__copy {
        order: 2;
        text-align: right;
    }
}

/* Avoid the footer taking up much of the screen on short displays */
@media all and (max-height: 56.25rem) {
    .site-footer__navigation {
        margin: 1.5rem auto 0 auto;
        padding-top: 0;
        padding-bottom: 0;
    }
}

@media screen and (min-width: 84.375rem) {
    .ui-logos .ui-logo {
        box-shadow: 0 6px 15px 1px rgba(0, 0, 0, 0.56);
    }
}

/* User Manual Home */
.technology-logos,
.ui-logos {
    display: flex;
    flex-direction: row;
    flex-wrap: wrap;
    justify-content: space-around;
}

.technology-logo,
.ui-logo {
    flex: 0 1 auto;
}

.ui-logo {
    width: 224px;
    height: 135px;
    margin: 12px;
}

/* Samples download button */
.download {
  display: flex;
}

.download a {
  border-style: solid;
  border-width: 1px;
  text-decoration: none;
  padding: 5px;
  display: block;
  width: 10em;
  margin: 5px;
}

.download ul {
  list-style: none;
  list-style-type: none;
}

.download li {
  float: right;
}

.docs-navigation {
    width: 18rem;
    padding: 20px 20px 20px 26px;
    background: #f8f8f7;
    border-right: 1px solid #e7e7e9;
    overflow-y: scroll;
    overflow-x: auto;
}

.layout {
    display: flex;
    flex-direction: column;
    overflow: hidden;
    height: 100vh;
}

.main-content {
    overflow-y: auto;
    overflow-x: auto;
    display: flex;
}

.content {
    flex: 1 1 auto;
    overflow: auto;
    padding-left: 0;
    padding-right: 0;
}

.content .chapter {
    padding: 2rem 2.4rem;
}

#toc a:active {
    font-weight: 500;
}

.site-header  {
    margin-bottom: 1rem;
}

.site-header__navigation-submenu-item {
    padding: 2px 0;
}

.site-footer {
    background-color: rgba(0, 0, 0, 0.8);
    padding: 20px 0 40px 0;
}

#header {
    margin-left: 0;
}

#header > h1:first-child {
    margin-bottom: 40px;
}

@media screen and (min-width: 64rem) {
    #header {
        margin-bottom: 20px;
    }

    .site-header  {
        margin-bottom: 0;
        z-index: 2;
    }

    .site-header__navigation-header {
        margin-top: -7px;
    }

    .site-header__navigation-submenu-section:after {
        content: '\2023';
        font-size: 28px;
        position: absolute;
        transform: rotate(90deg);
        margin-right: 10px;
        top: 5px;
        right: -7px;
    }

    .site-footer__navigation {
        flex-wrap: wrap;
    }

    .site-footer__link-group {
        flex: 1 1 auto;
    }

    .site-footer__links {
        margin-bottom: 1rem;

    }
}

@media screen and (min-width: 75rem) {
    .content .chapter {
        box-sizing: content-box;
    }

    #content {
        padding-right: 260px;
        margin: 0;
    }
    /* #toc here must be referenced as #header #toc
            since we don't want to change single page #toc */
    #header #toc {
        position: fixed;
        margin: 0 auto;
        padding-bottom: 0;
        right: 0;
        top: 65px;
        width: 260px;
        z-index: 1;
        overflow: auto;
        border-radius: 0 0 5px 0;
        max-height: calc(100% - 118px);
        margin-right: 15px;
    }

    #header #toctitle {
        margin-top: 1.3em;
    }

    #header #toc > ul {
        /* margin-left have to increase if you change border thickness of active toc item */
        margin-left: 1px;
        border-left: 1px solid #666;
        margin-bottom: 0;
        padding-right: 10px;
        padding-bottom: 0.5rem;
        background-color: #fff;
    }

    #header #toc > ul > li, #toc > ul > li:last-of-type {
        padding: 0.5rem 0;
        margin: 0;
    }

    #header #toc a {
        padding-left: 10px;
        font-weight: 400;
        color: #02303A;
        font-size: .95rem;
        display: inline-block;
    }

    #header #toc a.active {
        font-weight: 500;
        border-left: 3px solid #01303a;
        margin-left: -2px;
        padding-left: 9px;
    }

    .site-footer__navigation {
        flex-wrap: wrap;
        margin-left: auto;
        padding-left: 3rem;
    }

    .site-footer__link-group {
        flex: 0.15 1 auto;
    }

    .site-footer__subscribe-newsletter {
        /* A fix so subscribe disclaimer does not go under long ToC */
        max-width: calc(100% - 17rem);
    }
}

@media screen and (min-width: 80rem) {
    .site-footer__subscribe-newsletter {
        max-height: none;
    }
}

@media screen and (min-width: 100rem) {
    .content .chapter {
        max-width: 60.5rem;
    }

    #header {
        margin: 0 auto;
    }

    #content {
        padding-right: 0;
        margin: 0 auto;
    }

    #header #toc {
        right: initial;
        margin-left: 62.5rem;
    }

    .site-footer__navigation {
        padding-left: 0;
    }

    .site-footer__link-group {
        flex: 0.2 1 auto;
    }
}

@media screen and (min-width: 112rem) {
    .content .chapter {
        max-width: 62.5rem;
        margin: 0 auto;
        padding-right: 3.5rem;
        position: relative;
        left: -130px;
    }

    #header #toc {
        right: initial;
        margin-left: 64.5rem;
    }

    .site-footer__link-group {
        flex: 0.2 1 auto;
    }
}


@supports (-moz-appearance:meterbar) and (background-blend-mode:difference,normal) {
    /* Firefox only */
    .site-header__navigation-submenu-section:after {
        top: 2px;
        right: -4px;
    }
}

</style>
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/4.7.0/css/font-awesome.min.css">
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/prettify/r298/prettify.min.css">
<meta name="adoc-src-path" content="releases/upgrading/upgrading_version_4.adoc">
<link crossorigin href="//assets.gradle.com" rel="preconnect">
<link href="https://fonts.googleapis.com/css?family=Inconsolata:400,700" rel="stylesheet"/>


<style type="text/css">.multi-language-selector .language-option[data-lang='groovy'],
.exampleblock[data-lang=groovy] > .content .title {
    background-image: url('data:image/svg+xml;base64,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');
    background-position: 16px 80%;
    background-repeat: no-repeat;
    background-size: 20px 12px;
    padding-left: 2.5em;
}

.multi-language-selector .language-option[data-lang='kotlin'],
.exampleblock[data-lang=kotlin] > .content .title {
    background-image: url("data:image/svg+xml;utf8,<svg viewBox='0 0 8 8' xmlns='http://www.w3.org/2000/svg'><linearGradient id='g' gradientUnits='userSpaceOnUse' x1='8' y1='0' x2='0' y2='8'><stop offset='0' stop-color='%23e44857'/><stop offset='.4689' stop-color='%23c711e1'/><stop offset='1' stop-color='%237f52ff'/></linearGradient><polygon fill='url(%23g)' points='8 8 0 8 0 0 8 0 4 4'/></svg>");
    background-position: 17px 80%;
    background-repeat: no-repeat;
    background-size: 11px 11px;
    padding-left: 2.3em;
}

.multi-language-selector {
    display: block;
}

.multi-language-selector .language-option[data-lang='groovy'] {
    background-position: 20px center;
    padding-left: 32px;
}

.multi-language-selector .language-option[data-lang='kotlin'] {
    background-position: 30px center;
    padding-left: 27px;
}

.multi-language-selector .language-option {
    background-color: white;
    border: 1px solid #f7f7f8;
    border-radius: 4px 4px 0 0;
    cursor: pointer;
    display: inline-block;
    font-weight: normal;
    font-family: 'Lato', Arial, sans-serif;
    margin: 0;
    padding: 4px 20px;
    min-width: 130px;
    max-width: 320px;
    text-align: center;
    filter: grayscale(1);
    -webkit-filter: grayscale(1);
    opacity: 0.7;
}

.multi-language-selector .language-option.selected {
    background-color: #f7f7f8;
    color: #02303a;
    filter: none;
    -webkit-filter: none;
    opacity: 1;
}

.multi-language-text.hidden,
.multi-language-selector ~ .multi-language-sample.hidden {
    display: none;
}

.multi-language-sample {
    border-radius: 0 0 4px 4px;
}
</style><script type="text/javascript">function postProcessCodeBlocks() {
  // Assumptions:
  //  1) All siblings that are marked with class="multi-language-sample" should be grouped
  //  2) Only one language can be selected per domain (to allow selection to persist across all docs pages)
  //  3) There is exactly 1 small set of languages to choose from. This does not allow for multiple language preferences. For example, users cannot prefer both Kotlin and ZSH.
  //  4) Only 1 sample of each language can exist in the same collection.

  var GRADLE_DSLs = ["groovy", "kotlin"];
  var preferredBuildScriptLanguage = initPreferredBuildScriptLanguage();

  // Ensure preferred DSL is valid, defaulting to Kotlin DSL
  function initPreferredBuildScriptLanguage() {
    var lang = window.localStorage.getItem("preferred-gradle-dsl");
    if (GRADLE_DSLs.indexOf(lang) === -1) {
      window.localStorage.setItem("preferred-gradle-dsl", "kotlin");
      lang = "kotlin";
    }
    return lang;
  }

  function capitalizeFirstLetter(string) {
    return string.charAt(0).toUpperCase() + string.slice(1);
  }

  function processSampleEl(sampleEl, prefLangId) {
    var codeEl = sampleEl.querySelector("code[data-lang]");
    if (codeEl != null) {
      sampleEl.setAttribute("data-lang", codeEl.getAttribute("data-lang"));
      if (codeEl.getAttribute("data-lang") !== prefLangId) {
        sampleEl.classList.add("hidden");
      } else {
        sampleEl.classList.remove("hidden");
      }
    }
  }

  function switchSampleLanguage(languageId) {
    var multiLanguageSampleElements = [].slice.call(
      document.querySelectorAll(".multi-language-sample")
    );

    // Array of Arrays, each top-level array representing a single collection of samples
    var multiLanguageSets = [];
    for (var i = 0; i < multiLanguageSampleElements.length; i++) {
      var currentCollection = [multiLanguageSampleElements[i]];
      var currentSampleElement = multiLanguageSampleElements[i];
      processSampleEl(currentSampleElement, languageId);
      while (
        currentSampleElement.nextElementSibling != null &&
        currentSampleElement.nextElementSibling.classList.contains(
          "multi-language-sample"
        )
      ) {
        currentCollection.push(currentSampleElement.nextElementSibling);
        currentSampleElement = currentSampleElement.nextElementSibling;
        processSampleEl(currentSampleElement, languageId);
        i++;
      }

      multiLanguageSets.push(currentCollection);
    }

    multiLanguageSets.forEach(function (sampleCollection) {
      // Create selector element if not existing
      if (
        sampleCollection.length > 1 &&
        (sampleCollection[0].previousElementSibling == null ||
          !sampleCollection[0].previousElementSibling.classList.contains(
            "multi-language-selector"
          ))
      ) {
        var languageSelectorFragment = document.createDocumentFragment();
        var multiLanguageSelectorElement = document.createElement("div");
        multiLanguageSelectorElement.classList.add("multi-language-selector");
        languageSelectorFragment.appendChild(multiLanguageSelectorElement);

        sampleCollection.forEach(function (sampleEl) {
          var optionEl = document.createElement("code");
          var sampleLanguage = sampleEl.getAttribute("data-lang");
          optionEl.setAttribute("data-lang", sampleLanguage);
          optionEl.setAttribute("role", "button");
          optionEl.classList.add("language-option");

          optionEl.innerText = capitalizeFirstLetter(sampleLanguage);

          optionEl.addEventListener(
            "click",
            function updatePreferredLanguage(evt) {
              var preferredLanguageId = optionEl.getAttribute("data-lang");
              window.localStorage.setItem(
                "preferred-gradle-dsl",
                preferredLanguageId
              );

              // Record how far down the page the clicked element is before switching all samples
              var beforeOffset = evt.target.offsetTop;

              switchSampleLanguage(preferredLanguageId);

              // Scroll the window to account for content height differences between different sample languages
              window.scrollBy(0, evt.target.offsetTop - beforeOffset);
            }
          );
          multiLanguageSelectorElement.appendChild(optionEl);
        });
        sampleCollection[0].parentNode.insertBefore(
          languageSelectorFragment,
          sampleCollection[0]
        );
      }
    });

    [].slice
      .call(
        document.querySelectorAll(".multi-language-selector .language-option")
      )
      .forEach(function (optionEl) {
        if (optionEl.getAttribute("data-lang") === languageId) {
          optionEl.classList.add("selected");
        } else {
          optionEl.classList.remove("selected");
        }
      });

    [].slice
      .call(document.querySelectorAll(".multi-language-text"))
      .forEach(function (el) {
        if (!el.classList.contains("lang-" + languageId)) {
          el.classList.add("hidden");
        } else {
          el.classList.remove("hidden");
        }
      });
  }

  switchSampleLanguage(preferredBuildScriptLanguage);
}

document.addEventListener("DOMContentLoaded", function () {
  postProcessCodeBlocks();
});
</script>
</head>
<body id="upgrading_version_4" class="book">
<div class="layout">
<header class="site-layout__header site-header" itemscope="itemscope" itemtype="https://schema.org/WPHeader">
    <nav class="site-header__navigation" itemscope="itemscope" itemtype="https://schema.org/SiteNavigationElement">
        <div class="site-header__navigation-header">
            <a target="_top" class="logo" href="https://docs.gradle.org" title="Gradle Docs">
                <svg width="139px" height="43px" viewBox="0 0 278 86" version="1.1" xmlns="http://www.w3.org/2000/svg">
                    <defs>
                        <style>.cls-1 {
                            fill: #02303a;
                        }</style>
                    </defs>
                    <title>Gradle</title>
                    <path class="cls-1"
                          d="M155,56.32V70.27a18.32,18.32,0,0,1-5.59,2.83,21.82,21.82,0,0,1-6.36.89,21.08,21.08,0,0,1-7.64-1.31A17.12,17.12,0,0,1,129.59,69a16.14,16.14,0,0,1-3.73-5.58,18.78,18.78,0,0,1-1.31-7.08,19.58,19.58,0,0,1,1.26-7.14A15.68,15.68,0,0,1,135,40a20.39,20.39,0,0,1,7.45-1.29,22,22,0,0,1,3.92.33,20.43,20.43,0,0,1,3.39.92,15.16,15.16,0,0,1,2.85,1.42A17.3,17.3,0,0,1,155,43.25l-1.84,2.91a1.72,1.72,0,0,1-1.12.84,2,2,0,0,1-1.5-.34L149,45.75a10.49,10.49,0,0,0-1.75-.79,14.33,14.33,0,0,0-2.17-.54,15.29,15.29,0,0,0-2.78-.22,11.91,11.91,0,0,0-4.61.86,9.66,9.66,0,0,0-3.52,2.46,10.9,10.9,0,0,0-2.24,3.84,14.88,14.88,0,0,0-.79,5,15.23,15.23,0,0,0,.85,5.28,11.06,11.06,0,0,0,2.38,3.94A10.15,10.15,0,0,0,138.05,68a14.28,14.28,0,0,0,8.25.44,17.1,17.1,0,0,0,2.94-1.09V61.14h-4.35a1.3,1.3,0,0,1-1-.35,1.15,1.15,0,0,1-.35-.85V56.32Zm10.47-2.93a10.53,10.53,0,0,1,2.72-3.45,5.77,5.77,0,0,1,3.72-1.25,4.5,4.5,0,0,1,2.72.74l-.38,4.41a1.18,1.18,0,0,1-.34.61,1,1,0,0,1-.61.18,6.76,6.76,0,0,1-1.06-.12,8.22,8.22,0,0,0-1.38-.12,5,5,0,0,0-1.74.28,4.37,4.37,0,0,0-1.37.83,5.55,5.55,0,0,0-1.07,1.3,12.26,12.26,0,0,0-.87,1.74V73.61H160V49.14h3.45a1.94,1.94,0,0,1,1.27.32,1.9,1.9,0,0,1,.48,1.16Zm11.36-.84A14.49,14.49,0,0,1,187,48.69a9.92,9.92,0,0,1,3.84.7,8.06,8.06,0,0,1,2.86,2,8.38,8.38,0,0,1,1.78,3,11.64,11.64,0,0,1,.61,3.82V73.61h-2.68a2.64,2.64,0,0,1-1.28-.25,1.72,1.72,0,0,1-.72-1l-.52-1.77a20.25,20.25,0,0,1-1.82,1.47,10.86,10.86,0,0,1-1.83,1.06,10.36,10.36,0,0,1-2,.66,12,12,0,0,1-2.4.22,9.64,9.64,0,0,1-2.86-.41,6.28,6.28,0,0,1-2.27-1.26,5.6,5.6,0,0,1-1.48-2.07,7.38,7.38,0,0,1-.52-2.89,5.7,5.7,0,0,1,.31-1.85,5.3,5.3,0,0,1,1-1.75,8.25,8.25,0,0,1,1.83-1.57,11.17,11.17,0,0,1,2.75-1.29,23.28,23.28,0,0,1,3.81-.9,36.77,36.77,0,0,1,5-.41V58.16a5.35,5.35,0,0,0-1.05-3.64,3.83,3.83,0,0,0-3-1.18,7.3,7.3,0,0,0-2.38.33,9.39,9.39,0,0,0-1.65.75l-1.3.75a2.52,2.52,0,0,1-1.3.34,1.7,1.7,0,0,1-1.05-.32,2.61,2.61,0,0,1-.69-.76Zm13.5,10.61a31.66,31.66,0,0,0-4.3.45,11,11,0,0,0-2.79.82,3.57,3.57,0,0,0-1.5,1.17,2.89,2.89,0,0,0,.47,3.67,3.93,3.93,0,0,0,2.39.67,7,7,0,0,0,3.14-.66,9.52,9.52,0,0,0,2.59-2Zm32.53-25V73.61h-3.6a1.39,1.39,0,0,1-1.48-1.07l-.5-2.36a12.4,12.4,0,0,1-3.4,2.74,9.17,9.17,0,0,1-4.47,1,7.95,7.95,0,0,1-6.55-3.26A11.61,11.61,0,0,1,201,66.79a19.71,19.71,0,0,1-.66-5.34,16.77,16.77,0,0,1,.74-5.06,12.21,12.21,0,0,1,2.13-4,9.88,9.88,0,0,1,3.31-2.69,9.64,9.64,0,0,1,4.34-1,8.63,8.63,0,0,1,3.51.64,9,9,0,0,1,2.6,1.74V38.17ZM217,55.39a5.94,5.94,0,0,0-2.18-1.72,6.54,6.54,0,0,0-2.54-.5,5.68,5.68,0,0,0-2.41.5A4.87,4.87,0,0,0,208,55.19a7.19,7.19,0,0,0-1.17,2.57,14.83,14.83,0,0,0-.4,3.69,16.34,16.34,0,0,0,.34,3.63,7.14,7.14,0,0,0,1,2.44,3.79,3.79,0,0,0,1.58,1.36,5,5,0,0,0,2.07.41,6,6,0,0,0,3.13-.76A9.19,9.19,0,0,0,217,66.36Zm17.67-17.22V73.61h-5.89V38.17ZM245.1,62.11a11.37,11.37,0,0,0,.67,3.26,6.54,6.54,0,0,0,1.38,2.27,5.39,5.39,0,0,0,2,1.33,7.26,7.26,0,0,0,2.61.44,8.21,8.21,0,0,0,2.47-.33,11.51,11.51,0,0,0,1.81-.74c.52-.27,1-.52,1.36-.74a2.31,2.31,0,0,1,1.13-.33,1.21,1.21,0,0,1,1.1.55L261.36,70a9.45,9.45,0,0,1-2.19,1.92,12.18,12.18,0,0,1-2.54,1.24,14,14,0,0,1-2.7.66,18.78,18.78,0,0,1-2.65.19,12.93,12.93,0,0,1-4.75-.85,10.65,10.65,0,0,1-3.82-2.5,11.8,11.8,0,0,1-2.55-4.1,15.9,15.9,0,0,1-.93-5.67,13.55,13.55,0,0,1,.81-4.71,11.34,11.34,0,0,1,2.33-3.84,11,11,0,0,1,3.69-2.59,12.31,12.31,0,0,1,4.93-1,11.86,11.86,0,0,1,4.27.74,9.25,9.25,0,0,1,3.36,2.16,9.84,9.84,0,0,1,2.21,3.48,13,13,0,0,1,.8,4.71,3.82,3.82,0,0,1-.29,1.8,1.19,1.19,0,0,1-1.1.46Zm11.23-3.55A7.28,7.28,0,0,0,256,56.4a5.16,5.16,0,0,0-1-1.77,4.44,4.44,0,0,0-1.63-1.21,5.68,5.68,0,0,0-2.3-.44,5.46,5.46,0,0,0-4,1.45,7.13,7.13,0,0,0-1.87,4.13ZM112.26,14a13.72,13.72,0,0,0-19.08-.32,1.27,1.27,0,0,0-.41.93,1.31,1.31,0,0,0,.38.95l1.73,1.73a1.31,1.31,0,0,0,1.71.12,7.78,7.78,0,0,1,4.71-1.57,7.87,7.87,0,0,1,5.57,13.43C96,40.2,81.41,9.66,48.4,25.37a4.48,4.48,0,0,0-2,6.29l5.66,9.79a4.49,4.49,0,0,0,6.07,1.67l.14-.08-.11.08,2.51-1.41a57.72,57.72,0,0,0,7.91-5.89,1.37,1.37,0,0,1,1.8-.06h0a1.29,1.29,0,0,1,0,2A59.79,59.79,0,0,1,62.11,44l-.09.05-2.51,1.4a7,7,0,0,1-3.47.91,7.19,7.19,0,0,1-6.23-3.57l-5.36-9.24C34.17,40.81,27.93,54.8,31.28,72.5a1.31,1.31,0,0,0,1.29,1.06h6.09A1.3,1.3,0,0,0,40,72.42a8.94,8.94,0,0,1,17.73,0A1.3,1.3,0,0,0,59,73.56h5.94a1.31,1.31,0,0,0,1.3-1.14,8.93,8.93,0,0,1,17.72,0,1.3,1.3,0,0,0,1.29,1.14h5.87a1.3,1.3,0,0,0,1.3-1.28c.14-8.28,2.37-17.79,8.74-22.55C123.15,33.25,117.36,19.12,112.26,14ZM89.79,38.92l-4.2-2.11h0a2.64,2.64,0,1,1,4.2,2.12Z"/>
                </svg>
            </a>
            <div class="site-header__doc-type sr-only">User Manual</div>
            <div class="site-header-version"></div>
            <button type="button" aria-label="Navigation Menu" class="site-header__navigation-button hamburger">
                <span class="hamburger__bar"></span>
                <span class="hamburger__bar"></span>
                <span class="hamburger__bar"></span>
            </button>
        </div>
        <div class="site-header__navigation-collapsible site-header__navigation-collapsible--collapse">
            <ul class="site-header__navigation-items">
                <li class="site-header__navigation-item site-header__navigation-submenu-section" tabindex="0">
                    <span class="site-header__navigation-link">
                        Community
                    </span>
                    <div class="site-header__navigation-submenu">
                        <div class="site-header__navigation-submenu-item" itemprop="name">
                            <a target="_top" class="site-header__navigation-submenu-item-link" href="https://gradle.org/" itemprop="url">
                                <span class="site-header__navigation-submenu-item-link-text">Community Home</span>
                            </a>
                        </div>
                        <div class="site-header__navigation-submenu-item" itemprop="name">
                            <a target="_top" class="site-header__navigation-submenu-item-link" href="https://discuss.gradle.org/" itemprop="url">
                                <span class="site-header__navigation-submenu-item-link-text">Community Forums</span>
                            </a>
                        </div>
                        <div class="site-header__navigation-submenu-item" itemprop="name">
                            <a target="_top" class="site-header__navigation-submenu-item-link" href="https://plugins.gradle.org" itemprop="url">
                                <span class="site-header__navigation-submenu-item-link-text">Community Plugins</span>
                            </a>
                        </div>
                    </div>
                </li>
                <li class="site-header__navigation-item" itemprop="name">
                    <a target="_top" class="site-header__navigation-link" href="https://gradle.org/training/" itemprop="url">Training</a>
                </li>
                <li class="site-header__navigation-item site-header__navigation-submenu-section" tabindex="0">
                    <span class="site-header__navigation-link">
                        News
                    </span>
                    <div class="site-header__navigation-submenu">
                        <div class="site-header__navigation-submenu-item" itemprop="name">
                            <a class="site-header__navigation-submenu-item-link" href="https://newsletter.gradle.org" itemprop="url">
                                <span class="site-header__navigation-submenu-item-link-text">Newsletter</span>
                            </a>
                        </div>
                        <div class="site-header__navigation-submenu-item" itemprop="name">
                            <a class="site-header__navigation-submenu-item-link" href="https://blog.gradle.org" itemprop="url">
                                <span class="site-header__navigation-submenu-item-link-text">Blog</span>
                            </a>
                        </div>
                        <div class="site-header__navigation-submenu-item">
                            <a class="site-header__navigation-submenu-item-link" href="https://twitter.com/gradle">
                                <span class="site-header__navigation-submenu-item-link-text">Twitter</span>
                            </a>
                        </div>
                    </div>
                </li>
                <li class="site-header__navigation-item" itemprop="name">
                    <a target="_top" class="site-header__navigation-link" href="https://gradle.com" itemprop="url">Develocity</a>
                </li>
                <li class="site-header__navigation-item">
                    <a class="site-header__navigation-link" title="Gradle on GitHub" href="https://github.com/gradle/gradle"><svg width="20" height="20" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg"><title>github</title><path d="M10 0C4.477 0 0 4.477 0 10c0 4.418 2.865 8.166 6.839 9.489.5.092.682-.217.682-.482 0-.237-.008-.866-.013-1.7-2.782.603-3.369-1.342-3.369-1.342-.454-1.155-1.11-1.462-1.11-1.462-.908-.62.069-.608.069-.608 1.003.07 1.531 1.03 1.531 1.03.892 1.529 2.341 1.087 2.91.831.092-.646.35-1.086.636-1.336-2.22-.253-4.555-1.11-4.555-4.943 0-1.091.39-1.984 1.029-2.683-.103-.253-.446-1.27.098-2.647 0 0 .84-.268 2.75 1.026A9.578 9.578 0 0 1 10 4.836c.85.004 1.705.114 2.504.337 1.909-1.294 2.747-1.026 2.747-1.026.546 1.377.203 2.394.1 2.647.64.699 1.028 1.592 1.028 2.683 0 3.842-2.339 4.687-4.566 4.935.359.309.678.919.678 1.852 0 1.336-.012 2.415-.012 2.743 0 .267.18.579.688.481C17.137 18.163 20 14.418 20 10c0-5.523-4.478-10-10-10" fill="#02303A" fill-rule="evenodd"/></svg></a>
                </li>
            </ul>
        </div>
    </nav>
</header>

<main class="main-content">
    <!-- Primary Navigation -->
    <nav class="docs-navigation">
        <div class="search-container">
            <input type="search" name="q" id="search-input" class="search-input" placeholder="Search the docs"/>
        </div>
        <h3 id="overview">Overview</h3>
        <ul>
            <li><a href="../userguide/userguide.html">What is Gradle?</a></li>
            <li><a href="../userguide/about_manual.html">The User Manual</a></li>
        </ul>

        <h3 id="what-is-new">Releases</h3>
        <ul>
            <li><a href="https://gradle.org/releases/">All Releases</a></li>
            <li><a href="../release-notes.html">Release Notes</a></li>
            <li><a href="../userguide/compatibility.html">Compatibility Notes</a></li>
            <li><a href="../userguide/installation.html">Installing Gradle</a></li>
            <li><a class="nav-dropdown" data-toggle="collapse" href="#upgrading-gradle" aria-expanded="false" aria-controls="upgrading-gradle">Upgrading Gradle</a>
                <ul id="upgrading-gradle">
                    <li><a href="../userguide/upgrading_version_8.html">version 8.X to latest</a></li>
                    <li><a href="../userguide/upgrading_version_7.html">version 7.X to 8.0</a></li>
                    <li><a href="../userguide/upgrading_version_6.html">version 6.X to 7.0</a></li>
                    <li><a href="../userguide/upgrading_version_5.html">version 5.X to 6.0</a></li>
                    <li><a href="../userguide/upgrading_version_4.html">version 4.X to 5.0</a></li>
                </ul>
            </li>
            <li><a class="nav-dropdown" data-toggle="collapse" href="#migrating-to-gradle" aria-expanded="false" aria-controls="migrating-to-gradle">Migrating to Gradle</a>
                <ul id="migrating-to-gradle">
                    <li><a href="../userguide/migrating_from_maven.html">from Maven</a></li>
                    <li><a href="../userguide/migrating_from_ant.html">from Ant</a></li>
                </ul>
            </li>
            <li><a href="../userguide/feature_lifecycle.html">Gradle's Feature Lifecycle</a></li>
        </ul>

        <h3 id="running-gradle-builds">Running Gradle Builds</h3>
        <ul>
            <li><a href="../userguide/getting_started_eng.html">Getting Started</a></li>
            <li><a class="nav-dropdown" data-toggle="collapse" href="#running-introduction" aria-expanded="false" aria-controls="introduction">Core Concepts</a>
                <ul id="running-introduction">
                    <li><a href="../userguide/gradle_basics.html">1. Gradle Basics</a></li>
                    <li><a href="../userguide/gradle_wrapper_basics.html">2. Gradle Wrapper Basics</a></li>
                    <li><a href="../userguide/command_line_interface_basics.html">3. Command-Line Interface Basics</a></li>
                    <li><a href="../userguide/settings_file_basics.html">4. Settings File Basics</a></li>
                    <li><a href="../userguide/build_file_basics.html">5. Build File Basics</a></li>
                    <li><a href="../userguide/dependency_management_basics.html">6. Dependency Management Basics</a></li>
                    <li><a href="../userguide/task_basics.html">7. Task Basics</a></li>
                    <li><a href="../userguide/plugin_basics.html">8. Plugins Basics</a></li>
                    <li><a href="../userguide/gradle_optimizations.html">9. Incremental Builds + Caching</a></li>
                    <li><a href="../userguide/build_scans.html">10. Build Scans</a></li>

                </ul>
            </li>
            <li><a class="nav-dropdown" data-toggle="collapse" aria-expanded="false">Tutorial</a>
                <ul id="running-tutorial">
                    <li><a href="../userguide/part1_gradle_init.html">1. Initializing the Project</a></li>
                    <li><a href="../userguide/part2_gradle_tasks.html">2. Running Tasks</a></li>
                    <li><a href="../userguide/part3_gradle_dep_man.html">3. Understanding Dependencies</a></li>
                    <li><a href="../userguide/part4_gradle_plugins.html">4. Applying Plugins</a></li>
                    <li><a href="../userguide/part5_gradle_inc_builds.html">5. Exploring Incremental Builds</a></li>
                    <li><a href="../userguide/part6_gradle_caching.html">6. Enabling the Build Cache</a></li>
                    <li><a href="../userguide/part7_gradle_refs.html">7. Using Reference Materials</a></li>
                </ul>
            </li>
            <li><a href="../userguide/gradle_ides.html">Gradle in the IDE</a></li>
            <li><a href="../userguide/continuous_build.html">Continuous Build</a></li>
        </ul>

        <h3 id="authoring-gradle-builds">Authoring Gradle Builds</h3>
        <ul>
            <li><a href="../userguide/getting_started_dev.html">Getting Started</a></li>
            <li><a class="nav-dropdown" data-toggle="collapse" href="#learning-the-basics" aria-expanded="false" aria-controls="learning-the-basics">Learning the Basics</a>
                <ul id="authoring-introduction">
                    <li><a href="../userguide/gradle_directories.html">1. Gradle Directories</a></li>
                    <li><a href="../userguide/intro_multi_project_builds.html">2. Multi-Project Builds</a></li>
                    <li><a href="../userguide/build_lifecycle.html">3. Gradle Build Lifecycle</a></li>
                    <li><a href="../userguide/writing_settings_files.html">4. Writing Settings Files</a></li>
                    <li><a href="../userguide/writing_build_scripts.html">5. Writing Build Scripts</a></li>
                    <li><a href="../userguide/tutorial_using_tasks.html">6. Using Tasks</a></li>
                    <li><a href="../userguide/writing_tasks.html">7. Writing Tasks</a></li>
                    <li><a href="../userguide/plugins.html">8. Using Plugins</a></li>
                    <li><a href="../userguide/writing_plugins.html">9. Writing Plugins</a></li>
                </ul>
            </li>
            <li><a class="nav-dropdown" data-toggle="collapse" aria-expanded="false">Tutorial</a>
                <ul id="authoring-tutorial">
                    <li><a href="../userguide/partr1_gradle_init.html">1. Initializing the Project</a></li>
                    <li><a href="../userguide/partr2_build_lifecycle.html">2. Understanding the Build Lifecycle</a></li>
                    <li><a href="../userguide/partr3_multi_project_builds.html">3. Multi-Project Builds</a></li>
                    <li><a href="../userguide/partr4_settings_file.html">4. Writing the Settings File</a></li>
                    <li><a href="../userguide/partr5_build_scripts.html">5. Writing a Build Script</a></li>
                    <li><a href="../userguide/partr6_writing_tasks.html">6. Writing Tasks</a></li>
                    <li><a href="../userguide/partr7_writing_plugins.html">7. Writing Plugins</a></li>
                </ul>
            </li>
            <li><a class="nav-dropdown" data-toggle="collapse" href="#authoring-introduction" aria-expanded="false" aria-controls="introduction">Other Topics</a>
                <ul id="learning-the-basics">
                    <li><a href="../userguide/directory_layout.html">Gradle managed Directories</a></li>
                    <li><a href="../userguide/working_with_files.html">Working with Files</a></li>
                    <li><a href="../userguide/logging.html">Working with Logging</a></li>
                    <li><a href="../userguide/potential_traps.html">Avoiding Traps</a></li>
                    <li><a href="../userguide/build_environment.html">Configuring the Build Environment</a></li>
                    <li><a href="../userguide/init_scripts.html">Initialization Scripts</a></li>
                </ul>
            </li>
            <li><a class="nav-dropdown" data-toggle="collapse" href="#authoring-multi-project-builds" aria-expanded="false" aria-controls="authoring-multi-project-builds">Structuring Individual Builds</a>
                <ul id="authoring-multi-project-builds">
                    <li><a href="../userguide/multi_project_builds.html">Creating a Basic Multi-Project Build</a></li>
                    <li><a href="../userguide/declaring_dependencies_between_subprojects.html">Declaring Dependencies between Subprojects</a></li>
                    <li><a href="../userguide/sharing_build_logic_between_subprojects.html">Sharing Build Logic between Subprojects</a></li>
                    <li><a href="../userguide/fine_tuning_project_layout.html">Fine Tuning the Project Layout</a></li>
                    <li><a href="../userguide/multi_project_configuration_and_execution.html">Understanding Configuration and Execution</a></li>
                </ul>
            </li>
            <li><a class="nav-dropdown" data-toggle="collapse" href="#authoring-composite-builds" aria-expanded="false" aria-controls="authoring-composite-builds">Structuring Software Products</a>
                <ul id="structuring-software-product">
                    <li><a href="../userguide/structuring_software_products.html">Structuring Large Projects</a></li>
                    <li><a href="../userguide/structuring_software_products_details.html">Tweaking Project Structure</a></li>
                    <li><a href="../userguide/composite_builds.html">Composing Builds</a></li>
                </ul>
            </li>
            <li><a class="nav-dropdown" data-toggle="collapse" href="#authoring-sustainable-builds" aria-expanded="false" aria-controls="authoring-sustainable-builds">Authoring Sustainable Builds</a>
                <ul id="authoring-sustainable-builds">
                    <li><a href="../userguide/organizing_gradle_projects.html">Organizing Build Logic</a></li>
                    <li><a href="../userguide/authoring_maintainable_build_scripts.html">Following Best Practices</a></li>
                </ul>
            </li>
            <li><a class="nav-dropdown" data-toggle="collapse" href="#developing-tasks" aria-expanded="false" aria-controls="developing-tasks">Developing Gradle Tasks</a>
                <ul id="developing-tasks">
                    <li><a href="../userguide/more_about_tasks.html">Authoring Tasks</a></li>
                    <li><a href="../userguide/custom_tasks.html">Writing Gradle Task Types</a></li>
                    <li><a href="../userguide/lazy_configuration.html">Configuring Tasks Lazily</a></li>
                    <li><a href="../userguide/task_configuration_avoidance.html">Avoiding Unnecessary Task Configuration</a></li>
                    <li><a href="../userguide/worker_api.html">Developing Parallel Tasks</a></li>
                </ul>
            </li>
            <li><a class="nav-dropdown" data-toggle="collapse" href="#developing-plugins" aria-expanded="false" aria-controls="developing-plugins">Developing Gradle Plugins</a>
                <ul id="developing-plugins">
                    <li><a href="../userguide/custom_plugins.html">Starting Plugin Development</a></li>
                    <li><a href="../userguide/designing_gradle_plugins.html">Designing Plugins</a></li>
                    <li><a href="../userguide/implementing_gradle_plugins.html">Implementing Plugins</a></li>
                    <li><a href="../userguide/testing_gradle_plugins.html">Testing Plugins</a></li>
                    <li><a href="../userguide/publishing_gradle_plugins.html">Publishing Plugins</a></li>
                </ul>
            </li>
            <li><a class="nav-dropdown" data-toggle="collapse" href="#other-developing-topics" aria-expanded="false" aria-controls="other-developing-topics">Other Developing Gradle Topics</a>
                <ul id="other-developing-topics">
                    <li><a href="../userguide/custom_gradle_types.html">Writing Custom Gradle Types and Service Injection</a></li>
                    <li><a href="../userguide/build_services.html">Shared Build Services</a></li>
                    <li><a href="../userguide/dataflow_actions.html">Dataflow Actions</a></li>
                    <li><a href="../userguide/test_kit.html">Testing a Build with TestKit</a></li>
                    <li><a href="../userguide/ant.html">Using Ant from Gradle</a></li>
                </ul>
            </li>
        </ul>

        <h3 id="authoring-gradle-builds-java">Authoring JVM Builds</h3>
        <ul>
            <li><a href="../userguide/building_java_projects.html">Building Java &amp; JVM projects</a></li>
            <li><a href="../userguide/java_testing.html">Testing Java &amp; JVM projects</a></li>
            <li><a class="nav-dropdown" data-toggle="collapse" href="#java-toolchains" aria-expanded="false" aria-controls="java-toolchains">Java Toolchains</a>
                <ul id="java-toolchains">
                    <li><a href="../userguide/toolchains.html">Toolchains for JVM projects</a></li>
                    <li><a href="../userguide/toolchain_plugins.html">Toolchain Resolver Plugins</a></li>
                </ul>
            </li>
            <li><a href="../userguide/dependency_management_for_java_projects.html">Managing Dependencies</a></li>
            <li><a class="nav-dropdown" data-toggle="collapse" href="#jvm-plugins" aria-expanded="false" aria-controls="jvm-plugins">JVM Plugins</a>
                <ul id="jvm-plugins">
                    <li><a href="../userguide/java_library_plugin.html">Java Library Plugin</a></li>
                    <li><a href="../userguide/application_plugin.html">Java Application Plugin</a></li>
                    <li><a href="../userguide/java_platform_plugin.html">Java Platform Plugin</a></li>
                    <li><a href="../userguide/groovy_plugin.html">Groovy Plugin</a></li>
                    <li><a href="../userguide/scala_plugin.html">Scala Plugin</a></li>
                </ul>
            </li>
        </ul>

        <h3 id="managing-dependencies">Working with Dependencies</h3>
        <ul>
            <li><a href="../userguide/dependency_management_terminology.html">Terminology</a></li>
            <li><a class="nav-dropdown" data-toggle="collapse" href="#learning-the-basics-dependency-management" aria-expanded="false" aria-controls="learning-the-basics-dependency-management">Learning the Basics</a>
                <ul id="learning-the-basics-dependency-management">
                    <li><a href="../userguide/core_dependency_management.html">What is Dependency Management?</a></li>
                    <li><a href="../userguide/declaring_repositories.html">Declaring Repositories</a></li>
                    <li><a href="../userguide/declaring_dependencies.html">Declaring Dependencies</a></li>
                    <li><a href="../userguide/library_vs_application.html">Understanding Library and Application Differences</a></li>
                    <li><a href="../userguide/viewing_debugging_dependencies.html">Viewing and Debugging Dependencies</a></li>
                    <li><a href="../userguide/dependency_resolution.html">Understanding Resolution</a></li>
                    <li><a href="../userguide/dependency_verification.html">Verifying dependencies</a></li>
                </ul>
            </li>
            <li><a class="nav-dropdown" data-toggle="collapse" href="#declaring-dependency-versions" aria-expanded="false" aria-controls="declaring-dependency-versions">Declaring Versions</a>
                <ul id="declaring-dependency-versions">
                    <li><a href="../userguide/single_versions.html">Declaring Versions and Ranges</a></li>
                    <li><a href="../userguide/rich_versions.html">Declaring Rich Versions</a></li>
                    <li><a href="../userguide/dynamic_versions.html">Handling Changing Versions</a></li>
                    <li><a href="../userguide/dependency_locking.html">Locking Versions</a></li>
                </ul>
            </li>
            <li><a class="nav-dropdown" data-toggle="collapse" href="#controlling-transitive-dependencies" aria-expanded="false" aria-controls="controlling-transitive-dependencies">Controlling Transitives</a>
                <ul id="controlling-transitive-dependencies">
                    <li><a href="../userguide/dependency_constraints.html">Upgrading Versions</a></li>
                    <li><a href="../userguide/dependency_downgrade_and_exclude.html">Downgrading and Excluding</a></li>
                    <li><a href="../userguide/platforms.html">Sharing Versions</a></li>
                    <li><a href="../userguide/dependency_version_alignment.html">Aligning Dependencies</a></li>
                    <li><a href="../userguide/dependency_capability_conflict.html">Handling Mutually Exclusive Dependencies</a></li>
                    <li><a href="../userguide/component_metadata_rules.html">Fixing Metadata</a></li>
                    <li><a href="../userguide/resolution_rules.html">Customizing Resolution</a></li>
                    <li><a href="../userguide/resolution_strategy_tuning.html">Preventing accidental upgrades</a></li>
                </ul>
            </li>
            <li><a class="nav-dropdown" data-toggle="collapse" href="#modeling-features" aria-expanded="false" aria-controls="modeling-features">Producing and Consuming Variants of Libraries</a>
                <ul id="modeling-features">
                    <li><a href="../userguide/component_capabilities.html">Declaring Capabilities of a Library</a></li>
                    <li><a href="../userguide/feature_variants.html">Modeling Feature Variants and Optional Dependencies</a></li>
                    <li><a href="../userguide/variant_model.html">Understanding Variant Selection</a></li>
                    <li><a href="../userguide/variant_attributes.html">Declaring Variant Attributes</a></li>
                    <li><a href="../userguide/cross_project_publications.html">Sharing Outputs of Projects</a></li>
                    <li><a href="../userguide/artifact_transforms.html">Transforming Artifacts</a></li>
                </ul>
            </li>
            <li><a class="nav-dropdown" data-toggle="collapse" href="#publishing" aria-expanded="false" aria-controls="publishing">Publishing Libraries</a>
                <ul id="publishing">
                    <li><a href="../userguide/publishing_setup.html">Setting up Publishing</a></li>
                    <li><a href="../userguide/publishing_gradle_module_metadata.html">Understanding Gradle Module Metadata</a></li>
                    <li><a href="../userguide/publishing_signing.html">Signing Artifacts</a></li>
                    <li><a href="../userguide/publishing_customization.html">Customizing Publishing</a></li>
                    <li><a href="../userguide/publishing_maven.html">Maven Publish Plugin</a></li>
                    <li><a href="../userguide/publishing_ivy.html">Ivy Publish Plugin</a></li>
                </ul>
            </li>
        </ul>

        <h3 id="optimizing-build-performance">Optimizing Build Performance</h3>
        <ul>
            <li><a href="../userguide/performance.html">Improving Performance of Gradle Builds</a></li>
            <li><a href="../userguide/gradle_daemon.html">Gradle Daemon</a></li>
            <li><a href="../userguide/file_system_watching.html">File System Watching</a></li>
            <li><a href="../userguide/incremental_build.html">Incremental Build</a></li>
            <li><a class="nav-dropdown" data-toggle="collapse" href="#build-cache" aria-expanded="false" aria-controls="optimizing-build-performance">Using the Build Cache</a>
                <ul id="build-cache">
                    <li><a href="../userguide/build_cache.html">Enabling and Configuring</a></li>
                    <li><a href="../userguide/build_cache_use_cases.html">Why use the Build Cache?</a></li>
                    <li><a href="../userguide/build_cache_performance.html">Understanding the Impact</a></li>
                    <li><a href="../userguide/build_cache_concepts.html">Learning Basic Concepts</a></li>
                    <li><a href="../userguide/caching_java_projects.html">Caching Java Project</a></li>
                    <li><a href="../userguide/caching_android_projects.html">Caching Android Project</a></li>
                    <li><a href="../userguide/build_cache_debugging.html">Debugging Caching Issues</a></li>
                    <li><a href="../userguide/common_caching_problems.html">Troubleshooting</a></li>
                </ul>
            </li>
            <li><a href="../userguide/configuration_cache.html">Using the Configuration Cache</a></li>
            <li><a href="../userguide/inspect.html">Inspecting Gradle Builds</a></li>
            <li><a href="../userguide/config_gradle.html">Configuring Gradle</a></li>
            <li><a href="../userguide/project_properties.html">Project Properties</a></li>
            <li><a href="../userguide/networking.html">Gradle Networking</a></li>
        </ul>

        <h3 id="authoring-gradle-builds-native">Authoring C++/Swift Builds</h3>
        <ul>
            <li><a href="../userguide/building_cpp_projects.html">Building C++ projects</a></li>
            <li><a href="../userguide/cpp_testing.html">Testing C++ projects</a></li>
            <li><a href="../userguide/building_swift_projects.html">Building Swift projects</a></li>
            <li><a href="../userguide/swift_testing.html">Testing Swift projects</a></li>
        </ul>

        <h3 id="gradle-on-ci">Gradle on CI</h3>
        <ul>
            <!-- TODO ADD STUFF FROM Third party integration.html -->
            <li><a href="../userguide/jenkins.html">Jenkins</a></li>
            <li><a href="../userguide/teamcity.html">TeamCity</a></li>
            <li><a href="../userguide/github-actions.html">GitHub Actions</a></li>
            <li><a href="../userguide/travis-ci.html">Travis CI</a></li>
        </ul>

        <h3 id="reference">Reference</h3>
        <ul>
            <li><a class="nav-dropdown" data-toggle="collapse" href="#gradle-api" aria-expanded="false" aria-controls="gradle-api">Gradle DSLs and API</a>
                <ul id="gradle-api">
                    <li><a href="../javadoc/index.html?overview-summary.html">Javadoc</a></li>
                    <li><a href="../userguide/groovy_build_script_primer.html">Groovy DSL Primer</a></li>
                    <li><a href="../dsl/index.html">Groovy DSL Reference</a></li>
                    <li><a href="../userguide/kotlin_dsl.html">Kotlin DSL Primer</a></li>
                    <li><a href="../kotlin-dsl/index.html" target="_blank">Kotlin DSL API</a></li>
                    <li><a href="../userguide/migrating_from_groovy_to_kotlin_dsl.html">Groovy to Kotlin DSL Migration</a></li>
                    <li><a href="../samples/index.html">Samples</a></li>
                </ul>
            </li>
            <li><a href="../userguide/command_line_interface.html">Command-Line Interface</a></li>
            <li><a href="../userguide/gradle_wrapper.html">Gradle Wrapper</a></li>
            <li><a href="../userguide/plugin_reference.html">Core Plugins</a></li>
            <li id="third-party-integration"><a href="../userguide/third_party_integration.html">Gradle &amp; Third-party Tools</a></li>
            <li><a href="../userguide/userguide.pdf">User Manual PDF</a></li>
        </ul>
    </nav>
    <!-- End Primary Navigation -->

    <div class="content">
        <div class="chapter">
<div id="header">
<h1>Upgrading your build from Gradle 4.x to 5.0</h1>
<div class="details">
<span id="revnumber">version 8.5</span>
</div>
<div id="toc" class="toc">
<div id="toctitle">Contents</div>
<ul class="sectlevel1">
<li><a href="#for_all_users">For all users</a></li>
<li><a href="#changes_5.0">Upgrading from 4.10 and earlier</a></li>
<li><a href="#changes_4.10">Upgrading from 4.9 and earlier</a></li>
<li><a href="#changes_4.9">Upgrading from 4.8 and earlier</a></li>
<li><a href="#changes_4.8">Upgrading from 4.7 and earlier</a></li>
<li><a href="#changes_4.7">Upgrading from 4.6 and earlier</a></li>
<li><a href="#changes_4.6">Upgrading from 4.5 and earlier</a></li>
<li><a href="#changes_4.5">Upgrading from 4.4 and earlier</a></li>
<li><a href="#changes_4.4">Upgrading from 4.3 and earlier</a></li>
<li><a href="#changes_4.3">Upgrading from 4.2 and earlier</a></li>
<li><a href="#changes_4.2">Upgrading from 4.1 and earlier</a></li>
<li><a href="#changes_4.1">Upgrading from 4.0</a></li>
<li><a href="#changes_in_detail">Changes in detail</a></li>
</ul>
</div>
</div>
<div id="content">
<div id="preamble">
<div class="sectionbody">
<div class="paragraph">
<p>This chapter provides the information you need to migrate your older Gradle 4.x builds to Gradle 5.0.
In most cases, you will need to apply the changes from all versions that come after the one you&#8217;re upgrading from.
For example, if you&#8217;re upgrading from Gradle 4.3 to 5.0, you will also need to apply the changes since 4.4, 4.5, etc up to 5.0.</p>
</div>
<div class="admonitionblock tip">
<table>
<tr>
<td class="icon">
<i class="fa icon-tip" title="Tip"></i>
</td>
<td class="content">
If you are using Gradle for Android, you need to move to version 3.3 or higher of both the Android Gradle Plugin and Android Studio.
</td>
</tr>
</table>
</div>
</div>
</div>
<div class="sect1">
<h2 id="for_all_users"><a class="anchor" href="#for_all_users"></a><a class="link" href="#for_all_users">For all users</a></h2>
<div class="sectionbody">
<div class="olist arabic">
<ol class="arabic">
<li>
<p>If you are not already on the latest 4.10.x release, read the sections <a href="#changes_4.10">below</a> for help upgrading your project to the latest 4.10.x release.
We recommend upgrading to the latest 4.10.x release to get the most useful warnings and deprecations information before moving to 5.0.
Avoid upgrading Gradle and migrating to Kotlin DSL at the same time in order to ease troubleshooting in case of potential issues.</p>
</li>
<li>
<p>Try running <code>gradle help --scan</code> and view the <a href="https://gradle.com/enterprise/releases/2018.4/#identify-usages-of-deprecated-gradle-functionality">deprecations view</a> of the generated build scan.
If there are no warnings, the Deprecations tab will not appear.</p>
<div class="imageblock">
<div class="content">
<img src="img/deprecations.png" alt="Deprecations View of a Gradle Build Scan">
</div>
</div>
<div class="paragraph">
<p>This is so that you can see any deprecation warnings that apply to your build.
Gradle 5.x will generate (potentially less obvious) errors if you try to upgrade directly to it.</p>
</div>
<div class="paragraph">
<p>Alternatively, you can run <code>gradle help --warning-mode=all</code> to see the deprecations in the console, though it may not report as much detailed information.</p>
</div>
</li>
<li>
<p>Update your plugins.</p>
<div class="paragraph">
<p>Some plugins will break with this new version of Gradle, for example because they use internal APIs that have been removed or changed.
The previous step will help you identify potential problems by issuing deprecation warnings when a plugin does try to use a deprecated part of the API.</p>
</div>
<div class="paragraph">
<p>In particular, you will need to use at least a 2.x version of the <a href="https://plugins.gradle.org/plugin/com.github.johnrengelman.shadow"><strong>Shadow Plugin</strong></a>.</p>
</div>
</li>
<li>
<p>Run <code>gradle wrapper --gradle-version 5.0</code> to update the project to 5.0</p>
</li>
<li>
<p>Move to Java 8 or higher if you haven&#8217;t already. Whereas Gradle 4.x requires Java 7, Gradle 5 requires Java 8 to run.</p>
</li>
<li>
<p>Read the <a href="#changes_5.0">Upgrading from 4.10</a> section and make any necessary changes.</p>
</li>
<li>
<p>Try to run the project and debug any errors using the <a href="troubleshooting.html#troubleshooting">Troubleshooting Guide</a>.</p>
</li>
</ol>
</div>
<div class="paragraph">
<p>In addition, Gradle has added several significant new and improved features that you should consider using in your builds:</p>
</div>
<div class="ulist">
<ul>
<li>
<p><a href="#rel4.8:switch_to_publishing_plugins">Maven Publish and Ivy Publish Plugins</a> that now support digital signatures with the <a href="signing_plugin.html#signing_plugin">Signing Plugin</a>.</p>
</li>
<li>
<p>Use native <a href="#rel5.0:bom_import">BOM import</a> in your builds.</p>
</li>
<li>
<p>The <a href="custom_tasks.html#worker_api">Worker API</a> for enabling units of work to run in parallel.</p>
</li>
<li>
<p>A new API for <a href="#rel4.9:lazy_task_creation">creating and configuring tasks lazily</a> that can significantly improve your build&#8217;s configuration time.</p>
</li>
</ul>
</div>
<div class="paragraph">
<p>Other notable changes to be aware of that may break your build include:</p>
</div>
<div class="ulist">
<ul>
<li>
<p><a href="#rel5.0:pom_compile_runtime_separation">Separation of compile and runtime dependencies when consuming POMs</a></p>
</li>
<li>
<p>A change that means you should <a href="#rel4.8:configure_internal_tasks">configure existing <code>wrapper</code> and <code>init</code> tasks</a> rather than defining your own.</p>
</li>
<li>
<p>The <a href="#rel4.8:pom_wildcard_exclusions">honoring of implicit wildcards in Maven POM exclusions</a>, which may result in dependencies being excluded that weren&#8217;t before.</p>
</li>
<li>
<p>A <a href="#rel4.6:annotation_processor_configuration">change to the way you add Java annotation processors to a project</a>.</p>
</li>
<li>
<p>The <a href="#rel5.0:default_memory_settings">default memory settings</a> for the command-line client, the Gradle daemon, and all workers including compilers and test executors, have been greatly reduced.</p>
</li>
<li>
<p>The <a href="#rel5.0:default_tool_versions">default versions of several code quality plugins</a> have been updated.</p>
</li>
<li>
<p>Several <a href="#rel5.0:library_upgrades">library versions used by Gradle</a> have been upgraded.</p>
</li>
</ul>
</div>
</div>
</div>
<div class="sect1">
<h2 id="changes_5.0"><a class="anchor" href="#changes_5.0"></a><a class="link" href="#changes_5.0">Upgrading from 4.10 and earlier</a></h2>
<div class="sectionbody">
<div class="paragraph">
<p>If you are not already on version 4.10, skip down to the section that applies to your current Gradle version and work your way up until you reach here. Then, apply these changes when moving from Gradle 4.10 to 5.0.</p>
</div>
<div class="sect2">
<h3 id="other_changes"><a class="anchor" href="#other_changes"></a><a class="link" href="#other_changes">Other changes</a></h3>
<div class="ulist">
<ul>
<li>
<p>The <code>enableFeaturePreview('IMPROVED_POM_SUPPORT')</code> and <code>enableFeaturePreview('STABLE_PUBLISHING')</code> flags are no longer necessary. These features are now enabled by default.</p>
</li>
<li>
<p>Gradle now bundles <a href="#rel5.0:jaxb_and_java9">JAXB</a> for Java 9 and above. You can remove the <code>--add-modules java.xml.bind</code> option from <code>org.gradle.jvmargs</code>, if set.</p>
</li>
</ul>
</div>
</div>
<div class="sect2">
<h3 id="potential_breaking_changes"><a class="anchor" href="#potential_breaking_changes"></a><a class="link" href="#potential_breaking_changes">Potential breaking changes</a></h3>
<div class="paragraph">
<p>The changes in this section have the potential to break your build, but the vast majority have been deprecated for quite some time and few builds will be affected by a large number of them.
We strongly recommend upgrading to Gradle 4.10 first to get a report on what deprecations affect your build.</p>
</div>
<div class="paragraph">
<p>The following breaking changes are not from deprecations, but the result of changes in behavior:</p>
</div>
<div class="ulist">
<ul>
<li>
<p><a href="#rel5.0:pom_compile_runtime_separation">Separation of compile and runtime dependencies when consuming POMs</a></p>
</li>
<li>
<p>The evaluation of the <code>publishing {}</code> block is no longer deferred until needed but behaves like any other block.
Please use <code>afterEvaluate {}</code> if you need to defer evaluation.</p>
</li>
<li>
<p>The <a href="../dsl/org.gradle.api.tasks.javadoc.Javadoc.html"><code>Javadoc</code></a> and <a href="../dsl/org.gradle.api.tasks.javadoc.Groovydoc.html"><code>Groovydoc</code></a> tasks now delete the destination dir for the documentation before executing. This has been added to remove stale output files from the last task execution.</p>
</li>
<li>
<p>The <a href="java_library_distribution_plugin.html#java_library_distribution_plugin">Java Library Distribution Plugin</a> is now based on the <a href="java_library_plugin.html#java_library_plugin">Java Library Plugin</a> instead of the <a href="java_plugin.html#java_plugin">Java Plugin</a>.</p>
<div class="paragraph">
<p>While it applies the Java Plugin, it behaves slightly different (e.g. it adds the <code>api</code> configuration).
Thus, make sure to check whether your build behaves as expected after upgrading.</p>
</div>
</li>
<li>
<p>The <code>html</code> property on <code>CheckstyleReport</code> and <code>FindBugsReport</code> now returns a <a href="../dsl/org.gradle.api.reporting.CustomizableHtmlReport.html"><code>CustomizableHtmlReport</code></a> instance that is easier to configure from statically typed languages like Java and Kotlin.</p>
</li>
<li>
<p>The <a href="#rel5.0:configuration_avoidance">Configuration Avoidance API</a> has been updated to prevent the creation and configuration of tasks that are never used.</p>
</li>
<li>
<p>The <a href="#rel5.0:default_memory_settings">default memory settings</a> for the command-line client, the Gradle daemon, and all workers including compilers and test executors, have been greatly reduced.</p>
</li>
<li>
<p>The <a href="#rel5.0:default_tool_versions">default versions of several code quality plugins</a> have been updated.</p>
</li>
<li>
<p>Several <a href="#rel5.0:library_upgrades">library versions used by Gradle</a> have been upgraded.</p>
</li>
</ul>
</div>
<div class="paragraph">
<p>The following breaking changes will appear as deprecation warnings with Gradle 4.10:</p>
</div>
<div class="dlist">
<dl>
<dt class="hdlist1">General</dt>
<dd>
<div class="ulist">
<ul>
<li>
<p><code>&lt;&lt;</code> for task definitions no longer works. In other words, you can not use the syntax <code>task myTask &lt;&lt; { &#8230;&#8203; }</code>.</p>
<div class="paragraph">
<p>Use the <a href="../dsl/org.gradle.api.Task.html#org.gradle.api.Task:doLast(org.gradle.api.Action)">Task.doLast()</a> method instead, like this:</p>
</div>
<div class="listingblock">
<div class="content">
<pre>task myTask {
    doLast {
        ...
    }
}</pre>
</div>
</div>
</li>
<li>
<p>You can no longer use any of the following characters in domain object names, such as project and task names: &lt;space&gt; <code>/ \ : &lt; &gt; " ? * |</code> . You should also not use <code>.</code> as a leading or trailing character.</p>
</li>
</ul>
</div>
</dd>
<dt class="hdlist1">Running Gradle &amp; build environment</dt>
<dd>
<div class="ulist">
<ul>
<li>
<p>As mentioned before, Gradle can no longer be run on Java 7. However, you can still use <a href="building_java_projects.html#sec:java_cross_compilation">forked compilation and testing</a> to build and test software for Java 6 and above.</p>
</li>
<li>
<p>The <code>-Dtest.single</code> command-line option has been removed — use <a href="java_testing.html#test_filtering">test filtering</a> instead.</p>
</li>
<li>
<p>The <code>-Dtest.debug</code> command-line option has been removed — use the <a href="java_testing.html#sec:debugging_java_tests"><code>--debug-jvm</code> option</a> instead.</p>
</li>
<li>
<p>The <code>-u</code>/<code>--no-search-upward</code> command-line option has been removed — make sure all your builds have a <em>settings.gradle</em> file.</p>
</li>
<li>
<p>The <code>--recompile-scripts</code> command-line option has been removed.</p>
</li>
<li>
<p>You can no longer have a Gradle build nested in a subdirectory of another Gradle build unless the nested build has a <em>settings.gradle</em> file.</p>
</li>
<li>
<p>The <code>DirectoryBuildCache.setTargetSizeInMB(long)</code> method has been removed — use <a href="../dsl/org.gradle.caching.local.DirectoryBuildCache.html#org.gradle.caching.local.DirectoryBuildCache:removeUnusedEntriesAfterDays">DirectoryBuildCache.removeUnusedEntriesAfterDays</a> instead.</p>
</li>
<li>
<p>The <code>org.gradle.readLoggingConfigFile</code> system property no longer does anything — update affected tests to work with your <code>java.util.logging</code> settings.</p>
</li>
</ul>
</div>
</dd>
<dt class="hdlist1">Working with files</dt>
<dd>
<div class="ulist">
<ul>
<li>
<p>You can no longer cast <code>FileCollection</code> objects to other types using the <code>as</code> keyword or the <code>asType()</code> method.</p>
</li>
<li>
<p>You can no longer pass <code>null</code> as the configuration action of <a href="../javadoc/org/gradle/api/file/CopySpec.html#from-java.lang.Object-org.gradle.api.Action-">CopySpec.from(Object, Action)</a>.</p>
</li>
<li>
<p>For better compatibility with the Kotlin DSL, <a href="../javadoc/org/gradle/api/file/DuplicatesStrategy.html">CopySpec.duplicatesStrategy</a> is no longer nullable. The property setter no longer accepts <code>null</code> as a way
to reset the property back to its default value. Use <code>DuplicatesStrategy.INHERIT</code> instead.</p>
</li>
<li>
<p>The <code>FileCollection.stopExecutionIfEmpty()</code> method has been removed — use the <a href="../javadoc/org/gradle/api/tasks/SkipWhenEmpty.html">@SkipWhenEmpty</a> annotation on <code>FileCollection</code> task properties instead.</p>
</li>
<li>
<p>The <code>FileCollection.add()</code> method has been removed — use <a href="../dsl/org.gradle.api.Project.html#org.gradle.api.Project:files(java.lang.Object[])">Project.files()</a> and <a href="../dsl/org.gradle.api.Project.html#org.gradle.api.Project:fileTree(java.lang.Object)">Project.fileTree()</a> to create configurable file collections/file trees and add to them via <a href="../javadoc/org/gradle/api/file/ConfigurableFileCollection.html#from-java.lang.Object...-">ConfigurableFileCollection.from()</a>.</p>
</li>
<li>
<p><code>SimpleFileCollection</code> has been removed — use <a href="../dsl/org.gradle.api.Project.html#org.gradle.api.Project:files(java.lang.Object[])">Project.files(Object&#8230;&#8203;)</a> instead.</p>
</li>
<li>
<p>Don&#8217;t have your own classes extend <code>AbstractFileCollection</code> — use the <a href="../dsl/org.gradle.api.Project.html#org.gradle.api.Project:files(java.lang.Object[])">Project.files()</a> method instead. This problem may exhibit as a missing <code>getBuildDependencies()</code> method.</p>
</li>
</ul>
</div>
</dd>
<dt class="hdlist1">Java builds</dt>
<dd>
<div class="ulist">
<ul>
<li>
<p>The <code>CompileOptions.bootClasspath</code> property has been removed — use <a href="../dsl/org.gradle.api.tasks.compile.CompileOptions.html#org.gradle.api.tasks.compile.CompileOptions:bootstrapClasspath">CompileOptions.bootstrapClasspath</a> instead.</p>
</li>
<li>
<p>You can no longer use <code>-source-path</code> as a generic compiler argument — use <a href="../dsl/org.gradle.api.tasks.compile.CompileOptions.html#org.gradle.api.tasks.compile.CompileOptions:sourcepath">CompileOptions.sourcepath</a> instead.</p>
</li>
<li>
<p>You can no longer use <code>-processorpath</code> as a generic compiler argument — use <a href="../dsl/org.gradle.api.tasks.compile.CompileOptions.html#org.gradle.api.tasks.compile.CompileOptions:annotationProcessorPath">CompileOptions.annotationProcessorPath</a> instead.</p>
</li>
<li>
<p>Gradle will no longer automatically apply annotation processors that are on the compile classpath — use <a href="../dsl/org.gradle.api.tasks.compile.CompileOptions.html#org.gradle.api.tasks.compile.CompileOptions:annotationProcessorPath">CompileOptions.annotationProcessorPath</a> instead.</p>
</li>
<li>
<p>The <code>testClassesDir</code> property has been removed from the <a href="../dsl/org.gradle.api.tasks.testing.Test.html">Test</a> task — use <a href="../dsl/org.gradle.api.tasks.testing.Test.html#org.gradle.api.tasks.testing.Test:testClassesDirs">testClassesDirs</a> instead.</p>
</li>
<li>
<p>The <code>classesDir</code> property has been removed from both the <em>JDepend</em> task and <a href="../dsl/org.gradle.api.tasks.SourceSetOutput.html">SourceSetOutput</a>. Use the <em>JDepend.classesDirs</em> and <a href="../dsl/org.gradle.api.tasks.SourceSetOutput.html#org.gradle.api.tasks.SourceSetOutput:classesDirs">SourceSetOutput.classesDirs</a> properties instead.</p>
</li>
<li>
<p>The <code>JavaLibrary(PublishArtifact, DependencySet)</code> constructor has been removed — this was used by the <a href="https://plugins.gradle.org/plugin/com.github.johnrengelman.shadow">Shadow Plugin</a>, so make sure you upgrade to at least version 2.x of that plugin.</p>
</li>
<li>
<p>The <code>JavaBasePlugin.configureForSourceSet()</code> method has been removed.</p>
</li>
<li>
<p>You can no longer create your own instances of <a href="../javadoc/org/gradle/api/plugins/JavaPluginConvention.html">JavaPluginConvention</a>, <a href="../javadoc/org/gradle/api/plugins/ApplicationPluginConvention.html">ApplicationPluginConvention</a>, <a href="../javadoc/org/gradle/api/plugins/WarPluginConvention.html">WarPluginConvention</a>, <a href="../javadoc/org/gradle/plugins/ear/EarPluginConvention.html">EarPluginConvention</a>, <a href="../javadoc/org/gradle/api/plugins/BasePluginConvention.html">BasePluginConvention</a>, and <a href="../javadoc/org/gradle/api/plugins/ProjectReportsPluginConvention.html">ProjectReportsPluginConvention</a>.</p>
</li>
<li>
<p>The <code>Maven</code> Plugin used to publish the highly outdated Maven 2 metadata format. This has been changed and it will now publish Maven 3 metadata, just like the <code>Maven Publish</code> Plugin.</p>
<div class="paragraph">
<p>With the removal of Maven 2 support, the methods that configure unique snapshot behavior have also been removed. Maven 3 only supports unique snapshots, so we decided to remove them.</p>
</div>
</li>
</ul>
</div>
</dd>
<dt class="hdlist1">Tasks &amp; properties</dt>
<dd>
<div class="ulist">
<ul>
<li>
<p>The following legacy classes and methods related to <a href="lazy_configuration.html#lazy_properties">lazy properties</a> have been removed — use <a href="../javadoc/org/gradle/api/model/ObjectFactory.html#property-java.lang.Class-">ObjectFactory.property()</a> to create <code>Property</code> instances:</p>
<div class="ulist">
<ul>
<li>
<p><code>PropertyState</code></p>
</li>
<li>
<p><code>DirectoryVar</code></p>
</li>
<li>
<p><code>RegularFileVar</code></p>
</li>
<li>
<p><code>ProjectLayout.newDirectoryVar()</code></p>
</li>
<li>
<p><code>ProjectLayout.newFileVar()</code></p>
</li>
<li>
<p><code>Project.property(Class)</code></p>
</li>
<li>
<p><code>Script.property(Class)</code></p>
</li>
<li>
<p><code>ProviderFactory.property(Class)</code></p>
</li>
</ul>
</div>
</li>
<li>
<p>Tasks configured and registered with the <a href="task_configuration_avoidance.html#sec:task_configuration_avoidance_migration_guidelines">task configuration avoidance</a> APIs have more restrictions on the other methods that can be called from a configuration action.</p>
</li>
<li>
<p>The internal <code>@Option</code> and <code>@OptionValues</code> annotations — package <code>org.gradle.api.internal.tasks.options</code> — have been removed. Use the public <a href="../javadoc/org/gradle/api/tasks/options/Option.html">@Option</a> and <a href="../javadoc/org/gradle/api/tasks/options/OptionValues.html">@OptionValues</a> annotations instead.</p>
</li>
<li>
<p>The <code>Task.deleteAllActions()</code> method has been removed with no replacement.</p>
</li>
<li>
<p>The <code>Task.dependsOnTaskDidWork()</code> method has been removed — use <a href="incremental_build.html#incremental_build">declared inputs and outputs</a> instead.</p>
</li>
<li>
<p>The following properties and methods of <code>TaskInternal</code> have been removed — use task dependencies, task rules, reusable utility methods, or the <a href="custom_tasks.html#worker_api">Worker API</a> in place of executing a task directly.</p>
<div class="ulist">
<ul>
<li>
<p><code>execute()</code></p>
</li>
<li>
<p><code>executer</code></p>
</li>
<li>
<p><code>getValidators()</code></p>
</li>
<li>
<p><code>addValidator()</code></p>
</li>
</ul>
</div>
</li>
<li>
<p>The <a href="../javadoc/org/gradle/api/tasks/TaskInputs.html#file-java.lang.Object-">TaskInputs.file(Object)</a> method can no longer be called with an argument that resolves to anything other than a single regular file.</p>
</li>
<li>
<p>The <a href="../javadoc/org/gradle/api/tasks/TaskInputs.html#dir-java.lang.Object-">TaskInputs.dir(Object)</a> method can no longer be called with an argument that resolves to anything other than a single directory.</p>
</li>
<li>
<p>You can no longer register invalid inputs and outputs via <a href="../javadoc/org/gradle/api/tasks/TaskInputs.html">TaskInputs</a> and <a href="../javadoc/org/gradle/api/tasks/TaskOutputs.html">TaskOutputs</a>.</p>
</li>
<li>
<p>The <code>TaskDestroyables.file()</code> and <code>TaskDestroyables.files()</code> methods have been removed — use <a href="../javadoc/org/gradle/api/tasks/TaskDestroyables.html#register-java.lang.Object...-">TaskDestroyables.register()</a> instead.</p>
</li>
<li>
<p><code>SimpleWorkResult</code> has been removed — use <a href="../javadoc/org/gradle/api/tasks/WorkResult.html#getDidWork--">WorkResult.didWork</a>.</p>
</li>
<li>
<p>Overriding built-in tasks <a href="#deprecations_4.8">deprecated in 4.8</a> now produces an error.</p>
<div class="paragraph">
<p>Attempting to replace a built-in task will produce an error similar to the following:</p>
</div>
<div class="listingblock">
<div class="content">
<pre>&gt; Cannot add task 'wrapper' as a task with that name already exists.</pre>
</div>
</div>
</li>
</ul>
</div>
</dd>
<dt class="hdlist1">Scala &amp; Play</dt>
<dd>
<div class="ulist">
<ul>
<li>
<p>Play 2.2 is no longer supported — please upgrade the version of Play you are using.</p>
</li>
<li>
<p>The <code>ScalaDocOptions.styleSheet</code> property has been removed — the Scaladoc Ant task in Scala 2.11.8 and later no longer supports this property.</p>
</li>
</ul>
</div>
</dd>
<dt class="hdlist1">Kotlin DSL</dt>
<dd>
<div class="ulist">
<ul>
<li>
<p>Artifact configuration accessors now have the type <code>NamedDomainObjectProvider&lt;Configuration&gt;</code> instead of <code>Configuration</code></p>
</li>
<li>
<p><code>PluginAware.apply&lt;T&gt;(to)</code> was renamed <code>PluginAware.applyTo&lt;T&gt;(target)</code>.</p>
</li>
</ul>
</div>
<div class="paragraph">
<p>Both changes could cause script compilation errors. See the <a href="https://github.com/gradle/kotlin-dsl-samples/releases/tag/v1.0.2#breaking-changes">Gradle Kotlin DSL release notes</a> for more information and how to fix builds broken by the changes described above.</p>
</div>
</dd>
<dt class="hdlist1">Miscellaneous</dt>
<dd>
<div class="ulist">
<ul>
<li>
<p>The <code>ConfigurableReport.setDestination(Object)</code> method has been removed — use <a href="../javadoc/org/gradle/api/reporting/ConfigurableReport.html#setDestination-java.io.File-">ConfigurableReport.setDestination(File)</a> instead.</p>
</li>
<li>
<p>The <code>Signature.setFile(File)</code> method has been removed — Gradle does not support changing the output file for the generated signature.</p>
</li>
<li>
<p>The read-only <code>Signature.toSignArtifact</code> property has been removed — it should never have been part of the public API.</p>
</li>
<li>
<p>The <code>@DeferredConfigurable</code> annotation has been removed.</p>
</li>
<li>
<p>The method <code>isDeferredConfigurable()</code> was removed from <code>ExtensionSchema</code>.</p>
</li>
<li>
<p><code>IdeaPlugin.performPostEvaluationActions()</code> and <code>EclipsePlugin.performPostEvaluationActions()</code> have been removed.</p>
</li>
<li>
<p><code>The `BroadcastingCollectionEventRegister.getAddAction()</code> method has been removed with no replacement.</p>
</li>
<li>
<p>The internal <code>org.gradle.util</code> package is no longer imported by default.</p>
<div class="paragraph">
<p>Ideally you shouldn&#8217;t use classes from this package, but, as a quick fix, you can add explicit imports to your build scripts for those classes.</p>
</div>
</li>
<li>
<p>The <code>gradlePluginPortal()</code> repository <a href="#rel5.0:gradle_plugin_portal_metadata">no longer looks for JARs without a POM by default</a>.</p>
</li>
<li>
<p>The Tooling API can no longer connect to builds using a Gradle version below Gradle 2.6. The same applies to builds run through TestKit.</p>
</li>
<li>
<p>Gradle 5.0 requires a minimum Tooling API client version of 3.0. Older client libraries can no longer run builds with Gradle 5.0.</p>
</li>
<li>
<p>The IdeaModule Tooling API model element contains methods to retrieve resources and test resources so those elements were removed from the result of <code>IdeaModule.getSourceDirs()</code> and <code>IdeaModule.getTestSourceDirs()</code>.</p>
</li>
<li>
<p>In previous Gradle versions, the <code>source</code> field in <code>SourceTask</code> was accessible from subclasses. This is not the case anymore as the <code>source</code> field is now declared as <code>private</code>.</p>
</li>
<li>
<p>In the Worker API, <a href="#rel5.0:worker_api">the working directory of a worker can no longer be set</a>.</p>
</li>
<li>
<p>A change in behavior related to <a href="#rel5.0:dependency_constraints">dependency and version constraints</a> may impact a small number of users.</p>
</li>
<li>
<p>There have been several changes to <a href="#rel5.0:changes_to_default_task">property factory methods on DefaultTask</a> that may impact the creation of custom tasks.</p>
</li>
</ul>
</div>
</dd>
</dl>
</div>
</div>
</div>
</div>
<div class="sect1">
<h2 id="changes_4.10"><a class="anchor" href="#changes_4.10"></a><a class="link" href="#changes_4.10">Upgrading from 4.9 and earlier</a></h2>
<div class="sectionbody">
<div class="paragraph">
<p>If you are not already on version 4.9, skip down to the section that applies to your current Gradle version and work your way up until you reach here. Then, apply these changes when upgrading to Gradle 4.10.</p>
</div>
<div class="sect2">
<h3 id="deprecated_classes_methods_and_properties"><a class="anchor" href="#deprecated_classes_methods_and_properties"></a><a class="link" href="#deprecated_classes_methods_and_properties">Deprecated classes, methods and properties</a></h3>
<div class="paragraph">
<p>Follow the API links to learn how to deal with these deprecations (if no extra information is provided here):</p>
</div>
<div class="ulist">
<ul>
<li>
<p><code>TaskContainer.add()</code> and <code>TaskContainer.addAll()</code> — use <a href="../javadoc/org/gradle/api/tasks/TaskContainer.html#create-java.lang.String-java.lang.Class-org.gradle.api.Action-">TaskContainer.create()</a> or <a href="../javadoc/org/gradle/api/tasks/TaskContainer.html#register-java.lang.String-java.lang.Class-org.gradle.api.Action-">TaskContainer.register()</a> instead</p>
</li>
</ul>
</div>
</div>
<div class="sect2">
<h3 id="potential_breaking_changes_2"><a class="anchor" href="#potential_breaking_changes_2"></a><a class="link" href="#potential_breaking_changes_2">Potential breaking changes</a></h3>
<div class="ulist">
<ul>
<li>
<p>There have been several potentially breaking changes in Kotlin DSL — see the <em>Breaking changes</em> section of <a href="https://github.com/gradle/kotlin-dsl/releases/tag/v1.0-RC3">that project&#8217;s release notes</a>.</p>
</li>
<li>
<p>You can no longer use any of the <a href="../dsl/org.gradle.api.Project.html#org.gradle.api.Project:beforeEvaluate(org.gradle.api.Action)">Project.beforeEvaluate()</a> or <a href="../dsl/org.gradle.api.Project.html#org.gradle.api.Project:afterEvaluate(org.gradle.api.Action)">Project.afterEvaluate()</a> methods with lazy task configuration, for example inside a <a href="../javadoc/org/gradle/api/tasks/TaskContainer.html#register-java.lang.String-java.lang.Class-org.gradle.api.Action-">TaskContainer.register()</a> block.</p>
</li>
<li>
<p><a href="#rel4.10:aws_s3_permissions">Publishing to AWS S3 requires new permissions</a>.</p>
</li>
<li>
<p>Both <a href="../javadoc/org/gradle/plugin/devel/tasks/PluginUnderTestMetadata.html">PluginUnderTestMetadata</a> and <a href="../javadoc/org/gradle/plugin/devel/tasks/GeneratePluginDescriptors.html">GeneratePluginDescriptors</a> — classes used by the <a href="java_gradle_plugin.html#java_gradle_plugin">Java Gradle Plugin Development Plugin</a> — have been updated to use the Provider API.</p>
<div class="paragraph">
<p>Use the <a href="../javadoc/org/gradle/api/provider/Property.html#set-T-">Property.set()</a> method to modify their values rather than using standard property assignment syntax, unless you are doing so in a Groovy build script.
Standard property assignment still works in that one case.</p>
</div>
</li>
</ul>
</div>
</div>
</div>
</div>
<div class="sect1">
<h2 id="changes_4.9"><a class="anchor" href="#changes_4.9"></a><a class="link" href="#changes_4.9">Upgrading from 4.8 and earlier</a></h2>
<div class="sectionbody">
<div class="ulist">
<ul>
<li>
<p><a href="#rel4.9:lazy_task_creation">Consider trying the lazy API for task creation and configuration</a></p>
</li>
</ul>
</div>
<div class="sect2">
<h3 id="potential_breaking_changes_3"><a class="anchor" href="#potential_breaking_changes_3"></a><a class="link" href="#potential_breaking_changes_3">Potential breaking changes</a></h3>
<div class="ulist">
<ul>
<li>
<p>You can no longer use GPath syntax with <a href="../javadoc/org/gradle/api/tasks/TaskCollection.html#withType-java.lang.Class-">tasks.withType()</a>.</p>
<div class="paragraph">
<p>Use <a href="https://docs.groovy-lang.org/latest/html/documentation/#_spread_operator">Groovy&#8217;s spread operator</a> instead.
For example, you would replace <code>tasks.withType(JavaCompile).name</code> with <code>tasks.withType(JavaCompile)*.name</code>.</p>
</div>
</li>
</ul>
</div>
</div>
</div>
</div>
<div class="sect1">
<h2 id="changes_4.8"><a class="anchor" href="#changes_4.8"></a><a class="link" href="#changes_4.8">Upgrading from 4.7 and earlier</a></h2>
<div class="sectionbody">
<div class="ulist">
<ul>
<li>
<p><a href="#rel4.8:switch_to_publishing_plugins">Switch to the Maven Publish and Ivy Publish plugins</a></p>
</li>
<li>
<p><a href="#rel4.8:deferred_configuration">Use deferred configuration with the publishing plugins</a></p>
</li>
<li>
<p><a href="#rel4.8:configure_internal_tasks">Configure existing <code>wrapper</code> and <code>init</code> tasks</a> rather than defining your own</p>
</li>
<li>
<p>Consider migrating to the built-in <a href="dependency_locking.html#dependency-locking">dependency locking mechanism</a> if you are currently using a plugin or custom solution for this</p>
</li>
</ul>
</div>
<div class="sect2">
<h3 id="potential_breaking_changes_4"><a class="anchor" href="#potential_breaking_changes_4"></a><a class="link" href="#potential_breaking_changes_4">Potential breaking changes</a></h3>
<div class="ulist">
<ul>
<li>
<p>Build will now fail if a specified init script is not found.</p>
</li>
<li>
<p><code>TaskContainer.remove()</code> now actually removes the given task — some plugins may have accidentally relied on the old behavior.</p>
</li>
<li>
<p><a href="#rel4.8:pom_wildcard_exclusions">Gradle now honors implicit wildcards in Maven POM exclusions</a>.</p>
</li>
<li>
<p>The Kotlin DSL now respects JSR-305 package annotations.</p>
<div class="paragraph">
<p>This will lead to some types annotated according to JSR-305 being treated as nullable where they were treated as non-nullable before.
This may lead to compilation errors in the build script. See <a href="https://github.com/gradle/kotlin-dsl/releases/tag/v0.17.4">the relevant Kotlin DSL release notes</a> for details.</p>
</div>
</li>
<li>
<p>Error messages will be directed to standard error rather than standard output now, unless a console is attached to both standard output and standard error. This may affect tools that scrape a build&#8217;s plain console output. Ignore this change if you&#8217;re upgrading from an earlier version of Gradle.</p>
</li>
</ul>
</div>
</div>
<div class="sect2">
<h3 id="deprecations_4.8"><a class="anchor" href="#deprecations_4.8"></a><a class="link" href="#deprecations_4.8">Deprecations</a></h3>
<div class="paragraph">
<p>Prior to this release, builds were allowed to replace built-in tasks. <a href="https://docs.gradle.org/4.8/release-notes.html#overwriting-gradle's-built-in-tasks">This feature has been deprecated</a>.</p>
</div>
<div class="paragraph">
<p>The full list of built-in tasks that should not be replaced is:
<code>wrapper</code>, <code>init</code>, <code>help</code>, <code>tasks</code>, <code>projects</code>, <code>buildEnvironment</code>, <code>components</code>, <code>dependencies</code>, <code>dependencyInsight</code>, <code>dependentComponents</code>, <code>model</code>, <code>properties</code>.</p>
</div>
</div>
</div>
</div>
<div class="sect1">
<h2 id="changes_4.7"><a class="anchor" href="#changes_4.7"></a><a class="link" href="#changes_4.7">Upgrading from 4.6 and earlier</a></h2>
<div class="sectionbody">
<div class="sect2">
<h3 id="potential_breaking_changes_5"><a class="anchor" href="#potential_breaking_changes_5"></a><a class="link" href="#potential_breaking_changes_5">Potential breaking changes</a></h3>
<div class="ulist">
<ul>
<li>
<p>Gradle will now, by convention, look for Checkstyle configuration files in the root project&#8217;s <em>config/checkstyle</em> directory.</p>
<div class="paragraph">
<p>Checkstyle configuration files in subprojects — the old by-convention location — will be ignored unless you explicitly configure their path via <a href="../dsl/org.gradle.api.plugins.quality.CheckstyleExtension.html#org.gradle.api.plugins.quality.CheckstyleExtension:configDir">checkstyle.configDir</a> or <a href="../dsl/org.gradle.api.plugins.quality.CheckstyleExtension.html#org.gradle.api.plugins.quality.CheckstyleExtension:config">checkstyle.config</a>.</p>
</div>
</li>
<li>
<p>The structure of Gradle&#8217;s <a href="#rel4.7:plain_console_output">plain console output</a> has changed, which may break tools that scrape that output.</p>
</li>
<li>
<p>The APIs of many native tasks related to compilation, linking and installation <a href="#rel:4.6:native_task_api_changes">have changed in breaking ways</a>.</p>
</li>
<li>
<p>[Kotlin DSL] Delegated properties used to access Gradle&#8217;s build properties — defined in <em>gradle.properties</em> for example — must now be explicitly typed.</p>
</li>
<li>
<p>[Kotlin DSL] Declaring a <code>plugins {}</code> block inside a nested scope now throws an exception.</p>
</li>
<li>
<p>[Kotlin DSL] Only one <code>pluginManagement {}</code> block is allowed now.</p>
</li>
<li>
<p>The cache control DSL provided by the <code>org.gradle.api.artifacts.cache.*</code> interfaces are no longer available.</p>
</li>
<li>
<p><code>getEnabledDirectoryReportDestinations()</code>, <code>getEnabledFileReportDestinations()</code> and <code>getEnabledReportNames()</code> have all been removed from <code>org.gradle.api.reporting.ReportContainer</code>.</p>
</li>
<li>
<p><a href="../javadoc/org/gradle/StartParameter.html#getProjectProperties--">StartParameter.projectProperties</a> and <a href="../javadoc/org/gradle/StartParameter.html#getSystemPropertiesArgs--">StartParameter.systemPropertiesArgs</a> now return immutable maps.</p>
</li>
</ul>
</div>
</div>
</div>
</div>
<div class="sect1">
<h2 id="changes_4.6"><a class="anchor" href="#changes_4.6"></a><a class="link" href="#changes_4.6">Upgrading from 4.5 and earlier</a></h2>
<div class="sectionbody">
<div class="sect2">
<h3 id="deprecations"><a class="anchor" href="#deprecations"></a><a class="link" href="#deprecations">Deprecations</a></h3>
<div id="rel4.6:annotation_processor_configuration" class="ulist">
<ul>
<li>
<p>You should not put annotation processors on the compile classpath or declare them with the <code>-processorpath</code> compiler argument.</p>
<div class="paragraph">
<p>They should be added to the <code>annotationProcessor</code> configuration instead.
If you don&#8217;t want any processing, but your compile classpath contains a processor unintentionally (e.g. as part of a library you depend on), use the <code>-proc:none</code> compiler argument to ignore it.</p>
</div>
</li>
<li>
<p>Use <a href="../javadoc/org/gradle/process/CommandLineArgumentProvider.html">CommandLineArgumentProvider</a> in place of <a href="https://docs.gradle.org/4.10.3/javadoc/org/gradle/api/tasks/compile/CompilerArgumentProvider.html">CompilerArgumentProvider</a>.</p>
</li>
</ul>
</div>
</div>
<div class="sect2">
<h3 id="potential_breaking_changes_6"><a class="anchor" href="#potential_breaking_changes_6"></a><a class="link" href="#potential_breaking_changes_6">Potential breaking changes</a></h3>
<div class="ulist">
<ul>
<li>
<p>The Java plugins now add a <code><em>sourceSet</em>AnnotationProcessor</code> configuration for each source set, which might break if any of them match existing configurations you have. We recommend you remove your conflicting configuration declarations.</p>
</li>
<li>
<p>The <code>StartParameter.taskOutputCacheEnabled</code> property has been replaced by <a href="../javadoc/org/gradle/StartParameter.html#setBuildCacheEnabled-boolean-">StartParameter.setBuildCacheEnabled(boolean)</a>.</p>
</li>
<li>
<p>The Visual Studio integration now only <a href="#rel4.6:visual_studio_single_solution">configures a single solution for all components in a build</a>.</p>
</li>
<li>
<p>Gradle has replaced HttpClient 4.4.1 with version 4.5.5.</p>
</li>
<li>
<p>Gradle now bundles the <code>kotlin-stdlib-jdk8</code> artifact instead of <code>kotlin-stdlib-jre8</code>. This may affect your build. Please see the <a href="http://kotlinlang.org/docs/reference/whatsnew12.html#kotlin-standard-library-artifacts-and-split-packages">Kotlin documentation</a> for more details.</p>
</li>
</ul>
</div>
</div>
</div>
</div>
<div class="sect1">
<h2 id="changes_4.5"><a class="anchor" href="#changes_4.5"></a><a class="link" href="#changes_4.5">Upgrading from 4.4 and earlier</a></h2>
<div class="sectionbody">
<div class="ulist">
<ul>
<li>
<p>Make sure you have a <em>settings.gradle</em> file: it avoids a performance penalty and allows you to set the root project&#8217;s name.</p>
</li>
<li>
<p>Gradle now ignores the build cache configuration of included builds (<a href="composite_builds.html#composite_builds">composite builds</a>) and instead uses the root build&#8217;s configuration for all the builds.</p>
</li>
</ul>
</div>
<div class="sect2">
<h3 id="potential_breaking_changes_7"><a class="anchor" href="#potential_breaking_changes_7"></a><a class="link" href="#potential_breaking_changes_7">Potential breaking changes</a></h3>
<div class="ulist">
<ul>
<li>
<p>Two overloaded <code>ValidateTaskProperties.setOutputFile()</code> methods were removed. They are replaced with auto-generated setters when the task is accessed from a build script, but that won&#8217;t be the case from plugins and other code outside of the build script.</p>
</li>
<li>
<p>The Maven Publish Plugin now produces more complete maven-metadata.xml files, including maintaining a list of <code>&lt;snapshotVersion&gt;</code> elements. Some older versions of Maven may not be able to consume this metadata.</p>
</li>
<li>
<p><a href="#rel4.5:http_build_cache_no_follow_redirects"><code>HttpBuildCache</code> no longer follows redirects</a>.</p>
</li>
<li>
<p>The <code>Depend</code> task type has been removed.</p>
</li>
<li>
<p><a href="../dsl/org.gradle.api.Project.html#org.gradle.api.Project:file(java.lang.Object)">Project.file(Object)</a> no longer normalizes case for file paths on case-insensitive file systems. It now ignores case in such circumstances and does not touch the file system.</p>
</li>
<li>
<p><a href="../javadoc/org/gradle/api/provider/ListProperty.html">ListProperty</a> no longer extends <a href="../javadoc/org/gradle/api/provider/Property.html">Property</a>.</p>
</li>
</ul>
</div>
</div>
</div>
</div>
<div class="sect1">
<h2 id="changes_4.4"><a class="anchor" href="#changes_4.4"></a><a class="link" href="#changes_4.4">Upgrading from 4.3 and earlier</a></h2>
<div class="sectionbody">
<div class="sect2">
<h3 id="potential_breaking_changes_8"><a class="anchor" href="#potential_breaking_changes_8"></a><a class="link" href="#potential_breaking_changes_8">Potential breaking changes</a></h3>
<div class="ulist">
<ul>
<li>
<p><a href="../dsl/org.gradle.api.tasks.testing.AbstractTestTask.html">AbstractTestTask</a> is now extended by non-JVM test tasks as well as <a href="../dsl/org.gradle.api.tasks.testing.Test.html">Test</a>. Plugins should beware configuring all tasks of type <code>AbstractTestTask</code> because of this.</p>
</li>
<li>
<p>The default output location for <a href="../dsl/org.gradle.plugins.ide.eclipse.model.EclipseClasspath.html#org.gradle.plugins.ide.eclipse.model.EclipseClasspath:defaultOutputDir">EclipseClasspath.defaultOutputDir</a> has changed from <em><code>$projectDir</code>/bin</em> to <em><code>$projectDir</code>/bin/default</em>.</p>
</li>
<li>
<p>The deprecated <code>InstallExecutable.setDestinationDir(Provider)</code> was removed — use <a href="../dsl/org.gradle.nativeplatform.tasks.InstallExecutable.html#org.gradle.nativeplatform.tasks.InstallExecutable:installDirectory">InstallExecutable.installDirectory</a> instead.</p>
</li>
<li>
<p>The deprecated <code>InstallExecutable.setExecutable(Provider)</code> was removed — use <a href="../dsl/org.gradle.nativeplatform.tasks.InstallExecutable.html#org.gradle.nativeplatform.tasks.InstallExecutable:executableFile">InstallExecutable.executableFile</a> instead.</p>
</li>
<li>
<p>Gradle will no longer prefer a version of Visual Studio found on the path over other locations. It is now a last resort.</p>
<div class="paragraph">
<p>You can bypass the toolchain discovery by specifying the installation directory of the version of Visual Studio you want via <a href="../dsl/org.gradle.nativeplatform.toolchain.VisualCpp.html#org.gradle.nativeplatform.toolchain.VisualCpp:installDir">VisualCpp.setInstallDir(Object)</a>.</p>
</div>
</li>
<li>
<p><code>pluginManagement.repositories</code> is now of type <a href="../dsl/org.gradle.api.artifacts.dsl.RepositoryHandler.html">RepositoryHandler</a> rather than <code>PluginRepositoriesSpec</code>, which has been removed.</p>
</li>
<li>
<p>5xx HTTP errors during dependency resolution will now trigger exceptions in the build.</p>
</li>
<li>
<p>The embedded Apache Ant has been upgraded from 1.9.6 to 1.9.9.</p>
</li>
<li>
<p><a href="#rel4.4:security_library_upgrades">Several third-party libraries used by Gradle have been upgraded</a> to fix security issues.</p>
</li>
</ul>
</div>
</div>
</div>
</div>
<div class="sect1">
<h2 id="changes_4.3"><a class="anchor" href="#changes_4.3"></a><a class="link" href="#changes_4.3">Upgrading from 4.2 and earlier</a></h2>
<div class="sectionbody">
<div class="ulist">
<ul>
<li>
<p>The <code>plugins {}</code> block can now be <a href="plugins.html#sec:subprojects_plugins_dsl">used in subprojects</a> and for <a href="plugins.html#sec:buildsrc_plugins_dsl">plugins in the <em>buildSrc</em> directory</a>.</p>
</li>
</ul>
</div>
<div class="sect2">
<h3 id="other_deprecations"><a class="anchor" href="#other_deprecations"></a><a class="link" href="#other_deprecations">Other deprecations</a></h3>
<div class="ulist">
<ul>
<li>
<p>You should no longer run Gradle versions older than 2.6 via the Tooling API.</p>
</li>
<li>
<p>You should no longer run any version of Gradle via an older version of the Tooling API than 3.0.</p>
</li>
<li>
<p>You should no longer chain <a href="../javadoc/org/gradle/api/tasks/TaskInputs.html#property-java.lang.String-java.lang.Object-">TaskInputs.property(String,Object)</a> and <a href="../javadoc/org/gradle/api/tasks/TaskInputs.html#properties-java.util.Map-">TaskInputs.properties(Map)</a> methods.</p>
</li>
</ul>
</div>
</div>
<div class="sect2">
<h3 id="potential_breaking_changes_9"><a class="anchor" href="#potential_breaking_changes_9"></a><a class="link" href="#potential_breaking_changes_9">Potential breaking changes</a></h3>
<div class="ulist">
<ul>
<li>
<p><a href="../javadoc/org/gradle/api/DefaultTask.html#newOutputDirectory--">DefaultTask.newOutputDirectory()</a> now returns a <code>DirectoryProperty</code> instead of a <code>DirectoryVar</code>.</p>
</li>
<li>
<p><a href="../javadoc/org/gradle/api/DefaultTask.html#newOutputFile--">DefaultTask.newOutputFile()</a> now returns a <code>RegularFileProperty</code> instead of a <code>RegularFileVar</code>.</p>
</li>
<li>
<p><a href="../javadoc/org/gradle/api/DefaultTask.html#newInputFile--">DefaultTask.newInputFile()</a> now returns a <code>RegularFileProperty</code> instead of a <code>RegularFileVar</code>.</p>
</li>
<li>
<p><a href="../javadoc/org/gradle/api/file/ProjectLayout.html#getBuildDirectory--">ProjectLayout.buildDirectory</a> now returns a <code>DirectoryProperty</code> instead of a <code>DirectoryVar</code>.</p>
</li>
<li>
<p><a href="../dsl/org.gradle.language.nativeplatform.tasks.AbstractNativeCompileTask.html#org.gradle.language.nativeplatform.tasks.AbstractNativeCompileTask:compilerArgs">AbstractNativeCompileTask.compilerArgs</a> is now of type <code>ListProperty&lt;String&gt;</code> instead of <code>List&lt;String&gt;</code>.</p>
</li>
<li>
<p><a href="../dsl/org.gradle.language.nativeplatform.tasks.AbstractNativeCompileTask.html#org.gradle.language.nativeplatform.tasks.AbstractNativeCompileTask:objectFileDir">AbstractNativeCompileTask.objectFileDir</a> is now of type <code>DirectoryProperty</code> instead of <code>File</code>.</p>
</li>
<li>
<p><a href="../dsl/org.gradle.nativeplatform.tasks.AbstractLinkTask.html#org.gradle.nativeplatform.tasks.AbstractLinkTask:linkerArgs">AbstractLinkTask.linkerArgs</a> is now of type <code>ListProperty&lt;String&gt;</code> instead of <code>List&lt;String&gt;</code>.</p>
</li>
<li>
<p><code>TaskDestroyables.getFiles()</code> is no longer part of the public API.</p>
</li>
<li>
<p>Overlapping version ranges for a dependency now result in Gradle picking a version that satisfies all declared ranges.</p>
<div class="paragraph">
<p>For example, if a dependency on <code>some-module</code> is found with a version range of <code>[3,6]</code> and also transitively with a range of <code>[4,8]</code>, Gradle now selects version 6 instead of 8. The prior behavior was to select 8.</p>
</div>
</li>
<li>
<p>The order of elements in <code>Iterable</code> properties marked with either <code>@OutputFiles</code> or <code>@OutputDirectories</code> now matters. If the order changes, the property is no longer considered up to date.</p>
<div class="paragraph">
<p>Prefer using separate properties with <code>@OutputFile</code>/<code>@OutputDirectory</code> annotations or use <code>Map</code> properties with <code>@OutputFiles</code>/<code>@OutputDirectories</code> instead.</p>
</div>
</li>
<li>
<p>Gradle will no longer ignore dependency resolution errors from a repository when there is another repository it can check. Dependency resolution will fail instead. This results in more deterministic behavior with respect to resolution results.</p>
</li>
</ul>
</div>
</div>
</div>
</div>
<div class="sect1">
<h2 id="changes_4.2"><a class="anchor" href="#changes_4.2"></a><a class="link" href="#changes_4.2">Upgrading from 4.1 and earlier</a></h2>
<div class="sectionbody">
<div class="sect2">
<h3 id="potential_breaking_changes_10"><a class="anchor" href="#potential_breaking_changes_10"></a><a class="link" href="#potential_breaking_changes_10">Potential breaking changes</a></h3>
<div class="ulist">
<ul>
<li>
<p>The <code>withPathSensitivity()</code> methods on <a href="../javadoc/org/gradle/api/tasks/TaskFilePropertyBuilder.html">TaskFilePropertyBuilder</a> and <a href="../javadoc/org/gradle/api/tasks/TaskOutputFilePropertyBuilder.html">TaskOutputFilePropertyBuilder</a> have been removed.</p>
</li>
<li>
<p>The bundled <code>bndlib</code> has been upgraded from 3.2.0 to 3.4.0.</p>
</li>
<li>
<p>The FindBugs Plugin no longer renders progress information from its analysis. If you rely on that output in any way, you can enable it with <em>FindBugs.showProgress</em>.</p>
</li>
</ul>
</div>
</div>
</div>
</div>
<div class="sect1">
<h2 id="changes_4.1"><a class="anchor" href="#changes_4.1"></a><a class="link" href="#changes_4.1">Upgrading from 4.0</a></h2>
<div class="sectionbody">
<div class="ulist">
<ul>
<li>
<p>Consider using the new <a href="custom_tasks.html#worker_api">Worker API</a> to enable units of work within your build to run in parallel.</p>
</li>
</ul>
</div>
<div class="sect2">
<h3 id="deprecated_classes_methods_and_properties_2"><a class="anchor" href="#deprecated_classes_methods_and_properties_2"></a><a class="link" href="#deprecated_classes_methods_and_properties_2">Deprecated classes, methods and properties</a></h3>
<div class="paragraph">
<p>Follow the API links to learn how to deal with these deprecations (if no extra information is provided here):</p>
</div>
<div class="ulist">
<ul>
<li>
<p><a href="https://docs.gradle.org/4.10.3/javadoc/org/gradle/api/Nullable.html">Nullable</a></p>
</li>
</ul>
</div>
</div>
<div class="sect2">
<h3 id="potential_breaking_changes_11"><a class="anchor" href="#potential_breaking_changes_11"></a><a class="link" href="#potential_breaking_changes_11">Potential breaking changes</a></h3>
<div class="ulist">
<ul>
<li>
<p>Non-Java projects that have a <a href="declaring_dependencies.html#sub:project_dependencies">project dependency</a> on a Java project now consume the <code>runtimeElements</code> configuration by default instead of the <code>default</code> configuration.</p>
<div class="paragraph">
<p>To override this behavior, you can explicitly declare the configuration to use in the project dependency.
For example: <code>project(path: ':myJavaProject', configuration: 'default')</code>.</p>
</div>
</li>
<li>
<p>Default Zinc compiler upgraded from 0.3.13 to 0.3.15.</p>
</li>
<li>
<p>[Kotlin DSL] Base package renamed from <code>org.gradle.script.lang.kotlin</code> to <code>org.gradle.kotlin.dsl</code>.</p>
</li>
</ul>
</div>
</div>
</div>
</div>
<div class="sect1">
<h2 id="changes_in_detail"><a class="anchor" href="#changes_in_detail"></a><a class="link" href="#changes_in_detail">Changes in detail</a></h2>
<div class="sectionbody">
<div class="sect2">
<h3 id="rel5.0:default_memory_settings"><a class="anchor" href="#rel5.0:default_memory_settings"></a><a class="link" href="#rel5.0:default_memory_settings">[5.0] Default memory settings changed</a></h3>
<div class="paragraph">
<p>The command line client now starts with 64MB of heap instead of 1GB.
This may affect builds running directly inside the client VM using <code>--no-daemon</code> mode.
We discourage the use of <code>--no-daemon</code>, but if you must use it, you can increase the available memory using the <code>GRADLE_OPTS</code> environment variable.</p>
</div>
<div class="paragraph">
<p>The Gradle daemon now starts with 512MB of heap instead of 1GB.
Large projects may have to increase this setting using the <a href="config_gradle.html#sec:configuring_jvm_memory"><code>org.gradle.jvmargs</code></a> property.</p>
</div>
<div class="paragraph">
<p>All workers, including compilers and test executors, now start with 512MB of heap. The previous default was 1/4th of physical memory.
Large projects may have to increase this setting on the relevant tasks, e.g. <a href="config_gradle.html#sec:configuring_jvm_memory"><code>JavaCompile</code></a> or <a href="../dsl/org.gradle.api.tasks.testing.Test.html"><code>Test</code></a>.</p>
</div>
</div>
<div class="sect2">
<h3 id="rel5.0:default_tool_versions"><a class="anchor" href="#rel5.0:default_tool_versions"></a><a class="link" href="#rel5.0:default_tool_versions">[5.0] New default versions for code quality plugins</a></h3>
<div class="paragraph">
<p>The default tool versions of the following code quality plugins have been updated:</p>
</div>
<div class="ulist">
<ul>
<li>
<p>The <a href="http://checkstyle.sourceforge.net">Checkstyle Plugin</a> now uses <a href="http://checkstyle.sourceforge.net/releasenotes.html#Release_8.12">8.12</a> instead of 6.19 by default.</p>
</li>
<li>
<p>The <a href="https://codenarc.org/">CodeNarc Plugin</a> now uses <a href="https://github.com/CodeNarc/CodeNarc/blob/master/CHANGELOG.md#version-121-aug-2018">1.2.1</a> instead of 1.1 by default.</p>
</li>
<li>
<p>The <a href="https://www.jacoco.org/jacoco/">JaCoCo Plugin</a> now uses <a href="https://www.jacoco.org/jacoco/trunk/doc/changes.html">0.8.2</a> instead of 0.8.1 by default.</p>
</li>
<li>
<p>The <a href="https://pmd.github.io/">PMD Plugin</a> now uses <a href="https://pmd.github.io/pmd-6.8.0/pmd_release_notes.html#30-september-2018---680">6.8.0</a> instead of 5.6.1 by default.</p>
<div class="paragraph">
<p>In addition, the default ruleset was changed from the now deprecated <code>java-basic</code> to <code>category/java/errorprone.xml</code>.</p>
</div>
<div class="paragraph">
<p>We recommend configuring a ruleset explicitly, though.</p>
</div>
</li>
</ul>
</div>
</div>
<div class="sect2">
<h3 id="rel5.0:library_upgrades"><a class="anchor" href="#rel5.0:library_upgrades"></a><a class="link" href="#rel5.0:library_upgrades">[5.0] Library upgrades</a></h3>
<div class="paragraph">
<p>Several libraries that are used by Gradle have been upgraded:</p>
</div>
<div class="ulist">
<ul>
<li>
<p>Groovy was upgraded from 2.4.15 to <a href="https://groovy-lang.org/releasenotes/groovy-2.5.html">2.5.4</a>.</p>
</li>
<li>
<p>Ant has been upgraded from 1.9.11 to <a href="https://archive.apache.org/dist/ant/RELEASE-NOTES-1.9.13.html">1.9.13</a>.</p>
</li>
<li>
<p>The AWS SDK used to access S3-backed Maven/Ivy repositories has been upgraded from 1.11.267 to <a href="https://github.com/aws/aws-sdk-java/blob/master/CHANGELOG.md#111407-2018-09-11">1.11.407</a>.</p>
</li>
<li>
<p>The BND library used by the OSGi Plugin has been upgraded from 3.4.0 to <a href="https://github.com/bndtools/bnd/wiki/Changes-in-4.0.0">4.0.0</a>.</p>
</li>
<li>
<p>The Google Cloud Storage JSON API Client Library used to access Google Cloud Storage backed Maven/Ivy repositories has been upgraded from v1-rev116-1.23.0 to v1-rev136-1.25.0.</p>
</li>
<li>
<p>Ivy has been upgraded from 2.2.0 to <a href="http://ant.apache.org/ivy/history/2.3.0/release-notes.html">2.3.0</a>.</p>
</li>
<li>
<p>The JUnit Platform libraries used by the <code>Test</code> task have been upgraded from 1.0.3 to 1.3.1.</p>
</li>
<li>
<p>The Maven Wagon libraries used to access Maven repositories have been upgraded from 2.4 to 3.0.0.</p>
</li>
<li>
<p>SLF4J has been upgraded from 1.7.16 to <a href="https://www.slf4j.org/news.html">1.7.25</a>.</p>
</li>
</ul>
</div>
</div>
<div class="sect2">
<h3 id="rel5.0:dependency_constraints"><a class="anchor" href="#rel5.0:dependency_constraints"></a><a class="link" href="#rel5.0:dependency_constraints">[5.0] Improved support for dependency and version constraints</a></h3>
<div class="paragraph">
<p>Through the Gradle 4.x release stream, new <code>@Incubating</code> features were added to the dependency resolution engine.
These include sophisticated version constraints (<code>prefer</code>, <code>strictly</code>, <code>reject</code>), dependency constraints, and <code>platform</code> dependencies.</p>
</div>
<div class="paragraph">
<p>If you have been using the <code>IMPROVED_POM_SUPPORT</code> feature preview, playing with constraints or prefer, reject and other specific version indications, then make sure to take a good look at your dependency resolution results.</p>
</div>
</div>
<div class="sect2">
<h3 id="rel5.0:bom_import"><a class="anchor" href="#rel5.0:bom_import"></a><a class="link" href="#rel5.0:bom_import">[5.0] BOM import</a></h3>
<div class="paragraph">
<p>Gradle now provides support for importing bill of materials (BOM) files, which are effectively POM files that use <code>&lt;dependencyManagement&gt;</code> sections to control the versions of direct and transitive dependencies. All you need to do is declare the POM as a <code>platform</code> dependency.</p>
</div>
<div class="paragraph">
<p>The following example picks the versions of the <code>gson</code> and <code>dom4j</code> dependencies from the declared Spring Boot BOM:</p>
</div>
<div class="listingblock">
<div class="content">
<pre>dependencies {
    // import a BOM
    implementation platform('org.springframework.boot:spring-boot-dependencies:1.5.8.RELEASE')

    // define dependencies without versions
    implementation 'com.google.code.gson:gson'
    implementation 'dom4j:dom4j'
}</pre>
</div>
</div>
</div>
<div class="sect2">
<h3 id="rel5.0:pom_compile_runtime_separation"><a class="anchor" href="#rel5.0:pom_compile_runtime_separation"></a><a class="link" href="#rel5.0:pom_compile_runtime_separation">[5.0] Separation of compile and runtime dependencies when consuming POMs</a></h3>
<div class="paragraph">
<p>Since Gradle 1.0, runtime-scoped dependencies have been included in the Java compilation classpath, which has some drawbacks:</p>
</div>
<div class="ulist">
<ul>
<li>
<p>The compilation classpath is much larger than it needs to be, slowing down compilation.</p>
</li>
<li>
<p>The compilation classpath includes runtime-scoped files that do not impact compilation, resulting in unnecessary re-compilation when those files change.</p>
</li>
</ul>
</div>
<div class="paragraph">
<p>With this new behavior, the Java and Java Library plugins both honor the <a href="java_library_plugin.html#sec:java_library_separation">separation of compile and runtime scopes</a>. This means that the compilation classpath only includes compile-scoped dependencies, while the runtime classpath adds the runtime-scoped dependencies as well.
This is particularly useful if you develop and publish Java libraries with Gradle where the separation between <code>api</code> and <code>implementation</code> dependencies is reflected in the published scopes.</p>
</div>
</div>
<div class="sect2">
<h3 id="rel5.0:changes_to_default_task"><a class="anchor" href="#rel5.0:changes_to_default_task"></a><a class="link" href="#rel5.0:changes_to_default_task">[5.0] Changes to property factory methods on <code>DefaultTask</code></a></h3>
<div class="sect3">
<h4 id="property_factory_methods_on_defaulttask_are_now_final"><a class="anchor" href="#property_factory_methods_on_defaulttask_are_now_final"></a><a class="link" href="#property_factory_methods_on_defaulttask_are_now_final">Property factory methods on <code>DefaultTask</code> are now final</a></h4>
<div class="paragraph">
<p>The property factory methods such as <code>newInputFile()</code> are intended to be called from the constructor of a type that extends <code>DefaultTask</code>.
These methods are now final to avoid subclasses overriding these methods and using state that is not initialized.</p>
</div>
</div>
<div class="sect3">
<h4 id="inputs_and_outputs_are_not_automatically_registered"><a class="anchor" href="#inputs_and_outputs_are_not_automatically_registered"></a><a class="link" href="#inputs_and_outputs_are_not_automatically_registered">Inputs and outputs are not automatically registered</a></h4>
<div class="paragraph">
<p>The Property instances that are returned by these methods are no longer automatically registered as inputs or outputs of the task.
The Property instances need to be declared as inputs or outputs in the usual ways, such as attaching annotations such as <code>@OutputFile</code> or using the runtime API to register the property.</p>
</div>
<div class="paragraph">
<p>For example, you could previously use the following syntax and have both outputFile instances registered as declared outputs:</p>
</div>
<div class="exampleblock">
<div class="content">
<div class="exampleblock multi-language-sample">
<div class="content">
<div class="listingblock">
<div class="title">build.gradle</div>
<div class="content">
<pre class="prettyprint highlight"><code data-lang="groovy">class MyTask extends DefaultTask {
    // note: no annotation here
    final RegularFileProperty outputFile = newOutputFile()
}

task myOtherTask {
    def outputFile = newOutputFile()
    doLast { ... }
}</code></pre>
</div>
</div>
</div>
</div>
<div class="exampleblock multi-language-sample">
<div class="content">
<div class="listingblock">
<div class="title">build.gradle.kts</div>
<div class="content">
<pre class="prettyprint highlight"><code data-lang="kotlin">open class MyTask : DefaultTask() {
    // note: no annotation here
    val outputFile: RegularFileProperty = newOutputFile()
}

task("myOtherTask") {
    val outputFile = newOutputFile()
    doLast { ... }
}</code></pre>
</div>
</div>
</div>
</div>
</div>
</div>
<div class="paragraph">
<p>Now you have to explicitly register <code>outputFile</code>, like this:</p>
</div>
<div class="exampleblock">
<div class="content">
<div class="exampleblock multi-language-sample">
<div class="content">
<div class="listingblock">
<div class="title">build.gradle</div>
<div class="content">
<pre class="prettyprint highlight"><code data-lang="groovy">class MyTask extends DefaultTask {
    @OutputFile // property needs an annotation
    final RegularFileProperty outputFile = project.objects.fileProperty()
}

task myOtherTask {
    def outputFile = project.objects.fileProperty()
    outputs.file(outputFile) // or to be registered using the runtime API
    doLast { ... }
}</code></pre>
</div>
</div>
</div>
</div>
<div class="exampleblock multi-language-sample">
<div class="content">
<div class="listingblock">
<div class="title">build.gradle.kts</div>
<div class="content">
<pre class="prettyprint highlight"><code data-lang="kotlin">open class MyTask : DefaultTask() {
    @OutputFile // property needs an annotation
    val outputFile: RegularFileProperty = project.objects.fileProperty()
}

task("myOtherTask") {
    val outputFile = project.objects.fileProperty()
    outputs.file(outputFile) // or to be registered using the runtime API
    doLast { ... }
}</code></pre>
</div>
</div>
</div>
</div>
</div>
</div>
</div>
</div>
<div class="sect2">
<h3 id="rel5.0:jaxb_and_java9"><a class="anchor" href="#rel5.0:jaxb_and_java9"></a><a class="link" href="#rel5.0:jaxb_and_java9">[5.0] Gradle now bundles JAXB for Java 9 and above</a></h3>
<div class="paragraph">
<p>In order to use S3 backed artifact repositories, you previously had to add <code>--add-modules java.xml.bind</code> to <code>org.gradle.jvmargs</code> when running on Java 9 and above.</p>
</div>
<div class="paragraph">
<p>Since Java 11 no longer contains the <code>java.xml.bind</code> module, Gradle now bundles JAXB 2.3.1 (<code>com.sun.xml.bind:jaxb-impl</code>) and uses it on Java 9 and above.</p>
</div>
<div class="paragraph">
<p>Please remove the <code>--add-modules java.xml.bind</code> option from <code>org.gradle.jvmargs</code>, if set.</p>
</div>
</div>
<div class="sect2">
<h3 id="rel5.0:gradle_plugin_portal_metadata"><a class="anchor" href="#rel5.0:gradle_plugin_portal_metadata"></a><a class="link" href="#rel5.0:gradle_plugin_portal_metadata">[5.0] The <code>gradlePluginPortal()</code> repository no longer looks for JARs without a POM by default</a></h3>
<div class="paragraph">
<p>With this new behavior, if a plugin or a transitive dependency of a plugin found in the <code>gradlePluginPortal()</code> repository has no Maven POM it will fail to resolve.</p>
</div>
<div class="paragraph">
<p>Artifacts published to a Maven repository without a POM should be fixed.
If you encounter such artifacts, please ask the plugin or library author to publish a new version with proper metadata.</p>
</div>
<div class="paragraph">
<p>If you are stuck with a bad plugin, you can work around by re-enabling JARs as metadata source for the <code>gradlePluginPortal()</code> repository:</p>
</div>
<div class="exampleblock">
<div class="content">
<div class="exampleblock multi-language-sample">
<div class="content">
<div class="listingblock">
<div class="title">settings.gradle</div>
<div class="content">
<pre class="prettyprint highlight"><code data-lang="groovy">pluginManagement {
    repositories {
        gradlePluginPortal().tap {
            metadataSources {
                mavenPom()
                artifact()
            }
        }
    }
}</code></pre>
</div>
</div>
</div>
</div>
<div class="exampleblock multi-language-sample">
<div class="content">
<div class="listingblock">
<div class="title">settings.gradle.kts</div>
<div class="content">
<pre class="prettyprint highlight"><code data-lang="kotlin">pluginManagement {
    repositories {
        gradlePluginPortal().apply {
            (this as MavenArtifactRepository).metadataSources {
                mavenPom()
                artifact()
            }
        }
    }
}</code></pre>
</div>
</div>
</div>
</div>
</div>
</div>
</div>
<div class="sect2">
<h3 id="rel5.0:java_library_distribution_plugin"><a class="anchor" href="#rel5.0:java_library_distribution_plugin"></a><a class="link" href="#rel5.0:java_library_distribution_plugin">Java Library Distribution Plugin utilizes Java Library Plugin</a></h3>
<div class="paragraph">
<p>The <a href="java_library_distribution_plugin.html#java_library_distribution_plugin">Java Library Distribution Plugin</a> is now based on the
<a href="java_library_plugin.html#java_library_plugin">Java Library Plugin</a> instead of the <a href="java_plugin.html#java_plugin">Java Plugin</a>.</p>
</div>
<div class="paragraph">
<p>Additionally, the default distribution created by the plugin will contain all artifacts of the <code>runtimeClasspath</code> configuration instead of the deprecated <code>runtime</code> configuration.</p>
</div>
</div>
<div class="sect2">
<h3 id="rel5.0:configuration_avoidance"><a class="anchor" href="#rel5.0:configuration_avoidance"></a><a class="link" href="#rel5.0:configuration_avoidance">Configuration Avoidance API disallows common configuration errors</a></h3>
<div class="paragraph">
<p>The <a href="task_configuration_avoidance.html#task_configuration_avoidance">configuration avoidance API</a> introduced in Gradle 4.9 allows you to avoid creating and configuring tasks that are never used.</p>
</div>
<div class="paragraph">
<p>With the existing API, this example adds two tasks (<code>foo</code> and <code>bar</code>):</p>
</div>
<div class="exampleblock">
<div class="content">
<div class="exampleblock multi-language-sample">
<div class="content">
<div class="listingblock">
<div class="title">build.gradle</div>
<div class="content">
<pre class="prettyprint highlight"><code data-lang="groovy">tasks.create("foo") {
    tasks.create("bar")
}</code></pre>
</div>
</div>
</div>
</div>
<div class="exampleblock multi-language-sample">
<div class="content">
<div class="listingblock">
<div class="title">build.gradle.kts</div>
<div class="content">
<pre class="prettyprint highlight"><code data-lang="kotlin">tasks.create("foo") {
    tasks.create("bar")
}</code></pre>
</div>
</div>
</div>
</div>
</div>
</div>
<div class="paragraph">
<p>When converting this to use the new API, something surprising happens: <code>bar</code> doesn&#8217;t exist.
The new API only executes configuration actions when necessary,
so the <code>register()</code> for task <code>bar</code> only executes when <code>foo</code> is configured.</p>
</div>
<div class="exampleblock">
<div class="content">
<div class="exampleblock multi-language-sample">
<div class="content">
<div class="listingblock">
<div class="title">build.gradle</div>
<div class="content">
<pre class="prettyprint highlight"><code data-lang="groovy">tasks.register("foo") {
    tasks.register("bar") // WRONG
}</code></pre>
</div>
</div>
</div>
</div>
<div class="exampleblock multi-language-sample">
<div class="content">
<div class="listingblock">
<div class="title">build.gradle.kts</div>
<div class="content">
<pre class="prettyprint highlight"><code data-lang="kotlin">tasks.register("foo") {
    tasks.register("bar") // WRONG
}</code></pre>
</div>
</div>
</div>
</div>
</div>
</div>
<div class="paragraph">
<p>To avoid this, Gradle now detects this and prevents modification to the underlying container (through <code>create()</code> or <code>register()</code>) when using the new API.</p>
</div>
</div>
<div class="sect2">
<h3 id="rel5.0:worker_api"><a class="anchor" href="#rel5.0:worker_api"></a><a class="link" href="#rel5.0:worker_api">[5.0] Worker API: working directory of a worker can no longer be set</a></h3>
<div class="paragraph">
<p>Since JDK 11 no longer supports changing the working directory of a running process, setting the working directory of a worker via its fork options is now prohibited.</p>
</div>
<div class="paragraph">
<p>All workers now use the same working directory to enable reuse.</p>
</div>
<div class="paragraph">
<p>Please pass files and directories as arguments instead.</p>
</div>
</div>
<div class="sect2">
<h3 id="rel4.10:aws_s3_permissions"><a class="anchor" href="#rel4.10:aws_s3_permissions"></a><a class="link" href="#rel4.10:aws_s3_permissions">[4.10] Publishing to AWS S3 requires new permissions</a></h3>
<div class="paragraph">
<p>The S3 repository transport protocol allows Gradle to publish artifacts to AWS S3 buckets.
Starting with this release, every artifact uploaded to an S3 bucket will be equipped with the <code>bucket-owner-full-control</code> canned ACL.
Make sure that the AWS account used to publish artifacts has the <code>s3:PutObjectAcl</code> and <code>s3:PutObjectVersionAcl</code> permissions, otherwise the upload will fail.</p>
</div>
<div class="listingblock">
<div class="content">
<pre class="prettyprint highlight"><code data-lang="json">{
    "Version":"2012-10-17",
    "Statement":[
        // ...
        {
            "Effect":"Allow",
            "Action":[
                "s3:PutObject", // necessary for uploading objects
                "s3:PutObjectAcl", // required starting with this release
                "s3:PutObjectVersionAcl" // if S3 bucket versioning is enabled
            ],
            "Resource":"arn:aws:s3:::myCompanyBucket/*"
        }
    ]
}</code></pre>
</div>
</div>
<div class="paragraph">
<p>See <a href="declaring_repositories.html#sub:s3_cross_account">AWS S3 Cross Account Access</a> for more information.</p>
</div>
</div>
<div class="sect2">
<h3 id="rel4.9:lazy_task_creation"><a class="anchor" href="#rel4.9:lazy_task_creation"></a><a class="link" href="#rel4.9:lazy_task_creation">[4.9] Consider trying the lazy API for task creation and configuration</a></h3>
<div class="paragraph">
<p>Gradle 4.9 introduced a new way to create and configure tasks that works lazily.
When you use this approach for tasks that are expensive to configure, or when you have many, many tasks, your build configuration time can drop significantly when those tasks don&#8217;t run.</p>
</div>
<div class="paragraph">
<p>You can learn more about lazily creating tasks in the <a href="task_configuration_avoidance.html#task_configuration_avoidance">Task Configuration Avoidance</a> chapter. You can also read about the background to this new feature in <a href="https://blog.gradle.org/preview-avoiding-task-configuration-time">this blog post</a>.</p>
</div>
</div>
<div class="sect2">
<h3 id="rel4.8:switch_to_publishing_plugins"><a class="anchor" href="#rel4.8:switch_to_publishing_plugins"></a><a class="link" href="#rel4.8:switch_to_publishing_plugins">[4.8] Switch to the Maven Publish and Ivy Publish Plugins</a></h3>
<div class="paragraph">
<p>Now that the publishing plugins are stable, we recommend that you migrate from the legacy publishing mechanism for standard Java projects, i.e. those based on the <a href="java_plugin.html#java_plugin">Java Plugin</a>.
That includes projects that use any one of: <a href="java_library_plugin.html#java_library_plugin">Java Library Plugin</a>, <a href="application_plugin.html#application_plugin">Application Plugin</a> or <a href="war_plugin.html#war_plugin">War Plugin</a>.</p>
</div>
<div class="paragraph">
<p>To use the new approach, simply replace any <code>upload&lt;Conf&gt;</code> configuration with a <code>publishing {}</code> block. See the <a href="publishing_setup.html#publishing_overview">publishing overview chapter</a> for more information.</p>
</div>
</div>
<div class="sect2">
<h3 id="rel4.8:deferred_configuration"><a class="anchor" href="#rel4.8:deferred_configuration"></a><a class="link" href="#rel4.8:deferred_configuration">[4.8] Use deferred configuration for publishing plugins</a></h3>
<div class="paragraph">
<p>Prior to Gradle 4.8, the <code>publishing {}</code> block was implicitly treated as if all the logic inside it was executed after the project was evaluated.
This was confusing, because it was the only block that behaved that way.
As part of the stabilization effort in Gradle 4.8, we are deprecating this behavior and asking all users to migrate their build.</p>
</div>
<div class="paragraph">
<p>The new, stable behavior can be switched on by adding the following to your settings file:</p>
</div>
<div class="exampleblock">
<div class="content">
<div class="exampleblock multi-language-sample">
<div class="content">
<div class="listingblock">
<div class="title">settings.gradle</div>
<div class="content">
<pre class="prettyprint highlight"><code data-lang="groovy">enableFeaturePreview('STABLE_PUBLISHING')</code></pre>
</div>
</div>
</div>
</div>
<div class="exampleblock multi-language-sample">
<div class="content">
<div class="listingblock">
<div class="title">settings.gradle.kts</div>
<div class="content">
<pre class="prettyprint highlight"><code data-lang="kotlin">enableFeaturePreview("STABLE_PUBLISHING")</code></pre>
</div>
</div>
</div>
</div>
</div>
</div>
<div class="paragraph">
<p>We recommend doing a test run with a local repository to see whether all artifacts still have the expected coordinates.
In most cases everything should work as before and you are done.
However, your publishing block may rely on the implicit deferred configuration, particularly if it relies on values that may change during the configuration phase of the build.</p>
</div>
<div class="paragraph">
<p>For example, under the new behavior, the following logic assumes that <code>jar.archiveBaseName</code> doesn&#8217;t change after <code>artifactId</code> is set:</p>
</div>
<div class="exampleblock">
<div class="content">
<div class="exampleblock multi-language-sample">
<div class="content">
<div class="listingblock">
<div class="title">build.gradle</div>
<div class="content">
<pre class="prettyprint highlight"><code data-lang="groovy">subprojects {
    publishing {
        publications {
            mavenJava {
                from components.java
                artifactId = jar.archiveBaseName
            }
        }
    }
}</code></pre>
</div>
</div>
</div>
</div>
<div class="exampleblock multi-language-sample">
<div class="content">
<div class="listingblock">
<div class="title">build.gradle.kts</div>
<div class="content">
<pre class="prettyprint highlight"><code data-lang="kotlin">subprojects {
    publishing {
        publications {
            named&lt;MavenPublication&gt;("mavenJava") {
                from(components["java"])
                artifactId = tasks.jar.get().archiveBaseName.get()
            }
        }
    }
}</code></pre>
</div>
</div>
</div>
</div>
</div>
</div>
<div class="paragraph">
<p>If that assumption is incorrect or might possibly be incorrect in the future, the <code>artifactId</code> must be set within an <code>afterEvaluate {}</code> block, like so:</p>
</div>
<div class="exampleblock">
<div class="content">
<div class="exampleblock multi-language-sample">
<div class="content">
<div class="listingblock">
<div class="title">build.gradle</div>
<div class="content">
<pre class="prettyprint highlight"><code data-lang="groovy">subprojects {
    publishing {
        publications {
            mavenJava {
                from components.java
                afterEvaluate {
                    artifactId = jar.archiveBaseName
                }
            }
        }
    }
}</code></pre>
</div>
</div>
</div>
</div>
<div class="exampleblock multi-language-sample">
<div class="content">
<div class="listingblock">
<div class="title">build.gradle.kts</div>
<div class="content">
<pre class="prettyprint highlight"><code data-lang="kotlin">subprojects {
    publishing {
        publications {
            named&lt;MavenPublication&gt;("mavenJava") {
                from(components["java"])
                afterEvaluate {
                    artifactId = tasks.jar.get().archiveBbaseName.get()
                }
            }
        }
    }
}</code></pre>
</div>
</div>
</div>
</div>
</div>
</div>
</div>
<div class="sect2">
<h3 id="rel4.8:configure_internal_tasks"><a class="anchor" href="#rel4.8:configure_internal_tasks"></a><a class="link" href="#rel4.8:configure_internal_tasks">[4.8] Configure existing <code>wrapper</code> and <code>init</code> tasks</a></h3>
<div class="paragraph">
<p>You should no longer define your own <code>wrapper</code> and <code>init</code> tasks. Configure the existing tasks instead, for example by converting this:</p>
</div>
<div class="exampleblock">
<div class="content">
<div class="exampleblock multi-language-sample">
<div class="content">
<div class="listingblock">
<div class="title">build.gradle</div>
<div class="content">
<pre class="prettyprint highlight"><code data-lang="groovy">task wrapper(type: Wrapper) {
    ...
}</code></pre>
</div>
</div>
</div>
</div>
<div class="exampleblock multi-language-sample">
<div class="content">
<div class="listingblock">
<div class="title">build.gradle.kts</div>
<div class="content">
<pre class="prettyprint highlight"><code data-lang="kotlin">task&lt;Wrapper&gt;("wrapper") {
    ...
}</code></pre>
</div>
</div>
</div>
</div>
</div>
</div>
<div class="paragraph">
<p>to this:</p>
</div>
<div class="exampleblock">
<div class="content">
<div class="exampleblock multi-language-sample">
<div class="content">
<div class="listingblock">
<div class="title">build.gradle</div>
<div class="content">
<pre class="prettyprint highlight"><code data-lang="groovy">wrapper {
    ...
}</code></pre>
</div>
</div>
</div>
</div>
<div class="exampleblock multi-language-sample">
<div class="content">
<div class="listingblock">
<div class="title">build.gradle.kts</div>
<div class="content">
<pre class="prettyprint highlight"><code data-lang="kotlin">tasks.wrapper {
    ...
}</code></pre>
</div>
</div>
</div>
</div>
</div>
</div>
</div>
<div class="sect2">
<h3 id="rel4.8:pom_wildcard_exclusions"><a class="anchor" href="#rel4.8:pom_wildcard_exclusions"></a><a class="link" href="#rel4.8:pom_wildcard_exclusions">[4.8] Gradle now honors implicit wildcards in Maven POM exclusions</a></h3>
<div class="paragraph">
<p>If an exclusion in a Maven POM was missing either a <code>groupId</code> or <code>artifactId</code>, Gradle used to ignore the exclusion.
Now the missing elements are treated as implicit wildcards — e.g. <code>&lt;groupId&gt;*&lt;/groupId&gt;</code> — which means that some of your dependencies may now be excluded where they weren&#8217;t before.</p>
</div>
<div class="paragraph">
<p>You will need to explicitly declare any missing dependencies that you need.</p>
</div>
</div>
<div class="sect2">
<h3 id="rel4.7:plain_console_output"><a class="anchor" href="#rel4.7:plain_console_output"></a><a class="link" href="#rel4.7:plain_console_output">[4.7] Changes to the structure of Gradle&#8217;s plain console output</a></h3>
<div class="paragraph">
<p>The plain console mode now formats output consistently with the rich console, which means that the output format has changed.
For example:</p>
</div>
<div class="ulist">
<ul>
<li>
<p>The output produced by a given task is now grouped together, even when other tasks execute in parallel with it.</p>
</li>
<li>
<p>Task execution headers are printed with a "&gt; Task" prefix.</p>
</li>
<li>
<p>All output produced during build execution is written to the standard output file handle. This includes messages written to System.err unless you are redirecting standard error to a file or any other non-console destination.</p>
</li>
</ul>
</div>
<div class="paragraph">
<p>This may break tools that scrape details from the plain console output.</p>
</div>
</div>
<div class="sect2">
<h3 id="rel:4.6:native_task_api_changes"><a class="anchor" href="#rel:4.6:native_task_api_changes"></a><a class="link" href="#rel:4.6:native_task_api_changes">[4.6] Changes to the APIs of native tasks related to compilation, linking and installation</a></h3>
<div class="paragraph">
<p>Many tasks related to compiling, linking and installing native libraries and applications have been converted to the Provider API so that they support <a href="lazy_configuration.html#lazy_configuration">lazy configuration</a>.
This conversion has introduced some breaking changes to the APIs of the tasks so that they match the conventions of the Provider API.</p>
</div>
<div class="paragraph">
<p>The following tasks have been changed:</p>
</div>
<div class="dlist">
<dl>
<dt class="hdlist1"><a href="../dsl/org.gradle.nativeplatform.tasks.AbstractLinkTask.html">AbstractLinkTask</a> and its subclasses</dt>
<dd>
<div class="ulist">
<ul>
<li>
<p><code>getDestinationDir()</code> was replaced by <code>getDestinationDirectory()</code>.</p>
</li>
<li>
<p><code>getBinaryFile()</code>, <code>getOutputFile()</code> was replaced by <code>getLinkedFile()</code>.</p>
</li>
<li>
<p><code>setOutputFile(File)</code> was removed. Use <code>Property.set()</code> instead.</p>
</li>
<li>
<p><code>setOutputFile(Provider)</code> was removed. Use <code>Property.set()</code> instead.</p>
</li>
<li>
<p><code>getTargetPlatform()</code> was changed to return a <code>Property</code>.</p>
</li>
<li>
<p><code>setTargetPlatform(NativePlatform)</code> was removed. Use <code>Property.set()</code> instead.</p>
</li>
<li>
<p><code>getToolChain()</code> was changed to return a <code>Property</code>.</p>
</li>
<li>
<p><code>setToolChain(NativeToolChain)</code> was removed. Use <code>Property.set()</code> instead.</p>
</li>
</ul>
</div>
</dd>
<dt class="hdlist1"><a href="../dsl/org.gradle.nativeplatform.tasks.CreateStaticLibrary.html">CreateStaticLibrary</a></dt>
<dd>
<div class="ulist">
<ul>
<li>
<p><code>getOutputFile()</code> was changed to return a <code>Property</code>.</p>
</li>
<li>
<p><code>setOutputFile(File)</code> was removed. Use <code>Property.set()</code> instead.</p>
</li>
<li>
<p><code>setOutputFile(Provider)</code> was removed. Use <code>Property.set()</code> instead.</p>
</li>
<li>
<p><code>getTargetPlatform()</code> was changed to return a <code>Property</code>.</p>
</li>
<li>
<p><code>setTargetPlatform(NativePlatform)</code> was removed. Use <code>Property.set()</code> instead.</p>
</li>
<li>
<p><code>getToolChain()</code> was changed to return a <code>Property</code>.</p>
</li>
<li>
<p><code>setToolChain(NativeToolChain)</code> was removed. Use <code>Property.set()</code> instead.</p>
</li>
<li>
<p><code>getStaticLibArgs()</code> was changed to return a <code>ListProperty</code>.</p>
</li>
<li>
<p><code>setStaticLibArgs(List)</code> was removed. Use <code>ListProperty.set()</code> instead.</p>
</li>
</ul>
</div>
</dd>
<dt class="hdlist1"><a href="../dsl/org.gradle.nativeplatform.tasks.InstallExecutable.html">InstallExecutable</a></dt>
<dd>
<div class="ulist">
<ul>
<li>
<p><code>getSourceFile()</code> was replaced by <code>getExecutableFile()</code>.</p>
</li>
<li>
<p><code>getPlatform()</code> was replaced by <code>getTargetPlatform()</code>.</p>
</li>
<li>
<p><code>setTargetPlatform(NativePlatform)</code> was removed. Use <code>Property.set()</code> instead.</p>
</li>
<li>
<p><code>getToolChain()</code> was changed to return a <code>Property</code>.</p>
</li>
<li>
<p><code>setToolChain(NativeToolChain)</code> was removed. Use <code>Property.set()</code> instead.</p>
</li>
</ul>
</div>
</dd>
</dl>
</div>
<div class="paragraph">
<p>The following have also seen similar changes:</p>
</div>
<div class="ulist">
<ul>
<li>
<p><a href="../dsl/org.gradle.language.assembler.tasks.Assemble.html">Assemble</a></p>
</li>
<li>
<p><a href="../dsl/org.gradle.language.rc.tasks.WindowsResourceCompile.html">WindowsResourceCompile</a></p>
</li>
<li>
<p><a href="../javadoc/org/gradle/nativeplatform/tasks/StripSymbols.html">StripSymbols</a></p>
</li>
<li>
<p><a href="../javadoc/org/gradle/nativeplatform/tasks/ExtractSymbols.html">ExtractSymbols</a></p>
</li>
<li>
<p><a href="../javadoc/org/gradle/language/swift/tasks/SwiftCompile.html">SwiftCompile</a></p>
</li>
<li>
<p><a href="../javadoc/org/gradle/nativeplatform/tasks/LinkMachOBundle.html">LinkMachOBundle</a></p>
</li>
</ul>
</div>
</div>
<div class="sect2">
<h3 id="rel4.6:visual_studio_single_solution"><a class="anchor" href="#rel4.6:visual_studio_single_solution"></a><a class="link" href="#rel4.6:visual_studio_single_solution">[4.6] Visual Studio integration only supports a single solution file for all components of a build</a></h3>
<div class="paragraph">
<p><a href="../dsl/org.gradle.ide.visualstudio.VisualStudioExtension.html">VisualStudioExtension</a> no longer has a <code>solutions</code> property.
Instead, you configure a single solution via <a href="../dsl/org.gradle.ide.visualstudio.VisualStudioRootExtension.html">VisualStudioRootExtension</a> in the root project, like so:</p>
</div>
<div class="exampleblock">
<div class="content">
<div class="exampleblock multi-language-sample">
<div class="content">
<div class="listingblock">
<div class="title">build.gradle</div>
<div class="content">
<pre class="prettyprint highlight"><code data-lang="groovy">model {
    visualStudio {
        solution {
            solutionFile.location = "vs/${name}.sln"
        }
    }
}</code></pre>
</div>
</div>
</div>
</div>
</div>
</div>
<div class="paragraph">
<p>In addition, there are no longer individual tasks to generate the solution files for each component, but rather a single <code>visualStudio</code> task that generates a solution file that encompasses all components in the build.</p>
</div>
</div>
<div class="sect2">
<h3 id="rel4.5:http_build_cache_no_follow_redirects"><a class="anchor" href="#rel4.5:http_build_cache_no_follow_redirects"></a><a class="link" href="#rel4.5:http_build_cache_no_follow_redirects">[4.5] <code>HttpBuildCache</code> no longer follows redirects</a></h3>
<div class="paragraph">
<p>When connecting to an HTTP build cache backend via <code>HttpBuildCache</code>, Gradle does not follow redirects any more, treating them as errors instead.
Getting a redirect from the build cache backend is mostly a configuration error — using an "http" URL instead of "https" for example — and has negative effects on performance.</p>
</div>
</div>
<div class="sect2">
<h3 id="rel4.4:security_library_upgrades"><a class="anchor" href="#rel4.4:security_library_upgrades"></a><a class="link" href="#rel4.4:security_library_upgrades">[4.4] Third-party dependency upgrades</a></h3>
<div class="paragraph">
<p>This version includes several upgrades of third-party dependencies:</p>
</div>
<div class="ulist">
<ul>
<li>
<p>jackson: 2.6.6 &#8594; 2.8.9</p>
</li>
<li>
<p>plexus-utils: 2.0.6 &#8594; 2.1</p>
</li>
<li>
<p>xercesImpl: 2.9.1 &#8594; 2.11.0</p>
</li>
<li>
<p>bsh: 2.0b4 &#8594; 2.0b6</p>
</li>
<li>
<p>bouncycastle: 1.57 &#8594; 1.58</p>
</li>
</ul>
</div>
<div class="paragraph">
<p>This fix the following security issues:</p>
</div>
<div class="ulist">
<ul>
<li>
<p><a href="http://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2017-7525">CVE-2017-7525</a> (critical)</p>
</li>
<li>
<p>SONATYPE-2017-0359 (critical)</p>
</li>
<li>
<p>SONATYPE-2017-0355 (critical)</p>
</li>
<li>
<p>SONATYPE-2017-0398 (critical)</p>
</li>
<li>
<p><a href="https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2013-4002">CVE-2013-4002</a> (critical)</p>
</li>
<li>
<p><a href="https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2016-2510">CVE-2016-2510</a> (severe)</p>
</li>
<li>
<p>SONATYPE-2016-0397 (severe)</p>
</li>
<li>
<p><a href="https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2009-2625">CVE-2009-2625</a> (severe)</p>
</li>
<li>
<p>SONATYPE-2017-0348 (severe)</p>
</li>
</ul>
</div>
<div class="paragraph">
<p>Gradle does not expose public APIs for these 3rd-party dependencies, but those who customize Gradle will want to be aware.</p>
</div>
</div>
</div>
</div>
</div>
<script src="https://cdnjs.cloudflare.com/ajax/libs/prettify/r298/run_prettify.min.js"></script>
</div>
<!-- end div class="chapter" -->

<footer class="site-layout__footer site-footer" itemscope="itemscope" itemtype="https://schema.org/WPFooter">
    <nav class="site-footer__navigation" itemtype="https://schema.org/SiteNavigationElement">
        <section class="site-footer__links">
            <div class="site-footer__link-group">
                <header><strong>Docs</strong></header>
                <ul class="site-footer__links-list">
                    <li itemprop="name"><a href="/userguide/userguide.html" itemprop="url">User Manual</a></li>
                    <li itemprop="name"><a href="/dsl/" itemprop="url">DSL Reference</a></li>
                    <li itemprop="name"><a href="/release-notes.html" itemprop="url">Release Notes</a></li>
                    <li itemprop="name"><a href="/javadoc/" itemprop="url">Javadoc</a></li>
                </ul>
            </div>
            <div class="site-footer__link-group">
                <header><strong>News</strong></header>
                <ul class="site-footer__links-list">
                    <li itemprop="name"><a href="https://blog.gradle.org/" itemprop="url">Blog</a></li>
                    <li itemprop="name"><a href="https://newsletter.gradle.org/" itemprop="url">Newsletter</a></li>
                    <li itemprop="name"><a href="https://twitter.com/gradle" itemprop="url">Twitter</a></li>
                    <li itemprop="name"><a href="https://status.gradle.com/" itemprop="url">Status Page</a></li>
                </ul>
            </div>
            <div class="site-footer__link-group">
                <header><strong>Products</strong></header>
                <ul class="site-footer__links-list">
                    <li itemprop="name"><a href="https://gradle.com/build-scans/" itemprop="url">Build Scan™</a></li>
                    <li itemprop="name"><a href="https://gradle.com/build-cache/" itemprop="url">Build Cache</a></li>
                    <li itemprop="name"><a href="https://gradle.com/enterprise/resources/" itemprop="url">Develocity Docs</a></li>
                </ul>
            </div>
            <div class="site-footer__link-group">
                <header><strong>Get Help</strong></header>
                <ul class="site-footer__links-list">
                    <li itemprop="name"><a href="https://discuss.gradle.org/c/help-discuss" itemprop="url">Forums</a></li>
                    <li itemprop="name"><a href="https://github.com/gradle/" itemprop="url">GitHub</a></li>
                    <li itemprop="name"><a href="https://gradle.org/training/" itemprop="url">Training</a></li>
                    <li itemprop="name"><a href="https://gradle.org/services/" itemprop="url">Services</a></li>
                </ul>
            </div>
        </section>
        <section class="site-footer__subscribe-newsletter" id="newsletter-form-container">
            <header class="newsletter-form__header"><h5>Stay <code>UP-TO-DATE</code> on new features and news</h5></header>
            <p class="disclaimer">By entering your email, you agree to our <a href="https://gradle.com/legal/terms-of-service/">Terms</a> and <a href="https://gradle.com/legal/privacy/">Privacy Policy</a>, including receipt of emails. You can unsubscribe at any time.</p>
            <div class="newsletter-form__container">
                <form id="newsletter-form" class="newsletter-form" action="https://go.gradle.com/l/68052/2018-09-07/bk6wml" method="post">
                    <input id="email" class="email" name="email" type="email" placeholder="<EMAIL>" pattern="[^@\s]+@[^@\s]+\.[^@\s]+" maxlength="255" required=""/>
                    <button id="submit" class="submit" type="submit">Subscribe</button>
                </form>
            </div>
        </section>
    </nav>
</footer>

</div>
<!-- end div class="content" -->


</main>

<div class="site-footer-secondary">
    <div class="site-footer-secondary__contents">
        <div class="site-footer__copy">© <a href="https://gradle.com">Gradle Inc.</a>
            <time>2023</time>
            All rights reserved.
        </div>
        <div class="site-footer__logo"><a href="/">
            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 90 66.06">
                <defs>
                    <style>.cls-1 {
                        fill: #02303a;
                    }</style>
                </defs>
                <title>gradle</title>
                <path class="cls-1"
                      d="M85.11,4.18a14.27,14.27,0,0,0-19.83-.34,1.38,1.38,0,0,0,0,2L67,7.6a1.36,1.36,0,0,0,1.78.12A8.18,8.18,0,0,1,79.5,20.06C68.17,31.38,53.05-.36,18.73,16a4.65,4.65,0,0,0-2,6.54l5.89,10.17a4.64,4.64,0,0,0,6.3,1.73l.14-.08-.11.08L31.53,33a60.29,60.29,0,0,0,8.22-6.13,1.44,1.44,0,0,1,1.87-.06h0a1.34,1.34,0,0,1,.06,2A61.61,61.61,0,0,1,33,35.34l-.09,0-2.61,1.46a7.34,7.34,0,0,1-3.61.94,7.45,7.45,0,0,1-6.47-3.71l-5.57-9.61C4,32-2.54,46.56,1,65a1.36,1.36,0,0,0,1.33,1.11H8.61A1.36,1.36,0,0,0,10,64.87a9.29,9.29,0,0,1,18.42,0,1.35,1.35,0,0,0,1.34,1.19H35.9a1.36,1.36,0,0,0,1.34-1.19,9.29,9.29,0,0,1,18.42,0A1.36,1.36,0,0,0,57,66.06H63.1a1.36,1.36,0,0,0,1.36-1.34c.14-8.6,2.46-18.48,9.07-23.43C96.43,24.16,90.41,9.48,85.11,4.18ZM61.76,30.05l-4.37-2.19h0a2.74,2.74,0,1,1,4.37,2.2Z"/>
            </svg>
        </a></div>
        <div class="site-footer-secondary__links">
            <a href="https://gradle.com/careers/">Careers</a> |
            <a href="https://gradle.com/legal/privacy/">Privacy</a> |
            <a href="https://gradle.com/legal/terms-of-service/">Terms of Service</a> |
            <a href="https://gradle.org/contact/">Contact</a>
        </div>
    </div>
</div>

</div>
<!-- end div class="layout" -->

<script type="text/javascript">
    // Polyfill Element.matches()
    if (!Element.prototype.matches) {
        Element.prototype.matches = Element.prototype.msMatchesSelector || Element.prototype.webkitMatchesSelector;
    }
    // Polyfill Element.closest()
    if (!Element.prototype.closest) {
        Element.prototype.closest = function (s) {
            var el = this;
            if (!document.documentElement.contains(el)) return null;
            do {
                if (typeof el.matches === "function" && el.matches(s)) return el;
                el = el.parentElement || el.parentNode;
            } while (el !== null);
            return null;
        };
    }

    function getCurrentChapterFileName(givenUrl) {
        var currentChapterFileName = givenUrl.substr(givenUrl.lastIndexOf("/") + 1);
        if (currentChapterFileName === "index.html" || currentChapterFileName === "") {
            currentChapterFileName = givenUrl.substr(0, givenUrl.lastIndexOf("/"));
            currentChapterFileName = currentChapterFileName.substr(currentChapterFileName.lastIndexOf("/") + 1) + "/index.html";
        }
        return currentChapterFileName;
    }

    // The media query indicating that a device is a desktop.
    // The `min-width: 64rem` definition should be aligned to
    // the one of `css/manual.css`.
    const desktopMediaQuery = window.matchMedia("screen and (min-width: 64rem)");

    [].forEach.call(document.querySelectorAll(".docs-navigation a[href$='/" + getCurrentChapterFileName(window.location.pathname) + "']"), function (link) {
        // Add "active" to all links same as current URL
        link.classList.add("active");

        // Expand all parent navigation
        var parentListEl = link.closest("li");
        while (parentListEl !== null) {
            var dropDownEl = parentListEl.querySelector(".nav-dropdown");
            if (dropDownEl !== null) {
                dropDownEl.classList.add("expanded");
            }
            parentListEl = parentListEl.parentNode.closest("li");
        }

        // Only scroll if the device is a desktop.
        //
        // Mobile's `docs-navigation` is always at bottom of `content`,
        // so we should not slide down to where `docs-navigation` lays.
        if (desktopMediaQuery.matches) {
            // Scroll to center of the page
            link.scrollIntoView({behavior: 'auto', block: 'center', inline: 'center'})
        }
    });

    // Expand/contract multi-level side navigation
    [].forEach.call(document.querySelectorAll(".docs-navigation .nav-dropdown"), function registerSideNavActions(collapsibleElement) {
        collapsibleElement.addEventListener("click", function toggleExpandedSideNav(evt) {
            evt.preventDefault();
            evt.target.classList.toggle("expanded");
            evt.target.setAttribute("aria-expanded", evt.target.classList.contains("expanded").toString());
            return false;
        }, false);
    });

    // Fix a weird issue making the initial screen always at the bottom.
    document.querySelector(".content").scrollIntoView(true);
</script>

</body>
</html>