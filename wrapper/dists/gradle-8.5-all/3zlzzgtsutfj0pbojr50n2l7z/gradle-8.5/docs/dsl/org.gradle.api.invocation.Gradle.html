<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01//EN" "http://www.w3.org/TR/html4/strict.dtd">
<html><head><META http-equiv="Content-Type" content="text/html; charset=UTF-8"><title>Gradle - Gradle DSL Version 8.5</title><link xmlns:xslthl="http://xslthl.sf.net" rel="stylesheet" href="https://fonts.googleapis.com/css?family=Lato:400,400i,700"><link xmlns:xslthl="http://xslthl.sf.net" crossorigin="crossorigin" href="//assets.gradle.com" rel="preconnect"><meta xmlns:xslthl="http://xslthl.sf.net" content="width=device-width, initial-scale=1" name="viewport"><link xmlns:xslthl="http://xslthl.sf.net" type="text/css" rel="stylesheet" href="base.css"><meta content="DocBook XSL Stylesheets V1.75.2" name="generator"><link rel="home" href="index.html" title="Gradle DSL Version 8.5"><link rel="up" href="index.html" title="Gradle DSL Version 8.5"></head><body><div class="layout"><header xmlns:xslthl="http://xslthl.sf.net" itemtype="https://schema.org/WPHeader" itemscope="itemscope" class="site-layout__header site-header"><nav itemtype="https://schema.org/SiteNavigationElement" itemscope="itemscope" class="site-header__navigation"><div class="site-header__navigation-header"><a title="Gradle Docs" href="https://docs.gradle.org" class="logo" target="_top"><svg xmlns="http://www.w3.org/2000/svg" version="1.1" viewBox="0 0 278 86" height="43px" width="139px"><defs><style>.cls-1 {
                                    fill: #02303a;
                                    }</style></defs><title>Gradle</title><path d="M155,56.32V70.27a18.32,18.32,0,0,1-5.59,2.83,21.82,21.82,0,0,1-6.36.89,21.08,21.08,0,0,1-7.64-1.31A17.12,17.12,0,0,1,129.59,69a16.14,16.14,0,0,1-3.73-5.58,18.78,18.78,0,0,1-1.31-7.08,19.58,19.58,0,0,1,1.26-7.14A15.68,15.68,0,0,1,135,40a20.39,20.39,0,0,1,7.45-1.29,22,22,0,0,1,3.92.33,20.43,20.43,0,0,1,3.39.92,15.16,15.16,0,0,1,2.85,1.42A17.3,17.3,0,0,1,155,43.25l-1.84,2.91a1.72,1.72,0,0,1-1.12.84,2,2,0,0,1-1.5-.34L149,45.75a10.49,10.49,0,0,0-1.75-.79,14.33,14.33,0,0,0-2.17-.54,15.29,15.29,0,0,0-2.78-.22,11.91,11.91,0,0,0-4.61.86,9.66,9.66,0,0,0-3.52,2.46,10.9,10.9,0,0,0-2.24,3.84,14.88,14.88,0,0,0-.79,5,15.23,15.23,0,0,0,.85,5.28,11.06,11.06,0,0,0,2.38,3.94A10.15,10.15,0,0,0,138.05,68a14.28,14.28,0,0,0,8.25.44,17.1,17.1,0,0,0,2.94-1.09V61.14h-4.35a1.3,1.3,0,0,1-1-.35,1.15,1.15,0,0,1-.35-.85V56.32Zm10.47-2.93a10.53,10.53,0,0,1,2.72-3.45,5.77,5.77,0,0,1,3.72-1.25,4.5,4.5,0,0,1,2.72.74l-.38,4.41a1.18,1.18,0,0,1-.34.61,1,1,0,0,1-.61.18,6.76,6.76,0,0,1-1.06-.12,8.22,8.22,0,0,0-1.38-.12,5,5,0,0,0-1.74.28,4.37,4.37,0,0,0-1.37.83,5.55,5.55,0,0,0-1.07,1.3,12.26,12.26,0,0,0-.87,1.74V73.61H160V49.14h3.45a1.94,1.94,0,0,1,1.27.32,1.9,1.9,0,0,1,.48,1.16Zm11.36-.84A14.49,14.49,0,0,1,187,48.69a9.92,9.92,0,0,1,3.84.7,8.06,8.06,0,0,1,2.86,2,8.38,8.38,0,0,1,1.78,3,11.64,11.64,0,0,1,.61,3.82V73.61h-2.68a2.64,2.64,0,0,1-1.28-.25,1.72,1.72,0,0,1-.72-1l-.52-1.77a20.25,20.25,0,0,1-1.82,1.47,10.86,10.86,0,0,1-1.83,1.06,10.36,10.36,0,0,1-2,.66,12,12,0,0,1-2.4.22,9.64,9.64,0,0,1-2.86-.41,6.28,6.28,0,0,1-2.27-1.26,5.6,5.6,0,0,1-1.48-2.07,7.38,7.38,0,0,1-.52-2.89,5.7,5.7,0,0,1,.31-1.85,5.3,5.3,0,0,1,1-1.75,8.25,8.25,0,0,1,1.83-1.57,11.17,11.17,0,0,1,2.75-1.29,23.28,23.28,0,0,1,3.81-.9,36.77,36.77,0,0,1,5-.41V58.16a5.35,5.35,0,0,0-1.05-3.64,3.83,3.83,0,0,0-3-1.18,7.3,7.3,0,0,0-2.38.33,9.39,9.39,0,0,0-1.65.75l-1.3.75a2.52,2.52,0,0,1-1.3.34,1.7,1.7,0,0,1-1.05-.32,2.61,2.61,0,0,1-.69-.76Zm13.5,10.61a31.66,31.66,0,0,0-4.3.45,11,11,0,0,0-2.79.82,3.57,3.57,0,0,0-1.5,1.17,2.89,2.89,0,0,0,.47,3.67,3.93,3.93,0,0,0,2.39.67,7,7,0,0,0,3.14-.66,9.52,9.52,0,0,0,2.59-2Zm32.53-25V73.61h-3.6a1.39,1.39,0,0,1-1.48-1.07l-.5-2.36a12.4,12.4,0,0,1-3.4,2.74,9.17,9.17,0,0,1-4.47,1,7.95,7.95,0,0,1-6.55-3.26A11.61,11.61,0,0,1,201,66.79a19.71,19.71,0,0,1-.66-5.34,16.77,16.77,0,0,1,.74-5.06,12.21,12.21,0,0,1,2.13-4,9.88,9.88,0,0,1,3.31-2.69,9.64,9.64,0,0,1,4.34-1,8.63,8.63,0,0,1,3.51.64,9,9,0,0,1,2.6,1.74V38.17ZM217,55.39a5.94,5.94,0,0,0-2.18-1.72,6.54,6.54,0,0,0-2.54-.5,5.68,5.68,0,0,0-2.41.5A4.87,4.87,0,0,0,208,55.19a7.19,7.19,0,0,0-1.17,2.57,14.83,14.83,0,0,0-.4,3.69,16.34,16.34,0,0,0,.34,3.63,7.14,7.14,0,0,0,1,2.44,3.79,3.79,0,0,0,1.58,1.36,5,5,0,0,0,2.07.41,6,6,0,0,0,3.13-.76A9.19,9.19,0,0,0,217,66.36Zm17.67-17.22V73.61h-5.89V38.17ZM245.1,62.11a11.37,11.37,0,0,0,.67,3.26,6.54,6.54,0,0,0,1.38,2.27,5.39,5.39,0,0,0,2,1.33,7.26,7.26,0,0,0,2.61.44,8.21,8.21,0,0,0,2.47-.33,11.51,11.51,0,0,0,1.81-.74c.52-.27,1-.52,1.36-.74a2.31,2.31,0,0,1,1.13-.33,1.21,1.21,0,0,1,1.1.55L261.36,70a9.45,9.45,0,0,1-2.19,1.92,12.18,12.18,0,0,1-2.54,1.24,14,14,0,0,1-2.7.66,18.78,18.78,0,0,1-2.65.19,12.93,12.93,0,0,1-4.75-.85,10.65,10.65,0,0,1-3.82-2.5,11.8,11.8,0,0,1-2.55-4.1,15.9,15.9,0,0,1-.93-5.67,13.55,13.55,0,0,1,.81-4.71,11.34,11.34,0,0,1,2.33-3.84,11,11,0,0,1,3.69-2.59,12.31,12.31,0,0,1,4.93-1,11.86,11.86,0,0,1,4.27.74,9.25,9.25,0,0,1,3.36,2.16,9.84,9.84,0,0,1,2.21,3.48,13,13,0,0,1,.8,4.71,3.82,3.82,0,0,1-.29,1.8,1.19,1.19,0,0,1-1.1.46Zm11.23-3.55A7.28,7.28,0,0,0,256,56.4a5.16,5.16,0,0,0-1-1.77,4.44,4.44,0,0,0-1.63-1.21,5.68,5.68,0,0,0-2.3-.44,5.46,5.46,0,0,0-4,1.45,7.13,7.13,0,0,0-1.87,4.13ZM112.26,14a13.72,13.72,0,0,0-19.08-.32,1.27,1.27,0,0,0-.41.93,1.31,1.31,0,0,0,.38.95l1.73,1.73a1.31,1.31,0,0,0,1.71.12,7.78,7.78,0,0,1,4.71-1.57,7.87,7.87,0,0,1,5.57,13.43C96,40.2,81.41,9.66,48.4,25.37a4.48,4.48,0,0,0-2,6.29l5.66,9.79a4.49,4.49,0,0,0,6.07,1.67l.14-.08-.11.08,2.51-1.41a57.72,57.72,0,0,0,7.91-5.89,1.37,1.37,0,0,1,1.8-.06h0a1.29,1.29,0,0,1,0,2A59.79,59.79,0,0,1,62.11,44l-.09.05-2.51,1.4a7,7,0,0,1-3.47.91,7.19,7.19,0,0,1-6.23-3.57l-5.36-9.24C34.17,40.81,27.93,54.8,31.28,72.5a1.31,1.31,0,0,0,1.29,1.06h6.09A1.3,1.3,0,0,0,40,72.42a8.94,8.94,0,0,1,17.73,0A1.3,1.3,0,0,0,59,73.56h5.94a1.31,1.31,0,0,0,1.3-1.14,8.93,8.93,0,0,1,17.72,0,1.3,1.3,0,0,0,1.29,1.14h5.87a1.3,1.3,0,0,0,1.3-1.28c.14-8.28,2.37-17.79,8.74-22.55C123.15,33.25,117.36,19.12,112.26,14ZM89.79,38.92l-4.2-2.11h0a2.64,2.64,0,1,1,4.2,2.12Z" class="cls-1"/></svg></a><div class="site-header__doc-type sr-only">DSL Reference</div><div class="site-header-version">8.5</div><button class="site-header__navigation-button hamburger" aria-label="Navigation Menu" type="button"><span class="hamburger__bar"></span><span class="hamburger__bar"></span><span class="hamburger__bar"></span></button></div><div class="site-header__navigation-collapsible site-header__navigation-collapsible--collapse"><ul class="site-header__navigation-items"><li tabindex="0" class="site-header__navigation-item site-header__navigation-submenu-section"><span class="site-header__navigation-link">
                                Community
                            </span><div class="site-header__navigation-submenu"><div itemprop="name" class="site-header__navigation-submenu-item"><a itemprop="url" href="https://gradle.org/" class="site-header__navigation-submenu-item-link" target="_top"><span class="site-header__navigation-submenu-item-link-text">Community Home</span></a></div><div itemprop="name" class="site-header__navigation-submenu-item"><a itemprop="url" href="https://discuss.gradle.org/" class="site-header__navigation-submenu-item-link" target="_top"><span class="site-header__navigation-submenu-item-link-text">Community Forums</span></a></div><div itemprop="name" class="site-header__navigation-submenu-item"><a itemprop="url" href="https://plugins.gradle.org" class="site-header__navigation-submenu-item-link" target="_top"><span class="site-header__navigation-submenu-item-link-text">Community Plugins</span></a></div></div></li><li itemprop="name" class="site-header__navigation-item"><a itemprop="url" href="https://gradle.org/training/" class="site-header__navigation-link" target="_top">Training</a></li><li tabindex="0" class="site-header__navigation-item site-header__navigation-submenu-section"><span class="site-header__navigation-link">
                                News
                            </span><div class="site-header__navigation-submenu"><div itemprop="name" class="site-header__navigation-submenu-item"><a itemprop="url" href="https://newsletter.gradle.org" class="site-header__navigation-submenu-item-link"><span class="site-header__navigation-submenu-item-link-text">Newsletter</span></a></div><div itemprop="name" class="site-header__navigation-submenu-item"><a itemprop="url" href="https://blog.gradle.org" class="site-header__navigation-submenu-item-link"><span class="site-header__navigation-submenu-item-link-text">Blog</span></a></div><div class="site-header__navigation-submenu-item"><a href="https://twitter.com/gradle" class="site-header__navigation-submenu-item-link"><span class="site-header__navigation-submenu-item-link-text">Twitter</span></a></div></div></li><li itemprop="name" class="site-header__navigation-item"><a itemprop="url" href="https://gradle.com/enterprise" class="site-header__navigation-link" target="_top">Develocity</a></li><li class="site-header__navigation-item"><a href="https://github.com/gradle/gradle" title="Gradle on GitHub" class="site-header__navigation-link"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" height="20" width="20"><title>github</title><path fill-rule="evenodd" fill="#02303A" d="M10 0C4.477 0 0 4.477 0 10c0 4.418 2.865 8.166 6.839 9.489.5.092.682-.217.682-.482 0-.237-.008-.866-.013-1.7-2.782.603-3.369-1.342-3.369-1.342-.454-1.155-1.11-1.462-1.11-1.462-.908-.62.069-.608.069-.608 1.003.07 1.531 1.03 1.531 1.03.892 1.529 2.341 1.087 2.91.831.092-.646.35-1.086.636-1.336-2.22-.253-4.555-1.11-4.555-4.943 0-1.091.39-1.984 1.029-2.683-.103-.253-.446-1.27.098-2.647 0 0 .84-.268 2.75 1.026A9.578 9.578 0 0 1 10 4.836c.85.004 1.705.114 2.504.337 1.909-1.294 2.747-1.026 2.747-1.026.546 1.377.203 2.394.1 2.647.64.699 1.028 1.592 1.028 2.683 0 3.842-2.339 4.687-4.566 4.935.359.309.678.919.678 1.852 0 1.336-.012 2.415-.012 2.743 0 .267.18.579.688.481C17.137 18.163 20 14.418 20 10c0-5.523-4.478-10-10-10"/></svg></a></li></ul></div></nav></header><main class="main-content"><nav class="docs-navigation"><div class="search-container"><input placeholder="Search Docs" class="search-input" id="search-input" name="q" type="search"></div><ul><li><a class="reference-links" href="../userguide/userguide.html">User Manual Home</a></li><li><a class="reference-links" href="index.html">DSL Reference Home</a></li><li><a class="reference-links" href="../release-notes.html">Release Notes</a></li><ul class="sections"><li><a xmlns:xslthl="http://xslthl.sf.net" href="org.gradle.api.invocation.Gradle.html#N17A76" title="Properties">Properties</a></li><li><a xmlns:xslthl="http://xslthl.sf.net" href="org.gradle.api.invocation.Gradle.html#N17B01" title="Methods">Methods</a></li></ul><li><h3>Build script blocks</h3></li><li><a class="link" href="org.gradle.api.Project.html#org.gradle.api.Project:allprojects(groovy.lang.Closure)"><code class="literal">allprojects { }</code></a></li><li><a class="link" href="org.gradle.api.Project.html#org.gradle.api.Project:artifacts(groovy.lang.Closure)"><code class="literal">artifacts { }</code></a></li><li><a class="link" href="org.gradle.api.Project.html#org.gradle.api.Project:buildscript(groovy.lang.Closure)"><code class="literal">buildscript { }</code></a></li><li><a class="link" href="org.gradle.api.Project.html#org.gradle.api.Project:configurations(groovy.lang.Closure)"><code class="literal">configurations { }</code></a></li><li><a class="link" href="org.gradle.api.Project.html#org.gradle.api.Project:dependencies(groovy.lang.Closure)"><code class="literal">dependencies { }</code></a></li><li><a class="link" href="org.gradle.api.Project.html#org.gradle.api.Project:repositories(groovy.lang.Closure)"><code class="literal">repositories { }</code></a></li><li><a class="link" href="org.gradle.api.Project.html#org.gradle.api.Project:sourceSets(groovy.lang.Closure)"><code class="literal">sourceSets { }</code></a></li><li><a class="link" href="org.gradle.api.Project.html#org.gradle.api.Project:subprojects(groovy.lang.Closure)"><code class="literal">subprojects { }</code></a></li><li><a class="link" href="org.gradle.api.Project.html#org.gradle.api.Project:publishing(groovy.lang.Closure)"><code class="literal">publishing { }</code></a></li><li><h3>Core types</h3></li><li><a class="link" href="org.gradle.api.Project.html"><code class="literal">Project</code></a></li><li><a class="link" href="org.gradle.api.Task.html"><code class="literal">Task</code></a></li><li><a class="link" href="org.gradle.api.invocation.Gradle.html"><code class="literal">Gradle</code></a></li><li><a class="link" href="org.gradle.api.initialization.Settings.html"><code class="literal">Settings</code></a></li><li><a class="link" href="org.gradle.api.initialization.IncludedBuild.html"><code class="literal">IncludedBuild</code></a></li><li><a class="link" href="org.gradle.api.file.ProjectLayout.html"><code class="literal">ProjectLayout</code></a></li><li><a class="link" href="org.gradle.api.file.BuildLayout.html"><code class="literal">BuildLayout</code></a></li><li><a class="link" href="org.gradle.api.Script.html"><code class="literal">Script</code></a></li><li><a class="link" href="org.gradle.api.tasks.SourceSet.html"><code class="literal">SourceSet</code></a></li><li><a class="link" href="org.gradle.api.tasks.SourceSetOutput.html"><code class="literal">SourceSetOutput</code></a></li><li><a class="link" href="org.gradle.api.file.SourceDirectorySet.html"><code class="literal">SourceDirectorySet</code></a></li><li><a class="link" href="org.gradle.api.artifacts.Configuration.html"><code class="literal">Configuration</code></a></li><li><a class="link" href="org.gradle.api.artifacts.ConsumableConfiguration.html"><code class="literal">ConsumableConfiguration</code></a></li><li><a class="link" href="org.gradle.api.artifacts.ResolvableConfiguration.html"><code class="literal">ResolvableConfiguration</code></a></li><li><a class="link" href="org.gradle.api.artifacts.DependencyScopeConfiguration.html"><code class="literal">DependencyScopeConfiguration</code></a></li><li><a class="link" href="org.gradle.api.artifacts.ResolutionStrategy.html"><code class="literal">ResolutionStrategy</code></a></li><li><a class="link" href="org.gradle.api.artifacts.query.ArtifactResolutionQuery.html"><code class="literal">ArtifactResolutionQuery</code></a></li><li><a class="link" href="org.gradle.api.artifacts.ComponentSelection.html"><code class="literal">ComponentSelection</code></a></li><li><a class="link" href="org.gradle.api.artifacts.ComponentSelectionRules.html"><code class="literal">ComponentSelectionRules</code></a></li><li><a class="link" href="org.gradle.api.artifacts.dsl.DependencyAdder.html"><code class="literal">DependencyAdder</code></a></li><li><a class="link" href="org.gradle.api.plugins.ExtensionAware.html"><code class="literal">ExtensionAware</code></a></li><li><a class="link" href="org.gradle.api.plugins.ExtraPropertiesExtension.html"><code class="literal">ExtraPropertiesExtension</code></a></li><li><a class="link" href="org.gradle.plugin.use.PluginDependenciesSpec.html"><code class="literal">PluginDependenciesSpec</code></a></li><li><a class="link" href="org.gradle.plugin.use.PluginDependencySpec.html"><code class="literal">PluginDependencySpec</code></a></li><li><a class="link" href="org.gradle.plugin.management.PluginManagementSpec.html"><code class="literal">PluginManagementSpec</code></a></li><li><a class="link" href="org.gradle.api.provider.ProviderFactory.html"><code class="literal">ProviderFactory</code></a></li><li><a class="link" href="org.gradle.api.resources.ResourceHandler.html"><code class="literal">ResourceHandler</code></a></li><li><a class="link" href="org.gradle.api.resources.TextResourceFactory.html"><code class="literal">TextResourceFactory</code></a></li><li><a class="link" href="org.gradle.work.InputChanges.html"><code class="literal">InputChanges</code></a></li><li><a class="link" href="org.gradle.api.distribution.Distribution.html"><code class="literal">Distribution</code></a></li><li><h3>Publishing types</h3></li><li><a class="link" href="org.gradle.api.publish.PublishingExtension.html"><code class="literal">PublishingExtension</code></a></li><li><a class="link" href="org.gradle.api.publish.ivy.IvyPublication.html"><code class="literal">IvyPublication</code></a></li><li><a class="link" href="org.gradle.api.publish.ivy.IvyArtifact.html"><code class="literal">IvyArtifact</code></a></li><li><a class="link" href="org.gradle.api.publish.ivy.IvyArtifactSet.html"><code class="literal">IvyArtifactSet</code></a></li><li><a class="link" href="org.gradle.api.publish.ivy.IvyModuleDescriptorSpec.html"><code class="literal">IvyModuleDescriptorSpec</code></a></li><li><a class="link" href="org.gradle.api.publish.ivy.IvyModuleDescriptorAuthor.html"><code class="literal">IvyModuleDescriptorAuthor</code></a></li><li><a class="link" href="org.gradle.api.publish.ivy.IvyModuleDescriptorLicense.html"><code class="literal">IvyModuleDescriptorLicense</code></a></li><li><a class="link" href="org.gradle.api.publish.ivy.IvyModuleDescriptorDescription.html"><code class="literal">IvyModuleDescriptorDescription</code></a></li><li><a class="link" href="org.gradle.api.publish.maven.MavenPublication.html"><code class="literal">MavenPublication</code></a></li><li><a class="link" href="org.gradle.api.publish.maven.MavenArtifact.html"><code class="literal">MavenArtifact</code></a></li><li><a class="link" href="org.gradle.api.publish.maven.MavenArtifactSet.html"><code class="literal">MavenArtifactSet</code></a></li><li><a class="link" href="org.gradle.api.publish.maven.MavenPom.html"><code class="literal">MavenPom</code></a></li><li><a class="link" href="org.gradle.api.publish.maven.MavenPomCiManagement.html"><code class="literal">MavenPomCiManagement</code></a></li><li><a class="link" href="org.gradle.api.publish.maven.MavenPomContributor.html"><code class="literal">MavenPomContributor</code></a></li><li><a class="link" href="org.gradle.api.publish.maven.MavenPomContributorSpec.html"><code class="literal">MavenPomContributorSpec</code></a></li><li><a class="link" href="org.gradle.api.publish.maven.MavenPomDeveloper.html"><code class="literal">MavenPomDeveloper</code></a></li><li><a class="link" href="org.gradle.api.publish.maven.MavenPomDeveloperSpec.html"><code class="literal">MavenPomDeveloperSpec</code></a></li><li><a class="link" href="org.gradle.api.publish.maven.MavenPomDistributionManagement.html"><code class="literal">MavenPomDistributionManagement</code></a></li><li><a class="link" href="org.gradle.api.publish.maven.MavenPomIssueManagement.html"><code class="literal">MavenPomIssueManagement</code></a></li><li><a class="link" href="org.gradle.api.publish.maven.MavenPomLicense.html"><code class="literal">MavenPomLicense</code></a></li><li><a class="link" href="org.gradle.api.publish.maven.MavenPomLicenseSpec.html"><code class="literal">MavenPomLicenseSpec</code></a></li><li><a class="link" href="org.gradle.api.publish.maven.MavenPomMailingList.html"><code class="literal">MavenPomMailingList</code></a></li><li><a class="link" href="org.gradle.api.publish.maven.MavenPomMailingListSpec.html"><code class="literal">MavenPomMailingListSpec</code></a></li><li><a class="link" href="org.gradle.api.publish.maven.MavenPomOrganization.html"><code class="literal">MavenPomOrganization</code></a></li><li><a class="link" href="org.gradle.api.publish.maven.MavenPomRelocation.html"><code class="literal">MavenPomRelocation</code></a></li><li><a class="link" href="org.gradle.api.publish.maven.MavenPomScm.html"><code class="literal">MavenPomScm</code></a></li><li><h3>Container types</h3></li><li><a class="link" href="org.gradle.api.tasks.TaskContainer.html"><code class="literal">TaskContainer</code></a></li><li><a class="link" href="org.gradle.api.artifacts.ConfigurationContainer.html"><code class="literal">ConfigurationContainer</code></a></li><li><a class="link" href="org.gradle.api.artifacts.dsl.RepositoryHandler.html"><code class="literal">RepositoryHandler</code></a></li><li><a class="link" href="org.gradle.api.artifacts.dsl.DependencyHandler.html"><code class="literal">DependencyHandler</code></a></li><li><a class="link" href="org.gradle.api.artifacts.dsl.ComponentMetadataHandler.html"><code class="literal">ComponentMetadataHandler</code></a></li><li><a class="link" href="org.gradle.api.artifacts.dsl.ArtifactHandler.html"><code class="literal">ArtifactHandler</code></a></li><li><h3>Build Cache types</h3></li><li><a class="link" href="org.gradle.caching.configuration.BuildCacheConfiguration.html"><code class="literal">BuildCacheConfiguration</code></a></li><li><a class="link" href="org.gradle.caching.local.DirectoryBuildCache.html"><code class="literal">DirectoryBuildCache</code></a></li><li><a class="link" href="org.gradle.caching.http.HttpBuildCache.html"><code class="literal">HttpBuildCache</code></a></li><li><h3>Input Normalization types</h3></li><li><a class="link" href="org.gradle.normalization.InputNormalizationHandler.html"><code class="literal">InputNormalizationHandler</code></a></li><li><a class="link" href="org.gradle.normalization.InputNormalization.html"><code class="literal">InputNormalization</code></a></li><li><a class="link" href="org.gradle.normalization.RuntimeClasspathNormalization.html"><code class="literal">RuntimeClasspathNormalization</code></a></li><li><h3>Help Task types</h3></li><li><a class="link" href="org.gradle.api.tasks.diagnostics.TaskReportTask.html"><code class="literal">TaskReportTask</code></a></li><li><a class="link" href="org.gradle.api.tasks.diagnostics.ProjectReportTask.html"><code class="literal">ProjectReportTask</code></a></li><li><a class="link" href="org.gradle.api.tasks.diagnostics.DependencyReportTask.html"><code class="literal">DependencyReportTask</code></a></li><li><a class="link" href="org.gradle.api.tasks.diagnostics.DependencyInsightReportTask.html"><code class="literal">DependencyInsightReportTask</code></a></li><li><a class="link" href="org.gradle.api.tasks.diagnostics.PropertyReportTask.html"><code class="literal">PropertyReportTask</code></a></li><li><a class="link" href="org.gradle.api.reporting.components.ComponentReport.html"><code class="literal">ComponentReport</code></a></li><li><a class="link" href="org.gradle.api.reporting.dependents.DependentComponentsReport.html"><code class="literal">DependentComponentsReport</code></a></li><li><a class="link" href="org.gradle.api.reporting.model.ModelReport.html"><code class="literal">ModelReport</code></a></li><li><a class="link" href="org.gradle.api.tasks.diagnostics.OutgoingVariantsReportTask.html"><code class="literal">OutgoingVariantsReportTask</code></a></li><li><a class="link" href="org.gradle.api.tasks.diagnostics.ResolvableConfigurationsReportTask.html"><code class="literal">ResolvableConfigurationsReportTask</code></a></li><li><h3>Task types</h3></li><li><a class="link" href="org.gradle.api.plugins.antlr.AntlrTask.html"><code class="literal">AntlrTask</code></a></li><li><a class="link" href="org.gradle.api.tasks.diagnostics.BuildEnvironmentReportTask.html"><code class="literal">BuildEnvironmentReportTask</code></a></li><li><a class="link" href="org.gradle.api.plugins.quality.Checkstyle.html"><code class="literal">Checkstyle</code></a></li><li><a class="link" href="org.gradle.api.plugins.quality.CodeNarc.html"><code class="literal">CodeNarc</code></a></li><li><a class="link" href="org.gradle.api.tasks.Copy.html"><code class="literal">Copy</code></a></li><li><a class="link" href="org.gradle.jvm.application.tasks.CreateStartScripts.html"><code class="literal">CreateStartScripts</code></a></li><li><a class="link" href="org.gradle.api.tasks.Delete.html"><code class="literal">Delete</code></a></li><li><a class="link" href="org.gradle.plugins.ear.Ear.html"><code class="literal">Ear</code></a></li><li><a class="link" href="org.gradle.api.tasks.Exec.html"><code class="literal">Exec</code></a></li><li><a class="link" href="org.gradle.api.publish.ivy.tasks.GenerateIvyDescriptor.html"><code class="literal">GenerateIvyDescriptor</code></a></li><li><a class="link" href="org.gradle.api.publish.maven.tasks.GenerateMavenPom.html"><code class="literal">GenerateMavenPom</code></a></li><li><a class="link" href="org.gradle.api.reporting.GenerateBuildDashboard.html"><code class="literal">GenerateBuildDashboard</code></a></li><li><a class="link" href="org.gradle.api.tasks.GradleBuild.html"><code class="literal">GradleBuild</code></a></li><li><a class="link" href="org.gradle.api.tasks.compile.GroovyCompile.html"><code class="literal">GroovyCompile</code></a></li><li><a class="link" href="org.gradle.api.tasks.javadoc.Groovydoc.html"><code class="literal">Groovydoc</code></a></li><li><a class="link" href="org.gradle.api.reporting.dependencies.HtmlDependencyReportTask.html"><code class="literal">HtmlDependencyReportTask</code></a></li><li><a class="link" href="org.gradle.testing.jacoco.tasks.JacocoReport.html"><code class="literal">JacocoReport</code></a></li><li><a class="link" href="org.gradle.testing.jacoco.tasks.JacocoCoverageVerification.html"><code class="literal">JacocoCoverageVerification</code></a></li><li><a class="link" href="org.gradle.api.tasks.bundling.Jar.html"><code class="literal">Jar</code></a></li><li><a class="link" href="org.gradle.api.tasks.compile.JavaCompile.html"><code class="literal">JavaCompile</code></a></li><li><a class="link" href="org.gradle.api.tasks.javadoc.Javadoc.html"><code class="literal">Javadoc</code></a></li><li><a class="link" href="org.gradle.api.tasks.JavaExec.html"><code class="literal">JavaExec</code></a></li><li><a class="link" href="org.gradle.api.plugins.quality.Pmd.html"><code class="literal">Pmd</code></a></li><li><a class="link" href="org.gradle.language.jvm.tasks.ProcessResources.html"><code class="literal">ProcessResources</code></a></li><li><a class="link" href="org.gradle.api.publish.ivy.tasks.PublishToIvyRepository.html"><code class="literal">PublishToIvyRepository</code></a></li><li><a class="link" href="org.gradle.api.publish.maven.tasks.PublishToMavenRepository.html"><code class="literal">PublishToMavenRepository</code></a></li><li><a class="link" href="org.gradle.api.tasks.scala.ScalaCompile.html"><code class="literal">ScalaCompile</code></a></li><li><a class="link" href="org.gradle.api.tasks.scala.ScalaDoc.html"><code class="literal">ScalaDoc</code></a></li><li><a class="link" href="org.gradle.buildinit.tasks.InitBuild.html"><code class="literal">InitBuild</code></a></li><li><a class="link" href="org.gradle.plugins.signing.Sign.html"><code class="literal">Sign</code></a></li><li><a class="link" href="org.gradle.api.tasks.Sync.html"><code class="literal">Sync</code></a></li><li><a class="link" href="org.gradle.api.tasks.bundling.Tar.html"><code class="literal">Tar</code></a></li><li><a class="link" href="org.gradle.api.tasks.testing.AbstractTestTask.html"><code class="literal">AbstractTestTask</code></a></li><li><a class="link" href="org.gradle.api.tasks.testing.Test.html"><code class="literal">Test</code></a></li><li><a class="link" href="org.gradle.api.tasks.testing.TestReport.html"><code class="literal">TestReport</code></a></li><li><a class="link" href="org.gradle.api.tasks.bundling.War.html"><code class="literal">War</code></a></li><li><a class="link" href="org.gradle.api.tasks.wrapper.Wrapper.html"><code class="literal">Wrapper</code></a></li><li><a class="link" href="org.gradle.api.tasks.WriteProperties.html"><code class="literal">WriteProperties</code></a></li><li><a class="link" href="org.gradle.api.tasks.bundling.Zip.html"><code class="literal">Zip</code></a></li><li><h3>Test types</h3></li><li><a class="link" href="org.gradle.testing.base.TestingExtension.html"><code class="literal">TestingExtension</code></a></li><li><a class="link" href="org.gradle.testing.base.TestSuite.html"><code class="literal">TestSuite</code></a></li><li><a class="link" href="org.gradle.api.plugins.jvm.JvmTestSuite.html"><code class="literal">JvmTestSuite</code></a></li><li><a class="link" href="org.gradle.testing.base.TestSuiteTarget.html"><code class="literal">TestSuiteTarget</code></a></li><li><a class="link" href="org.gradle.api.plugins.jvm.JvmTestSuiteTarget.html"><code class="literal">JvmTestSuiteTarget</code></a></li><li><a class="link" href="org.gradle.api.tasks.testing.Test.html"><code class="literal">Test</code></a></li><li><a class="link" href="org.gradle.api.artifacts.dsl.Dependencies.html"><code class="literal">Dependencies</code></a></li><li><a class="link" href="org.gradle.api.artifacts.dsl.GradleDependencies.html"><code class="literal">GradleDependencies</code></a></li><li><a class="link" href="org.gradle.api.plugins.jvm.TestFixturesDependencyModifiers.html"><code class="literal">TestFixturesDependencyModifiers</code></a></li><li><a class="link" href="org.gradle.api.plugins.jvm.PlatformDependencyModifiers.html"><code class="literal">PlatformDependencyModifiers</code></a></li><li><a class="link" href="org.gradle.api.plugins.jvm.JvmComponentDependencies.html"><code class="literal">JvmComponentDependencies</code></a></li><li><h3>Reporting types</h3></li><li><a class="link" href="org.gradle.api.reporting.CustomizableHtmlReport.html"><code class="literal">CustomizableHtmlReport</code></a></li><li><a class="link" href="org.gradle.api.reporting.SingleFileReport.html"><code class="literal">SingleFileReport</code></a></li><li><a class="link" href="org.gradle.api.reporting.DirectoryReport.html"><code class="literal">DirectoryReport</code></a></li><li><a class="link" href="org.gradle.api.reporting.Report.html"><code class="literal">Report</code></a></li><li><a class="link" href="org.gradle.api.reporting.Reporting.html"><code class="literal">Reporting</code></a></li><li><a class="link" href="org.gradle.api.reporting.ReportContainer.html"><code class="literal">ReportContainer</code></a></li><li><a class="link" href="org.gradle.api.reporting.ReportingExtension.html"><code class="literal">ReportingExtension</code></a></li><li><a class="link" href="org.gradle.api.tasks.testing.AggregateTestReport.html"><code class="literal">AggregateTestReport</code></a></li><li><a class="link" href="org.gradle.testing.jacoco.plugins.JacocoCoverageReport.html"><code class="literal">JacocoCoverageReport</code></a></li><li><h3>Eclipse/IDEA model types</h3></li><li><a class="link" href="org.gradle.plugins.ide.eclipse.model.EclipseModel.html"><code class="literal">EclipseModel</code></a></li><li><a class="link" href="org.gradle.plugins.ide.eclipse.model.EclipseProject.html"><code class="literal">EclipseProject</code></a></li><li><a class="link" href="org.gradle.plugins.ide.eclipse.model.EclipseClasspath.html"><code class="literal">EclipseClasspath</code></a></li><li><a class="link" href="org.gradle.plugins.ide.eclipse.model.EclipseJdt.html"><code class="literal">EclipseJdt</code></a></li><li><a class="link" href="org.gradle.plugins.ide.eclipse.model.EclipseWtp.html"><code class="literal">EclipseWtp</code></a></li><li><a class="link" href="org.gradle.plugins.ide.eclipse.model.EclipseWtpComponent.html"><code class="literal">EclipseWtpComponent</code></a></li><li><a class="link" href="org.gradle.plugins.ide.eclipse.model.EclipseWtpFacet.html"><code class="literal">EclipseWtpFacet</code></a></li><li><a class="link" href="org.gradle.plugins.ide.idea.model.IdeaModel.html"><code class="literal">IdeaModel</code></a></li><li><a class="link" href="org.gradle.plugins.ide.idea.model.IdeaProject.html"><code class="literal">IdeaProject</code></a></li><li><a class="link" href="org.gradle.plugins.ide.idea.model.IdeaModule.html"><code class="literal">IdeaModule</code></a></li><li><a class="link" href="org.gradle.plugins.ide.idea.model.IdeaWorkspace.html"><code class="literal">IdeaWorkspace</code></a></li><li><a class="link" href="org.gradle.plugins.ide.api.XmlFileContentMerger.html"><code class="literal">XmlFileContentMerger</code></a></li><li><a class="link" href="org.gradle.plugins.ide.api.FileContentMerger.html"><code class="literal">FileContentMerger</code></a></li><li><h3>Eclipse/IDEA task types</h3></li><li><a class="link" href="org.gradle.plugins.ide.eclipse.GenerateEclipseProject.html"><code class="literal">GenerateEclipseProject</code></a></li><li><a class="link" href="org.gradle.plugins.ide.eclipse.GenerateEclipseClasspath.html"><code class="literal">GenerateEclipseClasspath</code></a></li><li><a class="link" href="org.gradle.plugins.ide.eclipse.GenerateEclipseJdt.html"><code class="literal">GenerateEclipseJdt</code></a></li><li><a class="link" href="org.gradle.plugins.ide.eclipse.GenerateEclipseWtpComponent.html"><code class="literal">GenerateEclipseWtpComponent</code></a></li><li><a class="link" href="org.gradle.plugins.ide.eclipse.GenerateEclipseWtpFacet.html"><code class="literal">GenerateEclipseWtpFacet</code></a></li><li><a class="link" href="org.gradle.plugins.ide.idea.GenerateIdeaModule.html"><code class="literal">GenerateIdeaModule</code></a></li><li><a class="link" href="org.gradle.plugins.ide.idea.GenerateIdeaProject.html"><code class="literal">GenerateIdeaProject</code></a></li><li><a class="link" href="org.gradle.plugins.ide.idea.GenerateIdeaWorkspace.html"><code class="literal">GenerateIdeaWorkspace</code></a></li><li><h3>Xcode task types</h3></li><li><a class="link" href="org.gradle.ide.xcode.tasks.GenerateSchemeFileTask.html"><code class="literal">GenerateSchemeFileTask</code></a></li><li><a class="link" href="org.gradle.ide.xcode.tasks.GenerateWorkspaceSettingsFileTask.html"><code class="literal">GenerateWorkspaceSettingsFileTask</code></a></li><li><a class="link" href="org.gradle.ide.xcode.tasks.GenerateXcodeProjectFileTask.html"><code class="literal">GenerateXcodeProjectFileTask</code></a></li><li><a class="link" href="org.gradle.ide.xcode.tasks.GenerateXcodeWorkspaceFileTask.html"><code class="literal">GenerateXcodeWorkspaceFileTask</code></a></li><li><h3>Visual Studio task types</h3></li><li><a class="link" href="org.gradle.ide.visualstudio.tasks.GenerateSolutionFileTask.html"><code class="literal">GenerateSolutionFileTask</code></a></li><li><a class="link" href="org.gradle.ide.visualstudio.tasks.GenerateProjectFileTask.html"><code class="literal">GenerateProjectFileTask</code></a></li><li><a class="link" href="org.gradle.ide.visualstudio.tasks.GenerateFiltersFileTask.html"><code class="literal">GenerateFiltersFileTask</code></a></li><li><h3>Artifact transform types</h3></li><li><a class="link" href="org.gradle.api.artifacts.transform.TransformAction.html"><code class="literal">TransformAction</code></a></li><li><a class="link" href="org.gradle.api.artifacts.transform.TransformOutputs.html"><code class="literal">TransformOutputs</code></a></li><li><a class="link" href="org.gradle.api.artifacts.transform.TransformSpec.html"><code class="literal">TransformSpec</code></a></li><li><h3>Native tool chain types</h3></li><li><a class="link" href="org.gradle.nativeplatform.toolchain.Gcc.html"><code class="literal">Gcc</code></a></li><li><a class="link" href="org.gradle.nativeplatform.toolchain.Clang.html"><code class="literal">Clang</code></a></li><li><a class="link" href="org.gradle.nativeplatform.toolchain.VisualCpp.html"><code class="literal">VisualCpp</code></a></li><li><a class="link" href="org.gradle.nativeplatform.toolchain.Swiftc.html"><code class="literal">Swiftc</code></a></li><li><h3>C++ component types</h3></li><li><a class="link" href="org.gradle.language.cpp.CppApplication.html"><code class="literal">CppApplication</code></a></li><li><a class="link" href="org.gradle.language.cpp.CppLibrary.html"><code class="literal">CppLibrary</code></a></li><li><a class="link" href="org.gradle.nativeplatform.test.cpp.CppTestSuite.html"><code class="literal">CppTestSuite</code></a></li><li><h3>Swift component types</h3></li><li><a class="link" href="org.gradle.language.swift.SwiftApplication.html"><code class="literal">SwiftApplication</code></a></li><li><a class="link" href="org.gradle.language.swift.SwiftLibrary.html"><code class="literal">SwiftLibrary</code></a></li><li><a class="link" href="org.gradle.nativeplatform.test.xctest.SwiftXCTestSuite.html"><code class="literal">SwiftXCTestSuite</code></a></li><li><h3>Native component task types</h3></li><li><a class="link" href="org.gradle.language.cpp.tasks.CppCompile.html"><code class="literal">CppCompile</code></a></li><li><a class="link" href="org.gradle.language.swift.tasks.SwiftCompile.html"><code class="literal">SwiftCompile</code></a></li><li><a class="link" href="org.gradle.nativeplatform.tasks.LinkExecutable.html"><code class="literal">LinkExecutable</code></a></li><li><a class="link" href="org.gradle.nativeplatform.tasks.LinkSharedLibrary.html"><code class="literal">LinkSharedLibrary</code></a></li><li><a class="link" href="org.gradle.nativeplatform.tasks.CreateStaticLibrary.html"><code class="literal">CreateStaticLibrary</code></a></li><li><a class="link" href="org.gradle.nativeplatform.tasks.LinkMachOBundle.html"><code class="literal">LinkMachOBundle</code></a></li><li><a class="link" href="org.gradle.nativeplatform.tasks.InstallExecutable.html"><code class="literal">InstallExecutable</code></a></li><li><a class="link" href="org.gradle.nativeplatform.test.xctest.tasks.InstallXCTestBundle.html"><code class="literal">InstallXCTestBundle</code></a></li><li><a class="link" href="org.gradle.nativeplatform.test.tasks.RunTestExecutable.html"><code class="literal">RunTestExecutable</code></a></li><li><a class="link" href="org.gradle.nativeplatform.test.xctest.tasks.XCTest.html"><code class="literal">XCTest</code></a></li></ul></nav><div class="content"><div id="content"><div class="chapter"><div class="titlepage"><div><div><h1 xmlns:xslthl="http://xslthl.sf.net"><a name="org.gradle.api.invocation.Gradle"></a>Gradle</h1></div></div></div><div class="toc"><p><b>Table of Contents</b></p><dl><dt><span class="section"><a href="org.gradle.api.invocation.Gradle.html#N17A76">Properties</a></span></dt><dt><span class="section"><a href="org.gradle.api.invocation.Gradle.html#N17B01">Methods</a></span></dt><dt><span class="section"><a href="org.gradle.api.invocation.Gradle.html#N17C2F">Script blocks</a></span></dt><dt><span class="section"><a href="org.gradle.api.invocation.Gradle.html#N17C34">Property details</a></span></dt><dt><span class="section"><a href="org.gradle.api.invocation.Gradle.html#N17D14">Method details</a></span></dt></dl></div><div class="segmentedlist"><table><tr><th>API Documentation:</th><td><a class="ulink" href="../javadoc/org/gradle/api/invocation/Gradle.html" target="_top"><code class="classname">Gradle</code></a></td></tr></table></div><p>Represents an invocation of Gradle.

</p><p>You can obtain a <code class="literal">Gradle</code> instance by calling <a class="ulink" href="../dsl/org.gradle.api.Project.html#org.gradle.api.Project:gradle" target="_top"><code class="classname">Project.getGradle()</code></a>.</p><div class="section"><div class="titlepage"><div><div><h3 class="title"><a xmlns:xslthl="http://xslthl.sf.net" name="N17A76" class="section-anchor" href="#N17A76"></a>Properties</h3></div></div></div><div xmlns:xslthl="http://xslthl.sf.net" class="table"><div class="table-contents"><table id="N17A79"><thead><tr><td>Property</td><td>Description</td></tr></thead><tr><td><a class="link" href="org.gradle.api.invocation.Gradle.html#org.gradle.api.invocation.Gradle:extensions"><code class="literal">extensions</code></a></td><td><p>The container of extensions.</p></td></tr><tr><td><a class="link" href="org.gradle.api.invocation.Gradle.html#org.gradle.api.invocation.Gradle:gradle"><code class="literal">gradle</code></a></td><td><p>Returns this <code class="literal">Gradle</code> instance.

This method is useful in init scripts to explicitly access Gradle
properties and methods. For example, using <code class="literal">gradle.parent</code> can express your intent better than using
<code class="literal">parent</code>. This property also allows you to access Gradle properties from a scope where the property
may be hidden, such as, for example, from a method or closure.</p></td></tr><tr><td><a class="link" href="org.gradle.api.invocation.Gradle.html#org.gradle.api.invocation.Gradle:gradleHomeDir"><code class="literal">gradleHomeDir</code></a></td><td><p>The Gradle home directory, if any.

This directory is the directory containing the Gradle distribution executing this build.
</p></td></tr><tr><td><a class="link" href="org.gradle.api.invocation.Gradle.html#org.gradle.api.invocation.Gradle:gradleUserHomeDir"><code class="literal">gradleUserHomeDir</code></a></td><td><p>The Gradle user home directory.

This directory is used to cache downloaded resources, compiled build scripts and so on.</p></td></tr><tr><td><a class="link" href="org.gradle.api.invocation.Gradle.html#org.gradle.api.invocation.Gradle:gradleVersion"><code class="literal">gradleVersion</code></a></td><td><p>The current Gradle version.</p></td></tr><tr><td><a class="link" href="org.gradle.api.invocation.Gradle.html#org.gradle.api.invocation.Gradle:includedBuilds"><code class="literal">includedBuilds</code></a></td><td><p>The included builds for this build.</p></td></tr><tr><td><a class="link" href="org.gradle.api.invocation.Gradle.html#org.gradle.api.invocation.Gradle:parent"><code class="literal">parent</code></a></td><td><p>The parent build of this build, if any.</p></td></tr><tr><td><a class="link" href="org.gradle.api.invocation.Gradle.html#org.gradle.api.invocation.Gradle:pluginManager"><code class="literal">pluginManager</code></a></td><td><p>The plugin manager for this plugin aware object.</p></td></tr><tr><td><a class="link" href="org.gradle.api.invocation.Gradle.html#org.gradle.api.invocation.Gradle:plugins"><code class="literal">plugins</code></a></td><td><p>The container of plugins that have been applied to this object.
</p></td></tr><tr><td><a class="link" href="org.gradle.api.invocation.Gradle.html#org.gradle.api.invocation.Gradle:rootProject"><code class="literal">rootProject</code></a></td><td><p>The root project of this build.</p></td></tr><tr><td><a class="link" href="org.gradle.api.invocation.Gradle.html#org.gradle.api.invocation.Gradle:startParameter"><code class="literal">startParameter</code></a></td><td><p>The <a class="ulink" href="../javadoc/org/gradle/StartParameter.html" target="_top"><code class="classname">StartParameter</code></a> used to start this build.</p></td></tr><tr><td><a class="link" href="org.gradle.api.invocation.Gradle.html#org.gradle.api.invocation.Gradle:taskGraph"><code class="literal">taskGraph</code></a></td><td><p>The <a class="ulink" href="../javadoc/org/gradle/api/execution/TaskExecutionGraph.html" target="_top"><code class="classname">TaskExecutionGraph</code></a> for this build.</p></td></tr></table></div></div></div><div class="section"><div class="titlepage"><div><div><h3 class="title"><a xmlns:xslthl="http://xslthl.sf.net" name="N17B01" class="section-anchor" href="#N17B01"></a>Methods</h3></div></div></div><div xmlns:xslthl="http://xslthl.sf.net" class="table"><div class="table-contents"><table id="N17B04"><thead><tr><td>Method</td><td>Description</td></tr></thead><tr><td><code class="literal"><a class="link" href="org.gradle.api.invocation.Gradle.html#org.gradle.api.invocation.Gradle:addBuildListener(org.gradle.BuildListener)">addBuildListener</a>(buildListener)</code></td><td><p>Adds a <a class="ulink" href="../javadoc/org/gradle/BuildListener.html" target="_top"><code class="classname">BuildListener</code></a> to this Build instance.

The listener is notified of events which occur during the execution of the build.</p></td></tr><tr><td><code class="literal"><a class="link" href="org.gradle.api.invocation.Gradle.html#org.gradle.api.invocation.Gradle:addListener(java.lang.Object)">addListener</a>(listener)</code></td><td><p>Adds the given listener to this build. The listener may implement any of the given listener interfaces:

</p></td></tr><tr><td><code class="literal"><a class="link" href="org.gradle.api.invocation.Gradle.html#org.gradle.api.invocation.Gradle:addProjectEvaluationListener(org.gradle.api.ProjectEvaluationListener)">addProjectEvaluationListener</a>(listener)</code></td><td><p>Adds a listener to this build, to receive notifications as projects are evaluated.</p></td></tr><tr><td><code class="literal"><a class="link" href="org.gradle.api.invocation.Gradle.html#org.gradle.api.invocation.Gradle:afterProject(groovy.lang.Closure)">afterProject</a>(closure)</code></td><td><p>Adds a closure to be called immediately after a project is evaluated.

The project is passed to the closure as the first parameter. The project evaluation failure, if any,
is passed as the second parameter. Both parameters are optional.</p></td></tr><tr><td><code class="literal"><a class="link" href="org.gradle.api.invocation.Gradle.html#org.gradle.api.invocation.Gradle:afterProject(org.gradle.api.Action)">afterProject</a>(action)</code></td><td><p>Adds an action to be called immediately after a project is evaluated.</p></td></tr><tr><td><code class="literal"><a class="link" href="org.gradle.api.invocation.Gradle.html#org.gradle.api.invocation.Gradle:allprojects(org.gradle.api.Action)">allprojects</a>(action)</code></td><td><p>Adds an action to execute against all projects of this build.

The action is executed immediately against all projects which are
already available. It is also executed as subsequent projects are added to this build.</p></td></tr><tr><td><code class="literal"><a class="link" href="org.gradle.api.invocation.Gradle.html#org.gradle.api.invocation.Gradle:apply(groovy.lang.Closure)">apply</a>(closure)</code></td><td><p>Applies zero or more plugins or scripts.
</p></td></tr><tr><td><code class="literal"><a class="link" href="org.gradle.api.invocation.Gradle.html#org.gradle.api.invocation.Gradle:apply(java.util.Map)">apply</a>(options)</code></td><td><p>Applies a plugin or script, using the given options provided as a map. Does nothing if the plugin has already been applied.
</p></td></tr><tr><td><code class="literal"><a class="link" href="org.gradle.api.invocation.Gradle.html#org.gradle.api.invocation.Gradle:apply(org.gradle.api.Action)">apply</a>(action)</code></td><td><p>Applies zero or more plugins or scripts.
</p></td></tr><tr><td><code class="literal"><a class="link" href="org.gradle.api.invocation.Gradle.html#org.gradle.api.invocation.Gradle:beforeProject(groovy.lang.Closure)">beforeProject</a>(closure)</code></td><td><p>Adds a closure to be called immediately before a project is evaluated. The project is passed to the closure as a
parameter.</p></td></tr><tr><td><code class="literal"><a class="link" href="org.gradle.api.invocation.Gradle.html#org.gradle.api.invocation.Gradle:beforeProject(org.gradle.api.Action)">beforeProject</a>(action)</code></td><td><p>Adds an action to be called immediately before a project is evaluated.</p></td></tr><tr><td><code class="literal"><a class="link" href="org.gradle.api.invocation.Gradle.html#org.gradle.api.invocation.Gradle:beforeSettings(groovy.lang.Closure)">beforeSettings</a>(closure)</code></td><td><p>Adds an action to be called before the build settings have been loaded and evaluated.</p></td></tr><tr><td><code class="literal"><a class="link" href="org.gradle.api.invocation.Gradle.html#org.gradle.api.invocation.Gradle:beforeSettings(org.gradle.api.Action)">beforeSettings</a>(action)</code></td><td><p>Adds an action to be called before the build settings have been loaded and evaluated.</p></td></tr><tr><td><code class="literal"><a class="link" href="org.gradle.api.invocation.Gradle.html#org.gradle.api.invocation.Gradle:buildFinished(groovy.lang.Closure)">buildFinished</a>(closure)</code></td><td><div class="caution" title="Caution">Deprecated</div><p>Adds a closure to be called when the build is completed.

All selected tasks have been executed.
A <a class="ulink" href="../javadoc/org/gradle/BuildResult.html" target="_top"><code class="classname">BuildResult</code></a> instance is passed to the closure as a parameter.</p></td></tr><tr><td><code class="literal"><a class="link" href="org.gradle.api.invocation.Gradle.html#org.gradle.api.invocation.Gradle:buildFinished(org.gradle.api.Action)">buildFinished</a>(action)</code></td><td><div class="caution" title="Caution">Deprecated</div><p>Adds an action to be called when the build is completed.

All selected tasks have been executed.</p></td></tr><tr><td><code class="literal"><a class="link" href="org.gradle.api.invocation.Gradle.html#org.gradle.api.invocation.Gradle:includedBuild(java.lang.String)">includedBuild</a>(name)</code></td><td><p>Returns the included build with the specified name for this build.</p></td></tr><tr><td><code class="literal"><a class="link" href="org.gradle.api.invocation.Gradle.html#org.gradle.api.invocation.Gradle:projectsEvaluated(groovy.lang.Closure)">projectsEvaluated</a>(closure)</code></td><td><p>Adds a closure to be called when all projects for the build have been evaluated.

The project objects are fully configured and are ready to use to populate the task graph.
This <code class="literal">Gradle</code> instance is passed to the closure as a parameter.</p></td></tr><tr><td><code class="literal"><a class="link" href="org.gradle.api.invocation.Gradle.html#org.gradle.api.invocation.Gradle:projectsEvaluated(org.gradle.api.Action)">projectsEvaluated</a>(action)</code></td><td><p>Adds an action to be called when all projects for the build have been evaluated.

The project objects are fully configured and are ready to use to populate the task graph.</p></td></tr><tr><td><code class="literal"><a class="link" href="org.gradle.api.invocation.Gradle.html#org.gradle.api.invocation.Gradle:projectsLoaded(groovy.lang.Closure)">projectsLoaded</a>(closure)</code></td><td><p>Adds a closure to be called when the projects for the build have been created from the settings.

None of the projects have been evaluated. This <code class="literal">Gradle</code> instance is passed to the closure as a parameter.
</p></td></tr><tr><td><code class="literal"><a class="link" href="org.gradle.api.invocation.Gradle.html#org.gradle.api.invocation.Gradle:projectsLoaded(org.gradle.api.Action)">projectsLoaded</a>(action)</code></td><td><p>Adds an action to be called when the projects for the build have been created from the settings.

None of the projects have been evaluated.</p></td></tr><tr><td><code class="literal"><a class="link" href="org.gradle.api.invocation.Gradle.html#org.gradle.api.invocation.Gradle:removeListener(java.lang.Object)">removeListener</a>(listener)</code></td><td><p>Removes the given listener from this build.</p></td></tr><tr><td><code class="literal"><a class="link" href="org.gradle.api.invocation.Gradle.html#org.gradle.api.invocation.Gradle:removeProjectEvaluationListener(org.gradle.api.ProjectEvaluationListener)">removeProjectEvaluationListener</a>(listener)</code></td><td><p>Removes the given listener from this build.</p></td></tr><tr><td><code class="literal"><a class="link" href="org.gradle.api.invocation.Gradle.html#org.gradle.api.invocation.Gradle:rootProject(org.gradle.api.Action)">rootProject</a>(action)</code></td><td><p>Adds an action to execute against the root project of this build.

If the root project is already available, the action
is executed immediately. Otherwise, the action is executed when the root project becomes available.</p></td></tr><tr><td><code class="literal"><a class="link" href="org.gradle.api.invocation.Gradle.html#org.gradle.api.invocation.Gradle:settingsEvaluated(groovy.lang.Closure)">settingsEvaluated</a>(closure)</code></td><td><p>Adds a closure to be called when the build settings have been loaded and evaluated.

The settings object is fully configured and is ready to use to load the build projects. The
<a class="ulink" href="../dsl/org.gradle.api.initialization.Settings.html" target="_top"><code class="classname">Settings</code></a> object is passed to the closure as a parameter.</p></td></tr><tr><td><code class="literal"><a class="link" href="org.gradle.api.invocation.Gradle.html#org.gradle.api.invocation.Gradle:settingsEvaluated(org.gradle.api.Action)">settingsEvaluated</a>(action)</code></td><td><p>Adds an action to be called when the build settings have been loaded and evaluated.

The settings object is fully configured and is ready to use to load the build projects.</p></td></tr><tr><td><code class="literal"><a class="link" href="org.gradle.api.invocation.Gradle.html#org.gradle.api.invocation.Gradle:useLogger(java.lang.Object)">useLogger</a>(logger)</code></td><td><p>Uses the given object as a logger.

The logger object may implement any of the listener interfaces supported by
<a class="ulink" href="../dsl/org.gradle.api.invocation.Gradle.html#org.gradle.api.invocation.Gradle:addListener(java.lang.Object)" target="_top"><code class="classname">Gradle.addListener(java.lang.Object)</code></a>.
</p></td></tr></table></div></div></div><div class="section"><div class="titlepage"><div><div><h3 class="title"><a xmlns:xslthl="http://xslthl.sf.net" name="N17C2F" class="section-anchor" href="#N17C2F"></a>Script blocks</h3></div></div></div><p>No script blocks</p></div><div class="section"><div class="titlepage"><div><div><h3 class="title"><a xmlns:xslthl="http://xslthl.sf.net" name="N17C34" class="section-anchor" href="#N17C34"></a>Property details</h3></div></div></div><div class="section"><div class="titlepage"><div><div><h4 class="signature"><a xmlns:xslthl="http://xslthl.sf.net" name="org.gradle.api.invocation.Gradle:extensions" class="section-anchor" href="#org.gradle.api.invocation.Gradle:extensions"></a><a class="ulink" href="../javadoc/org/gradle/api/plugins/ExtensionContainer.html" target="_top"><code class="classname">ExtensionContainer</code></a> <code class="literal">extensions</code> (read-only)</h4></div></div></div><p>The container of extensions.</p></div><div class="section"><div class="titlepage"><div><div><h4 class="signature"><a xmlns:xslthl="http://xslthl.sf.net" name="org.gradle.api.invocation.Gradle:gradle" class="section-anchor" href="#org.gradle.api.invocation.Gradle:gradle"></a><a class="ulink" href="../dsl/org.gradle.api.invocation.Gradle.html" target="_top"><code class="classname">Gradle</code></a> <code class="literal">gradle</code> (read-only)</h4></div></div></div><p>Returns this <code class="literal">Gradle</code> instance.

This method is useful in init scripts to explicitly access Gradle
properties and methods. For example, using <code class="literal">gradle.parent</code> can express your intent better than using
<code class="literal">parent</code>. This property also allows you to access Gradle properties from a scope where the property
may be hidden, such as, for example, from a method or closure.</p></div><div class="section"><div class="titlepage"><div><div><h4 class="signature"><a xmlns:xslthl="http://xslthl.sf.net" name="org.gradle.api.invocation.Gradle:gradleHomeDir" class="section-anchor" href="#org.gradle.api.invocation.Gradle:gradleHomeDir"></a><a class="ulink" href="https://docs.oracle.com/javase/8/docs/api/java/io/File.html" target="_top"><code class="classname">File</code></a> <code class="literal">gradleHomeDir</code> (read-only)</h4></div></div></div><p>The Gradle home directory, if any.

This directory is the directory containing the Gradle distribution executing this build.
</p><p>
When using the &ldquo;Gradle Daemon&rdquo;, this may not be the same Gradle distribution that the build was started with.
If an existing daemon process is running that is deemed compatible (e.g. has the desired JVM characteristics)
then this daemon may be used instead of starting a new process and it may have been started from a different &ldquo;gradle home&rdquo;.
However, it is guaranteed to be the same version of Gradle. For more information on the Gradle Daemon, please consult the
<a class="ulink" href="https://docs.gradle.org/current/userguide/gradle_daemon.html" target="_top">User Manual</a>.</p></div><div class="section"><div class="titlepage"><div><div><h4 class="signature"><a xmlns:xslthl="http://xslthl.sf.net" name="org.gradle.api.invocation.Gradle:gradleUserHomeDir" class="section-anchor" href="#org.gradle.api.invocation.Gradle:gradleUserHomeDir"></a><a class="ulink" href="https://docs.oracle.com/javase/8/docs/api/java/io/File.html" target="_top"><code class="classname">File</code></a> <code class="literal">gradleUserHomeDir</code> (read-only)</h4></div></div></div><p>The Gradle user home directory.

This directory is used to cache downloaded resources, compiled build scripts and so on.</p></div><div class="section"><div class="titlepage"><div><div><h4 class="signature"><a xmlns:xslthl="http://xslthl.sf.net" name="org.gradle.api.invocation.Gradle:gradleVersion" class="section-anchor" href="#org.gradle.api.invocation.Gradle:gradleVersion"></a><a class="ulink" href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html" target="_top"><code class="classname">String</code></a> <code class="literal">gradleVersion</code> (read-only)</h4></div></div></div><p>The current Gradle version.</p></div><div class="section"><div class="titlepage"><div><div><h4 class="signature"><a xmlns:xslthl="http://xslthl.sf.net" name="org.gradle.api.invocation.Gradle:includedBuilds" class="section-anchor" href="#org.gradle.api.invocation.Gradle:includedBuilds"></a><code class="classname"><a class="ulink" href="https://docs.oracle.com/javase/8/docs/api/java/util/Collection.html" target="_top"><code class="classname">Collection</code></a>&lt;<a class="ulink" href="../dsl/org.gradle.api.initialization.IncludedBuild.html" target="_top"><code class="classname">IncludedBuild</code></a>&gt;</code> <code class="literal">includedBuilds</code> (read-only)</h4></div></div></div><p>The included builds for this build.</p></div><div class="section"><div class="titlepage"><div><div><h4 class="signature"><a xmlns:xslthl="http://xslthl.sf.net" name="org.gradle.api.invocation.Gradle:parent" class="section-anchor" href="#org.gradle.api.invocation.Gradle:parent"></a><a class="ulink" href="../dsl/org.gradle.api.invocation.Gradle.html" target="_top"><code class="classname">Gradle</code></a> <code class="literal">parent</code> (read-only)</h4></div></div></div><p>The parent build of this build, if any.</p></div><div class="section"><div class="titlepage"><div><div><h4 class="signature"><a xmlns:xslthl="http://xslthl.sf.net" name="org.gradle.api.invocation.Gradle:pluginManager" class="section-anchor" href="#org.gradle.api.invocation.Gradle:pluginManager"></a><a class="ulink" href="../dsl/org.gradle.api.plugins.PluginManager.html" target="_top"><code class="classname">PluginManager</code></a> <code class="literal">pluginManager</code> (read-only)</h4></div></div></div><p>The plugin manager for this plugin aware object.</p></div><div class="section"><div class="titlepage"><div><div><h4 class="signature"><a xmlns:xslthl="http://xslthl.sf.net" name="org.gradle.api.invocation.Gradle:plugins" class="section-anchor" href="#org.gradle.api.invocation.Gradle:plugins"></a><a class="ulink" href="../javadoc/org/gradle/api/plugins/PluginContainer.html" target="_top"><code class="classname">PluginContainer</code></a> <code class="literal">plugins</code> (read-only)</h4></div></div></div><p>The container of plugins that have been applied to this object.
</p><p>
While not deprecated, it is preferred to use the methods of this interface or the <a class="ulink" href="../dsl/org.gradle.api.plugins.PluginAware.html#org.gradle.api.plugins.PluginAware:pluginManager" target="_top"><code class="classname">PluginAware.getPluginManager()</code></a> than use the plugin container.
</p><p>
Use one of the 'apply' methods on this interface or on the <a class="ulink" href="../dsl/org.gradle.api.plugins.PluginAware.html#org.gradle.api.plugins.PluginAware:pluginManager" target="_top"><code class="classname">PluginAware.getPluginManager()</code></a> to apply plugins instead of applying via the plugin container.
</p><p>
Use <a class="ulink" href="../dsl/org.gradle.api.plugins.PluginManager.html#org.gradle.api.plugins.PluginManager:hasPlugin(java.lang.String)" target="_top"><code class="classname">PluginManager.hasPlugin(java.lang.String)</code></a> or similar to query for the application of plugins instead of doing so via the plugin container.</p></div><div class="section"><div class="titlepage"><div><div><h4 class="signature"><a xmlns:xslthl="http://xslthl.sf.net" name="org.gradle.api.invocation.Gradle:rootProject" class="section-anchor" href="#org.gradle.api.invocation.Gradle:rootProject"></a><a class="ulink" href="../dsl/org.gradle.api.Project.html" target="_top"><code class="classname">Project</code></a> <code class="literal">rootProject</code> (read-only)</h4></div></div></div><p>The root project of this build.</p></div><div class="section"><div class="titlepage"><div><div><h4 class="signature"><a xmlns:xslthl="http://xslthl.sf.net" name="org.gradle.api.invocation.Gradle:startParameter" class="section-anchor" href="#org.gradle.api.invocation.Gradle:startParameter"></a><a class="ulink" href="../javadoc/org/gradle/StartParameter.html" target="_top"><code class="classname">StartParameter</code></a> <code class="literal">startParameter</code> (read-only)</h4></div></div></div><p>The <a class="ulink" href="../javadoc/org/gradle/StartParameter.html" target="_top"><code class="classname">StartParameter</code></a> used to start this build.</p></div><div class="section"><div class="titlepage"><div><div><h4 class="signature"><a xmlns:xslthl="http://xslthl.sf.net" name="org.gradle.api.invocation.Gradle:taskGraph" class="section-anchor" href="#org.gradle.api.invocation.Gradle:taskGraph"></a><a class="ulink" href="../javadoc/org/gradle/api/execution/TaskExecutionGraph.html" target="_top"><code class="classname">TaskExecutionGraph</code></a> <code class="literal">taskGraph</code> (read-only)</h4></div></div></div><p>The <a class="ulink" href="../javadoc/org/gradle/api/execution/TaskExecutionGraph.html" target="_top"><code class="classname">TaskExecutionGraph</code></a> for this build.</p></div></div><div class="section"><div class="titlepage"><div><div><h3 class="title"><a xmlns:xslthl="http://xslthl.sf.net" name="N17D14" class="section-anchor" href="#N17D14"></a>Method details</h3></div></div></div><div class="section"><div class="titlepage"><div><div><h4 class="signature"><a xmlns:xslthl="http://xslthl.sf.net" name="org.gradle.api.invocation.Gradle:addBuildListener(org.gradle.BuildListener)" class="section-anchor" href="#org.gradle.api.invocation.Gradle:addBuildListener(org.gradle.BuildListener)"></a><code class="classname">void</code> <code class="literal">addBuildListener</code>(<a class="ulink" href="../javadoc/org/gradle/BuildListener.html" target="_top"><code class="classname">BuildListener</code></a> buildListener)</h4></div></div></div><p>Adds a <a class="ulink" href="../javadoc/org/gradle/BuildListener.html" target="_top"><code class="classname">BuildListener</code></a> to this Build instance.

The listener is notified of events which occur during the execution of the build.</p></div><div class="section"><div class="titlepage"><div><div><h4 class="signature"><a xmlns:xslthl="http://xslthl.sf.net" name="org.gradle.api.invocation.Gradle:addListener(java.lang.Object)" class="section-anchor" href="#org.gradle.api.invocation.Gradle:addListener(java.lang.Object)"></a><code class="classname">void</code> <code class="literal">addListener</code>(<a class="ulink" href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html" target="_top"><code class="classname">Object</code></a> listener)</h4></div></div></div><p>Adds the given listener to this build. The listener may implement any of the given listener interfaces:

</p><div class="itemizedlist"><ul class="itemizedlist"><li class="listitem"><a class="ulink" href="../javadoc/org/gradle/api/execution/TaskExecutionGraphListener.html" target="_top"><code class="classname">TaskExecutionGraphListener</code></a></li><li class="listitem"><a class="ulink" href="../javadoc/org/gradle/api/ProjectEvaluationListener.html" target="_top"><code class="classname">ProjectEvaluationListener</code></a></li><li class="listitem"><a class="ulink" href="../javadoc/org/gradle/api/artifacts/DependencyResolutionListener.html" target="_top"><code class="classname">DependencyResolutionListener</code></a></li></ul></div><p>The following listener types can be used, but are not supported when configuration caching is enabled.
Their usage is deprecated and adding a listener of these types become an error in a future Gradle version:</p><div class="itemizedlist"><ul class="itemizedlist"><li class="listitem"><a class="ulink" href="../javadoc/org/gradle/BuildListener.html" target="_top"><code class="classname">BuildListener</code></a></li><li class="listitem"><a class="ulink" href="../javadoc/org/gradle/api/execution/TaskExecutionListener.html" target="_top"><code class="classname">TaskExecutionListener</code></a></li><li class="listitem"><a class="ulink" href="../javadoc/org/gradle/api/execution/TaskActionListener.html" target="_top"><code class="classname">TaskActionListener</code></a></li><li class="listitem"><a class="ulink" href="../javadoc/org/gradle/api/tasks/testing/TestListener.html" target="_top"><code class="classname">TestListener</code></a></li><li class="listitem"><a class="ulink" href="../javadoc/org/gradle/api/tasks/testing/TestOutputListener.html" target="_top"><code class="classname">TestOutputListener</code></a></li></ul></div></div><div class="section"><div class="titlepage"><div><div><h4 class="signature"><a xmlns:xslthl="http://xslthl.sf.net" name="org.gradle.api.invocation.Gradle:addProjectEvaluationListener(org.gradle.api.ProjectEvaluationListener)" class="section-anchor" href="#org.gradle.api.invocation.Gradle:addProjectEvaluationListener(org.gradle.api.ProjectEvaluationListener)"></a><a class="ulink" href="../javadoc/org/gradle/api/ProjectEvaluationListener.html" target="_top"><code class="classname">ProjectEvaluationListener</code></a> <code class="literal">addProjectEvaluationListener</code>(<a class="ulink" href="../javadoc/org/gradle/api/ProjectEvaluationListener.html" target="_top"><code class="classname">ProjectEvaluationListener</code></a> listener)</h4></div></div></div><p>Adds a listener to this build, to receive notifications as projects are evaluated.</p></div><div class="section"><div class="titlepage"><div><div><h4 class="signature"><a xmlns:xslthl="http://xslthl.sf.net" name="org.gradle.api.invocation.Gradle:afterProject(groovy.lang.Closure)" class="section-anchor" href="#org.gradle.api.invocation.Gradle:afterProject(groovy.lang.Closure)"></a><code class="classname">void</code> <code class="literal">afterProject</code>(<a class="ulink" href="https://docs.groovy-lang.org/3.0.17/html/gapi/groovy/lang/Closure.html" target="_top"><code class="classname">Closure</code></a> closure)</h4></div></div></div><p>Adds a closure to be called immediately after a project is evaluated.

The project is passed to the closure as the first parameter. The project evaluation failure, if any,
is passed as the second parameter. Both parameters are optional.</p></div><div class="section"><div class="titlepage"><div><div><h4 class="signature"><a xmlns:xslthl="http://xslthl.sf.net" name="org.gradle.api.invocation.Gradle:afterProject(org.gradle.api.Action)" class="section-anchor" href="#org.gradle.api.invocation.Gradle:afterProject(org.gradle.api.Action)"></a><code class="classname">void</code> <code class="literal">afterProject</code>(<code class="classname"><a class="ulink" href="../javadoc/org/gradle/api/Action.html" target="_top"><code class="classname">Action</code></a>&lt;? super <a class="ulink" href="../dsl/org.gradle.api.Project.html" target="_top"><code class="classname">Project</code></a>&gt;</code> action)</h4></div></div></div><p>Adds an action to be called immediately after a project is evaluated.</p></div><div class="section"><div class="titlepage"><div><div><h4 class="signature"><a xmlns:xslthl="http://xslthl.sf.net" name="org.gradle.api.invocation.Gradle:allprojects(org.gradle.api.Action)" class="section-anchor" href="#org.gradle.api.invocation.Gradle:allprojects(org.gradle.api.Action)"></a><code class="classname">void</code> <code class="literal">allprojects</code>(<code class="classname"><a class="ulink" href="../javadoc/org/gradle/api/Action.html" target="_top"><code class="classname">Action</code></a>&lt;? super <a class="ulink" href="../dsl/org.gradle.api.Project.html" target="_top"><code class="classname">Project</code></a>&gt;</code> action)</h4></div></div></div><p>Adds an action to execute against all projects of this build.

The action is executed immediately against all projects which are
already available. It is also executed as subsequent projects are added to this build.</p></div><div class="section"><div class="titlepage"><div><div><h4 class="signature"><a xmlns:xslthl="http://xslthl.sf.net" name="org.gradle.api.invocation.Gradle:apply(groovy.lang.Closure)" class="section-anchor" href="#org.gradle.api.invocation.Gradle:apply(groovy.lang.Closure)"></a><code class="classname">void</code> <code class="literal">apply</code>(<a class="ulink" href="https://docs.groovy-lang.org/3.0.17/html/gapi/groovy/lang/Closure.html" target="_top"><code class="classname">Closure</code></a> closure)</h4></div></div></div><p>Applies zero or more plugins or scripts.
</p><p>
The given closure is used to configure an <a class="ulink" href="../javadoc/org/gradle/api/plugins/ObjectConfigurationAction.html" target="_top"><code class="classname">ObjectConfigurationAction</code></a>, which &ldquo;builds&rdquo; the plugin application.
</p><p>
This method differs from <a class="ulink" href="../dsl/org.gradle.api.plugins.PluginAware.html#org.gradle.api.plugins.PluginAware:apply(java.util.Map)" target="_top"><code class="classname">PluginAware.apply(java.util.Map)</code></a> in that it allows methods of the configuration action to be invoked more than once.</p></div><div class="section"><div class="titlepage"><div><div><h4 class="signature"><a xmlns:xslthl="http://xslthl.sf.net" name="org.gradle.api.invocation.Gradle:apply(java.util.Map)" class="section-anchor" href="#org.gradle.api.invocation.Gradle:apply(java.util.Map)"></a><code class="classname">void</code> <code class="literal">apply</code>(<code class="classname"><a class="ulink" href="https://docs.oracle.com/javase/8/docs/api/java/util/Map.html" target="_top"><code class="classname">Map</code></a>&lt;<a class="ulink" href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html" target="_top"><code class="classname">String</code></a>, ?&gt;</code> options)</h4></div></div></div><p>Applies a plugin or script, using the given options provided as a map. Does nothing if the plugin has already been applied.
</p><p>
The given map is applied as a series of method calls to a newly created <a class="ulink" href="../javadoc/org/gradle/api/plugins/ObjectConfigurationAction.html" target="_top"><code class="classname">ObjectConfigurationAction</code></a>.
That is, each key in the map is expected to be the name of a method <a class="ulink" href="../javadoc/org/gradle/api/plugins/ObjectConfigurationAction.html" target="_top"><code class="classname">ObjectConfigurationAction</code></a> and the value to be compatible arguments to that method.

</p><p>The following options are available:</p><div class="itemizedlist"><ul class="itemizedlist"><li class="listitem"><code class="literal">from</code>: A script to apply. Accepts any path supported by <a class="ulink" href="../dsl/org.gradle.api.Project.html#org.gradle.api.Project:uri(java.lang.Object)" target="_top"><code class="classname">Project.uri(java.lang.Object)</code></a>.</li><li class="listitem"><code class="literal">plugin</code>: The id or implementation class of the plugin to apply.</li><li class="listitem"><code class="literal">to</code>: The target delegate object or objects. The default is this plugin aware object. Use this to configure objects other than this object.</li></ul></div></div><div class="section"><div class="titlepage"><div><div><h4 class="signature"><a xmlns:xslthl="http://xslthl.sf.net" name="org.gradle.api.invocation.Gradle:apply(org.gradle.api.Action)" class="section-anchor" href="#org.gradle.api.invocation.Gradle:apply(org.gradle.api.Action)"></a><code class="classname">void</code> <code class="literal">apply</code>(<code class="classname"><a class="ulink" href="../javadoc/org/gradle/api/Action.html" target="_top"><code class="classname">Action</code></a>&lt;? super <a class="ulink" href="../javadoc/org/gradle/api/plugins/ObjectConfigurationAction.html" target="_top"><code class="classname">ObjectConfigurationAction</code></a>&gt;</code> action)</h4></div></div></div><p>Applies zero or more plugins or scripts.
</p><p>
The given closure is used to configure an <a class="ulink" href="../javadoc/org/gradle/api/plugins/ObjectConfigurationAction.html" target="_top"><code class="classname">ObjectConfigurationAction</code></a>, which &ldquo;builds&rdquo; the plugin application.
</p><p>
This method differs from <a class="ulink" href="../dsl/org.gradle.api.plugins.PluginAware.html#org.gradle.api.plugins.PluginAware:apply(java.util.Map)" target="_top"><code class="classname">PluginAware.apply(java.util.Map)</code></a> in that it allows methods of the configuration action to be invoked more than once.</p></div><div class="section"><div class="titlepage"><div><div><h4 class="signature"><a xmlns:xslthl="http://xslthl.sf.net" name="org.gradle.api.invocation.Gradle:beforeProject(groovy.lang.Closure)" class="section-anchor" href="#org.gradle.api.invocation.Gradle:beforeProject(groovy.lang.Closure)"></a><code class="classname">void</code> <code class="literal">beforeProject</code>(<a class="ulink" href="https://docs.groovy-lang.org/3.0.17/html/gapi/groovy/lang/Closure.html" target="_top"><code class="classname">Closure</code></a> closure)</h4></div></div></div><p>Adds a closure to be called immediately before a project is evaluated. The project is passed to the closure as a
parameter.</p></div><div class="section"><div class="titlepage"><div><div><h4 class="signature"><a xmlns:xslthl="http://xslthl.sf.net" name="org.gradle.api.invocation.Gradle:beforeProject(org.gradle.api.Action)" class="section-anchor" href="#org.gradle.api.invocation.Gradle:beforeProject(org.gradle.api.Action)"></a><code class="classname">void</code> <code class="literal">beforeProject</code>(<code class="classname"><a class="ulink" href="../javadoc/org/gradle/api/Action.html" target="_top"><code class="classname">Action</code></a>&lt;? super <a class="ulink" href="../dsl/org.gradle.api.Project.html" target="_top"><code class="classname">Project</code></a>&gt;</code> action)</h4></div></div></div><p>Adds an action to be called immediately before a project is evaluated.</p></div><div class="section"><div class="titlepage"><div><div><h4 class="signature"><a xmlns:xslthl="http://xslthl.sf.net" name="org.gradle.api.invocation.Gradle:beforeSettings(groovy.lang.Closure)" class="section-anchor" href="#org.gradle.api.invocation.Gradle:beforeSettings(groovy.lang.Closure)"></a><code class="classname">void</code> <code class="literal">beforeSettings</code>(<code class="classname"><a class="ulink" href="https://docs.groovy-lang.org/3.0.17/html/gapi/groovy/lang/Closure.html" target="_top"><code class="classname">Closure</code></a>&lt;?&gt;</code> closure)</h4></div></div></div><p>Adds an action to be called before the build settings have been loaded and evaluated.</p></div><div class="section"><div class="titlepage"><div><div><h4 class="signature"><a xmlns:xslthl="http://xslthl.sf.net" name="org.gradle.api.invocation.Gradle:beforeSettings(org.gradle.api.Action)" class="section-anchor" href="#org.gradle.api.invocation.Gradle:beforeSettings(org.gradle.api.Action)"></a><code class="classname">void</code> <code class="literal">beforeSettings</code>(<code class="classname"><a class="ulink" href="../javadoc/org/gradle/api/Action.html" target="_top"><code class="classname">Action</code></a>&lt;? super <a class="ulink" href="../dsl/org.gradle.api.initialization.Settings.html" target="_top"><code class="classname">Settings</code></a>&gt;</code> action)</h4></div></div></div><p>Adds an action to be called before the build settings have been loaded and evaluated.</p></div><div class="section"><div class="titlepage"><div><div><h4 class="signature"><a xmlns:xslthl="http://xslthl.sf.net" name="org.gradle.api.invocation.Gradle:buildFinished(groovy.lang.Closure)" class="section-anchor" href="#org.gradle.api.invocation.Gradle:buildFinished(groovy.lang.Closure)"></a><code class="classname">void</code> <code class="literal">buildFinished</code>(<a class="ulink" href="https://docs.groovy-lang.org/3.0.17/html/gapi/groovy/lang/Closure.html" target="_top"><code class="classname">Closure</code></a> closure)</h4></div></div></div><div class="caution" title="Caution"><p>Note: This method is <a class="ulink" href="../userguide/feature_lifecycle.html" target="_top">deprecated</a> and will be removed in the next major version of Gradle.</p></div><p>Adds a closure to be called when the build is completed.

All selected tasks have been executed.
A <a class="ulink" href="../javadoc/org/gradle/BuildResult.html" target="_top"><code class="classname">BuildResult</code></a> instance is passed to the closure as a parameter.</p></div><div class="section"><div class="titlepage"><div><div><h4 class="signature"><a xmlns:xslthl="http://xslthl.sf.net" name="org.gradle.api.invocation.Gradle:buildFinished(org.gradle.api.Action)" class="section-anchor" href="#org.gradle.api.invocation.Gradle:buildFinished(org.gradle.api.Action)"></a><code class="classname">void</code> <code class="literal">buildFinished</code>(<code class="classname"><a class="ulink" href="../javadoc/org/gradle/api/Action.html" target="_top"><code class="classname">Action</code></a>&lt;? super <a class="ulink" href="../javadoc/org/gradle/BuildResult.html" target="_top"><code class="classname">BuildResult</code></a>&gt;</code> action)</h4></div></div></div><div class="caution" title="Caution"><p>Note: This method is <a class="ulink" href="../userguide/feature_lifecycle.html" target="_top">deprecated</a> and will be removed in the next major version of Gradle.</p></div><p>Adds an action to be called when the build is completed.

All selected tasks have been executed.</p></div><div class="section"><div class="titlepage"><div><div><h4 class="signature"><a xmlns:xslthl="http://xslthl.sf.net" name="org.gradle.api.invocation.Gradle:includedBuild(java.lang.String)" class="section-anchor" href="#org.gradle.api.invocation.Gradle:includedBuild(java.lang.String)"></a><a class="ulink" href="../dsl/org.gradle.api.initialization.IncludedBuild.html" target="_top"><code class="classname">IncludedBuild</code></a> <code class="literal">includedBuild</code>(<a class="ulink" href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html" target="_top"><code class="classname">String</code></a> name)</h4></div></div></div><p>Returns the included build with the specified name for this build.</p></div><div class="section"><div class="titlepage"><div><div><h4 class="signature"><a xmlns:xslthl="http://xslthl.sf.net" name="org.gradle.api.invocation.Gradle:projectsEvaluated(groovy.lang.Closure)" class="section-anchor" href="#org.gradle.api.invocation.Gradle:projectsEvaluated(groovy.lang.Closure)"></a><code class="classname">void</code> <code class="literal">projectsEvaluated</code>(<a class="ulink" href="https://docs.groovy-lang.org/3.0.17/html/gapi/groovy/lang/Closure.html" target="_top"><code class="classname">Closure</code></a> closure)</h4></div></div></div><p>Adds a closure to be called when all projects for the build have been evaluated.

The project objects are fully configured and are ready to use to populate the task graph.
This <code class="literal">Gradle</code> instance is passed to the closure as a parameter.</p></div><div class="section"><div class="titlepage"><div><div><h4 class="signature"><a xmlns:xslthl="http://xslthl.sf.net" name="org.gradle.api.invocation.Gradle:projectsEvaluated(org.gradle.api.Action)" class="section-anchor" href="#org.gradle.api.invocation.Gradle:projectsEvaluated(org.gradle.api.Action)"></a><code class="classname">void</code> <code class="literal">projectsEvaluated</code>(<code class="classname"><a class="ulink" href="../javadoc/org/gradle/api/Action.html" target="_top"><code class="classname">Action</code></a>&lt;? super <a class="ulink" href="../dsl/org.gradle.api.invocation.Gradle.html" target="_top"><code class="classname">Gradle</code></a>&gt;</code> action)</h4></div></div></div><p>Adds an action to be called when all projects for the build have been evaluated.

The project objects are fully configured and are ready to use to populate the task graph.</p></div><div class="section"><div class="titlepage"><div><div><h4 class="signature"><a xmlns:xslthl="http://xslthl.sf.net" name="org.gradle.api.invocation.Gradle:projectsLoaded(groovy.lang.Closure)" class="section-anchor" href="#org.gradle.api.invocation.Gradle:projectsLoaded(groovy.lang.Closure)"></a><code class="classname">void</code> <code class="literal">projectsLoaded</code>(<a class="ulink" href="https://docs.groovy-lang.org/3.0.17/html/gapi/groovy/lang/Closure.html" target="_top"><code class="classname">Closure</code></a> closure)</h4></div></div></div><p>Adds a closure to be called when the projects for the build have been created from the settings.

None of the projects have been evaluated. This <code class="literal">Gradle</code> instance is passed to the closure as a parameter.
</p><p>
An example of hooking into the projectsLoaded to configure buildscript classpath from the init script.
</p><pre class="programlisting">
<span xmlns:xslthl="http://xslthl.sf.net" class="hl-comment">//init.gradle</span>
gradle.projectsLoaded {
  rootProject.buildscript {
    repositories {
      <span xmlns:xslthl="http://xslthl.sf.net" class="hl-comment">//...</span>
    }
    dependencies {
      <span xmlns:xslthl="http://xslthl.sf.net" class="hl-comment">//...</span>
    }
  }
}
</pre></div><div class="section"><div class="titlepage"><div><div><h4 class="signature"><a xmlns:xslthl="http://xslthl.sf.net" name="org.gradle.api.invocation.Gradle:projectsLoaded(org.gradle.api.Action)" class="section-anchor" href="#org.gradle.api.invocation.Gradle:projectsLoaded(org.gradle.api.Action)"></a><code class="classname">void</code> <code class="literal">projectsLoaded</code>(<code class="classname"><a class="ulink" href="../javadoc/org/gradle/api/Action.html" target="_top"><code class="classname">Action</code></a>&lt;? super <a class="ulink" href="../dsl/org.gradle.api.invocation.Gradle.html" target="_top"><code class="classname">Gradle</code></a>&gt;</code> action)</h4></div></div></div><p>Adds an action to be called when the projects for the build have been created from the settings.

None of the projects have been evaluated.</p></div><div class="section"><div class="titlepage"><div><div><h4 class="signature"><a xmlns:xslthl="http://xslthl.sf.net" name="org.gradle.api.invocation.Gradle:removeListener(java.lang.Object)" class="section-anchor" href="#org.gradle.api.invocation.Gradle:removeListener(java.lang.Object)"></a><code class="classname">void</code> <code class="literal">removeListener</code>(<a class="ulink" href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html" target="_top"><code class="classname">Object</code></a> listener)</h4></div></div></div><p>Removes the given listener from this build.</p></div><div class="section"><div class="titlepage"><div><div><h4 class="signature"><a xmlns:xslthl="http://xslthl.sf.net" name="org.gradle.api.invocation.Gradle:removeProjectEvaluationListener(org.gradle.api.ProjectEvaluationListener)" class="section-anchor" href="#org.gradle.api.invocation.Gradle:removeProjectEvaluationListener(org.gradle.api.ProjectEvaluationListener)"></a><code class="classname">void</code> <code class="literal">removeProjectEvaluationListener</code>(<a class="ulink" href="../javadoc/org/gradle/api/ProjectEvaluationListener.html" target="_top"><code class="classname">ProjectEvaluationListener</code></a> listener)</h4></div></div></div><p>Removes the given listener from this build.</p></div><div class="section"><div class="titlepage"><div><div><h4 class="signature"><a xmlns:xslthl="http://xslthl.sf.net" name="org.gradle.api.invocation.Gradle:rootProject(org.gradle.api.Action)" class="section-anchor" href="#org.gradle.api.invocation.Gradle:rootProject(org.gradle.api.Action)"></a><code class="classname">void</code> <code class="literal">rootProject</code>(<code class="classname"><a class="ulink" href="../javadoc/org/gradle/api/Action.html" target="_top"><code class="classname">Action</code></a>&lt;? super <a class="ulink" href="../dsl/org.gradle.api.Project.html" target="_top"><code class="classname">Project</code></a>&gt;</code> action)</h4></div></div></div><p>Adds an action to execute against the root project of this build.

If the root project is already available, the action
is executed immediately. Otherwise, the action is executed when the root project becomes available.</p></div><div class="section"><div class="titlepage"><div><div><h4 class="signature"><a xmlns:xslthl="http://xslthl.sf.net" name="org.gradle.api.invocation.Gradle:settingsEvaluated(groovy.lang.Closure)" class="section-anchor" href="#org.gradle.api.invocation.Gradle:settingsEvaluated(groovy.lang.Closure)"></a><code class="classname">void</code> <code class="literal">settingsEvaluated</code>(<a class="ulink" href="https://docs.groovy-lang.org/3.0.17/html/gapi/groovy/lang/Closure.html" target="_top"><code class="classname">Closure</code></a> closure)</h4></div></div></div><p>Adds a closure to be called when the build settings have been loaded and evaluated.

The settings object is fully configured and is ready to use to load the build projects. The
<a class="ulink" href="../dsl/org.gradle.api.initialization.Settings.html" target="_top"><code class="classname">Settings</code></a> object is passed to the closure as a parameter.</p></div><div class="section"><div class="titlepage"><div><div><h4 class="signature"><a xmlns:xslthl="http://xslthl.sf.net" name="org.gradle.api.invocation.Gradle:settingsEvaluated(org.gradle.api.Action)" class="section-anchor" href="#org.gradle.api.invocation.Gradle:settingsEvaluated(org.gradle.api.Action)"></a><code class="classname">void</code> <code class="literal">settingsEvaluated</code>(<code class="classname"><a class="ulink" href="../javadoc/org/gradle/api/Action.html" target="_top"><code class="classname">Action</code></a>&lt;? super <a class="ulink" href="../dsl/org.gradle.api.initialization.Settings.html" target="_top"><code class="classname">Settings</code></a>&gt;</code> action)</h4></div></div></div><p>Adds an action to be called when the build settings have been loaded and evaluated.

The settings object is fully configured and is ready to use to load the build projects.</p></div><div class="section"><div class="titlepage"><div><div><h4 class="signature"><a xmlns:xslthl="http://xslthl.sf.net" name="org.gradle.api.invocation.Gradle:useLogger(java.lang.Object)" class="section-anchor" href="#org.gradle.api.invocation.Gradle:useLogger(java.lang.Object)"></a><code class="classname">void</code> <code class="literal">useLogger</code>(<a class="ulink" href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html" target="_top"><code class="classname">Object</code></a> logger)</h4></div></div></div><p>Uses the given object as a logger.

The logger object may implement any of the listener interfaces supported by
<a class="ulink" href="../dsl/org.gradle.api.invocation.Gradle.html#org.gradle.api.invocation.Gradle:addListener(java.lang.Object)" target="_top"><code class="classname">Gradle.addListener(java.lang.Object)</code></a>.
</p><p>
Each listener interface has exactly one associated logger. When you call this
method with a logger of a given listener type, the new logger will replace whichever logger is currently
associated with the listener type. This allows you to selectively replace the standard logging which Gradle
provides with your own implementation, for certain types of events.</p></div></div></div></div><footer xmlns:xslthl="http://xslthl.sf.net" itemtype="https://schema.org/WPFooter" itemscope="itemscope" class="site-layout__footer site-footer"><nav itemtype="https://schema.org/SiteNavigationElement" class="site-footer__navigation"><section class="site-footer__links"><div class="site-footer__link-group"><header><strong>Docs</strong></header><ul class="site-footer__links-list"><li itemprop="name"><a itemprop="url" href="/userguide/userguide.html">User Manual</a></li><li itemprop="name"><a itemprop="url" href="/dsl/">DSL Reference</a></li><li itemprop="name"><a itemprop="url" href="/release-notes.html">Release Notes</a></li><li itemprop="name"><a itemprop="url" href="/javadoc/">Javadoc</a></li></ul></div><div class="site-footer__link-group"><header><strong>News</strong></header><ul class="site-footer__links-list"><li itemprop="name"><a itemprop="url" href="https://blog.gradle.org/">Blog</a></li><li itemprop="name"><a itemprop="url" href="https://newsletter.gradle.org/">Newsletter</a></li><li itemprop="name"><a itemprop="url" href="https://twitter.com/gradle">Twitter</a></li></ul></div><div class="site-footer__link-group"><header><strong>Products</strong></header><ul class="site-footer__links-list"><li itemprop="name"><a itemprop="url" href="https://gradle.com/build-scans">Build Scan&trade;</a></li><li itemprop="name"><a itemprop="url" href="https://gradle.com/build-cache">Build Cache</a></li><li itemprop="name"><a itemprop="url" href="https://gradle.com/enterprise/resources">Develocity Docs</a></li></ul></div><div class="site-footer__link-group"><header><strong>Get Help</strong></header><ul class="site-footer__links-list"><li itemprop="name"><a itemprop="url" href="https://discuss.gradle.org/c/help-discuss">Forums</a></li><li itemprop="name"><a itemprop="url" href="https://github.com/gradle/">GitHub</a></li><li itemprop="name"><a itemprop="url" href="https://gradle.org/training/">Training</a></li><li itemprop="name"><a itemprop="url" href="https://gradle.org/services/">Services</a></li></ul></div></section><section id="newsletter-form-container" class="site-footer__subscribe-newsletter"><header class="newsletter-form__header"><h5>Stay <code>UP-TO-DATE</code> on new features and news</h5></header><p class="disclaimer">By entering your email, you agree to our <a href="https://gradle.org/terms/">Terms</a> and <a href="https://gradle.org/privacy/">Privacy Policy</a>, including receipt of emails. You can unsubscribe at any time.</p><div class="newsletter-form__container"><form method="post" action="https://go.gradle.com/l/68052/2018-09-07/bk6wml" class="newsletter-form" id="newsletter-form"><input required="" maxlength="255" pattern="[^@\s]+@[^@\s]+\.[^@\s]+" placeholder="<EMAIL>" type="email" name="email" class="email" id="email"><button type="submit" class="submit" id="submit">Subscribe</button></form></div></section></nav></footer><aside class="secondary-navigation"></aside></div></main><div class="site-footer-secondary"><div class="site-footer-secondary__contents"><div class="site-footer__copy">&copy; <a href="https://gradle.com">Gradle Inc. </a><time>2021</time>
                                All rights reserved.
                            </div><div class="site-footer__logo"><a href="/"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 90 66.06"><defs><style>.cls-1 {
                                            fill: #02303a;
                                            }</style></defs><title>gradle</title><path d="M85.11,4.18a14.27,14.27,0,0,0-19.83-.34,1.38,1.38,0,0,0,0,2L67,7.6a1.36,1.36,0,0,0,1.78.12A8.18,8.18,0,0,1,79.5,20.06C68.17,31.38,53.05-.36,18.73,16a4.65,4.65,0,0,0-2,6.54l5.89,10.17a4.64,4.64,0,0,0,6.3,1.73l.14-.08-.11.08L31.53,33a60.29,60.29,0,0,0,8.22-6.13,1.44,1.44,0,0,1,1.87-.06h0a1.34,1.34,0,0,1,.06,2A61.61,61.61,0,0,1,33,35.34l-.09,0-2.61,1.46a7.34,7.34,0,0,1-3.61.94,7.45,7.45,0,0,1-6.47-3.71l-5.57-9.61C4,32-2.54,46.56,1,65a1.36,1.36,0,0,0,1.33,1.11H8.61A1.36,1.36,0,0,0,10,64.87a9.29,9.29,0,0,1,18.42,0,1.35,1.35,0,0,0,1.34,1.19H35.9a1.36,1.36,0,0,0,1.34-1.19,9.29,9.29,0,0,1,18.42,0A1.36,1.36,0,0,0,57,66.06H63.1a1.36,1.36,0,0,0,1.36-1.34c.14-8.6,2.46-18.48,9.07-23.43C96.43,24.16,90.41,9.48,85.11,4.18ZM61.76,30.05l-4.37-2.19h0a2.74,2.74,0,1,1,4.37,2.2Z" class="cls-1"/></svg></a></div><div class="site-footer-secondary__links"><a href="https://gradle.com/careers">Careers</a> |
                                <a href="https://gradle.org/privacy">Privacy</a> |
                                <a href="https://gradle.org/terms">Terms of Service</a> |
                                <a href="https://gradle.org/contact/">Contact</a></div></div></div></div></body></html>