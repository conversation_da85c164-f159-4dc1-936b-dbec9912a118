<!DOCTYPE html>
<html>
<head>
    <meta name="viewport" content="width=device-width, initial-scale=1" charset="UTF-8">
    <title>org.gradle.api.artifacts.repositories</title>
    <link href="../../images/logo-icon.svg" rel="icon" type="image/svg">
    <script>var pathToRoot = "../../";</script>
    <script>const storage = localStorage.getItem("dokka-dark-mode")
    if (storage == null) {
        const osDarkSchemePreferred = window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches
        if (osDarkSchemePreferred === true) {
            document.getElementsByTagName("html")[0].classList.add("theme-dark")
        }
    } else {
        const savedDarkMode = JSON.parse(storage)
        if(savedDarkMode === true) {
            document.getElementsByTagName("html")[0].classList.add("theme-dark")
        }
    }
    </script>
<script type="text/javascript" src="../../scripts/sourceset_dependencies.js" async="async"></script>
<link href="../../styles/style.css" rel="Stylesheet">
<link href="../../styles/jetbrains-mono.css" rel="Stylesheet">
<link href="../../styles/main.css" rel="Stylesheet">
<link href="../../styles/prism.css" rel="Stylesheet">
<link href="../../styles/logo-styles.css" rel="Stylesheet">
<script type="text/javascript" src="../../scripts/clipboard.js" async="async"></script>
<script type="text/javascript" src="../../scripts/navigation-loader.js" async="async"></script>
<script type="text/javascript" src="../../scripts/platform-content-handler.js" async="async"></script>
<script type="text/javascript" src="../../scripts/main.js" defer="defer"></script>
<script type="text/javascript" src="../../scripts/prism.js" async="async"></script>
<script type="text/javascript" src="../../scripts/symbol-parameters-wrapper_deferred.js" defer="defer"></script>
<link href="../../images/gradle-logo.svg">
<link href="../../styles/gradle.css" rel="Stylesheet">
</head>
<body>
<div class="navigation-wrapper" id="navigation-wrapper">
    <div id="leftToggler"><span class="icon-toggler"></span></div>
    <div class="library-name">
            <a href="../../index.html">
                    <span>gradle</span>
            </a>
    </div>
    <div>
8.5    </div>
    <div class="pull-right d-flex">
        <div class="filter-section" id="filter-section">
                <button class="platform-tag platform-selector jvm-like" data-active="" data-filter=":docs/java_api">API</button>
        </div>
        <button id="theme-toggle-button"><span id="theme-toggle"></span></button>
        <div id="searchBar"></div>
    </div>
</div>
<div id="container">
    <div id="leftColumn">
        <div id="sideMenu"></div>
    </div>
    <div id="main">
<div class="main-content" data-page-type="package" id="content" pageIds="gradle::org.gradle.api.artifacts.repositories////PointingToDeclaration//-**********">
  <div class="breadcrumbs"><a href="../../index.html">gradle</a><span class="delimiter">/</span><span class="current">org.gradle.api.artifacts.repositories</span></div>
  <div class="cover ">
    <h1 class="cover"><span><span>Package-level</span></span> <span><span>declarations</span></span></h1>
    <div class="platform-hinted UnderCoverText with-platform-tabs" data-platform-hinted="data-platform-hinted">
      <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api" data-active="" data-toggle=":docs/java_api">API</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/java_api"><p class="paragraph">Classes for declaring and using artifact repositories.</p></div>    </div>
  </div>
  <div class="tabbedcontent">
    <div class="tabs-section" tabs-section="tabs-section"><button class="section-tab" data-active="" data-togglable="TYPE">Types</button></div>
    <div class="tabs-section-body">
      <div data-togglable="TYPE">
        <h2 class="">Types</h2>
        <div class="table"><a data-name="328722284%2FClasslikes%2F-**********" anchor-label="ArtifactRepository" id="328722284%2FClasslikes%2F-**********" data-filterable-set=":docs/java_api"></a>
          <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="-artifact-repository/index.html"><span>Artifact</span><wbr></wbr><span><span>Repository</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="328722284%2FClasslikes%2F-**********"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
                    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api" data-active="" data-toggle=":docs/java_api">API</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/java_api"><div class="symbol monospace"><span class="token keyword">interface </span><a href="-artifact-repository/index.html">ArtifactRepository</a></div><div class="brief ">A repository for resolving and publishing artifacts.</div></div>                  </div>
                </div>
              </div>
            </div>
          </div>
<a data-name="129989457%2FClasslikes%2F-**********" anchor-label="AuthenticationContainer" id="129989457%2FClasslikes%2F-**********" data-filterable-set=":docs/java_api"></a>
          <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="-authentication-container/index.html"><span>Authentication</span><wbr></wbr><span><span>Container</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="129989457%2FClasslikes%2F-**********"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
                    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api" data-active="" data-toggle=":docs/java_api">API</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/java_api"><div class="symbol monospace"><span class="token keyword">interface </span><a href="-authentication-container/index.html">AuthenticationContainer</a> : <a href="../org.gradle.api/-polymorphic-domain-object-container/index.html">PolymorphicDomainObjectContainer</a><span class="token operator">&lt;</span><a href="../org.gradle.api/-polymorphic-domain-object-container/index.html">T</a><span class="token operator">&gt; </span></div><div class="brief ">Container for configuring repository authentication schemes of type <a href="../org.gradle.authentication/-authentication/index.html">org.gradle.authentication.Authentication</a>.</div></div>                  </div>
                </div>
              </div>
            </div>
          </div>
<a data-name="-1848930364%2FClasslikes%2F-**********" anchor-label="AuthenticationSupported" id="-1848930364%2FClasslikes%2F-**********" data-filterable-set=":docs/java_api"></a>
          <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="-authentication-supported/index.html"><span>Authentication</span><wbr></wbr><span><span>Supported</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="-1848930364%2FClasslikes%2F-**********"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
                    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api" data-active="" data-toggle=":docs/java_api">API</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/java_api"><div class="symbol monospace"><span class="token keyword">interface </span><a href="-authentication-supported/index.html">AuthenticationSupported</a></div><div class="brief ">An artifact repository which supports username/password authentication.</div></div>                  </div>
                </div>
              </div>
            </div>
          </div>
<a data-name="1782795683%2FClasslikes%2F-**********" anchor-label="ExclusiveContentRepository" id="1782795683%2FClasslikes%2F-**********" data-filterable-set=":docs/java_api"></a>
          <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="-exclusive-content-repository/index.html"><span>Exclusive</span><wbr></wbr><span>Content</span><wbr></wbr><span><span>Repository</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="1782795683%2FClasslikes%2F-**********"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
                    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api" data-active="" data-toggle=":docs/java_api">API</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/java_api"><div class="symbol monospace"><span class="token keyword">interface </span><a href="-exclusive-content-repository/index.html">ExclusiveContentRepository</a></div><div class="brief ">Describes one or more repositories which together constitute the only possible source for an artifact, independently of the others.</div></div>                  </div>
                </div>
              </div>
            </div>
          </div>
<a data-name="208443466%2FClasslikes%2F-**********" anchor-label="FlatDirectoryArtifactRepository" id="208443466%2FClasslikes%2F-**********" data-filterable-set=":docs/java_api"></a>
          <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="-flat-directory-artifact-repository/index.html"><span>Flat</span><wbr></wbr><span>Directory</span><wbr></wbr><span>Artifact</span><wbr></wbr><span><span>Repository</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="208443466%2FClasslikes%2F-**********"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
                    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api" data-active="" data-toggle=":docs/java_api">API</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/java_api"><div class="symbol monospace"><span class="token keyword">interface </span><a href="-flat-directory-artifact-repository/index.html">FlatDirectoryArtifactRepository</a> : <a href="-artifact-repository/index.html">ArtifactRepository</a></div><div class="brief ">A repository that looks into a number of directories for artifacts.</div></div>                  </div>
                </div>
              </div>
            </div>
          </div>
<a data-name="-1162197482%2FClasslikes%2F-**********" anchor-label="InclusiveRepositoryContentDescriptor" id="-1162197482%2FClasslikes%2F-**********" data-filterable-set=":docs/java_api"></a>
          <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="-inclusive-repository-content-descriptor/index.html"><span>Inclusive</span><wbr></wbr><span>Repository</span><wbr></wbr><span>Content</span><wbr></wbr><span><span>Descriptor</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="-1162197482%2FClasslikes%2F-**********"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
                    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api" data-active="" data-toggle=":docs/java_api">API</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/java_api"><div class="symbol monospace"><span class="token keyword">interface </span><a href="-inclusive-repository-content-descriptor/index.html">InclusiveRepositoryContentDescriptor</a></div><div class="brief ">Descriptor of a repository content, used to avoid reaching to an external repository when not needed.</div></div>                  </div>
                </div>
              </div>
            </div>
          </div>
<a data-name="364014226%2FClasslikes%2F-**********" anchor-label="IvyArtifactRepository" id="364014226%2FClasslikes%2F-**********" data-filterable-set=":docs/java_api"></a>
          <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="-ivy-artifact-repository/index.html"><span>Ivy</span><wbr></wbr><span>Artifact</span><wbr></wbr><span><span>Repository</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="364014226%2FClasslikes%2F-**********"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
                    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api" data-active="" data-toggle=":docs/java_api">API</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/java_api"><div class="symbol monospace"><span class="token keyword">interface </span><a href="-ivy-artifact-repository/index.html">IvyArtifactRepository</a> : <a href="-artifact-repository/index.html">ArtifactRepository</a>, <a href="-url-artifact-repository/index.html">UrlArtifactRepository</a>, <a href="-authentication-supported/index.html">AuthenticationSupported</a>, <a href="-metadata-supplier-aware/index.html">MetadataSupplierAware</a></div><div class="brief ">An artifact repository which uses an Ivy format to store artifacts and meta-data.</div></div>                  </div>
                </div>
              </div>
            </div>
          </div>
<a data-name="-813303278%2FClasslikes%2F-**********" anchor-label="IvyArtifactRepositoryMetaDataProvider" id="-813303278%2FClasslikes%2F-**********" data-filterable-set=":docs/java_api"></a>
          <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="-ivy-artifact-repository-meta-data-provider/index.html"><span>Ivy</span><wbr></wbr><span>Artifact</span><wbr></wbr><span>Repository</span><wbr></wbr><span>Meta</span><wbr></wbr><span>Data</span><wbr></wbr><span><span>Provider</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="-813303278%2FClasslikes%2F-**********"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
                    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api" data-active="" data-toggle=":docs/java_api">API</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/java_api"><div class="symbol monospace"><span class="token keyword">interface </span><a href="-ivy-artifact-repository-meta-data-provider/index.html">IvyArtifactRepositoryMetaDataProvider</a></div><div class="brief ">The meta-data provider for an Ivy repository.</div></div>                  </div>
                </div>
              </div>
            </div>
          </div>
<a data-name="-19965328%2FClasslikes%2F-**********" anchor-label="IvyPatternRepositoryLayout" id="-19965328%2FClasslikes%2F-**********" data-filterable-set=":docs/java_api"></a>
          <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="-ivy-pattern-repository-layout/index.html"><span>Ivy</span><wbr></wbr><span>Pattern</span><wbr></wbr><span>Repository</span><wbr></wbr><span><span>Layout</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="-19965328%2FClasslikes%2F-**********"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
                    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api" data-active="" data-toggle=":docs/java_api">API</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/java_api"><div class="symbol monospace"><span class="token keyword">interface </span><a href="-ivy-pattern-repository-layout/index.html">IvyPatternRepositoryLayout</a> : <a href="-repository-layout/index.html">RepositoryLayout</a></div><div class="brief ">A repository layout that uses user-supplied patterns.</div></div>                  </div>
                </div>
              </div>
            </div>
          </div>
<a data-name="-857592237%2FClasslikes%2F-**********" anchor-label="MavenArtifactRepository" id="-857592237%2FClasslikes%2F-**********" data-filterable-set=":docs/java_api"></a>
          <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="-maven-artifact-repository/index.html"><span>Maven</span><wbr></wbr><span>Artifact</span><wbr></wbr><span><span>Repository</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="-857592237%2FClasslikes%2F-**********"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
                    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api" data-active="" data-toggle=":docs/java_api">API</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/java_api"><div class="symbol monospace"><span class="token keyword">interface </span><a href="-maven-artifact-repository/index.html">MavenArtifactRepository</a> : <a href="-artifact-repository/index.html">ArtifactRepository</a>, <a href="-url-artifact-repository/index.html">UrlArtifactRepository</a>, <a href="-authentication-supported/index.html">AuthenticationSupported</a>, <a href="-metadata-supplier-aware/index.html">MetadataSupplierAware</a></div><div class="brief ">An artifact repository which uses a Maven format to store artifacts and meta-data.</div></div>                  </div>
                </div>
              </div>
            </div>
          </div>
<a data-name="-1518985291%2FClasslikes%2F-**********" anchor-label="MavenRepositoryContentDescriptor" id="-1518985291%2FClasslikes%2F-**********" data-filterable-set=":docs/java_api"></a>
          <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="-maven-repository-content-descriptor/index.html"><span>Maven</span><wbr></wbr><span>Repository</span><wbr></wbr><span>Content</span><wbr></wbr><span><span>Descriptor</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="-1518985291%2FClasslikes%2F-**********"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
                    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api" data-active="" data-toggle=":docs/java_api">API</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/java_api"><div class="symbol monospace"><span class="token keyword">interface </span><a href="-maven-repository-content-descriptor/index.html">MavenRepositoryContentDescriptor</a> : <a href="-repository-content-descriptor/index.html">RepositoryContentDescriptor</a></div><div class="brief ">Extends the repository content descriptor with Maven repositories specific options.</div></div>                  </div>
                </div>
              </div>
            </div>
          </div>
<a data-name="-1162429769%2FClasslikes%2F-**********" anchor-label="MetadataSupplierAware" id="-1162429769%2FClasslikes%2F-**********" data-filterable-set=":docs/java_api"></a>
          <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="-metadata-supplier-aware/index.html"><span>Metadata</span><wbr></wbr><span>Supplier</span><wbr></wbr><span><span>Aware</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="-1162429769%2FClasslikes%2F-**********"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
                    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api" data-active="" data-toggle=":docs/java_api">API</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/java_api"><div class="symbol monospace"><span class="token keyword">interface </span><a href="-metadata-supplier-aware/index.html">MetadataSupplierAware</a></div><div class="brief ">Interface for repositories which support custom metadata suppliers and/or version listers.</div></div>                  </div>
                </div>
              </div>
            </div>
          </div>
<a data-name="-1140412839%2FClasslikes%2F-**********" anchor-label="PasswordCredentials" id="-1140412839%2FClasslikes%2F-**********" data-filterable-set=":docs/java_api"></a>
          <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="-password-credentials/index.html"><span>Password</span><wbr></wbr><span><span>Credentials</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="-1140412839%2FClasslikes%2F-**********"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
                    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api" data-active="" data-toggle=":docs/java_api">API</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/java_api"><div class="symbol monospace"><span class="token keyword">interface </span><a href="-password-credentials/index.html">PasswordCredentials</a> : <a href="../org.gradle.api.credentials/-password-credentials/index.html">PasswordCredentials</a></div><div class="brief ">A username/password credentials that can be used to login to password-protected remote repository.</div></div>                  </div>
                </div>
              </div>
            </div>
          </div>
<a data-name="139236028%2FClasslikes%2F-**********" anchor-label="RepositoryContentDescriptor" id="139236028%2FClasslikes%2F-**********" data-filterable-set=":docs/java_api"></a>
          <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="-repository-content-descriptor/index.html"><span>Repository</span><wbr></wbr><span>Content</span><wbr></wbr><span><span>Descriptor</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="139236028%2FClasslikes%2F-**********"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
                    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api" data-active="" data-toggle=":docs/java_api">API</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/java_api"><div class="symbol monospace"><span class="token keyword">interface </span><a href="-repository-content-descriptor/index.html">RepositoryContentDescriptor</a> : <a href="-inclusive-repository-content-descriptor/index.html">InclusiveRepositoryContentDescriptor</a></div><div class="brief ">Descriptor of a repository content, used to avoid reaching to an external repository when not needed.</div></div>                  </div>
                </div>
              </div>
            </div>
          </div>
<a data-name="-962254892%2FClasslikes%2F-**********" anchor-label="RepositoryLayout" id="-962254892%2FClasslikes%2F-**********" data-filterable-set=":docs/java_api"></a>
          <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="-repository-layout/index.html"><span>Repository</span><wbr></wbr><span><span>Layout</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="-962254892%2FClasslikes%2F-**********"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
                    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api" data-active="" data-toggle=":docs/java_api">API</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/java_api"><div class="symbol monospace"><span class="token keyword">interface </span><a href="-repository-layout/index.html">RepositoryLayout</a></div><div class="brief ">Represents the directory structure for a repository.</div></div>                  </div>
                </div>
              </div>
            </div>
          </div>
<a data-name="2030869673%2FClasslikes%2F-**********" anchor-label="RepositoryResourceAccessor" id="2030869673%2FClasslikes%2F-**********" data-filterable-set=":docs/java_api"></a>
          <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="-repository-resource-accessor/index.html"><span>Repository</span><wbr></wbr><span>Resource</span><wbr></wbr><span><span>Accessor</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="2030869673%2FClasslikes%2F-**********"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
                    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api" data-active="" data-toggle=":docs/java_api">API</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/java_api"><div class="symbol monospace"><span class="token keyword">interface </span><a href="-repository-resource-accessor/index.html">RepositoryResourceAccessor</a></div><div class="brief ">Provides access to resources on an artifact repository.</div></div>                  </div>
                </div>
              </div>
            </div>
          </div>
<a data-name="1992862255%2FClasslikes%2F-**********" anchor-label="UrlArtifactRepository" id="1992862255%2FClasslikes%2F-**********" data-filterable-set=":docs/java_api"></a>
          <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="-url-artifact-repository/index.html"><span>Url</span><wbr></wbr><span>Artifact</span><wbr></wbr><span><span>Repository</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="1992862255%2FClasslikes%2F-**********"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
                    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api" data-active="" data-toggle=":docs/java_api">API</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/java_api"><div class="symbol monospace"><span class="token keyword">interface </span><a href="-url-artifact-repository/index.html">UrlArtifactRepository</a></div><div class="brief ">A repository that supports resolving artifacts from a URL.</div></div>                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
      <div class="footer">
        <span class="go-to-top-icon"><a href="#content" id="go-to-top-link"></a></span><span>Gradle Kotlin DSL Reference</span><span
                class="pull-right"><span>Generated by </span><a
                href="https://github.com/Kotlin/dokka"><span>dokka</span><span class="padded-icon"></span></a></span>
      </div>
    </div>
</div>
</body>
</html>
