<!DOCTYPE html>
<html>
<head>
    <meta name="viewport" content="width=device-width, initial-scale=1" charset="UTF-8">
    <title>apply</title>
    <link href="../../images/logo-icon.svg" rel="icon" type="image/svg">
    <script>var pathToRoot = "../../";</script>
    <script>const storage = localStorage.getItem("dokka-dark-mode")
    if (storage == null) {
        const osDarkSchemePreferred = window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches
        if (osDarkSchemePreferred === true) {
            document.getElementsByTagName("html")[0].classList.add("theme-dark")
        }
    } else {
        const savedDarkMode = JSON.parse(storage)
        if(savedDarkMode === true) {
            document.getElementsByTagName("html")[0].classList.add("theme-dark")
        }
    }
    </script>
<script type="text/javascript" src="../../scripts/sourceset_dependencies.js" async="async"></script>
<link href="../../styles/style.css" rel="Stylesheet">
<link href="../../styles/jetbrains-mono.css" rel="Stylesheet">
<link href="../../styles/main.css" rel="Stylesheet">
<link href="../../styles/prism.css" rel="Stylesheet">
<link href="../../styles/logo-styles.css" rel="Stylesheet">
<script type="text/javascript" src="../../scripts/clipboard.js" async="async"></script>
<script type="text/javascript" src="../../scripts/navigation-loader.js" async="async"></script>
<script type="text/javascript" src="../../scripts/platform-content-handler.js" async="async"></script>
<script type="text/javascript" src="../../scripts/main.js" defer="defer"></script>
<script type="text/javascript" src="../../scripts/prism.js" async="async"></script>
<script type="text/javascript" src="../../scripts/symbol-parameters-wrapper_deferred.js" defer="defer"></script>
<link href="../../images/gradle-logo.svg">
<link href="../../styles/gradle.css" rel="Stylesheet">
</head>
<body>
<div class="navigation-wrapper" id="navigation-wrapper">
    <div id="leftToggler"><span class="icon-toggler"></span></div>
    <div class="library-name">
            <a href="../../index.html">
                    <span>gradle</span>
            </a>
    </div>
    <div>
8.5    </div>
    <div class="pull-right d-flex">
        <div class="filter-section" id="filter-section">
                <button class="platform-tag platform-selector jvm-like" data-active="" data-filter=":docs/kotlin_dsl">DSL</button>
        </div>
        <button id="theme-toggle-button"><span id="theme-toggle"></span></button>
        <div id="searchBar"></div>
    </div>
</div>
<div id="container">
    <div id="leftColumn">
        <div id="sideMenu"></div>
    </div>
    <div id="main">
<div class="main-content" data-page-type="member" id="content" pageIds="gradle::org.gradle.kotlin.dsl//apply/org.gradle.api.invocation.Gradle#/PointingToDeclaration//-1867656071">
  <div class="breadcrumbs"><a href="../../index.html">gradle</a><span class="delimiter">/</span><a href="index.html">org.gradle.kotlin.dsl</a><span class="delimiter">/</span><span class="current">apply</span></div>
  <div class="cover ">
    <h1 class="cover"><span><span>apply</span></span></h1>
  </div>
  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/kotlin_dsl" data-filterable-set=":docs/kotlin_dsl" data-active="" data-toggle=":docs/kotlin_dsl">DSL</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/kotlin_dsl"><div class="symbol monospace"><span class="token keyword">inline </span><span class="token keyword">fun </span><span class="token operator">&lt;</span><span class="token keyword"></span><a href="apply.html">T</a><span class="token operator"> : </span><span data-unresolved-link="org.gradle.api/Plugin///PointingToDeclaration/">Plugin</span><span class="token operator">&lt;</span><span class="token keyword"></span><a href="../org.gradle.api.invocation/-gradle/index.html#1640847228%2FMain%2F-1867656071">Gradle</a><span class="token operator">&gt;</span><span class="token operator">&gt; </span><a href="../org.gradle.api.invocation/-gradle/index.html#1640847228%2FMain%2F-1867656071">Gradle</a><span class="token punctuation">.</span><a href="apply.html"><span class="token function">apply</span></a><span class="token punctuation">(</span><span class="token punctuation">)</span><span class="clearfix"><span class="floating-right">(<a href="https://github.com/gradle/gradle/blob/28aca86a7180baa17117e0e5ba01d8ea9feca598/platforms/core-configuration/kotlin-dsl/src/main/kotlin/org/gradle/kotlin/dsl/GradleExtensions.kt#L32">source</a>)</span></span></div><div class="symbol monospace"><span class="token keyword">inline </span><span class="token keyword">fun </span><span class="token operator">&lt;</span><span class="token keyword"></span><a href="apply.html">T</a><span class="token operator"> : </span><span data-unresolved-link="org.gradle.api/Plugin///PointingToDeclaration/">Plugin</span><span class="token operator">&lt;</span><span class="token keyword"></span><a href="../org.gradle.api/-project/index.html#144527338%2FMain%2F-1867656071">Project</a><span class="token operator">&gt;</span><span class="token operator">&gt; </span><a href="../org.gradle.api/-project/index.html#144527338%2FMain%2F-1867656071">Project</a><span class="token punctuation">.</span><a href="apply.html"><span class="token function">apply</span></a><span class="token punctuation">(</span><span class="token punctuation">)</span><span class="clearfix"><span class="floating-right">(<a href="https://github.com/gradle/gradle/blob/28aca86a7180baa17117e0e5ba01d8ea9feca598/platforms/core-configuration/kotlin-dsl/src/main/kotlin/org/gradle/kotlin/dsl/ProjectExtensions.kt#L77">source</a>)</span></span></div><div class="symbol monospace"><span class="token keyword">inline </span><span class="token keyword">fun </span><span class="token operator">&lt;</span><span class="token keyword"></span><a href="apply.html">T</a><span class="token operator"> : </span><span data-unresolved-link="org.gradle.api/Plugin///PointingToDeclaration/">Plugin</span><span class="token operator">&lt;</span><span class="token keyword"></span><a href="../org.gradle.api.initialization/-settings/index.html#1688538672%2FMain%2F-1867656071">Settings</a><span class="token operator">&gt;</span><span class="token operator">&gt; </span><a href="../org.gradle.api.initialization/-settings/index.html#1688538672%2FMain%2F-1867656071">Settings</a><span class="token punctuation">.</span><a href="apply.html"><span class="token function">apply</span></a><span class="token punctuation">(</span><span class="token punctuation">)</span><span class="clearfix"><span class="floating-right">(<a href="https://github.com/gradle/gradle/blob/28aca86a7180baa17117e0e5ba01d8ea9feca598/platforms/core-configuration/kotlin-dsl/src/main/kotlin/org/gradle/kotlin/dsl/SettingsExtensions.kt#L34">source</a>)</span></span></div><p class="paragraph">Applies the plugin of the given type <a href="apply.html">T</a>. Does nothing if the plugin has already been applied.</p><p class="paragraph">The given class should implement the <span data-unresolved-link="org.gradle.api/Plugin///PointingToDeclaration/">Plugin</span> interface, and be parameterized for a compatible type of <code class="lang-kotlin">this</code>.</p><h4 class="">Parameters</h4><div class="table"><div class="table-row" data-filterable-current=":docs/kotlin_dsl" data-filterable-set=":docs/kotlin_dsl"><div class="main-subrow keyValue "><div class=""><span class="inline-flex"><div><u><span><span>T</span></span></u></div></span></div><div><div class="title"><p class="paragraph">the plugin type.</p></div></div></div></div></div><h4 class="">See also</h4><div class="table"><div class="table-row" data-filterable-current=":docs/kotlin_dsl" data-filterable-set=":docs/kotlin_dsl"><div class="main-subrow keyValue "><div class=""><span class="inline-flex"><div><a href="-kotlin-settings-script-template/index.html#*********%2FFunctions%2F-1867656071"><span>Plugin</span><wbr></wbr><span>Aware.</span><wbr></wbr><span>apply</span></a></div></span></div><div></div></div></div></div><hr><div class="symbol monospace"><span class="token keyword"></span><span class="token keyword">fun </span><a href="../org.gradle.api.plugins/-plugin-aware/index.html#2145094630%2FMain%2F-1867656071">PluginAware</a><span class="token punctuation">.</span><a href="apply.html"><span class="token function">apply</span></a><span class="token punctuation">(</span><span class="parameters "><span class="parameter ">from<span class="token operator">: </span><a href="https://kotlinlang.org/api/latest/jvm/stdlib/kotlin/-any/index.html">Any</a><span class="token operator">?</span><span class="token operator"> = </span>null<span class="token punctuation">, </span></span><span class="parameter ">plugin<span class="token operator">: </span><a href="https://kotlinlang.org/api/latest/jvm/stdlib/kotlin/-string/index.html">String</a><span class="token operator">?</span><span class="token operator"> = </span>null<span class="token punctuation">, </span></span><span class="parameter ">to<span class="token operator">: </span><a href="https://kotlinlang.org/api/latest/jvm/stdlib/kotlin/-any/index.html">Any</a><span class="token operator">?</span><span class="token operator"> = </span>null</span></span><span class="token punctuation">)</span><span class="clearfix"><span class="floating-right">(<a href="https://github.com/gradle/gradle/blob/28aca86a7180baa17117e0e5ba01d8ea9feca598/platforms/core-configuration/kotlin-dsl/src/main/kotlin/org/gradle/kotlin/dsl/PluginAwareExtensions.kt#L31">source</a>)</span></span></div><p class="paragraph">Applies the given plugin or script.</p><h4 class="">Parameters</h4><div class="table"><div class="table-row" data-filterable-current=":docs/kotlin_dsl" data-filterable-set=":docs/kotlin_dsl"><div class="main-subrow keyValue "><div class=""><span class="inline-flex"><div><u><span><span>from</span></span></u></div></span></div><div><div class="title"><p class="paragraph">a script to apply, evaluated as per <span data-unresolved-link="org.gradle.api/Project/file/#kotlin.Any/PointingToDeclaration/">Project.file</span></p></div></div></div></div><div class="table-row" data-filterable-current=":docs/kotlin_dsl" data-filterable-set=":docs/kotlin_dsl"><div class="main-subrow keyValue "><div class=""><span class="inline-flex"><div><u><span><span>plugin</span></span></u></div></span></div><div><div class="title"><p class="paragraph">a id of the plugin to apply</p></div></div></div></div><div class="table-row" data-filterable-current=":docs/kotlin_dsl" data-filterable-set=":docs/kotlin_dsl"><div class="main-subrow keyValue "><div class=""><span class="inline-flex"><div><u><span><span>to</span></span></u></div></span></div><div><div class="title"><p class="paragraph">the plugin target object or collection of objects, target is self when null</p></div></div></div></div></div><h4 class="">See also</h4><div class="table"><div class="table-row" data-filterable-current=":docs/kotlin_dsl" data-filterable-set=":docs/kotlin_dsl"><div class="main-subrow keyValue "><div class=""><span class="inline-flex"><div><a href="-kotlin-settings-script-template/index.html#*********%2FFunctions%2F-1867656071"><span>Plugin</span><wbr></wbr><span>Aware.</span><wbr></wbr><span>apply</span></a></div></span></div><div></div></div></div></div><hr><div class="symbol monospace"><span class="token keyword">inline </span><span class="token keyword">fun </span><span class="token operator">&lt;</span><span class="token keyword"></span><a href="apply.html">T</a><span class="token operator"> : </span><span data-unresolved-link="org.gradle.api/Plugin///PointingToDeclaration/">Plugin</span><span class="token operator">&lt;</span><span class="token operator">*</span><span class="token operator">&gt;</span><span class="token operator">&gt; </span><a href="../org.gradle.api.plugins/-plugin-aware/index.html#2145094630%2FMain%2F-1867656071">PluginAware</a><span class="token punctuation">.</span><a href="apply.html"><span class="token function">apply</span></a><span class="token punctuation">(</span><span class="token punctuation">)</span><span class="clearfix"><span class="floating-right">(<a href="https://github.com/gradle/gradle/blob/28aca86a7180baa17117e0e5ba01d8ea9feca598/platforms/core-configuration/kotlin-dsl/src/main/kotlin/org/gradle/kotlin/dsl/PluginAwareExtensions.kt#L49">source</a>)</span></span></div><p class="paragraph">Applies the plugin of the given type <a href="apply.html">T</a>. Does nothing if the plugin has already been applied.</p><p class="paragraph">The given class should implement the <span data-unresolved-link="org.gradle.api/Plugin///PointingToDeclaration/">Plugin</span> interface.</p><h4 class="">Parameters</h4><div class="table"><div class="table-row" data-filterable-current=":docs/kotlin_dsl" data-filterable-set=":docs/kotlin_dsl"><div class="main-subrow keyValue "><div class=""><span class="inline-flex"><div><u><span><span>T</span></span></u></div></span></div><div><div class="title"><p class="paragraph">the plugin type.</p></div></div></div></div></div><h4 class="">See also</h4><div class="table"><div class="table-row" data-filterable-current=":docs/kotlin_dsl" data-filterable-set=":docs/kotlin_dsl"><div class="main-subrow keyValue "><div class=""><span class="inline-flex"><div><a href="-kotlin-settings-script-template/index.html#*********%2FFunctions%2F-1867656071"><span>Plugin</span><wbr></wbr><span>Aware.</span><wbr></wbr><span>apply</span></a></div></span></div><div></div></div></div></div><hr><div class="symbol monospace"><span class="token keyword">infix </span><span class="token keyword">fun </span><a href="../org.gradle.plugin.use/-plugin-dependency-spec/index.html#-1386847054%2FMain%2F-1867656071">PluginDependencySpec</a><span class="token punctuation">.</span><a href="apply.html"><span class="token function">apply</span></a><span class="token punctuation">(</span><span class="parameters "><span class="parameter ">apply<span class="token operator">: </span><a href="https://kotlinlang.org/api/latest/jvm/stdlib/kotlin/-boolean/index.html">Boolean</a></span></span><span class="token punctuation">)</span><span class="token operator">: </span><a href="../org.gradle.plugin.use/-plugin-dependency-spec/index.html#-1386847054%2FMain%2F-1867656071">PluginDependencySpec</a><span class="clearfix"><span class="floating-right">(<a href="https://github.com/gradle/gradle/blob/28aca86a7180baa17117e0e5ba01d8ea9feca598/platforms/core-configuration/kotlin-dsl/src/main/kotlin/org/gradle/kotlin/dsl/PluginDependenciesSpecScope.kt#L74">source</a>)</span></span></div><p class="paragraph">Specifies whether the plugin should be applied to the current project. Otherwise it is only put on the project's classpath.</p><p class="paragraph">Infix version of <span data-unresolved-link="org.gradle.plugin.use/PluginDependencySpec/apply/#kotlin.Boolean/PointingToDeclaration/">PluginDependencySpec.apply</span>.</p><hr><div class="symbol monospace"><span class="token keyword">inline </span><span class="token keyword">fun </span><span class="token operator">&lt;</span><span class="token keyword"></span><a href="apply.html">T</a><span class="token operator"> : </span><span data-unresolved-link="org.gradle.api/Plugin///PointingToDeclaration/">Plugin</span><span class="token operator">&lt;</span><span class="token operator">*</span><span class="token operator">&gt;</span><span class="token operator">&gt; </span><a href="../org.gradle.api.plugins/-plugin-container/index.html#-1935936093%2FMain%2F-1867656071">PluginContainer</a><span class="token punctuation">.</span><a href="apply.html"><span class="token function">apply</span></a><span class="token punctuation">(</span><span class="parameters "><span class="parameter ">type<span class="token operator">: </span><a href="https://kotlinlang.org/api/latest/jvm/stdlib/kotlin.reflect/-k-class/index.html">KClass</a><span class="token operator">&lt;</span><span class="token keyword"></span><a href="apply.html">T</a><span class="token operator">&gt;</span></span></span><span class="token punctuation">)</span><span class="token operator">: </span><a href="apply.html">T</a><span class="clearfix"><span class="floating-right">(<a href="https://github.com/gradle/gradle/blob/28aca86a7180baa17117e0e5ba01d8ea9feca598/subprojects/distributions-full/build/generated-sources/kotlin-dsl-extensions/org/gradle/kotlin/dsl/GradleApiKotlinDslExtensions_1j20ljs9xowlw6c963ek286gg.kt#L40">source</a>)</span></span></div><div class="symbol monospace"><span class="token keyword">inline </span><span class="token keyword">fun </span><span class="token operator">&lt;</span><span class="token keyword"></span><a href="apply.html">T</a><span class="token operator"> : </span><span data-unresolved-link="org.gradle.api/Plugin///PointingToDeclaration/">Plugin</span><span class="token operator">&lt;</span><span class="token operator">*</span><span class="token operator">&gt;</span><span class="token operator">&gt; </span><a href="../org.gradle.api.plugins/-plugin-container/index.html#-1935936093%2FMain%2F-1867656071">PluginContainer</a><span class="token punctuation">.</span><a href="apply.html"><span class="token function">apply</span></a><span class="token punctuation">(</span><span class="parameters "><span class="parameter ">type<span class="token operator">: </span><a href="https://kotlinlang.org/api/latest/jvm/stdlib/kotlin.reflect/-k-class/index.html">KClass</a><span class="token operator">&lt;</span><span class="token keyword"></span><a href="apply.html">T</a><span class="token operator">&gt;</span></span></span><span class="token punctuation">)</span><span class="token operator">: </span><a href="apply.html">T</a><span class="clearfix"><span class="floating-right">(<a href="https://github.com/gradle/gradle/blob/28aca86a7180baa17117e0e5ba01d8ea9feca598/subprojects/distributions-full/build/generated-sources/kotlin-dsl-extensions/org/gradle/kotlin/dsl/GradleApiKotlinDslExtensions_1j20ljs9xowlw6c963ek286gg.kt#L40">source</a>)</span></span></div><p class="paragraph">Kotlin extension function taking <a href="https://kotlinlang.org/api/latest/jvm/stdlib/kotlin.reflect/-k-class/index.html">kotlin.reflect.KClass</a> for <span data-unresolved-link="org.gradle.api.plugins/PluginContainer/apply/#kotlin.String/PointingToDeclaration/">org.gradle.api.plugins.PluginContainer.apply</span>.</p><h4 class="">See also</h4><div class="table"><div class="table-row" data-filterable-current=":docs/kotlin_dsl" data-filterable-set=":docs/kotlin_dsl"><div class="main-subrow keyValue "><div class=""><span class="inline-flex"><div><span data-unresolved-link="org.gradle.api.plugins/PluginContainer/apply/#kotlin.String/PointingToDeclaration/"><span>Plugin</span><wbr></wbr><span>Container.</span><wbr></wbr><span>apply</span></span></div></span></div><div></div></div></div></div><hr><div class="symbol monospace"><span class="token keyword">inline </span><span class="token keyword">fun </span><a href="../org.gradle.api/-script/index.html#1581714132%2FMain%2F-1867656071">Script</a><span class="token punctuation">.</span><a href="apply.html"><span class="token function">apply</span></a><span class="token punctuation">(</span><span class="parameters "><span class="parameter "><span class="token keyword">vararg </span>options<span class="token operator">: </span><a href="https://kotlinlang.org/api/latest/jvm/stdlib/kotlin/-pair/index.html">Pair</a><span class="token operator">&lt;</span><span class="token keyword"></span><a href="https://kotlinlang.org/api/latest/jvm/stdlib/kotlin/-string/index.html">String</a><span class="token punctuation">, </span><span class="token keyword"></span><a href="https://kotlinlang.org/api/latest/jvm/stdlib/kotlin/-any/index.html">Any</a><span class="token operator">?</span><span class="token operator">&gt;</span></span></span><span class="token punctuation">)</span><span class="clearfix"><span class="floating-right">(<a href="https://github.com/gradle/gradle/blob/28aca86a7180baa17117e0e5ba01d8ea9feca598/subprojects/distributions-full/build/generated-sources/kotlin-dsl-extensions/org/gradle/kotlin/dsl/GradleApiKotlinDslExtensions_3junz439hluumqhrib3vuxsly.kt#L40">source</a>)</span></span></div><div class="symbol monospace"><span class="token keyword">inline </span><span class="token keyword">fun </span><a href="../org.gradle.api/-script/index.html#1581714132%2FMain%2F-1867656071">Script</a><span class="token punctuation">.</span><a href="apply.html"><span class="token function">apply</span></a><span class="token punctuation">(</span><span class="parameters "><span class="parameter "><span class="token keyword">vararg </span>options<span class="token operator">: </span><a href="https://kotlinlang.org/api/latest/jvm/stdlib/kotlin/-pair/index.html">Pair</a><span class="token operator">&lt;</span><span class="token keyword"></span><a href="https://kotlinlang.org/api/latest/jvm/stdlib/kotlin/-string/index.html">String</a><span class="token punctuation">, </span><span class="token keyword"></span><a href="https://kotlinlang.org/api/latest/jvm/stdlib/kotlin/-any/index.html">Any</a><span class="token operator">?</span><span class="token operator">&gt;</span></span></span><span class="token punctuation">)</span><span class="clearfix"><span class="floating-right">(<a href="https://github.com/gradle/gradle/blob/28aca86a7180baa17117e0e5ba01d8ea9feca598/subprojects/distributions-full/build/generated-sources/kotlin-dsl-extensions/org/gradle/kotlin/dsl/GradleApiKotlinDslExtensions_3junz439hluumqhrib3vuxsly.kt#L40">source</a>)</span></span></div><p class="paragraph">Kotlin extension function for <span data-unresolved-link="org.gradle.api/Script/apply/#groovy.lang.Closure[kotlin.Any]/PointingToDeclaration/">org.gradle.api.Script.apply</span>.</p><h4 class="">See also</h4><div class="table"><div class="table-row" data-filterable-current=":docs/kotlin_dsl" data-filterable-set=":docs/kotlin_dsl"><div class="main-subrow keyValue "><div class=""><span class="inline-flex"><div><span data-unresolved-link="org.gradle.api/Script/apply/#groovy.lang.Closure[kotlin.Any]/PointingToDeclaration/"><span>Script.</span><wbr></wbr><span>apply</span></span></div></span></div><div></div></div></div></div><hr><div class="symbol monospace"><span class="token keyword">inline </span><span class="token keyword">fun </span><a href="../org.gradle.api.plugins/-plugin-manager/index.html#-92146505%2FMain%2F-1867656071">PluginManager</a><span class="token punctuation">.</span><a href="apply.html"><span class="token function">apply</span></a><span class="token punctuation">(</span><span class="parameters "><span class="parameter ">type<span class="token operator">: </span><a href="https://kotlinlang.org/api/latest/jvm/stdlib/kotlin.reflect/-k-class/index.html">KClass</a><span class="token operator">&lt;</span><span class="token operator">*</span><span class="token operator">&gt;</span></span></span><span class="token punctuation">)</span><span class="clearfix"><span class="floating-right">(<a href="https://github.com/gradle/gradle/blob/28aca86a7180baa17117e0e5ba01d8ea9feca598/subprojects/distributions-full/build/generated-sources/kotlin-dsl-extensions/org/gradle/kotlin/dsl/GradleApiKotlinDslExtensions_4bjyvck275dm80nsvunexs63s.kt#L40">source</a>)</span></span></div><div class="symbol monospace"><span class="token keyword">inline </span><span class="token keyword">fun </span><a href="../org.gradle.api.plugins/-plugin-manager/index.html#-92146505%2FMain%2F-1867656071">PluginManager</a><span class="token punctuation">.</span><a href="apply.html"><span class="token function">apply</span></a><span class="token punctuation">(</span><span class="parameters "><span class="parameter ">type<span class="token operator">: </span><a href="https://kotlinlang.org/api/latest/jvm/stdlib/kotlin.reflect/-k-class/index.html">KClass</a><span class="token operator">&lt;</span><span class="token operator">*</span><span class="token operator">&gt;</span></span></span><span class="token punctuation">)</span><span class="clearfix"><span class="floating-right">(<a href="https://github.com/gradle/gradle/blob/28aca86a7180baa17117e0e5ba01d8ea9feca598/subprojects/distributions-full/build/generated-sources/kotlin-dsl-extensions/org/gradle/kotlin/dsl/GradleApiKotlinDslExtensions_4bjyvck275dm80nsvunexs63s.kt#L40">source</a>)</span></span></div><p class="paragraph">Kotlin extension function taking <a href="https://kotlinlang.org/api/latest/jvm/stdlib/kotlin.reflect/-k-class/index.html">kotlin.reflect.KClass</a> for <span data-unresolved-link="org.gradle.api.plugins/PluginManager/apply/#kotlin.String/PointingToDeclaration/">org.gradle.api.plugins.PluginManager.apply</span>.</p><h4 class="">See also</h4><div class="table"><div class="table-row" data-filterable-current=":docs/kotlin_dsl" data-filterable-set=":docs/kotlin_dsl"><div class="main-subrow keyValue "><div class=""><span class="inline-flex"><div><span data-unresolved-link="org.gradle.api.plugins/PluginManager/apply/#kotlin.String/PointingToDeclaration/"><span>Plugin</span><wbr></wbr><span>Manager.</span><wbr></wbr><span>apply</span></span></div></span></div><div></div></div></div></div><hr><div class="symbol monospace"><span class="token keyword">inline </span><span class="token keyword">fun </span><a href="../org.gradle.api.plugins/-plugin-aware/index.html#2145094630%2FMain%2F-1867656071">PluginAware</a><span class="token punctuation">.</span><a href="apply.html"><span class="token function">apply</span></a><span class="token punctuation">(</span><span class="parameters "><span class="parameter "><span class="token keyword">vararg </span>options<span class="token operator">: </span><a href="https://kotlinlang.org/api/latest/jvm/stdlib/kotlin/-pair/index.html">Pair</a><span class="token operator">&lt;</span><span class="token keyword"></span><a href="https://kotlinlang.org/api/latest/jvm/stdlib/kotlin/-string/index.html">String</a><span class="token punctuation">, </span><span class="token keyword"></span><a href="https://kotlinlang.org/api/latest/jvm/stdlib/kotlin/-any/index.html">Any</a><span class="token operator">?</span><span class="token operator">&gt;</span></span></span><span class="token punctuation">)</span><span class="clearfix"><span class="floating-right">(<a href="https://github.com/gradle/gradle/blob/28aca86a7180baa17117e0e5ba01d8ea9feca598/subprojects/distributions-full/build/generated-sources/kotlin-dsl-extensions/org/gradle/kotlin/dsl/GradleApiKotlinDslExtensions_atax470l3ngupolpii62aprge.kt#L40">source</a>)</span></span></div><div class="symbol monospace"><span class="token keyword">inline </span><span class="token keyword">fun </span><a href="../org.gradle.api.plugins/-plugin-aware/index.html#2145094630%2FMain%2F-1867656071">PluginAware</a><span class="token punctuation">.</span><a href="apply.html"><span class="token function">apply</span></a><span class="token punctuation">(</span><span class="parameters "><span class="parameter "><span class="token keyword">vararg </span>options<span class="token operator">: </span><a href="https://kotlinlang.org/api/latest/jvm/stdlib/kotlin/-pair/index.html">Pair</a><span class="token operator">&lt;</span><span class="token keyword"></span><a href="https://kotlinlang.org/api/latest/jvm/stdlib/kotlin/-string/index.html">String</a><span class="token punctuation">, </span><span class="token keyword"></span><a href="https://kotlinlang.org/api/latest/jvm/stdlib/kotlin/-any/index.html">Any</a><span class="token operator">?</span><span class="token operator">&gt;</span></span></span><span class="token punctuation">)</span><span class="clearfix"><span class="floating-right">(<a href="https://github.com/gradle/gradle/blob/28aca86a7180baa17117e0e5ba01d8ea9feca598/subprojects/distributions-full/build/generated-sources/kotlin-dsl-extensions/org/gradle/kotlin/dsl/GradleApiKotlinDslExtensions_atax470l3ngupolpii62aprge.kt#L40">source</a>)</span></span></div><p class="paragraph">Kotlin extension function for <a href="-kotlin-settings-script-template/index.html#*********%2FFunctions%2F-1867656071">org.gradle.api.plugins.PluginAware.apply</a>.</p><h4 class="">See also</h4><div class="table"><div class="table-row" data-filterable-current=":docs/kotlin_dsl" data-filterable-set=":docs/kotlin_dsl"><div class="main-subrow keyValue "><div class=""><span class="inline-flex"><div><a href="-kotlin-settings-script-template/index.html#*********%2FFunctions%2F-1867656071"><span>Plugin</span><wbr></wbr><span>Aware.</span><wbr></wbr><span>apply</span></a></div></span></div><div></div></div></div></div></div>  </div>
</div>
      <div class="footer">
        <span class="go-to-top-icon"><a href="#content" id="go-to-top-link"></a></span><span>Gradle Kotlin DSL Reference</span><span
                class="pull-right"><span>Generated by </span><a
                href="https://github.com/Kotlin/dokka"><span>dokka</span><span class="padded-icon"></span></a></span>
      </div>
    </div>
</div>
</body>
</html>
