<!DOCTYPE html>
<html>
<head>
    <meta name="viewport" content="width=device-width, initial-scale=1" charset="UTF-8">
    <title>invoke</title>
    <link href="../../../images/logo-icon.svg" rel="icon" type="image/svg">
    <script>var pathToRoot = "../../../";</script>
    <script>const storage = localStorage.getItem("dokka-dark-mode")
    if (storage == null) {
        const osDarkSchemePreferred = window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches
        if (osDarkSchemePreferred === true) {
            document.getElementsByTagName("html")[0].classList.add("theme-dark")
        }
    } else {
        const savedDarkMode = JSON.parse(storage)
        if(savedDarkMode === true) {
            document.getElementsByTagName("html")[0].classList.add("theme-dark")
        }
    }
    </script>
<script type="text/javascript" src="../../../scripts/sourceset_dependencies.js" async="async"></script>
<link href="../../../styles/style.css" rel="Stylesheet">
<link href="../../../styles/jetbrains-mono.css" rel="Stylesheet">
<link href="../../../styles/main.css" rel="Stylesheet">
<link href="../../../styles/prism.css" rel="Stylesheet">
<link href="../../../styles/logo-styles.css" rel="Stylesheet">
<script type="text/javascript" src="../../../scripts/clipboard.js" async="async"></script>
<script type="text/javascript" src="../../../scripts/navigation-loader.js" async="async"></script>
<script type="text/javascript" src="../../../scripts/platform-content-handler.js" async="async"></script>
<script type="text/javascript" src="../../../scripts/main.js" defer="defer"></script>
<script type="text/javascript" src="../../../scripts/prism.js" async="async"></script>
<script type="text/javascript" src="../../../scripts/symbol-parameters-wrapper_deferred.js" defer="defer"></script>
<link href="../../../images/gradle-logo.svg">
<link href="../../../styles/gradle.css" rel="Stylesheet">
</head>
<body>
<div class="navigation-wrapper" id="navigation-wrapper">
    <div id="leftToggler"><span class="icon-toggler"></span></div>
    <div class="library-name">
            <a href="../../../index.html">
                    <span>gradle</span>
            </a>
    </div>
    <div>
8.5    </div>
    <div class="pull-right d-flex">
        <div class="filter-section" id="filter-section">
                <button class="platform-tag platform-selector jvm-like" data-active="" data-filter=":docs/kotlin_dsl">DSL</button>
        </div>
        <button id="theme-toggle-button"><span id="theme-toggle"></span></button>
        <div id="searchBar"></div>
    </div>
</div>
<div id="container">
    <div id="leftColumn">
        <div id="sideMenu"></div>
    </div>
    <div id="main">
<div class="main-content" data-page-type="member" id="content" pageIds="gradle::org.gradle.kotlin.dsl/NamedDomainObjectContainerScope/invoke/kotlin.String#kotlin.Function1[TypeParam(bounds=[kotlin.Any]),kotlin.Unit]/PointingToDeclaration//-**********">
  <div class="breadcrumbs"><a href="../../../index.html">gradle</a><span class="delimiter">/</span><a href="../index.html">org.gradle.kotlin.dsl</a><span class="delimiter">/</span><a href="index.html">NamedDomainObjectContainerScope</a><span class="delimiter">/</span><span class="current">invoke</span></div>
  <div class="cover ">
    <h1 class="cover"><span><span>invoke</span></span></h1>
  </div>
  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/kotlin_dsl" data-filterable-set=":docs/kotlin_dsl" data-active="" data-toggle=":docs/kotlin_dsl">DSL</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/kotlin_dsl"><div class="symbol monospace"><span class="token keyword">operator </span><span class="token keyword">fun </span><a href="https://kotlinlang.org/api/latest/jvm/stdlib/kotlin/-string/index.html">String</a><span class="token punctuation">.</span><a href="invoke.html"><span class="token function">invoke</span></a><span class="token punctuation">(</span><span class="parameters "><span class="parameter ">configuration<span class="token operator">: </span><span class="token keyword"></span><a href="index.html">T</a><span class="token punctuation">.</span><span class="token punctuation">(</span><span class="token punctuation">)</span><span class="token operator"> -&gt; </span><span class="token keyword"></span><a href="https://kotlinlang.org/api/latest/jvm/stdlib/kotlin/-unit/index.html">Unit</a></span></span><span class="token punctuation">)</span><span class="token operator">: </span><a href="../../org.gradle.api/-named-domain-object-provider/index.html#306700278%2FMain%2F-**********">NamedDomainObjectProvider</a><span class="token operator">&lt;</span><span class="token keyword"></span><a href="index.html">T</a><span class="token operator">&gt;</span><span class="clearfix"><span class="floating-right">(<a href="https://github.com/gradle/gradle/blob/28aca86a7180baa17117e0e5ba01d8ea9feca598/platforms/core-configuration/kotlin-dsl/src/main/kotlin/org/gradle/kotlin/dsl/NamedDomainObjectContainerExtensions.kt#L256">source</a>)</span></span></div><p class="paragraph">Configures an object by name, without triggering its creation or configuration, failing if there is no such object.</p><h4 class="">See also</h4><div class="table"><div class="table-row" data-filterable-current=":docs/kotlin_dsl" data-filterable-set=":docs/kotlin_dsl"><div class="main-subrow keyValue "><div class=""><span class="inline-flex"><div><span data-unresolved-link="org.gradle.api/NamedDomainObjectContainer/named/#kotlin.String/PointingToDeclaration/"><span>Named</span><wbr></wbr><span>Domain</span><wbr></wbr><span>Object</span><wbr></wbr><span>Container.</span><wbr></wbr><span>named</span></span></div></span></div><div></div></div></div><div class="table-row" data-filterable-current=":docs/kotlin_dsl" data-filterable-set=":docs/kotlin_dsl"><div class="main-subrow keyValue "><div class=""><span class="inline-flex"><div><span data-unresolved-link="org.gradle.api/NamedDomainObjectProvider/configure/#org.gradle.api.Action[TypeParam(bounds=[kotlin.Any])]/PointingToDeclaration/"><span>Named</span><wbr></wbr><span>Domain</span><wbr></wbr><span>Object</span><wbr></wbr><span>Provider.</span><wbr></wbr><span>configure</span></span></div></span></div><div></div></div></div></div><hr><div class="symbol monospace"><span class="token keyword">operator </span><span class="token keyword">fun </span><a href="https://kotlinlang.org/api/latest/jvm/stdlib/kotlin/-string/index.html">String</a><span class="token punctuation">.</span><a href="invoke.html"><span class="token function">invoke</span></a><span class="token punctuation">(</span><span class="token punctuation">)</span><span class="token operator">: </span><a href="../../org.gradle.api/-named-domain-object-provider/index.html#306700278%2FMain%2F-**********">NamedDomainObjectProvider</a><span class="token operator">&lt;</span><span class="token keyword"></span><a href="index.html">T</a><span class="token operator">&gt;</span><span class="clearfix"><span class="floating-right">(<a href="https://github.com/gradle/gradle/blob/28aca86a7180baa17117e0e5ba01d8ea9feca598/platforms/core-configuration/kotlin-dsl/src/main/kotlin/org/gradle/kotlin/dsl/NamedDomainObjectContainerExtensions.kt#L264">source</a>)</span></span></div><p class="paragraph">Locates an object by name, without triggering its creation or configuration, failing if there is no such object.</p><h4 class="">See also</h4><div class="table"><div class="table-row" data-filterable-current=":docs/kotlin_dsl" data-filterable-set=":docs/kotlin_dsl"><div class="main-subrow keyValue "><div class=""><span class="inline-flex"><div><span data-unresolved-link="org.gradle.api/NamedDomainObjectContainer/named/#kotlin.String/PointingToDeclaration/"><span>Named</span><wbr></wbr><span>Domain</span><wbr></wbr><span>Object</span><wbr></wbr><span>Container.</span><wbr></wbr><span>named</span></span></div></span></div><div></div></div></div></div><hr><div class="symbol monospace"><span class="token keyword">operator </span><span class="token keyword">fun </span><span class="token operator">&lt;</span><span class="token keyword"></span><a href="invoke.html">U</a><span class="token operator"> : </span><a href="index.html">T</a><span class="token operator">&gt; </span><a href="https://kotlinlang.org/api/latest/jvm/stdlib/kotlin/-string/index.html">String</a><span class="token punctuation">.</span><a href="invoke.html"><span class="token function">invoke</span></a><span class="token punctuation">(</span><span class="parameters "><span class="parameter ">type<span class="token operator">: </span><a href="https://kotlinlang.org/api/latest/jvm/stdlib/kotlin.reflect/-k-class/index.html">KClass</a><span class="token operator">&lt;</span><span class="token keyword"></span><a href="invoke.html">U</a><span class="token operator">&gt;</span><span class="token punctuation">, </span></span><span class="parameter ">configuration<span class="token operator">: </span><span class="token keyword"></span><a href="invoke.html">U</a><span class="token punctuation">.</span><span class="token punctuation">(</span><span class="token punctuation">)</span><span class="token operator"> -&gt; </span><span class="token keyword"></span><a href="https://kotlinlang.org/api/latest/jvm/stdlib/kotlin/-unit/index.html">Unit</a></span></span><span class="token punctuation">)</span><span class="token operator">: </span><a href="../../org.gradle.api/-named-domain-object-provider/index.html#306700278%2FMain%2F-**********">NamedDomainObjectProvider</a><span class="token operator">&lt;</span><span class="token keyword"></span><a href="invoke.html">U</a><span class="token operator">&gt;</span><span class="clearfix"><span class="floating-right">(<a href="https://github.com/gradle/gradle/blob/28aca86a7180baa17117e0e5ba01d8ea9feca598/platforms/core-configuration/kotlin-dsl/src/main/kotlin/org/gradle/kotlin/dsl/NamedDomainObjectContainerExtensions.kt#L273">source</a>)</span></span></div><p class="paragraph">Configures an object by name, without triggering its creation or configuration, failing if there is no such object.</p><h4 class="">See also</h4><div class="table"><div class="table-row" data-filterable-current=":docs/kotlin_dsl" data-filterable-set=":docs/kotlin_dsl"><div class="main-subrow keyValue "><div class=""><span class="inline-flex"><div><span data-unresolved-link="org.gradle.api/PolymorphicDomainObjectContainer/named/#kotlin.String/PointingToDeclaration/"><span>Polymorphic</span><wbr></wbr><span>Domain</span><wbr></wbr><span>Object</span><wbr></wbr><span>Container.</span><wbr></wbr><span>named</span></span></div></span></div><div></div></div></div><div class="table-row" data-filterable-current=":docs/kotlin_dsl" data-filterable-set=":docs/kotlin_dsl"><div class="main-subrow keyValue "><div class=""><span class="inline-flex"><div><span data-unresolved-link="org.gradle.api/NamedDomainObjectProvider/configure/#org.gradle.api.Action[TypeParam(bounds=[kotlin.Any])]/PointingToDeclaration/"><span>Named</span><wbr></wbr><span>Domain</span><wbr></wbr><span>Object</span><wbr></wbr><span>Provider.</span><wbr></wbr><span>configure</span></span></div></span></div><div></div></div></div></div><hr><div class="symbol monospace"><span class="token keyword">operator </span><span class="token keyword">fun </span><span class="token operator">&lt;</span><span class="token keyword"></span><a href="invoke.html">U</a><span class="token operator"> : </span><a href="index.html">T</a><span class="token operator">&gt; </span><a href="https://kotlinlang.org/api/latest/jvm/stdlib/kotlin/-string/index.html">String</a><span class="token punctuation">.</span><a href="invoke.html"><span class="token function">invoke</span></a><span class="token punctuation">(</span><span class="parameters "><span class="parameter ">type<span class="token operator">: </span><a href="https://kotlinlang.org/api/latest/jvm/stdlib/kotlin.reflect/-k-class/index.html">KClass</a><span class="token operator">&lt;</span><span class="token keyword"></span><a href="invoke.html">U</a><span class="token operator">&gt;</span></span></span><span class="token punctuation">)</span><span class="token operator">: </span><a href="../../org.gradle.api/-named-domain-object-provider/index.html#306700278%2FMain%2F-**********">NamedDomainObjectProvider</a><span class="token operator">&lt;</span><span class="token keyword"></span><a href="invoke.html">U</a><span class="token operator">&gt;</span><span class="clearfix"><span class="floating-right">(<a href="https://github.com/gradle/gradle/blob/28aca86a7180baa17117e0e5ba01d8ea9feca598/platforms/core-configuration/kotlin-dsl/src/main/kotlin/org/gradle/kotlin/dsl/NamedDomainObjectContainerExtensions.kt#L281">source</a>)</span></span></div><p class="paragraph">Locates an object by name and type, without triggering its creation or configuration, failing if there is no such object.</p><h4 class="">See also</h4><div class="table"><div class="table-row" data-filterable-current=":docs/kotlin_dsl" data-filterable-set=":docs/kotlin_dsl"><div class="main-subrow keyValue "><div class=""><span class="inline-flex"><div><span data-unresolved-link="org.gradle.api/PolymorphicDomainObjectContainer/named/#kotlin.String/PointingToDeclaration/"><span>Polymorphic</span><wbr></wbr><span>Domain</span><wbr></wbr><span>Object</span><wbr></wbr><span>Container.</span><wbr></wbr><span>named</span></span></div></span></div><div></div></div></div></div></div>  </div>
</div>
      <div class="footer">
        <span class="go-to-top-icon"><a href="#content" id="go-to-top-link"></a></span><span>Gradle Kotlin DSL Reference</span><span
                class="pull-right"><span>Generated by </span><a
                href="https://github.com/Kotlin/dokka"><span>dokka</span><span class="padded-icon"></span></a></span>
      </div>
    </div>
</div>
</body>
</html>
