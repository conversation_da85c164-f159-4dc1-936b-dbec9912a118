<!DOCTYPE html>
<html>
<head>
    <meta name="viewport" content="width=device-width, initial-scale=1" charset="UTF-8">
    <title>MavenPublishPlugin</title>
    <link href="../../../images/logo-icon.svg" rel="icon" type="image/svg">
    <script>var pathToRoot = "../../../";</script>
    <script>const storage = localStorage.getItem("dokka-dark-mode")
    if (storage == null) {
        const osDarkSchemePreferred = window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches
        if (osDarkSchemePreferred === true) {
            document.getElementsByTagName("html")[0].classList.add("theme-dark")
        }
    } else {
        const savedDarkMode = JSON.parse(storage)
        if(savedDarkMode === true) {
            document.getElementsByTagName("html")[0].classList.add("theme-dark")
        }
    }
    </script>
<script type="text/javascript" src="../../../scripts/sourceset_dependencies.js" async="async"></script>
<link href="../../../styles/style.css" rel="Stylesheet">
<link href="../../../styles/jetbrains-mono.css" rel="Stylesheet">
<link href="../../../styles/main.css" rel="Stylesheet">
<link href="../../../styles/prism.css" rel="Stylesheet">
<link href="../../../styles/logo-styles.css" rel="Stylesheet">
<script type="text/javascript" src="../../../scripts/clipboard.js" async="async"></script>
<script type="text/javascript" src="../../../scripts/navigation-loader.js" async="async"></script>
<script type="text/javascript" src="../../../scripts/platform-content-handler.js" async="async"></script>
<script type="text/javascript" src="../../../scripts/main.js" defer="defer"></script>
<script type="text/javascript" src="../../../scripts/prism.js" async="async"></script>
<script type="text/javascript" src="../../../scripts/symbol-parameters-wrapper_deferred.js" defer="defer"></script>
<link href="../../../images/gradle-logo.svg">
<link href="../../../styles/gradle.css" rel="Stylesheet">
</head>
<body>
<div class="navigation-wrapper" id="navigation-wrapper">
    <div id="leftToggler"><span class="icon-toggler"></span></div>
    <div class="library-name">
            <a href="../../../index.html">
                    <span>gradle</span>
            </a>
    </div>
    <div>
8.5    </div>
    <div class="pull-right d-flex">
        <div class="filter-section" id="filter-section">
                <button class="platform-tag platform-selector jvm-like" data-active="" data-filter=":docs/java_api">API</button>
        </div>
        <button id="theme-toggle-button"><span id="theme-toggle"></span></button>
        <div id="searchBar"></div>
    </div>
</div>
<div id="container">
    <div id="leftColumn">
        <div id="sideMenu"></div>
    </div>
    <div id="main">
<div class="main-content" data-page-type="member" id="content" pageIds="gradle::org.gradle.api.publish.maven.plugins/MavenPublishPlugin/MavenPublishPlugin/#org.gradle.internal.instantiation.InstantiatorFactory#org.gradle.api.model.ObjectFactory#org.gradle.api.internal.artifacts.configurations.DependencyMetaDataProvider#org.gradle.api.internal.file.FileResolver#org.gradle.api.internal.attributes.ImmutableAttributesFactory#org.gradle.api.provider.ProviderFactory#org.gradle.api.internal.tasks.TaskDependencyFactory/PointingToDeclaration//-**********">
  <div class="breadcrumbs"><a href="../../../index.html">gradle</a><span class="delimiter">/</span><a href="../index.html">org.gradle.api.publish.maven.plugins</a><span class="delimiter">/</span><a href="index.html">MavenPublishPlugin</a><span class="delimiter">/</span><span class="current">MavenPublishPlugin</span></div>
  <div class="cover ">
    <h1 class="cover"><span>Maven</span><wbr></wbr><span>Publish</span><wbr></wbr><span><span>Plugin</span></span></h1>
  </div>
  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api" data-active="" data-toggle=":docs/java_api">API</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/java_api"><div class="symbol monospace"><div class="block"><div class="block"><span class="token annotation builtin">@</span><span data-unresolved-link="javax.inject/Inject///PointingToDeclaration/"><span class="token annotation builtin">Inject</span></span></div></div><span class="token keyword">constructor</span><span class="token punctuation">(</span><span class="parameters "><span class="parameter ">instantiatorFactory<span class="token operator">: </span><span data-unresolved-link="org.gradle.internal.instantiation/InstantiatorFactory///PointingToDeclaration/">InstantiatorFactory</span><span class="token punctuation">, </span></span><span class="parameter ">objectFactory<span class="token operator">: </span><a href="../../org.gradle.api.model/-object-factory/index.html">ObjectFactory</a><span class="token punctuation">, </span></span><span class="parameter ">dependencyMetaDataProvider<span class="token operator">: </span><span data-unresolved-link="org.gradle.api.internal.artifacts.configurations/DependencyMetaDataProvider///PointingToDeclaration/">DependencyMetaDataProvider</span><span class="token punctuation">, </span></span><span class="parameter ">fileResolver<span class="token operator">: </span><span data-unresolved-link="org.gradle.api.internal.file/FileResolver///PointingToDeclaration/">FileResolver</span><span class="token punctuation">, </span></span><span class="parameter ">immutableAttributesFactory<span class="token operator">: </span><span data-unresolved-link="org.gradle.api.internal.attributes/ImmutableAttributesFactory///PointingToDeclaration/">ImmutableAttributesFactory</span><span class="token punctuation">, </span></span><span class="parameter ">providerFactory<span class="token operator">: </span><a href="../../org.gradle.api.provider/-provider-factory/index.html">ProviderFactory</a><span class="token punctuation">, </span></span><span class="parameter ">taskDependencyFactory<span class="token operator">: </span><span data-unresolved-link="org.gradle.api.internal.tasks/TaskDependencyFactory///PointingToDeclaration/">TaskDependencyFactory</span></span></span><span class="token punctuation">)</span><span class="clearfix"><span class="floating-right">(<a href="https://github.com/gradle/gradle/blob/28aca86a7180baa17117e0e5ba01d8ea9feca598/platforms/software/maven/src/main/java/org/gradle/api/publish/maven/plugins/MavenPublishPlugin.java#L82">source</a>)</span></span></div></div>  </div>
</div>
      <div class="footer">
        <span class="go-to-top-icon"><a href="#content" id="go-to-top-link"></a></span><span>Gradle Kotlin DSL Reference</span><span
                class="pull-right"><span>Generated by </span><a
                href="https://github.com/Kotlin/dokka"><span>dokka</span><span class="padded-icon"></span></a></span>
      </div>
    </div>
</div>
</body>
</html>
