<!DOCTYPE html>
<html>
<head>
    <meta name="viewport" content="width=device-width, initial-scale=1" charset="UTF-8">
    <title>org.gradle.plugins.ide.eclipse.model</title>
    <link href="../../images/logo-icon.svg" rel="icon" type="image/svg">
    <script>var pathToRoot = "../../";</script>
    <script>const storage = localStorage.getItem("dokka-dark-mode")
    if (storage == null) {
        const osDarkSchemePreferred = window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches
        if (osDarkSchemePreferred === true) {
            document.getElementsByTagName("html")[0].classList.add("theme-dark")
        }
    } else {
        const savedDarkMode = JSON.parse(storage)
        if(savedDarkMode === true) {
            document.getElementsByTagName("html")[0].classList.add("theme-dark")
        }
    }
    </script>
<script type="text/javascript" src="../../scripts/sourceset_dependencies.js" async="async"></script>
<link href="../../styles/style.css" rel="Stylesheet">
<link href="../../styles/jetbrains-mono.css" rel="Stylesheet">
<link href="../../styles/main.css" rel="Stylesheet">
<link href="../../styles/prism.css" rel="Stylesheet">
<link href="../../styles/logo-styles.css" rel="Stylesheet">
<script type="text/javascript" src="../../scripts/clipboard.js" async="async"></script>
<script type="text/javascript" src="../../scripts/navigation-loader.js" async="async"></script>
<script type="text/javascript" src="../../scripts/platform-content-handler.js" async="async"></script>
<script type="text/javascript" src="../../scripts/main.js" defer="defer"></script>
<script type="text/javascript" src="../../scripts/prism.js" async="async"></script>
<script type="text/javascript" src="../../scripts/symbol-parameters-wrapper_deferred.js" defer="defer"></script>
<link href="../../images/gradle-logo.svg">
<link href="../../styles/gradle.css" rel="Stylesheet">
</head>
<body>
<div class="navigation-wrapper" id="navigation-wrapper">
    <div id="leftToggler"><span class="icon-toggler"></span></div>
    <div class="library-name">
            <a href="../../index.html">
                    <span>gradle</span>
            </a>
    </div>
    <div>
8.5    </div>
    <div class="pull-right d-flex">
        <div class="filter-section" id="filter-section">
                <button class="platform-tag platform-selector jvm-like" data-active="" data-filter=":docs/java_api">API</button>
        </div>
        <button id="theme-toggle-button"><span id="theme-toggle"></span></button>
        <div id="searchBar"></div>
    </div>
</div>
<div id="container">
    <div id="leftColumn">
        <div id="sideMenu"></div>
    </div>
    <div id="main">
<div class="main-content" data-page-type="package" id="content" pageIds="gradle::org.gradle.plugins.ide.eclipse.model////PointingToDeclaration//-1793262594">
  <div class="breadcrumbs"><a href="../../index.html">gradle</a><span class="delimiter">/</span><span class="current">org.gradle.plugins.ide.eclipse.model</span></div>
  <div class="cover ">
    <h1 class="cover"><span><span>Package-level</span></span> <span><span>declarations</span></span></h1>
    <div class="platform-hinted UnderCoverText with-platform-tabs" data-platform-hinted="data-platform-hinted">
      <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api" data-active="" data-toggle=":docs/java_api">API</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/java_api"><p class="paragraph">Classes for the model used by the eclipse plugins.</p></div>    </div>
  </div>
  <div class="tabbedcontent">
    <div class="tabs-section" tabs-section="tabs-section"><button class="section-tab" data-active="" data-togglable="TYPE">Types</button></div>
    <div class="tabs-section-body">
      <div data-togglable="TYPE">
        <h2 class="">Types</h2>
        <div class="table"><a data-name="36306654%2FClasslikes%2F-1793262594" anchor-label="AbstractClasspathEntry" id="36306654%2FClasslikes%2F-1793262594" data-filterable-set=":docs/java_api"></a>
          <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="-abstract-classpath-entry/index.html"><span>Abstract</span><wbr></wbr><span>Classpath</span><wbr></wbr><span><span>Entry</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="36306654%2FClasslikes%2F-1793262594"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
                    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api" data-active="" data-toggle=":docs/java_api">API</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/java_api"><div class="symbol monospace"><span class="token keyword">abstract </span><span class="token keyword">class </span><a href="-abstract-classpath-entry/index.html">AbstractClasspathEntry</a> : <a href="-classpath-entry/index.html">ClasspathEntry</a></div><div class="brief ">Common superclass for all <a href="-classpath-entry/index.html">ClasspathEntry</a> instances.</div></div>                  </div>
                </div>
              </div>
            </div>
          </div>
<a data-name="1200301108%2FClasslikes%2F-1793262594" anchor-label="AbstractLibrary" id="1200301108%2FClasslikes%2F-1793262594" data-filterable-set=":docs/java_api"></a>
          <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="-abstract-library/index.html"><span>Abstract</span><wbr></wbr><span><span>Library</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="1200301108%2FClasslikes%2F-1793262594"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
                    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api" data-active="" data-toggle=":docs/java_api">API</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/java_api"><div class="symbol monospace"><span class="token keyword">abstract </span><span class="token keyword">class </span><a href="-abstract-library/index.html">AbstractLibrary</a> : <a href="-abstract-classpath-entry/index.html">AbstractClasspathEntry</a></div><div class="brief ">Common superclass for the library elements.</div></div>                  </div>
                </div>
              </div>
            </div>
          </div>
<a data-name="544540693%2FClasslikes%2F-1793262594" anchor-label="AccessRule" id="544540693%2FClasslikes%2F-1793262594" data-filterable-set=":docs/java_api"></a>
          <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="-access-rule/index.html"><span>Access</span><wbr></wbr><span><span>Rule</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="544540693%2FClasslikes%2F-1793262594"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
                    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api" data-active="" data-toggle=":docs/java_api">API</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/java_api"><div class="symbol monospace"><span class="token keyword">open </span><span class="token keyword">class </span><a href="-access-rule/index.html">AccessRule</a></div><div class="brief ">Access rule associated to a classpath entry.</div></div>                  </div>
                </div>
              </div>
            </div>
          </div>
<a data-name="766217016%2FClasslikes%2F-1793262594" anchor-label="BuildCommand" id="766217016%2FClasslikes%2F-1793262594" data-filterable-set=":docs/java_api"></a>
          <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="-build-command/index.html"><span>Build</span><wbr></wbr><span><span>Command</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="766217016%2FClasslikes%2F-1793262594"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
                    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api" data-active="" data-toggle=":docs/java_api">API</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/java_api"><div class="symbol monospace"><span class="token keyword">open </span><span class="token keyword">class </span><a href="-build-command/index.html">BuildCommand</a> : <a href="https://docs.oracle.com/en/java/javase/11/docs/api/java.base/java/io/Serializable.html">Serializable</a></div><div class="brief ">A build command.</div></div>                  </div>
                </div>
              </div>
            </div>
          </div>
<a data-name="1003123280%2FClasslikes%2F-1793262594" anchor-label="Classpath" id="1003123280%2FClasslikes%2F-1793262594" data-filterable-set=":docs/java_api"></a>
          <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="-classpath/index.html"><span><span>Classpath</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="1003123280%2FClasslikes%2F-1793262594"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
                    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api" data-active="" data-toggle=":docs/java_api">API</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/java_api"><div class="symbol monospace"><span class="token keyword">open </span><span class="token keyword">class </span><a href="-classpath/index.html">Classpath</a> : <span data-unresolved-link="org.gradle.plugins.ide.internal.generator/XmlPersistableConfigurationObject///PointingToDeclaration/">XmlPersistableConfigurationObject</span></div><div class="brief ">Represents the customizable elements of an eclipse classpath file.</div></div>                  </div>
                </div>
              </div>
            </div>
          </div>
<a data-name="-1006011040%2FClasslikes%2F-1793262594" anchor-label="ClasspathEntry" id="-1006011040%2FClasslikes%2F-1793262594" data-filterable-set=":docs/java_api"></a>
          <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="-classpath-entry/index.html"><span>Classpath</span><wbr></wbr><span><span>Entry</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="-1006011040%2FClasslikes%2F-1793262594"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
                    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api" data-active="" data-toggle=":docs/java_api">API</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/java_api"><div class="symbol monospace"><span class="token keyword">interface </span><a href="-classpath-entry/index.html">ClasspathEntry</a></div><div class="brief ">Represents an entry in the Eclipse classpath.</div></div>                  </div>
                </div>
              </div>
            </div>
          </div>
<a data-name="2025127916%2FClasslikes%2F-1793262594" anchor-label="Container" id="2025127916%2FClasslikes%2F-1793262594" data-filterable-set=":docs/java_api"></a>
          <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="-container/index.html"><span><span>Container</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="2025127916%2FClasslikes%2F-1793262594"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
                    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api" data-active="" data-toggle=":docs/java_api">API</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/java_api"><div class="symbol monospace"><span class="token keyword">open </span><span class="token keyword">class </span><a href="-container/index.html">Container</a> : <a href="-abstract-classpath-entry/index.html">AbstractClasspathEntry</a></div><div class="brief ">A container classpath entry.</div></div>                  </div>
                </div>
              </div>
            </div>
          </div>
<a data-name="-1186562753%2FClasslikes%2F-1793262594" anchor-label="EclipseClasspath" id="-1186562753%2FClasslikes%2F-1793262594" data-filterable-set=":docs/java_api"></a>
          <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="-eclipse-classpath/index.html"><span>Eclipse</span><wbr></wbr><span><span>Classpath</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="-1186562753%2FClasslikes%2F-1793262594"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
                    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api" data-active="" data-toggle=":docs/java_api">API</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/java_api"><div class="symbol monospace"><span class="token keyword">abstract </span><span class="token keyword">class </span><a href="-eclipse-classpath/index.html">EclipseClasspath</a></div><div class="brief ">The build path settings for the generated Eclipse project.</div></div>                  </div>
                </div>
              </div>
            </div>
          </div>
<a data-name="-109487294%2FClasslikes%2F-1793262594" anchor-label="EclipseJdt" id="-109487294%2FClasslikes%2F-1793262594" data-filterable-set=":docs/java_api"></a>
          <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="-eclipse-jdt/index.html"><span>Eclipse</span><wbr></wbr><span><span>Jdt</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="-109487294%2FClasslikes%2F-1793262594"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
                    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api" data-active="" data-toggle=":docs/java_api">API</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/java_api"><div class="symbol monospace"><span class="token keyword">abstract </span><span class="token keyword">class </span><a href="-eclipse-jdt/index.html">EclipseJdt</a></div><div class="brief ">Enables fine-tuning jdt details of the Eclipse plugin <div class="sample-container"><pre><code class="block lang-kotlin" theme="idea">plugins {
    id 'java'
    id 'eclipse'
}

eclipse {
  jdt {
    //if you want to alter the java versions (by default they are configured with gradle java plugin settings):
    sourceCompatibility = 1.6
    targetCompatibility = 1.5
    javaRuntimeName = &quot;J2SE-1.5&quot;

    file {
      //whenMerged closure is the highest voodoo
      //and probably should be used only to solve tricky edge cases.
      //the type passed to the closure is <a href="-jdt/index.html">Jdt</a>

      //closure executed after jdt file content is loaded from existing file
      //and after gradle build information is merged
      whenMerged { jdt
        //you can tinker with the <a href="-jdt/index.html">Jdt</a> here
      }

      //withProperties allows addition of properties not currently
      //modeled by Gradle
      withProperties { properties -&gt;
          //you can tinker with the <a href="https://docs.oracle.com/en/java/javase/11/docs/api/java.base/java/util/Properties.html">java.util.Properties</a> here
      }
    }
  }
}
</code></pre><span class="top-right-position"><span class="copy-icon"></span><div class="copy-popup-wrapper popup-to-left"><span class="copy-popup-icon"></span><span>Content copied to clipboard</span></div></span></div></div></div>                  </div>
                </div>
              </div>
            </div>
          </div>
<a data-name="1910204083%2FClasslikes%2F-1793262594" anchor-label="EclipseModel" id="1910204083%2FClasslikes%2F-1793262594" data-filterable-set=":docs/java_api"></a>
          <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="-eclipse-model/index.html"><span>Eclipse</span><wbr></wbr><span><span>Model</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="1910204083%2FClasslikes%2F-1793262594"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
                    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api" data-active="" data-toggle=":docs/java_api">API</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/java_api"><div class="symbol monospace"><span class="token keyword">abstract </span><span class="token keyword">class </span><a href="-eclipse-model/index.html">EclipseModel</a></div><div class="brief ">DSL-friendly model of the Eclipse project information.</div></div>                  </div>
                </div>
              </div>
            </div>
          </div>
<a data-name="1379610115%2FClasslikes%2F-1793262594" anchor-label="EclipseProject" id="1379610115%2FClasslikes%2F-1793262594" data-filterable-set=":docs/java_api"></a>
          <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="-eclipse-project/index.html"><span>Eclipse</span><wbr></wbr><span><span>Project</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="1379610115%2FClasslikes%2F-1793262594"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
                    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api" data-active="" data-toggle=":docs/java_api">API</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/java_api"><div class="symbol monospace"><span class="token keyword">abstract </span><span class="token keyword">class </span><a href="-eclipse-project/index.html">EclipseProject</a></div><div class="brief ">Enables fine-tuning project details (.</div></div>                  </div>
                </div>
              </div>
            </div>
          </div>
<a data-name="-1044219223%2FClasslikes%2F-1793262594" anchor-label="EclipseWtp" id="-1044219223%2FClasslikes%2F-1793262594" data-filterable-set=":docs/java_api"></a>
          <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="-eclipse-wtp/index.html"><span>Eclipse</span><wbr></wbr><span><span>Wtp</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="-1044219223%2FClasslikes%2F-1793262594"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
                    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api" data-active="" data-toggle=":docs/java_api">API</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/java_api"><div class="symbol monospace"><span class="token keyword">abstract </span><span class="token keyword">class </span><a href="-eclipse-wtp/index.html">EclipseWtp</a></div><div class="brief ">Enables fine-tuning wtp/wst details of the Eclipse plugin  For projects applying the eclipse plugin and either one of the ear or war plugins, this plugin is auto-applied.</div></div>                  </div>
                </div>
              </div>
            </div>
          </div>
<a data-name="43957884%2FClasslikes%2F-1793262594" anchor-label="EclipseWtpComponent" id="43957884%2FClasslikes%2F-1793262594" data-filterable-set=":docs/java_api"></a>
          <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="-eclipse-wtp-component/index.html"><span>Eclipse</span><wbr></wbr><span>Wtp</span><wbr></wbr><span><span>Component</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="43957884%2FClasslikes%2F-1793262594"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
                    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api" data-active="" data-toggle=":docs/java_api">API</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/java_api"><div class="symbol monospace"><span class="token keyword">abstract </span><span class="token keyword">class </span><a href="-eclipse-wtp-component/index.html">EclipseWtpComponent</a></div><div class="brief ">Enables fine-tuning wtp component details of the Eclipse plugin  Example of use with a blend of all possible properties.</div></div>                  </div>
                </div>
              </div>
            </div>
          </div>
<a data-name="1863704322%2FClasslikes%2F-1793262594" anchor-label="EclipseWtpFacet" id="1863704322%2FClasslikes%2F-1793262594" data-filterable-set=":docs/java_api"></a>
          <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="-eclipse-wtp-facet/index.html"><span>Eclipse</span><wbr></wbr><span>Wtp</span><wbr></wbr><span><span>Facet</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="1863704322%2FClasslikes%2F-1793262594"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
                    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api" data-active="" data-toggle=":docs/java_api">API</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/java_api"><div class="symbol monospace"><span class="token keyword">abstract </span><span class="token keyword">class </span><a href="-eclipse-wtp-facet/index.html">EclipseWtpFacet</a></div><div class="brief ">Enables fine-tuning wtp facet details of the Eclipse plugin  Advanced configuration closures beforeMerged and whenMerged receive <a href="-wtp-facet/index.html">WtpFacet</a> object as parameter.</div></div>                  </div>
                </div>
              </div>
            </div>
          </div>
<a data-name="-1017057930%2FClasslikes%2F-1793262594" anchor-label="Facet" id="-1017057930%2FClasslikes%2F-1793262594" data-filterable-set=":docs/java_api"></a>
          <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="-facet/index.html"><span><span>Facet</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="-1017057930%2FClasslikes%2F-1793262594"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
                    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api" data-active="" data-toggle=":docs/java_api">API</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/java_api"><div class="symbol monospace"><span class="token keyword">open </span><span class="token keyword">class </span><a href="-facet/index.html">Facet</a></div><div class="brief ">A project facet.</div></div>                  </div>
                </div>
              </div>
            </div>
          </div>
<a data-name="-133494530%2FClasslikes%2F-1793262594" anchor-label="FileReference" id="-133494530%2FClasslikes%2F-1793262594" data-filterable-set=":docs/java_api"></a>
          <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="-file-reference/index.html"><span>File</span><wbr></wbr><span><span>Reference</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="-133494530%2FClasslikes%2F-1793262594"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
                    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api" data-active="" data-toggle=":docs/java_api">API</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/java_api"><div class="symbol monospace"><span class="token keyword">interface </span><a href="-file-reference/index.html">FileReference</a></div><div class="brief ">A reference to a file in eclipse.</div></div>                  </div>
                </div>
              </div>
            </div>
          </div>
<a data-name="1461367059%2FClasslikes%2F-1793262594" anchor-label="Jdt" id="1461367059%2FClasslikes%2F-1793262594" data-filterable-set=":docs/java_api"></a>
          <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="-jdt/index.html"><span><span>Jdt</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="1461367059%2FClasslikes%2F-1793262594"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
                    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api" data-active="" data-toggle=":docs/java_api">API</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/java_api"><div class="symbol monospace"><span class="token keyword">open </span><span class="token keyword">class </span><a href="-jdt/index.html">Jdt</a> : <span data-unresolved-link="org.gradle.plugins.ide.internal.generator/PropertiesPersistableConfigurationObject///PointingToDeclaration/">PropertiesPersistableConfigurationObject</span></div><div class="brief ">Represents the Eclipse JDT settings.</div></div>                  </div>
                </div>
              </div>
            </div>
          </div>
<a data-name="-1221585934%2FClasslikes%2F-1793262594" anchor-label="Library" id="-1221585934%2FClasslikes%2F-1793262594" data-filterable-set=":docs/java_api"></a>
          <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="-library/index.html"><span><span>Library</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="-1221585934%2FClasslikes%2F-1793262594"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
                    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api" data-active="" data-toggle=":docs/java_api">API</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/java_api"><div class="symbol monospace"><span class="token keyword">open </span><span class="token keyword">class </span><a href="-library/index.html">Library</a> : <a href="-abstract-library/index.html">AbstractLibrary</a></div><div class="brief ">A classpath entry representing a library.</div></div>                  </div>
                </div>
              </div>
            </div>
          </div>
<a data-name="661233371%2FClasslikes%2F-1793262594" anchor-label="Link" id="661233371%2FClasslikes%2F-1793262594" data-filterable-set=":docs/java_api"></a>
          <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="-link/index.html"><span><span>Link</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="661233371%2FClasslikes%2F-1793262594"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
                    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api" data-active="" data-toggle=":docs/java_api">API</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/java_api"><div class="symbol monospace"><span class="token keyword">open </span><span class="token keyword">class </span><a href="-link/index.html">Link</a></div><div class="brief ">Link.</div></div>                  </div>
                </div>
              </div>
            </div>
          </div>
<a data-name="-1073261900%2FClasslikes%2F-1793262594" anchor-label="Output" id="-1073261900%2FClasslikes%2F-1793262594" data-filterable-set=":docs/java_api"></a>
          <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="-output/index.html"><span><span>Output</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="-1073261900%2FClasslikes%2F-1793262594"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
                    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api" data-active="" data-toggle=":docs/java_api">API</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/java_api"><div class="symbol monospace"><span class="token keyword">open </span><span class="token keyword">class </span><a href="-output/index.html">Output</a> : <a href="-classpath-entry/index.html">ClasspathEntry</a></div><div class="brief ">A classpath entry representing an output folder.</div></div>                  </div>
                </div>
              </div>
            </div>
          </div>
<a data-name="-1330957484%2FClasslikes%2F-1793262594" anchor-label="Project" id="-1330957484%2FClasslikes%2F-1793262594" data-filterable-set=":docs/java_api"></a>
          <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="-project/index.html"><span><span>Project</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="-1330957484%2FClasslikes%2F-1793262594"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
                    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api" data-active="" data-toggle=":docs/java_api">API</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/java_api"><div class="symbol monospace"><span class="token keyword">open </span><span class="token keyword">class </span><a href="-project/index.html">Project</a> : <span data-unresolved-link="org.gradle.plugins.ide.internal.generator/XmlPersistableConfigurationObject///PointingToDeclaration/">XmlPersistableConfigurationObject</span></div><div class="brief ">Represents the customizable elements of an eclipse project file.</div></div>                  </div>
                </div>
              </div>
            </div>
          </div>
<a data-name="1386697353%2FClasslikes%2F-1793262594" anchor-label="ProjectDependency" id="1386697353%2FClasslikes%2F-1793262594" data-filterable-set=":docs/java_api"></a>
          <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="-project-dependency/index.html"><span>Project</span><wbr></wbr><span><span>Dependency</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="1386697353%2FClasslikes%2F-1793262594"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
                    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api" data-active="" data-toggle=":docs/java_api">API</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/java_api"><div class="symbol monospace"><span class="token keyword">open </span><span class="token keyword">class </span><a href="-project-dependency/index.html">ProjectDependency</a> : <a href="-abstract-classpath-entry/index.html">AbstractClasspathEntry</a></div><div class="brief ">A classpath entry representing a project dependency.</div></div>                  </div>
                </div>
              </div>
            </div>
          </div>
<a data-name="-1004465617%2FClasslikes%2F-1793262594" anchor-label="ResourceFilter" id="-1004465617%2FClasslikes%2F-1793262594" data-filterable-set=":docs/java_api"></a>
          <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="-resource-filter/index.html"><span>Resource</span><wbr></wbr><span><span>Filter</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="-1004465617%2FClasslikes%2F-1793262594"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
                    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api" data-active="" data-toggle=":docs/java_api">API</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/java_api"><div class="symbol monospace"><span class="token keyword">interface </span><a href="-resource-filter/index.html">ResourceFilter</a></div><div class="brief ">The gradle DSL model of an Eclipse resource filter.</div></div>                  </div>
                </div>
              </div>
            </div>
          </div>
<a data-name="449451468%2FClasslikes%2F-1793262594" anchor-label="ResourceFilterAppliesTo" id="449451468%2FClasslikes%2F-1793262594" data-filterable-set=":docs/java_api"></a>
          <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="-resource-filter-applies-to/index.html"><span>Resource</span><wbr></wbr><span>Filter</span><wbr></wbr><span>Applies</span><wbr></wbr><span><span>To</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="449451468%2FClasslikes%2F-1793262594"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
                    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api" data-active="" data-toggle=":docs/java_api">API</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/java_api"><div class="symbol monospace"><span class="token keyword">enum </span><a href="-resource-filter-applies-to/index.html">ResourceFilterAppliesTo</a></div><div class="brief ">Specifies the type of resource that the Eclipse <a href="-resource-filter/index.html">ResourceFilter</a> applies to.</div></div>                  </div>
                </div>
              </div>
            </div>
          </div>
<a data-name="-743598175%2FClasslikes%2F-1793262594" anchor-label="ResourceFilterMatcher" id="-743598175%2FClasslikes%2F-1793262594" data-filterable-set=":docs/java_api"></a>
          <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="-resource-filter-matcher/index.html"><span>Resource</span><wbr></wbr><span>Filter</span><wbr></wbr><span><span>Matcher</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="-743598175%2FClasslikes%2F-1793262594"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
                    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api" data-active="" data-toggle=":docs/java_api">API</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/java_api"><div class="symbol monospace"><span class="token keyword">interface </span><a href="-resource-filter-matcher/index.html">ResourceFilterMatcher</a></div><div class="brief ">The model of an Eclipse resource filter matcher.</div></div>                  </div>
                </div>
              </div>
            </div>
          </div>
<a data-name="1098873109%2FClasslikes%2F-1793262594" anchor-label="ResourceFilterType" id="1098873109%2FClasslikes%2F-1793262594" data-filterable-set=":docs/java_api"></a>
          <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="-resource-filter-type/index.html"><span>Resource</span><wbr></wbr><span>Filter</span><wbr></wbr><span><span>Type</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="1098873109%2FClasslikes%2F-1793262594"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
                    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api" data-active="" data-toggle=":docs/java_api">API</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/java_api"><div class="symbol monospace"><span class="token keyword">enum </span><a href="-resource-filter-type/index.html">ResourceFilterType</a></div><div class="brief ">Specifies whether an Eclipse <a href="-resource-filter/index.html">ResourceFilter</a> is including or excluding resources.</div></div>                  </div>
                </div>
              </div>
            </div>
          </div>
<a data-name="51053900%2FClasslikes%2F-1793262594" anchor-label="SourceFolder" id="51053900%2FClasslikes%2F-1793262594" data-filterable-set=":docs/java_api"></a>
          <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="-source-folder/index.html"><span>Source</span><wbr></wbr><span><span>Folder</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="51053900%2FClasslikes%2F-1793262594"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
                    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api" data-active="" data-toggle=":docs/java_api">API</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/java_api"><div class="symbol monospace"><span class="token keyword">open </span><span class="token keyword">class </span><a href="-source-folder/index.html">SourceFolder</a> : <a href="-abstract-classpath-entry/index.html">AbstractClasspathEntry</a></div><div class="brief ">SourceFolder.path contains only project relative path.</div></div>                  </div>
                </div>
              </div>
            </div>
          </div>
<a data-name="-1381186909%2FClasslikes%2F-1793262594" anchor-label="UnresolvedLibrary" id="-1381186909%2FClasslikes%2F-1793262594" data-filterable-set=":docs/java_api"></a>
          <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="-unresolved-library/index.html"><span>Unresolved</span><wbr></wbr><span><span>Library</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="-1381186909%2FClasslikes%2F-1793262594"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
                    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api" data-active="" data-toggle=":docs/java_api">API</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/java_api"><div class="symbol monospace"><span class="token keyword">open </span><span class="token keyword">class </span><a href="-unresolved-library/index.html">UnresolvedLibrary</a> : <a href="-library/index.html">Library</a></div><div class="brief ">A library that turned out to be unresolved.</div></div>                  </div>
                </div>
              </div>
            </div>
          </div>
<a data-name="189898905%2FClasslikes%2F-1793262594" anchor-label="Variable" id="189898905%2FClasslikes%2F-1793262594" data-filterable-set=":docs/java_api"></a>
          <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="-variable/index.html"><span><span>Variable</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="189898905%2FClasslikes%2F-1793262594"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
                    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api" data-active="" data-toggle=":docs/java_api">API</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/java_api"><div class="symbol monospace"><span class="token keyword">open </span><span class="token keyword">class </span><a href="-variable/index.html">Variable</a> : <a href="-abstract-library/index.html">AbstractLibrary</a></div><div class="brief ">A variable library entry.</div></div>                  </div>
                </div>
              </div>
            </div>
          </div>
<a data-name="1151996237%2FClasslikes%2F-1793262594" anchor-label="WbDependentModule" id="1151996237%2FClasslikes%2F-1793262594" data-filterable-set=":docs/java_api"></a>
          <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="-wb-dependent-module/index.html"><span>Wb</span><wbr></wbr><span>Dependent</span><wbr></wbr><span><span>Module</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="1151996237%2FClasslikes%2F-1793262594"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
                    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api" data-active="" data-toggle=":docs/java_api">API</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/java_api"><div class="symbol monospace"><span class="token keyword">open </span><span class="token keyword">class </span><a href="-wb-dependent-module/index.html">WbDependentModule</a> : <a href="-wb-module-entry/index.html">WbModuleEntry</a></div><div class="brief ">A wtp descriptor dependent module entry.</div></div>                  </div>
                </div>
              </div>
            </div>
          </div>
<a data-name="-1414603246%2FClasslikes%2F-1793262594" anchor-label="WbModuleEntry" id="-1414603246%2FClasslikes%2F-1793262594" data-filterable-set=":docs/java_api"></a>
          <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="-wb-module-entry/index.html"><span>Wb</span><wbr></wbr><span>Module</span><wbr></wbr><span><span>Entry</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="-1414603246%2FClasslikes%2F-1793262594"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
                    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api" data-active="" data-toggle=":docs/java_api">API</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/java_api"><div class="symbol monospace"><span class="token keyword">interface </span><a href="-wb-module-entry/index.html">WbModuleEntry</a></div><div class="brief ">Represents an entry in <code class="lang-kotlin">wb-module</code>.</div></div>                  </div>
                </div>
              </div>
            </div>
          </div>
<a data-name="632998101%2FClasslikes%2F-1793262594" anchor-label="WbProperty" id="632998101%2FClasslikes%2F-1793262594" data-filterable-set=":docs/java_api"></a>
          <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="-wb-property/index.html"><span>Wb</span><wbr></wbr><span><span>Property</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="632998101%2FClasslikes%2F-1793262594"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
                    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api" data-active="" data-toggle=":docs/java_api">API</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/java_api"><div class="symbol monospace"><span class="token keyword">open </span><span class="token keyword">class </span><a href="-wb-property/index.html">WbProperty</a> : <a href="-wb-module-entry/index.html">WbModuleEntry</a></div><div class="brief ">A wtp descriptor property entry.</div></div>                  </div>
                </div>
              </div>
            </div>
          </div>
<a data-name="730801340%2FClasslikes%2F-1793262594" anchor-label="WbResource" id="730801340%2FClasslikes%2F-1793262594" data-filterable-set=":docs/java_api"></a>
          <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="-wb-resource/index.html"><span>Wb</span><wbr></wbr><span><span>Resource</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="730801340%2FClasslikes%2F-1793262594"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
                    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api" data-active="" data-toggle=":docs/java_api">API</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/java_api"><div class="symbol monospace"><span class="token keyword">open </span><span class="token keyword">class </span><a href="-wb-resource/index.html">WbResource</a> : <a href="-wb-module-entry/index.html">WbModuleEntry</a></div><div class="brief ">A wtp descriptor resource entry.</div></div>                  </div>
                </div>
              </div>
            </div>
          </div>
<a data-name="1017275339%2FClasslikes%2F-1793262594" anchor-label="WtpComponent" id="1017275339%2FClasslikes%2F-1793262594" data-filterable-set=":docs/java_api"></a>
          <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="-wtp-component/index.html"><span>Wtp</span><wbr></wbr><span><span>Component</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="1017275339%2FClasslikes%2F-1793262594"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
                    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api" data-active="" data-toggle=":docs/java_api">API</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/java_api"><div class="symbol monospace"><span class="token keyword">open </span><span class="token keyword">class </span><a href="-wtp-component/index.html">WtpComponent</a> : <span data-unresolved-link="org.gradle.plugins.ide.internal.generator/XmlPersistableConfigurationObject///PointingToDeclaration/">XmlPersistableConfigurationObject</span></div><div class="brief ">Creates the .settings/org.eclipse.wst.common.component file for WTP projects.</div></div>                  </div>
                </div>
              </div>
            </div>
          </div>
<a data-name="-559512623%2FClasslikes%2F-1793262594" anchor-label="WtpFacet" id="-559512623%2FClasslikes%2F-1793262594" data-filterable-set=":docs/java_api"></a>
          <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="-wtp-facet/index.html"><span>Wtp</span><wbr></wbr><span><span>Facet</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="-559512623%2FClasslikes%2F-1793262594"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
                    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api" data-active="" data-toggle=":docs/java_api">API</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/java_api"><div class="symbol monospace"><span class="token keyword">open </span><span class="token keyword">class </span><a href="-wtp-facet/index.html">WtpFacet</a> : <span data-unresolved-link="org.gradle.plugins.ide.internal.generator/XmlPersistableConfigurationObject///PointingToDeclaration/">XmlPersistableConfigurationObject</span></div><div class="brief ">Creates the .settings/org.eclipse.wst.common.project.facet.core.xml file for WTP projects.</div></div>                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
      <div class="footer">
        <span class="go-to-top-icon"><a href="#content" id="go-to-top-link"></a></span><span>Gradle Kotlin DSL Reference</span><span
                class="pull-right"><span>Generated by </span><a
                href="https://github.com/Kotlin/dokka"><span>dokka</span><span class="padded-icon"></span></a></span>
      </div>
    </div>
</div>
</body>
</html>
