<!DOCTYPE html>
<html>
<head>
    <meta name="viewport" content="width=device-width, initial-scale=1" charset="UTF-8">
    <title>VisualCppPlatformToolChain</title>
    <link href="../../../images/logo-icon.svg" rel="icon" type="image/svg">
    <script>var pathToRoot = "../../../";</script>
    <script>const storage = localStorage.getItem("dokka-dark-mode")
    if (storage == null) {
        const osDarkSchemePreferred = window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches
        if (osDarkSchemePreferred === true) {
            document.getElementsByTagName("html")[0].classList.add("theme-dark")
        }
    } else {
        const savedDarkMode = JSON.parse(storage)
        if(savedDarkMode === true) {
            document.getElementsByTagName("html")[0].classList.add("theme-dark")
        }
    }
    </script>
<script type="text/javascript" src="../../../scripts/sourceset_dependencies.js" async="async"></script>
<link href="../../../styles/style.css" rel="Stylesheet">
<link href="../../../styles/jetbrains-mono.css" rel="Stylesheet">
<link href="../../../styles/main.css" rel="Stylesheet">
<link href="../../../styles/prism.css" rel="Stylesheet">
<link href="../../../styles/logo-styles.css" rel="Stylesheet">
<script type="text/javascript" src="../../../scripts/clipboard.js" async="async"></script>
<script type="text/javascript" src="../../../scripts/navigation-loader.js" async="async"></script>
<script type="text/javascript" src="../../../scripts/platform-content-handler.js" async="async"></script>
<script type="text/javascript" src="../../../scripts/main.js" defer="defer"></script>
<script type="text/javascript" src="../../../scripts/prism.js" async="async"></script>
<script type="text/javascript" src="../../../scripts/symbol-parameters-wrapper_deferred.js" defer="defer"></script>
<link href="../../../images/gradle-logo.svg">
<link href="../../../styles/gradle.css" rel="Stylesheet">
</head>
<body>
<div class="navigation-wrapper" id="navigation-wrapper">
    <div id="leftToggler"><span class="icon-toggler"></span></div>
    <div class="library-name">
            <a href="../../../index.html">
                    <span>gradle</span>
            </a>
    </div>
    <div>
8.5    </div>
    <div class="pull-right d-flex">
        <div class="filter-section" id="filter-section">
                <button class="platform-tag platform-selector jvm-like" data-active="" data-filter=":docs/java_api">API</button>
        </div>
        <button id="theme-toggle-button"><span id="theme-toggle"></span></button>
        <div id="searchBar"></div>
    </div>
</div>
<div id="container">
    <div id="leftColumn">
        <div id="sideMenu"></div>
    </div>
    <div id="main">
<div class="main-content" data-page-type="classlike" id="content" pageIds="gradle::org.gradle.nativeplatform.toolchain/VisualCppPlatformToolChain///PointingToDeclaration//-1793262594">
  <div class="breadcrumbs"><a href="../../../index.html">gradle</a><span class="delimiter">/</span><a href="../index.html">org.gradle.nativeplatform.toolchain</a><span class="delimiter">/</span><span class="current">VisualCppPlatformToolChain</span></div>
  <div class="cover ">
    <h1 class="cover"><span>Visual</span><wbr></wbr><span>Cpp</span><wbr></wbr><span>Platform</span><wbr></wbr><span>Tool</span><wbr></wbr><span><span>Chain</span></span></h1>
    <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
      <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api" data-active="" data-toggle=":docs/java_api">API</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/java_api"><div class="symbol monospace"><div class="block"><div class="block"><span class="token annotation builtin">@</span><a href="../../org.gradle.api/-incubating/index.html"><span class="token annotation builtin">Incubating</span></a></div></div><span class="token keyword">interface </span><a href="index.html">VisualCppPlatformToolChain</a> : <a href="../-native-platform-tool-chain/index.html">NativePlatformToolChain</a><span class="clearfix"><span class="floating-right">(<a href="https://github.com/gradle/gradle/blob/28aca86a7180baa17117e0e5ba01d8ea9feca598/subprojects/platform-native/src/main/java/org/gradle/nativeplatform/toolchain/VisualCppPlatformToolChain.java#L25">source</a>)</span></span></div><p class="paragraph">Visual C++ specific settings for the tools used to build for a particular platform.</p></div>    </div>
  </div>
  <div class="tabbedcontent">
    <div class="tabs-section" tabs-section="tabs-section"><button class="section-tab" data-active="" data-togglable="CONSTRUCTOR,TYPE,PROPERTY,FUNCTION">Members</button></div>
    <div class="tabs-section-body">
      <div data-togglable="FUNCTION">
        <h2 class="">Functions</h2>
        <div class="table"><a data-name="-965272668%2FFunctions%2F-1793262594" anchor-label="getAssembler" id="-965272668%2FFunctions%2F-1793262594" data-filterable-set=":docs/java_api"></a>
          <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="get-assembler.html"><span>get</span><wbr></wbr><span><span>Assembler</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="-965272668%2FFunctions%2F-1793262594"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
                    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api" data-active="" data-toggle=":docs/java_api">API</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/java_api"><div class="symbol monospace"><span class="token keyword">abstract </span><span class="token keyword"></span><span class="token keyword">fun </span><a href="get-assembler.html"><span class="token function">getAssembler</span></a><span class="token punctuation">(</span><span class="token punctuation">)</span><span class="token operator">: </span><a href="../-command-line-tool-configuration/index.html">CommandLineToolConfiguration</a></div><div class="brief ">Returns the settings to use for the assembler.</div></div>                  </div>
                </div>
              </div>
            </div>
          </div>
<a data-name="-168399742%2FFunctions%2F-1793262594" anchor-label="getcCompiler" id="-168399742%2FFunctions%2F-1793262594" data-filterable-set=":docs/java_api"></a>
          <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="getc-compiler.html"><span>getc</span><wbr></wbr><span><span>Compiler</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="-168399742%2FFunctions%2F-1793262594"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
                    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api" data-active="" data-toggle=":docs/java_api">API</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/java_api"><div class="symbol monospace"><span class="token keyword">abstract </span><span class="token keyword"></span><span class="token keyword">fun </span><a href="getc-compiler.html"><span class="token function">getcCompiler</span></a><span class="token punctuation">(</span><span class="token punctuation">)</span><span class="token operator">: </span><a href="../-command-line-tool-configuration/index.html">CommandLineToolConfiguration</a></div><div class="brief ">Returns the settings to use for the C compiler.</div></div>                  </div>
                </div>
              </div>
            </div>
          </div>
<a data-name="-879330910%2FFunctions%2F-1793262594" anchor-label="getCppCompiler" id="-879330910%2FFunctions%2F-1793262594" data-filterable-set=":docs/java_api"></a>
          <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="get-cpp-compiler.html"><span>get</span><wbr></wbr><span>Cpp</span><wbr></wbr><span><span>Compiler</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="-879330910%2FFunctions%2F-1793262594"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
                    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api" data-active="" data-toggle=":docs/java_api">API</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/java_api"><div class="symbol monospace"><span class="token keyword">abstract </span><span class="token keyword"></span><span class="token keyword">fun </span><a href="get-cpp-compiler.html"><span class="token function">getCppCompiler</span></a><span class="token punctuation">(</span><span class="token punctuation">)</span><span class="token operator">: </span><a href="../-command-line-tool-configuration/index.html">CommandLineToolConfiguration</a></div><div class="brief ">Returns the settings to use for the C++ compiler.</div></div>                  </div>
                </div>
              </div>
            </div>
          </div>
<a data-name="1974189071%2FFunctions%2F-1793262594" anchor-label="getLinker" id="1974189071%2FFunctions%2F-1793262594" data-filterable-set=":docs/java_api"></a>
          <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="get-linker.html"><span>get</span><wbr></wbr><span><span>Linker</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="1974189071%2FFunctions%2F-1793262594"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
                    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api" data-active="" data-toggle=":docs/java_api">API</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/java_api"><div class="symbol monospace"><span class="token keyword">abstract </span><span class="token keyword"></span><span class="token keyword">fun </span><a href="get-linker.html"><span class="token function">getLinker</span></a><span class="token punctuation">(</span><span class="token punctuation">)</span><span class="token operator">: </span><a href="../-command-line-tool-configuration/index.html">CommandLineToolConfiguration</a></div><div class="brief ">Returns the settings to use for the linker.</div></div>                  </div>
                </div>
              </div>
            </div>
          </div>
<a data-name="235320849%2FFunctions%2F-1793262594" anchor-label="getPlatform" id="235320849%2FFunctions%2F-1793262594" data-filterable-set=":docs/java_api"></a>
          <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="../-native-platform-tool-chain/get-platform.html"><span>get</span><wbr></wbr><span><span>Platform</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="235320849%2FFunctions%2F-1793262594"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
                    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api" data-active="" data-toggle=":docs/java_api">API</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/java_api"><div class="symbol monospace"><span class="token keyword">abstract </span><span class="token keyword"></span><span class="token keyword">fun </span><a href="../-native-platform-tool-chain/get-platform.html"><span class="token function">getPlatform</span></a><span class="token punctuation">(</span><span class="token punctuation">)</span><span class="token operator">: </span><a href="../../org.gradle.nativeplatform.platform/-native-platform/index.html">NativePlatform</a></div></div>                  </div>
                </div>
              </div>
            </div>
          </div>
<a data-name="1507914726%2FFunctions%2F-1793262594" anchor-label="getRcCompiler" id="1507914726%2FFunctions%2F-1793262594" data-filterable-set=":docs/java_api"></a>
          <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="get-rc-compiler.html"><span>get</span><wbr></wbr><span>Rc</span><wbr></wbr><span><span>Compiler</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="1507914726%2FFunctions%2F-1793262594"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
                    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api" data-active="" data-toggle=":docs/java_api">API</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/java_api"><div class="symbol monospace"><span class="token keyword">abstract </span><span class="token keyword"></span><span class="token keyword">fun </span><a href="get-rc-compiler.html"><span class="token function">getRcCompiler</span></a><span class="token punctuation">(</span><span class="token punctuation">)</span><span class="token operator">: </span><a href="../-command-line-tool-configuration/index.html">CommandLineToolConfiguration</a></div><div class="brief ">Returns the settings to use for the Windows resources compiler.</div></div>                  </div>
                </div>
              </div>
            </div>
          </div>
<a data-name="816701725%2FFunctions%2F-1793262594" anchor-label="getStaticLibArchiver" id="816701725%2FFunctions%2F-1793262594" data-filterable-set=":docs/java_api"></a>
          <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="get-static-lib-archiver.html"><span>get</span><wbr></wbr><span>Static</span><wbr></wbr><span>Lib</span><wbr></wbr><span><span>Archiver</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="816701725%2FFunctions%2F-1793262594"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
                    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api" data-active="" data-toggle=":docs/java_api">API</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/java_api"><div class="symbol monospace"><span class="token keyword">abstract </span><span class="token keyword"></span><span class="token keyword">fun </span><a href="get-static-lib-archiver.html"><span class="token function">getStaticLibArchiver</span></a><span class="token punctuation">(</span><span class="token punctuation">)</span><span class="token operator">: </span><a href="../-command-line-tool-configuration/index.html">CommandLineToolConfiguration</a></div><div class="brief ">Returns the settings to use for the archiver.</div></div>                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
      <div class="footer">
        <span class="go-to-top-icon"><a href="#content" id="go-to-top-link"></a></span><span>Gradle Kotlin DSL Reference</span><span
                class="pull-right"><span>Generated by </span><a
                href="https://github.com/Kotlin/dokka"><span>dokka</span><span class="padded-icon"></span></a></span>
      </div>
    </div>
</div>
</body>
</html>
