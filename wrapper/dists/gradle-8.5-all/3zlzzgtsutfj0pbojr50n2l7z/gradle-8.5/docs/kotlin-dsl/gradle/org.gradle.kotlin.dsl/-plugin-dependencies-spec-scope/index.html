<!DOCTYPE html>
<html>
<head>
    <meta name="viewport" content="width=device-width, initial-scale=1" charset="UTF-8">
    <title>PluginDependenciesSpecScope</title>
    <link href="../../../images/logo-icon.svg" rel="icon" type="image/svg">
    <script>var pathToRoot = "../../../";</script>
    <script>const storage = localStorage.getItem("dokka-dark-mode")
    if (storage == null) {
        const osDarkSchemePreferred = window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches
        if (osDarkSchemePreferred === true) {
            document.getElementsByTagName("html")[0].classList.add("theme-dark")
        }
    } else {
        const savedDarkMode = JSON.parse(storage)
        if(savedDarkMode === true) {
            document.getElementsByTagName("html")[0].classList.add("theme-dark")
        }
    }
    </script>
<script type="text/javascript" src="../../../scripts/sourceset_dependencies.js" async="async"></script>
<link href="../../../styles/style.css" rel="Stylesheet">
<link href="../../../styles/jetbrains-mono.css" rel="Stylesheet">
<link href="../../../styles/main.css" rel="Stylesheet">
<link href="../../../styles/prism.css" rel="Stylesheet">
<link href="../../../styles/logo-styles.css" rel="Stylesheet">
<script type="text/javascript" src="../../../scripts/clipboard.js" async="async"></script>
<script type="text/javascript" src="../../../scripts/navigation-loader.js" async="async"></script>
<script type="text/javascript" src="../../../scripts/platform-content-handler.js" async="async"></script>
<script type="text/javascript" src="../../../scripts/main.js" defer="defer"></script>
<script type="text/javascript" src="../../../scripts/prism.js" async="async"></script>
<script type="text/javascript" src="../../../scripts/symbol-parameters-wrapper_deferred.js" defer="defer"></script>
<link href="../../../images/gradle-logo.svg">
<link href="../../../styles/gradle.css" rel="Stylesheet">
</head>
<body>
<div class="navigation-wrapper" id="navigation-wrapper">
    <div id="leftToggler"><span class="icon-toggler"></span></div>
    <div class="library-name">
            <a href="../../../index.html">
                    <span>gradle</span>
            </a>
    </div>
    <div>
8.5    </div>
    <div class="pull-right d-flex">
        <div class="filter-section" id="filter-section">
                <button class="platform-tag platform-selector jvm-like" data-active="" data-filter=":docs/kotlin_dsl">DSL</button>
        </div>
        <button id="theme-toggle-button"><span id="theme-toggle"></span></button>
        <div id="searchBar"></div>
    </div>
</div>
<div id="container">
    <div id="leftColumn">
        <div id="sideMenu"></div>
    </div>
    <div id="main">
<div class="main-content" data-page-type="classlike" id="content" pageIds="gradle::org.gradle.kotlin.dsl/PluginDependenciesSpecScope///PointingToDeclaration//-**********">
  <div class="breadcrumbs"><a href="../../../index.html">gradle</a><span class="delimiter">/</span><a href="../index.html">org.gradle.kotlin.dsl</a><span class="delimiter">/</span><span class="current">PluginDependenciesSpecScope</span></div>
  <div class="cover ">
    <h1 class="cover"><span>Plugin</span><wbr></wbr><span>Dependencies</span><wbr></wbr><span>Spec</span><wbr></wbr><span><span>Scope</span></span></h1>
    <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
      <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/kotlin_dsl" data-filterable-set=":docs/kotlin_dsl" data-active="" data-toggle=":docs/kotlin_dsl">DSL</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/kotlin_dsl"><div class="symbol monospace"><span class="token keyword">open </span><span class="token keyword">class </span><a href="index.html">PluginDependenciesSpecScope</a> : <a href="../../org.gradle.plugin.use/-plugin-dependencies-spec/index.html#552792532%2FMain%2F-**********">PluginDependenciesSpec</a><span class="clearfix"><span class="floating-right">(<a href="https://github.com/gradle/gradle/blob/28aca86a7180baa17117e0e5ba01d8ea9feca598/platforms/core-configuration/kotlin-dsl/src/main/kotlin/org/gradle/kotlin/dsl/PluginDependenciesSpecScope.kt#L35">source</a>)</span></span></div><p class="paragraph">Receiver for the <code class="lang-kotlin">plugins</code> block.</p><p class="paragraph">This class exists for the sole purpose of marking the <code class="lang-kotlin">plugins</code> block as a <a href="../-gradle-dsl/index.html">GradleDsl</a> thus hiding all members provided by the outer script scope.</p><h4 class="">See also</h4><div class="table"><div class="table-row" data-filterable-current=":docs/kotlin_dsl" data-filterable-set=":docs/kotlin_dsl"><div class="main-subrow keyValue "><div class=""><span class="inline-flex"><div><a href="../../org.gradle.plugin.use/-plugin-dependencies-spec/index.html#552792532%2FMain%2F-**********"><span>Plugin</span><wbr></wbr><span>Dependencies</span><wbr></wbr><span><span>Spec</span></span></a></div></span></div><div></div></div></div></div></div>    </div>
  </div>
  <div class="tabbedcontent">
    <div class="tabs-section" tabs-section="tabs-section"><button class="section-tab" data-active="" data-togglable="CONSTRUCTOR,TYPE,PROPERTY,FUNCTION">Members</button><button class="section-tab" data-togglable="CONSTRUCTOR,TYPE,PROPERTY,FUNCTION,EXTENSION_PROPERTY,EXTENSION_FUNCTION">Members &amp; Extensions</button></div>
    <div class="tabs-section-body">
      <div data-togglable="EXTENSION_PROPERTY">
        <h2 class="">Properties</h2>
        <div class="table"><a data-name="1968149145%2FProperties%2F-**********" anchor-label="antlr" id="1968149145%2FProperties%2F-**********" data-filterable-set=":docs/kotlin_dsl"></a>
          <div class="table-row" data-togglable="EXTENSION_PROPERTY" data-filterable-current=":docs/kotlin_dsl" data-filterable-set=":docs/kotlin_dsl">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="../antlr.html"><span><span>antlr</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="1968149145%2FProperties%2F-**********"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
                    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/kotlin_dsl" data-filterable-set=":docs/kotlin_dsl" data-active="" data-toggle=":docs/kotlin_dsl">DSL</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/kotlin_dsl"><div class="symbol monospace"><span class="token keyword"></span><span class="token keyword">val </span><a href="../../org.gradle.plugin.use/-plugin-dependencies-spec/index.html#552792532%2FMain%2F-**********">PluginDependenciesSpec</a><span class="token punctuation">.</span><a href="../antlr.html">antlr</a><span class="token operator">: </span><a href="../../org.gradle.plugin.use/-plugin-dependency-spec/index.html#-**********%2FMain%2F-**********">PluginDependencySpec</a></div><div class="brief "><p class="paragraph">The builtin Gradle plugin implemented by <span data-unresolved-link="org.gradle.api.plugins.antlr/AntlrPlugin///PointingToDeclaration/">org.gradle.api.plugins.antlr.AntlrPlugin</span>.</p></div></div>                  </div>
                </div>
              </div>
            </div>
          </div>
<a data-name="-623491306%2FProperties%2F-**********" anchor-label="application" id="-623491306%2FProperties%2F-**********" data-filterable-set=":docs/kotlin_dsl"></a>
          <div class="table-row" data-togglable="EXTENSION_PROPERTY" data-filterable-current=":docs/kotlin_dsl" data-filterable-set=":docs/kotlin_dsl">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="../application.html"><span><span>application</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="-623491306%2FProperties%2F-**********"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
                    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/kotlin_dsl" data-filterable-set=":docs/kotlin_dsl" data-active="" data-toggle=":docs/kotlin_dsl">DSL</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/kotlin_dsl"><div class="symbol monospace"><span class="token keyword"></span><span class="token keyword">val </span><a href="../../org.gradle.plugin.use/-plugin-dependencies-spec/index.html#552792532%2FMain%2F-**********">PluginDependenciesSpec</a><span class="token punctuation">.</span><a href="../application.html">application</a><span class="token operator">: </span><a href="../../org.gradle.plugin.use/-plugin-dependency-spec/index.html#-**********%2FMain%2F-**********">PluginDependencySpec</a></div><div class="brief "><p class="paragraph">The builtin Gradle plugin implemented by <span data-unresolved-link="org.gradle.api.plugins/ApplicationPlugin///PointingToDeclaration/">org.gradle.api.plugins.ApplicationPlugin</span>.</p></div></div>                  </div>
                </div>
              </div>
            </div>
          </div>
<a data-name="1079869638%2FProperties%2F-**********" anchor-label="assembler" id="1079869638%2FProperties%2F-**********" data-filterable-set=":docs/kotlin_dsl"></a>
          <div class="table-row" data-togglable="EXTENSION_PROPERTY" data-filterable-current=":docs/kotlin_dsl" data-filterable-set=":docs/kotlin_dsl">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="../assembler.html"><span><span>assembler</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="1079869638%2FProperties%2F-**********"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
                    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/kotlin_dsl" data-filterable-set=":docs/kotlin_dsl" data-active="" data-toggle=":docs/kotlin_dsl">DSL</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/kotlin_dsl"><div class="symbol monospace"><span class="token keyword"></span><span class="token keyword">val </span><a href="../../org.gradle.plugin.use/-plugin-dependencies-spec/index.html#552792532%2FMain%2F-**********">PluginDependenciesSpec</a><span class="token punctuation">.</span><a href="../assembler.html">assembler</a><span class="token operator">: </span><a href="../../org.gradle.plugin.use/-plugin-dependency-spec/index.html#-**********%2FMain%2F-**********">PluginDependencySpec</a></div><div class="brief "><p class="paragraph">The builtin Gradle plugin implemented by <span data-unresolved-link="org.gradle.language.assembler.plugins/AssemblerPlugin///PointingToDeclaration/">org.gradle.language.assembler.plugins.AssemblerPlugin</span>.</p></div></div>                  </div>
                </div>
              </div>
            </div>
          </div>
<a data-name="-1028361583%2FProperties%2F-**********" anchor-label="assembler-lang" id="-1028361583%2FProperties%2F-**********" data-filterable-set=":docs/kotlin_dsl"></a>
          <div class="table-row" data-togglable="EXTENSION_PROPERTY" data-filterable-current=":docs/kotlin_dsl" data-filterable-set=":docs/kotlin_dsl">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="../assembler-lang.html"><span><span>assembler-lang</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="-1028361583%2FProperties%2F-**********"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
                    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/kotlin_dsl" data-filterable-set=":docs/kotlin_dsl" data-active="" data-toggle=":docs/kotlin_dsl">DSL</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/kotlin_dsl"><div class="symbol monospace"><span class="token keyword"></span><span class="token keyword">val </span><a href="../../org.gradle.plugin.use/-plugin-dependencies-spec/index.html#552792532%2FMain%2F-**********">PluginDependenciesSpec</a><span class="token punctuation">.</span><a href="../assembler-lang.html">assembler-lang</a><span class="token operator">: </span><a href="../../org.gradle.plugin.use/-plugin-dependency-spec/index.html#-**********%2FMain%2F-**********">PluginDependencySpec</a></div><div class="brief "><p class="paragraph">The builtin Gradle plugin implemented by <span data-unresolved-link="org.gradle.language.assembler.plugins/AssemblerLangPlugin///PointingToDeclaration/">org.gradle.language.assembler.plugins.AssemblerLangPlugin</span>.</p></div></div>                  </div>
                </div>
              </div>
            </div>
          </div>
<a data-name="-1509154821%2FProperties%2F-**********" anchor-label="base" id="-1509154821%2FProperties%2F-**********" data-filterable-set=":docs/kotlin_dsl"></a>
          <div class="table-row" data-togglable="EXTENSION_PROPERTY" data-filterable-current=":docs/kotlin_dsl" data-filterable-set=":docs/kotlin_dsl">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="../base.html"><span><span>base</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="-1509154821%2FProperties%2F-**********"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
                    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/kotlin_dsl" data-filterable-set=":docs/kotlin_dsl" data-active="" data-toggle=":docs/kotlin_dsl">DSL</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/kotlin_dsl"><div class="symbol monospace"><span class="token keyword"></span><span class="token keyword">val </span><a href="../../org.gradle.plugin.use/-plugin-dependencies-spec/index.html#552792532%2FMain%2F-**********">PluginDependenciesSpec</a><span class="token punctuation">.</span><a href="../base.html">base</a><span class="token operator">: </span><a href="../../org.gradle.plugin.use/-plugin-dependency-spec/index.html#-**********%2FMain%2F-**********">PluginDependencySpec</a></div><div class="brief "><p class="paragraph">The builtin Gradle plugin implemented by <span data-unresolved-link="org.gradle.api.plugins/BasePlugin///PointingToDeclaration/">org.gradle.api.plugins.BasePlugin</span>.</p></div></div>                  </div>
                </div>
              </div>
            </div>
          </div>
<a data-name="742299497%2FProperties%2F-**********" anchor-label="binary-base" id="742299497%2FProperties%2F-**********" data-filterable-set=":docs/kotlin_dsl"></a>
          <div class="table-row" data-togglable="EXTENSION_PROPERTY" data-filterable-current=":docs/kotlin_dsl" data-filterable-set=":docs/kotlin_dsl">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="../binary-base.html"><span><span>binary-base</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="742299497%2FProperties%2F-**********"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
                    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/kotlin_dsl" data-filterable-set=":docs/kotlin_dsl" data-active="" data-toggle=":docs/kotlin_dsl">DSL</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/kotlin_dsl"><div class="symbol monospace"><span class="token keyword"></span><span class="token keyword">val </span><a href="../../org.gradle.plugin.use/-plugin-dependencies-spec/index.html#552792532%2FMain%2F-**********">PluginDependenciesSpec</a><span class="token punctuation">.</span><a href="../binary-base.html">binary-base</a><span class="token operator">: </span><a href="../../org.gradle.plugin.use/-plugin-dependency-spec/index.html#-**********%2FMain%2F-**********">PluginDependencySpec</a></div><div class="brief "><p class="paragraph">The builtin Gradle plugin implemented by <span data-unresolved-link="org.gradle.platform.base.plugins/BinaryBasePlugin///PointingToDeclaration/">org.gradle.platform.base.plugins.BinaryBasePlugin</span>.</p></div></div>                  </div>
                </div>
              </div>
            </div>
          </div>
<a data-name="358855441%2FProperties%2F-**********" anchor-label="build-dashboard" id="358855441%2FProperties%2F-**********" data-filterable-set=":docs/kotlin_dsl"></a>
          <div class="table-row" data-togglable="EXTENSION_PROPERTY" data-filterable-current=":docs/kotlin_dsl" data-filterable-set=":docs/kotlin_dsl">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="../build-dashboard.html"><span><span>build-dashboard</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="358855441%2FProperties%2F-**********"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
                    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/kotlin_dsl" data-filterable-set=":docs/kotlin_dsl" data-active="" data-toggle=":docs/kotlin_dsl">DSL</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/kotlin_dsl"><div class="symbol monospace"><span class="token keyword"></span><span class="token keyword">val </span><a href="../../org.gradle.plugin.use/-plugin-dependencies-spec/index.html#552792532%2FMain%2F-**********">PluginDependenciesSpec</a><span class="token punctuation">.</span><a href="../build-dashboard.html">build-dashboard</a><span class="token operator">: </span><a href="../../org.gradle.plugin.use/-plugin-dependency-spec/index.html#-**********%2FMain%2F-**********">PluginDependencySpec</a></div><div class="brief "><p class="paragraph">The builtin Gradle plugin implemented by <span data-unresolved-link="org.gradle.api.reporting.plugins/BuildDashboardPlugin///PointingToDeclaration/">org.gradle.api.reporting.plugins.BuildDashboardPlugin</span>.</p></div></div>                  </div>
                </div>
              </div>
            </div>
          </div>
<a data-name="-713963971%2FProperties%2F-**********" anchor-label="build-init" id="-713963971%2FProperties%2F-**********" data-filterable-set=":docs/kotlin_dsl"></a>
          <div class="table-row" data-togglable="EXTENSION_PROPERTY" data-filterable-current=":docs/kotlin_dsl" data-filterable-set=":docs/kotlin_dsl">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="../build-init.html"><span><span>build-init</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="-713963971%2FProperties%2F-**********"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
                    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/kotlin_dsl" data-filterable-set=":docs/kotlin_dsl" data-active="" data-toggle=":docs/kotlin_dsl">DSL</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/kotlin_dsl"><div class="symbol monospace"><span class="token keyword"></span><span class="token keyword">val </span><a href="../../org.gradle.plugin.use/-plugin-dependencies-spec/index.html#552792532%2FMain%2F-**********">PluginDependenciesSpec</a><span class="token punctuation">.</span><a href="../build-init.html">build-init</a><span class="token operator">: </span><a href="../../org.gradle.plugin.use/-plugin-dependency-spec/index.html#-**********%2FMain%2F-**********">PluginDependencySpec</a></div><div class="brief "><p class="paragraph">The builtin Gradle plugin implemented by <span data-unresolved-link="org.gradle.buildinit.plugins/BuildInitPlugin///PointingToDeclaration/">org.gradle.buildinit.plugins.BuildInitPlugin</span>.</p></div></div>                  </div>
                </div>
              </div>
            </div>
          </div>
<a data-name="1081232419%2FProperties%2F-**********" anchor-label="c" id="1081232419%2FProperties%2F-**********" data-filterable-set=":docs/kotlin_dsl"></a>
          <div class="table-row" data-togglable="EXTENSION_PROPERTY" data-filterable-current=":docs/kotlin_dsl" data-filterable-set=":docs/kotlin_dsl">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="../c.html"><span><span>c</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="1081232419%2FProperties%2F-**********"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
                    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/kotlin_dsl" data-filterable-set=":docs/kotlin_dsl" data-active="" data-toggle=":docs/kotlin_dsl">DSL</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/kotlin_dsl"><div class="symbol monospace"><span class="token keyword"></span><span class="token keyword">val </span><a href="../../org.gradle.plugin.use/-plugin-dependencies-spec/index.html#552792532%2FMain%2F-**********">PluginDependenciesSpec</a><span class="token punctuation">.</span><a href="../c.html">c</a><span class="token operator">: </span><a href="../../org.gradle.plugin.use/-plugin-dependency-spec/index.html#-**********%2FMain%2F-**********">PluginDependencySpec</a></div><div class="brief "><p class="paragraph">The builtin Gradle plugin implemented by <span data-unresolved-link="org.gradle.language.c.plugins/CPlugin///PointingToDeclaration/">org.gradle.language.c.plugins.CPlugin</span>.</p></div></div>                  </div>
                </div>
              </div>
            </div>
          </div>
<a data-name="-1248249516%2FProperties%2F-**********" anchor-label="c-lang" id="-1248249516%2FProperties%2F-**********" data-filterable-set=":docs/kotlin_dsl"></a>
          <div class="table-row" data-togglable="EXTENSION_PROPERTY" data-filterable-current=":docs/kotlin_dsl" data-filterable-set=":docs/kotlin_dsl">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="../c-lang.html"><span><span>c-lang</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="-1248249516%2FProperties%2F-**********"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
                    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/kotlin_dsl" data-filterable-set=":docs/kotlin_dsl" data-active="" data-toggle=":docs/kotlin_dsl">DSL</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/kotlin_dsl"><div class="symbol monospace"><span class="token keyword"></span><span class="token keyword">val </span><a href="../../org.gradle.plugin.use/-plugin-dependencies-spec/index.html#552792532%2FMain%2F-**********">PluginDependenciesSpec</a><span class="token punctuation">.</span><a href="../c-lang.html">c-lang</a><span class="token operator">: </span><a href="../../org.gradle.plugin.use/-plugin-dependency-spec/index.html#-**********%2FMain%2F-**********">PluginDependencySpec</a></div><div class="brief "><p class="paragraph">The builtin Gradle plugin implemented by <span data-unresolved-link="org.gradle.language.c.plugins/CLangPlugin///PointingToDeclaration/">org.gradle.language.c.plugins.CLangPlugin</span>.</p></div></div>                  </div>
                </div>
              </div>
            </div>
          </div>
<a data-name="1631346147%2FProperties%2F-**********" anchor-label="checkstyle" id="1631346147%2FProperties%2F-**********" data-filterable-set=":docs/kotlin_dsl"></a>
          <div class="table-row" data-togglable="EXTENSION_PROPERTY" data-filterable-current=":docs/kotlin_dsl" data-filterable-set=":docs/kotlin_dsl">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="../checkstyle.html"><span><span>checkstyle</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="1631346147%2FProperties%2F-**********"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
                    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/kotlin_dsl" data-filterable-set=":docs/kotlin_dsl" data-active="" data-toggle=":docs/kotlin_dsl">DSL</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/kotlin_dsl"><div class="symbol monospace"><span class="token keyword"></span><span class="token keyword">val </span><a href="../../org.gradle.plugin.use/-plugin-dependencies-spec/index.html#552792532%2FMain%2F-**********">PluginDependenciesSpec</a><span class="token punctuation">.</span><a href="../checkstyle.html">checkstyle</a><span class="token operator">: </span><a href="../../org.gradle.plugin.use/-plugin-dependency-spec/index.html#-**********%2FMain%2F-**********">PluginDependencySpec</a></div><div class="brief "><p class="paragraph">The builtin Gradle plugin implemented by <span data-unresolved-link="org.gradle.api.plugins.quality/CheckstylePlugin///PointingToDeclaration/">org.gradle.api.plugins.quality.CheckstylePlugin</span>.</p></div></div>                  </div>
                </div>
              </div>
            </div>
          </div>
<a data-name="-1483067599%2FProperties%2F-**********" anchor-label="clang-compiler" id="-1483067599%2FProperties%2F-**********" data-filterable-set=":docs/kotlin_dsl"></a>
          <div class="table-row" data-togglable="EXTENSION_PROPERTY" data-filterable-current=":docs/kotlin_dsl" data-filterable-set=":docs/kotlin_dsl">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="../clang-compiler.html"><span><span>clang-compiler</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="-1483067599%2FProperties%2F-**********"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
                    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/kotlin_dsl" data-filterable-set=":docs/kotlin_dsl" data-active="" data-toggle=":docs/kotlin_dsl">DSL</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/kotlin_dsl"><div class="symbol monospace"><span class="token keyword"></span><span class="token keyword">val </span><a href="../../org.gradle.plugin.use/-plugin-dependencies-spec/index.html#552792532%2FMain%2F-**********">PluginDependenciesSpec</a><span class="token punctuation">.</span><a href="../clang-compiler.html">clang-compiler</a><span class="token operator">: </span><a href="../../org.gradle.plugin.use/-plugin-dependency-spec/index.html#-**********%2FMain%2F-**********">PluginDependencySpec</a></div><div class="brief "><p class="paragraph">The builtin Gradle plugin implemented by <span data-unresolved-link="org.gradle.nativeplatform.toolchain.plugins/ClangCompilerPlugin///PointingToDeclaration/">org.gradle.nativeplatform.toolchain.plugins.ClangCompilerPlugin</span>.</p></div></div>                  </div>
                </div>
              </div>
            </div>
          </div>
<a data-name="767640251%2FProperties%2F-**********" anchor-label="codenarc" id="767640251%2FProperties%2F-**********" data-filterable-set=":docs/kotlin_dsl"></a>
          <div class="table-row" data-togglable="EXTENSION_PROPERTY" data-filterable-current=":docs/kotlin_dsl" data-filterable-set=":docs/kotlin_dsl">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="../codenarc.html"><span><span>codenarc</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="767640251%2FProperties%2F-**********"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
                    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/kotlin_dsl" data-filterable-set=":docs/kotlin_dsl" data-active="" data-toggle=":docs/kotlin_dsl">DSL</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/kotlin_dsl"><div class="symbol monospace"><span class="token keyword"></span><span class="token keyword">val </span><a href="../../org.gradle.plugin.use/-plugin-dependencies-spec/index.html#552792532%2FMain%2F-**********">PluginDependenciesSpec</a><span class="token punctuation">.</span><a href="../codenarc.html">codenarc</a><span class="token operator">: </span><a href="../../org.gradle.plugin.use/-plugin-dependency-spec/index.html#-**********%2FMain%2F-**********">PluginDependencySpec</a></div><div class="brief "><p class="paragraph">The builtin Gradle plugin implemented by <span data-unresolved-link="org.gradle.api.plugins.quality/CodeNarcPlugin///PointingToDeclaration/">org.gradle.api.plugins.quality.CodeNarcPlugin</span>.</p></div></div>                  </div>
                </div>
              </div>
            </div>
          </div>
<a data-name="-1005608085%2FProperties%2F-**********" anchor-label="component-base" id="-1005608085%2FProperties%2F-**********" data-filterable-set=":docs/kotlin_dsl"></a>
          <div class="table-row" data-togglable="EXTENSION_PROPERTY" data-filterable-current=":docs/kotlin_dsl" data-filterable-set=":docs/kotlin_dsl">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="../component-base.html"><span><span>component-base</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="-1005608085%2FProperties%2F-**********"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
                    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/kotlin_dsl" data-filterable-set=":docs/kotlin_dsl" data-active="" data-toggle=":docs/kotlin_dsl">DSL</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/kotlin_dsl"><div class="symbol monospace"><span class="token keyword"></span><span class="token keyword">val </span><a href="../../org.gradle.plugin.use/-plugin-dependencies-spec/index.html#552792532%2FMain%2F-**********">PluginDependenciesSpec</a><span class="token punctuation">.</span><a href="../component-base.html">component-base</a><span class="token operator">: </span><a href="../../org.gradle.plugin.use/-plugin-dependency-spec/index.html#-**********%2FMain%2F-**********">PluginDependencySpec</a></div><div class="brief "><p class="paragraph">The builtin Gradle plugin implemented by <span data-unresolved-link="org.gradle.platform.base.plugins/ComponentBasePlugin///PointingToDeclaration/">org.gradle.platform.base.plugins.ComponentBasePlugin</span>.</p></div></div>                  </div>
                </div>
              </div>
            </div>
          </div>
<a data-name="422722439%2FProperties%2F-**********" anchor-label="component-model-base" id="422722439%2FProperties%2F-**********" data-filterable-set=":docs/kotlin_dsl"></a>
          <div class="table-row" data-togglable="EXTENSION_PROPERTY" data-filterable-current=":docs/kotlin_dsl" data-filterable-set=":docs/kotlin_dsl">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="../component-model-base.html"><span><span>component-model-base</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="422722439%2FProperties%2F-**********"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
                    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/kotlin_dsl" data-filterable-set=":docs/kotlin_dsl" data-active="" data-toggle=":docs/kotlin_dsl">DSL</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/kotlin_dsl"><div class="symbol monospace"><span class="token keyword"></span><span class="token keyword">val </span><a href="../../org.gradle.plugin.use/-plugin-dependencies-spec/index.html#552792532%2FMain%2F-**********">PluginDependenciesSpec</a><span class="token punctuation">.</span><a href="../component-model-base.html">component-model-base</a><span class="token operator">: </span><a href="../../org.gradle.plugin.use/-plugin-dependency-spec/index.html#-**********%2FMain%2F-**********">PluginDependencySpec</a></div><div class="brief "><p class="paragraph">The builtin Gradle plugin implemented by <span data-unresolved-link="org.gradle.language.base.plugins/ComponentModelBasePlugin///PointingToDeclaration/">org.gradle.language.base.plugins.ComponentModelBasePlugin</span>.</p></div></div>                  </div>
                </div>
              </div>
            </div>
          </div>
<a data-name="-1155376221%2FProperties%2F-**********" anchor-label="cpp" id="-1155376221%2FProperties%2F-**********" data-filterable-set=":docs/kotlin_dsl"></a>
          <div class="table-row" data-togglable="EXTENSION_PROPERTY" data-filterable-current=":docs/kotlin_dsl" data-filterable-set=":docs/kotlin_dsl">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="../cpp.html"><span><span>cpp</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="-1155376221%2FProperties%2F-**********"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
                    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/kotlin_dsl" data-filterable-set=":docs/kotlin_dsl" data-active="" data-toggle=":docs/kotlin_dsl">DSL</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/kotlin_dsl"><div class="symbol monospace"><span class="token keyword"></span><span class="token keyword">val </span><a href="../../org.gradle.plugin.use/-plugin-dependencies-spec/index.html#552792532%2FMain%2F-**********">PluginDependenciesSpec</a><span class="token punctuation">.</span><a href="../cpp.html">cpp</a><span class="token operator">: </span><a href="../../org.gradle.plugin.use/-plugin-dependency-spec/index.html#-**********%2FMain%2F-**********">PluginDependencySpec</a></div><div class="brief "><p class="paragraph">The builtin Gradle plugin implemented by <span data-unresolved-link="org.gradle.language.cpp.plugins/CppPlugin///PointingToDeclaration/">org.gradle.language.cpp.plugins.CppPlugin</span>.</p></div></div>                  </div>
                </div>
              </div>
            </div>
          </div>
<a data-name="1859131264%2FProperties%2F-**********" anchor-label="cpp-application" id="1859131264%2FProperties%2F-**********" data-filterable-set=":docs/kotlin_dsl"></a>
          <div class="table-row" data-togglable="EXTENSION_PROPERTY" data-filterable-current=":docs/kotlin_dsl" data-filterable-set=":docs/kotlin_dsl">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="../cpp-application.html"><span><span>cpp-application</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="1859131264%2FProperties%2F-**********"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
                    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/kotlin_dsl" data-filterable-set=":docs/kotlin_dsl" data-active="" data-toggle=":docs/kotlin_dsl">DSL</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/kotlin_dsl"><div class="symbol monospace"><span class="token keyword"></span><span class="token keyword">val </span><a href="../../org.gradle.plugin.use/-plugin-dependencies-spec/index.html#552792532%2FMain%2F-**********">PluginDependenciesSpec</a><span class="token punctuation">.</span><a href="../cpp-application.html">cpp-application</a><span class="token operator">: </span><a href="../../org.gradle.plugin.use/-plugin-dependency-spec/index.html#-**********%2FMain%2F-**********">PluginDependencySpec</a></div><div class="brief "><p class="paragraph">The builtin Gradle plugin implemented by <span data-unresolved-link="org.gradle.language.cpp.plugins/CppApplicationPlugin///PointingToDeclaration/">org.gradle.language.cpp.plugins.CppApplicationPlugin</span>.</p></div></div>                  </div>
                </div>
              </div>
            </div>
          </div>
<a data-name="-603530796%2FProperties%2F-**********" anchor-label="cpp-lang" id="-603530796%2FProperties%2F-**********" data-filterable-set=":docs/kotlin_dsl"></a>
          <div class="table-row" data-togglable="EXTENSION_PROPERTY" data-filterable-current=":docs/kotlin_dsl" data-filterable-set=":docs/kotlin_dsl">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="../cpp-lang.html"><span><span>cpp-lang</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="-603530796%2FProperties%2F-**********"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
                    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/kotlin_dsl" data-filterable-set=":docs/kotlin_dsl" data-active="" data-toggle=":docs/kotlin_dsl">DSL</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/kotlin_dsl"><div class="symbol monospace"><span class="token keyword"></span><span class="token keyword">val </span><a href="../../org.gradle.plugin.use/-plugin-dependencies-spec/index.html#552792532%2FMain%2F-**********">PluginDependenciesSpec</a><span class="token punctuation">.</span><a href="../cpp-lang.html">cpp-lang</a><span class="token operator">: </span><a href="../../org.gradle.plugin.use/-plugin-dependency-spec/index.html#-**********%2FMain%2F-**********">PluginDependencySpec</a></div><div class="brief "><p class="paragraph">The builtin Gradle plugin implemented by <span data-unresolved-link="org.gradle.language.cpp.plugins/CppLangPlugin///PointingToDeclaration/">org.gradle.language.cpp.plugins.CppLangPlugin</span>.</p></div></div>                  </div>
                </div>
              </div>
            </div>
          </div>
<a data-name="-177127819%2FProperties%2F-**********" anchor-label="cpp-library" id="-177127819%2FProperties%2F-**********" data-filterable-set=":docs/kotlin_dsl"></a>
          <div class="table-row" data-togglable="EXTENSION_PROPERTY" data-filterable-current=":docs/kotlin_dsl" data-filterable-set=":docs/kotlin_dsl">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="../cpp-library.html"><span><span>cpp-library</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="-177127819%2FProperties%2F-**********"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
                    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/kotlin_dsl" data-filterable-set=":docs/kotlin_dsl" data-active="" data-toggle=":docs/kotlin_dsl">DSL</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/kotlin_dsl"><div class="symbol monospace"><span class="token keyword"></span><span class="token keyword">val </span><a href="../../org.gradle.plugin.use/-plugin-dependencies-spec/index.html#552792532%2FMain%2F-**********">PluginDependenciesSpec</a><span class="token punctuation">.</span><a href="../cpp-library.html">cpp-library</a><span class="token operator">: </span><a href="../../org.gradle.plugin.use/-plugin-dependency-spec/index.html#-**********%2FMain%2F-**********">PluginDependencySpec</a></div><div class="brief "><p class="paragraph">The builtin Gradle plugin implemented by <span data-unresolved-link="org.gradle.language.cpp.plugins/CppLibraryPlugin///PointingToDeclaration/">org.gradle.language.cpp.plugins.CppLibraryPlugin</span>.</p></div></div>                  </div>
                </div>
              </div>
            </div>
          </div>
<a data-name="-711681483%2FProperties%2F-**********" anchor-label="cpp-unit-test" id="-711681483%2FProperties%2F-**********" data-filterable-set=":docs/kotlin_dsl"></a>
          <div class="table-row" data-togglable="EXTENSION_PROPERTY" data-filterable-current=":docs/kotlin_dsl" data-filterable-set=":docs/kotlin_dsl">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="../cpp-unit-test.html"><span><span>cpp-unit-test</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="-711681483%2FProperties%2F-**********"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
                    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/kotlin_dsl" data-filterable-set=":docs/kotlin_dsl" data-active="" data-toggle=":docs/kotlin_dsl">DSL</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/kotlin_dsl"><div class="symbol monospace"><span class="token keyword"></span><span class="token keyword">val </span><a href="../../org.gradle.plugin.use/-plugin-dependencies-spec/index.html#552792532%2FMain%2F-**********">PluginDependenciesSpec</a><span class="token punctuation">.</span><a href="../cpp-unit-test.html">cpp-unit-test</a><span class="token operator">: </span><a href="../../org.gradle.plugin.use/-plugin-dependency-spec/index.html#-**********%2FMain%2F-**********">PluginDependencySpec</a></div><div class="brief "><p class="paragraph">The builtin Gradle plugin implemented by <span data-unresolved-link="org.gradle.nativeplatform.test.cpp.plugins/CppUnitTestPlugin///PointingToDeclaration/">org.gradle.nativeplatform.test.cpp.plugins.CppUnitTestPlugin</span>.</p></div></div>                  </div>
                </div>
              </div>
            </div>
          </div>
<a data-name="-704495585%2FProperties%2F-**********" anchor-label="cunit" id="-704495585%2FProperties%2F-**********" data-filterable-set=":docs/kotlin_dsl"></a>
          <div class="table-row" data-togglable="EXTENSION_PROPERTY" data-filterable-current=":docs/kotlin_dsl" data-filterable-set=":docs/kotlin_dsl">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="../cunit.html"><span><span>cunit</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="-704495585%2FProperties%2F-**********"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
                    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/kotlin_dsl" data-filterable-set=":docs/kotlin_dsl" data-active="" data-toggle=":docs/kotlin_dsl">DSL</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/kotlin_dsl"><div class="symbol monospace"><span class="token keyword"></span><span class="token keyword">val </span><a href="../../org.gradle.plugin.use/-plugin-dependencies-spec/index.html#552792532%2FMain%2F-**********">PluginDependenciesSpec</a><span class="token punctuation">.</span><a href="../cunit.html">cunit</a><span class="token operator">: </span><a href="../../org.gradle.plugin.use/-plugin-dependency-spec/index.html#-**********%2FMain%2F-**********">PluginDependencySpec</a></div><div class="brief "><p class="paragraph">The builtin Gradle plugin implemented by <span data-unresolved-link="org.gradle.nativeplatform.test.cunit.plugins/CUnitConventionPlugin///PointingToDeclaration/">org.gradle.nativeplatform.test.cunit.plugins.CUnitConventionPlugin</span>.</p></div></div>                  </div>
                </div>
              </div>
            </div>
          </div>
<a data-name="-1642109335%2FProperties%2F-**********" anchor-label="cunit-test-suite" id="-1642109335%2FProperties%2F-**********" data-filterable-set=":docs/kotlin_dsl"></a>
          <div class="table-row" data-togglable="EXTENSION_PROPERTY" data-filterable-current=":docs/kotlin_dsl" data-filterable-set=":docs/kotlin_dsl">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="../cunit-test-suite.html"><span><span>cunit-test-suite</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="-1642109335%2FProperties%2F-**********"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
                    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/kotlin_dsl" data-filterable-set=":docs/kotlin_dsl" data-active="" data-toggle=":docs/kotlin_dsl">DSL</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/kotlin_dsl"><div class="symbol monospace"><span class="token keyword"></span><span class="token keyword">val </span><a href="../../org.gradle.plugin.use/-plugin-dependencies-spec/index.html#552792532%2FMain%2F-**********">PluginDependenciesSpec</a><span class="token punctuation">.</span><a href="../cunit-test-suite.html">cunit-test-suite</a><span class="token operator">: </span><a href="../../org.gradle.plugin.use/-plugin-dependency-spec/index.html#-**********%2FMain%2F-**********">PluginDependencySpec</a></div><div class="brief "><p class="paragraph">The builtin Gradle plugin implemented by <span data-unresolved-link="org.gradle.nativeplatform.test.cunit.plugins/CUnitPlugin///PointingToDeclaration/">org.gradle.nativeplatform.test.cunit.plugins.CUnitPlugin</span>.</p></div></div>                  </div>
                </div>
              </div>
            </div>
          </div>
<a data-name="-288955096%2FProperties%2F-**********" anchor-label="distribution" id="-288955096%2FProperties%2F-**********" data-filterable-set=":docs/kotlin_dsl"></a>
          <div class="table-row" data-togglable="EXTENSION_PROPERTY" data-filterable-current=":docs/kotlin_dsl" data-filterable-set=":docs/kotlin_dsl">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="../distribution.html"><span><span>distribution</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="-288955096%2FProperties%2F-**********"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
                    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/kotlin_dsl" data-filterable-set=":docs/kotlin_dsl" data-active="" data-toggle=":docs/kotlin_dsl">DSL</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/kotlin_dsl"><div class="symbol monospace"><span class="token keyword"></span><span class="token keyword">val </span><a href="../../org.gradle.plugin.use/-plugin-dependencies-spec/index.html#552792532%2FMain%2F-**********">PluginDependenciesSpec</a><span class="token punctuation">.</span><a href="../distribution.html">distribution</a><span class="token operator">: </span><a href="../../org.gradle.plugin.use/-plugin-dependency-spec/index.html#-**********%2FMain%2F-**********">PluginDependencySpec</a></div><div class="brief "><p class="paragraph">The builtin Gradle plugin implemented by <span data-unresolved-link="org.gradle.api.distribution.plugins/DistributionPlugin///PointingToDeclaration/">org.gradle.api.distribution.plugins.DistributionPlugin</span>.</p></div></div>                  </div>
                </div>
              </div>
            </div>
          </div>
<a data-name="-504822320%2FProperties%2F-**********" anchor-label="ear" id="-504822320%2FProperties%2F-**********" data-filterable-set=":docs/kotlin_dsl"></a>
          <div class="table-row" data-togglable="EXTENSION_PROPERTY" data-filterable-current=":docs/kotlin_dsl" data-filterable-set=":docs/kotlin_dsl">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="../ear.html"><span><span>ear</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="-504822320%2FProperties%2F-**********"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
                    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/kotlin_dsl" data-filterable-set=":docs/kotlin_dsl" data-active="" data-toggle=":docs/kotlin_dsl">DSL</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/kotlin_dsl"><div class="symbol monospace"><span class="token keyword"></span><span class="token keyword">val </span><a href="../../org.gradle.plugin.use/-plugin-dependencies-spec/index.html#552792532%2FMain%2F-**********">PluginDependenciesSpec</a><span class="token punctuation">.</span><a href="../ear.html">ear</a><span class="token operator">: </span><a href="../../org.gradle.plugin.use/-plugin-dependency-spec/index.html#-**********%2FMain%2F-**********">PluginDependencySpec</a></div><div class="brief "><p class="paragraph">The builtin Gradle plugin implemented by <span data-unresolved-link="org.gradle.plugins.ear/EarPlugin///PointingToDeclaration/">org.gradle.plugins.ear.EarPlugin</span>.</p></div></div>                  </div>
                </div>
              </div>
            </div>
          </div>
<a data-name="138856191%2FProperties%2F-**********" anchor-label="eclipse" id="138856191%2FProperties%2F-**********" data-filterable-set=":docs/kotlin_dsl"></a>
          <div class="table-row" data-togglable="EXTENSION_PROPERTY" data-filterable-current=":docs/kotlin_dsl" data-filterable-set=":docs/kotlin_dsl">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="../eclipse.html"><span><span>eclipse</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="138856191%2FProperties%2F-**********"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
                    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/kotlin_dsl" data-filterable-set=":docs/kotlin_dsl" data-active="" data-toggle=":docs/kotlin_dsl">DSL</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/kotlin_dsl"><div class="symbol monospace"><span class="token keyword"></span><span class="token keyword">val </span><a href="../../org.gradle.plugin.use/-plugin-dependencies-spec/index.html#552792532%2FMain%2F-**********">PluginDependenciesSpec</a><span class="token punctuation">.</span><a href="../eclipse.html">eclipse</a><span class="token operator">: </span><a href="../../org.gradle.plugin.use/-plugin-dependency-spec/index.html#-**********%2FMain%2F-**********">PluginDependencySpec</a></div><div class="brief "><p class="paragraph">The builtin Gradle plugin implemented by <span data-unresolved-link="org.gradle.plugins.ide.eclipse/EclipsePlugin///PointingToDeclaration/">org.gradle.plugins.ide.eclipse.EclipsePlugin</span>.</p></div></div>                  </div>
                </div>
              </div>
            </div>
          </div>
<a data-name="-808680999%2FProperties%2F-**********" anchor-label="eclipse-wtp" id="-808680999%2FProperties%2F-**********" data-filterable-set=":docs/kotlin_dsl"></a>
          <div class="table-row" data-togglable="EXTENSION_PROPERTY" data-filterable-current=":docs/kotlin_dsl" data-filterable-set=":docs/kotlin_dsl">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="../eclipse-wtp.html"><span><span>eclipse-wtp</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="-808680999%2FProperties%2F-**********"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
                    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/kotlin_dsl" data-filterable-set=":docs/kotlin_dsl" data-active="" data-toggle=":docs/kotlin_dsl">DSL</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/kotlin_dsl"><div class="symbol monospace"><span class="token keyword"></span><span class="token keyword">val </span><a href="../../org.gradle.plugin.use/-plugin-dependencies-spec/index.html#552792532%2FMain%2F-**********">PluginDependenciesSpec</a><span class="token punctuation">.</span><a href="../eclipse-wtp.html">eclipse-wtp</a><span class="token operator">: </span><a href="../../org.gradle.plugin.use/-plugin-dependency-spec/index.html#-**********%2FMain%2F-**********">PluginDependencySpec</a></div><div class="brief "><p class="paragraph">The builtin Gradle plugin implemented by <span data-unresolved-link="org.gradle.plugins.ide.eclipse/EclipseWtpPlugin///PointingToDeclaration/">org.gradle.plugins.ide.eclipse.EclipseWtpPlugin</span>.</p></div></div>                  </div>
                </div>
              </div>
            </div>
          </div>
<a data-name="-2052478782%2FProperties%2F-**********" anchor-label="embedded-kotlin" id="-2052478782%2FProperties%2F-**********" data-filterable-set=":docs/kotlin_dsl"></a>
          <div class="table-row" data-togglable="EXTENSION_PROPERTY" data-filterable-current=":docs/kotlin_dsl" data-filterable-set=":docs/kotlin_dsl">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="../embedded-kotlin.html"><span><span>embedded-kotlin</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="-2052478782%2FProperties%2F-**********"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
                    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/kotlin_dsl" data-filterable-set=":docs/kotlin_dsl" data-active="" data-toggle=":docs/kotlin_dsl">DSL</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/kotlin_dsl"><div class="symbol monospace"><span class="token keyword"></span><span class="token keyword">val </span><a href="../../org.gradle.plugin.use/-plugin-dependencies-spec/index.html#552792532%2FMain%2F-**********">PluginDependenciesSpec</a><span class="token punctuation">.</span><a href="../embedded-kotlin.html">embedded-kotlin</a><span class="token operator">: </span><a href="../../org.gradle.plugin.use/-plugin-dependency-spec/index.html#-**********%2FMain%2F-**********">PluginDependencySpec</a></div><div class="brief "><p class="paragraph">The <code class="lang-kotlin">embedded-kotlin</code> plugin.</p></div></div>                  </div>
                </div>
              </div>
            </div>
          </div>
<a data-name="745869223%2FProperties%2F-**********" anchor-label="gcc-compiler" id="745869223%2FProperties%2F-**********" data-filterable-set=":docs/kotlin_dsl"></a>
          <div class="table-row" data-togglable="EXTENSION_PROPERTY" data-filterable-current=":docs/kotlin_dsl" data-filterable-set=":docs/kotlin_dsl">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="../gcc-compiler.html"><span><span>gcc-compiler</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="745869223%2FProperties%2F-**********"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
                    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/kotlin_dsl" data-filterable-set=":docs/kotlin_dsl" data-active="" data-toggle=":docs/kotlin_dsl">DSL</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/kotlin_dsl"><div class="symbol monospace"><span class="token keyword"></span><span class="token keyword">val </span><a href="../../org.gradle.plugin.use/-plugin-dependencies-spec/index.html#552792532%2FMain%2F-**********">PluginDependenciesSpec</a><span class="token punctuation">.</span><a href="../gcc-compiler.html">gcc-compiler</a><span class="token operator">: </span><a href="../../org.gradle.plugin.use/-plugin-dependency-spec/index.html#-**********%2FMain%2F-**********">PluginDependencySpec</a></div><div class="brief "><p class="paragraph">The builtin Gradle plugin implemented by <span data-unresolved-link="org.gradle.nativeplatform.toolchain.plugins/GccCompilerPlugin///PointingToDeclaration/">org.gradle.nativeplatform.toolchain.plugins.GccCompilerPlugin</span>.</p></div></div>                  </div>
                </div>
              </div>
            </div>
          </div>
<a data-name="-1848803712%2FProperties%2F-**********" anchor-label="google-test" id="-1848803712%2FProperties%2F-**********" data-filterable-set=":docs/kotlin_dsl"></a>
          <div class="table-row" data-togglable="EXTENSION_PROPERTY" data-filterable-current=":docs/kotlin_dsl" data-filterable-set=":docs/kotlin_dsl">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="../google-test.html"><span><span>google-test</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="-1848803712%2FProperties%2F-**********"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
                    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/kotlin_dsl" data-filterable-set=":docs/kotlin_dsl" data-active="" data-toggle=":docs/kotlin_dsl">DSL</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/kotlin_dsl"><div class="symbol monospace"><span class="token keyword"></span><span class="token keyword">val </span><a href="../../org.gradle.plugin.use/-plugin-dependencies-spec/index.html#552792532%2FMain%2F-**********">PluginDependenciesSpec</a><span class="token punctuation">.</span><a href="../google-test.html">google-test</a><span class="token operator">: </span><a href="../../org.gradle.plugin.use/-plugin-dependency-spec/index.html#-**********%2FMain%2F-**********">PluginDependencySpec</a></div><div class="brief "><p class="paragraph">The builtin Gradle plugin implemented by <span data-unresolved-link="org.gradle.nativeplatform.test.googletest.plugins/GoogleTestConventionPlugin///PointingToDeclaration/">org.gradle.nativeplatform.test.googletest.plugins.GoogleTestConventionPlugin</span>.</p></div></div>                  </div>
                </div>
              </div>
            </div>
          </div>
<a data-name="366023016%2FProperties%2F-**********" anchor-label="google-test-test-suite" id="366023016%2FProperties%2F-**********" data-filterable-set=":docs/kotlin_dsl"></a>
          <div class="table-row" data-togglable="EXTENSION_PROPERTY" data-filterable-current=":docs/kotlin_dsl" data-filterable-set=":docs/kotlin_dsl">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="../google-test-test-suite.html"><span><span>google-test-test-suite</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="366023016%2FProperties%2F-**********"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
                    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/kotlin_dsl" data-filterable-set=":docs/kotlin_dsl" data-active="" data-toggle=":docs/kotlin_dsl">DSL</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/kotlin_dsl"><div class="symbol monospace"><span class="token keyword"></span><span class="token keyword">val </span><a href="../../org.gradle.plugin.use/-plugin-dependencies-spec/index.html#552792532%2FMain%2F-**********">PluginDependenciesSpec</a><span class="token punctuation">.</span><a href="../google-test-test-suite.html">google-test-test-suite</a><span class="token operator">: </span><a href="../../org.gradle.plugin.use/-plugin-dependency-spec/index.html#-**********%2FMain%2F-**********">PluginDependencySpec</a></div><div class="brief "><p class="paragraph">The builtin Gradle plugin implemented by <span data-unresolved-link="org.gradle.nativeplatform.test.googletest.plugins/GoogleTestPlugin///PointingToDeclaration/">org.gradle.nativeplatform.test.googletest.plugins.GoogleTestPlugin</span>.</p></div></div>                  </div>
                </div>
              </div>
            </div>
          </div>
<a data-name="221658207%2FProperties%2F-**********" anchor-label="gradle-enterprise" id="221658207%2FProperties%2F-**********" data-filterable-set=":docs/kotlin_dsl"></a>
          <div class="table-row" data-togglable="EXTENSION_PROPERTY" data-filterable-current=":docs/kotlin_dsl" data-filterable-set=":docs/kotlin_dsl">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="../gradle-enterprise.html"><span><span>gradle-enterprise</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="221658207%2FProperties%2F-**********"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
                    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/kotlin_dsl" data-filterable-set=":docs/kotlin_dsl" data-active="" data-toggle=":docs/kotlin_dsl">DSL</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/kotlin_dsl"><div class="symbol monospace"><span class="token keyword"></span><span class="token keyword">val </span><a href="../../org.gradle.plugin.use/-plugin-dependencies-spec/index.html#552792532%2FMain%2F-**********">PluginDependenciesSpec</a><span class="token punctuation">.</span><a href="../gradle-enterprise.html">gradle-enterprise</a><span class="token operator">: </span><a href="../../org.gradle.plugin.use/-plugin-dependency-spec/index.html#-**********%2FMain%2F-**********">PluginDependencySpec</a></div><div class="brief "><p class="paragraph">The <code class="lang-kotlin">gradle-enterprise</code> plugin.</p></div></div>                  </div>
                </div>
              </div>
            </div>
          </div>
<a data-name="857933854%2FProperties%2F-**********" anchor-label="groovy" id="857933854%2FProperties%2F-**********" data-filterable-set=":docs/kotlin_dsl"></a>
          <div class="table-row" data-togglable="EXTENSION_PROPERTY" data-filterable-current=":docs/kotlin_dsl" data-filterable-set=":docs/kotlin_dsl">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="../groovy.html"><span><span>groovy</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="857933854%2FProperties%2F-**********"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
                    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/kotlin_dsl" data-filterable-set=":docs/kotlin_dsl" data-active="" data-toggle=":docs/kotlin_dsl">DSL</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/kotlin_dsl"><div class="symbol monospace"><span class="token keyword"></span><span class="token keyword">val </span><a href="../../org.gradle.plugin.use/-plugin-dependencies-spec/index.html#552792532%2FMain%2F-**********">PluginDependenciesSpec</a><span class="token punctuation">.</span><a href="../groovy.html">groovy</a><span class="token operator">: </span><a href="../../org.gradle.plugin.use/-plugin-dependency-spec/index.html#-**********%2FMain%2F-**********">PluginDependencySpec</a></div><div class="brief "><p class="paragraph">The builtin Gradle plugin implemented by <span data-unresolved-link="org.gradle.api.plugins/GroovyPlugin///PointingToDeclaration/">org.gradle.api.plugins.GroovyPlugin</span>.</p></div></div>                  </div>
                </div>
              </div>
            </div>
          </div>
<a data-name="-283608266%2FProperties%2F-**********" anchor-label="groovy-base" id="-283608266%2FProperties%2F-**********" data-filterable-set=":docs/kotlin_dsl"></a>
          <div class="table-row" data-togglable="EXTENSION_PROPERTY" data-filterable-current=":docs/kotlin_dsl" data-filterable-set=":docs/kotlin_dsl">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="../groovy-base.html"><span><span>groovy-base</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="-283608266%2FProperties%2F-**********"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
                    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/kotlin_dsl" data-filterable-set=":docs/kotlin_dsl" data-active="" data-toggle=":docs/kotlin_dsl">DSL</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/kotlin_dsl"><div class="symbol monospace"><span class="token keyword"></span><span class="token keyword">val </span><a href="../../org.gradle.plugin.use/-plugin-dependencies-spec/index.html#552792532%2FMain%2F-**********">PluginDependenciesSpec</a><span class="token punctuation">.</span><a href="../groovy-base.html">groovy-base</a><span class="token operator">: </span><a href="../../org.gradle.plugin.use/-plugin-dependency-spec/index.html#-**********%2FMain%2F-**********">PluginDependencySpec</a></div><div class="brief "><p class="paragraph">The builtin Gradle plugin implemented by <span data-unresolved-link="org.gradle.api.plugins/GroovyBasePlugin///PointingToDeclaration/">org.gradle.api.plugins.GroovyBasePlugin</span>.</p></div></div>                  </div>
                </div>
              </div>
            </div>
          </div>
<a data-name="1248552562%2FProperties%2F-**********" anchor-label="groovy-gradle-plugin" id="1248552562%2FProperties%2F-**********" data-filterable-set=":docs/kotlin_dsl"></a>
          <div class="table-row" data-togglable="EXTENSION_PROPERTY" data-filterable-current=":docs/kotlin_dsl" data-filterable-set=":docs/kotlin_dsl">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="../groovy-gradle-plugin.html"><span><span>groovy-gradle-plugin</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="1248552562%2FProperties%2F-**********"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
                    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/kotlin_dsl" data-filterable-set=":docs/kotlin_dsl" data-active="" data-toggle=":docs/kotlin_dsl">DSL</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/kotlin_dsl"><div class="symbol monospace"><span class="token keyword"></span><span class="token keyword">val </span><a href="../../org.gradle.plugin.use/-plugin-dependencies-spec/index.html#552792532%2FMain%2F-**********">PluginDependenciesSpec</a><span class="token punctuation">.</span><a href="../groovy-gradle-plugin.html">groovy-gradle-plugin</a><span class="token operator">: </span><a href="../../org.gradle.plugin.use/-plugin-dependency-spec/index.html#-**********%2FMain%2F-**********">PluginDependencySpec</a></div><div class="brief "><p class="paragraph">The builtin Gradle plugin implemented by <span data-unresolved-link="org.gradle.plugin.devel.internal.precompiled/PrecompiledGroovyPluginsPlugin///PointingToDeclaration/">org.gradle.plugin.devel.internal.precompiled.PrecompiledGroovyPluginsPlugin</span>.</p></div></div>                  </div>
                </div>
              </div>
            </div>
          </div>
<a data-name="-1768855254%2FProperties%2F-**********" anchor-label="help-tasks" id="-1768855254%2FProperties%2F-**********" data-filterable-set=":docs/kotlin_dsl"></a>
          <div class="table-row" data-togglable="EXTENSION_PROPERTY" data-filterable-current=":docs/kotlin_dsl" data-filterable-set=":docs/kotlin_dsl">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="../help-tasks.html"><span><span>help-tasks</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="-1768855254%2FProperties%2F-**********"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
                    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/kotlin_dsl" data-filterable-set=":docs/kotlin_dsl" data-active="" data-toggle=":docs/kotlin_dsl">DSL</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/kotlin_dsl"><div class="symbol monospace"><span class="token keyword"></span><span class="token keyword">val </span><a href="../../org.gradle.plugin.use/-plugin-dependencies-spec/index.html#552792532%2FMain%2F-**********">PluginDependenciesSpec</a><span class="token punctuation">.</span><a href="../help-tasks.html">help-tasks</a><span class="token operator">: </span><a href="../../org.gradle.plugin.use/-plugin-dependency-spec/index.html#-**********%2FMain%2F-**********">PluginDependencySpec</a></div><div class="brief "><p class="paragraph">The builtin Gradle plugin implemented by <span data-unresolved-link="org.gradle.api.plugins/HelpTasksPlugin///PointingToDeclaration/">org.gradle.api.plugins.HelpTasksPlugin</span>.</p></div></div>                  </div>
                </div>
              </div>
            </div>
          </div>
<a data-name="1314537365%2FProperties%2F-**********" anchor-label="idea" id="1314537365%2FProperties%2F-**********" data-filterable-set=":docs/kotlin_dsl"></a>
          <div class="table-row" data-togglable="EXTENSION_PROPERTY" data-filterable-current=":docs/kotlin_dsl" data-filterable-set=":docs/kotlin_dsl">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="../idea.html"><span><span>idea</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="1314537365%2FProperties%2F-**********"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
                    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/kotlin_dsl" data-filterable-set=":docs/kotlin_dsl" data-active="" data-toggle=":docs/kotlin_dsl">DSL</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/kotlin_dsl"><div class="symbol monospace"><span class="token keyword"></span><span class="token keyword">val </span><a href="../../org.gradle.plugin.use/-plugin-dependencies-spec/index.html#552792532%2FMain%2F-**********">PluginDependenciesSpec</a><span class="token punctuation">.</span><a href="../idea.html">idea</a><span class="token operator">: </span><a href="../../org.gradle.plugin.use/-plugin-dependency-spec/index.html#-**********%2FMain%2F-**********">PluginDependencySpec</a></div><div class="brief "><p class="paragraph">The builtin Gradle plugin implemented by <span data-unresolved-link="org.gradle.plugins.ide.idea/IdeaPlugin///PointingToDeclaration/">org.gradle.plugins.ide.idea.IdeaPlugin</span>.</p></div></div>                  </div>
                </div>
              </div>
            </div>
          </div>
<a data-name="771820024%2FProperties%2F-**********" anchor-label="ivy-publish" id="771820024%2FProperties%2F-**********" data-filterable-set=":docs/kotlin_dsl"></a>
          <div class="table-row" data-togglable="EXTENSION_PROPERTY" data-filterable-current=":docs/kotlin_dsl" data-filterable-set=":docs/kotlin_dsl">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="../ivy-publish.html"><span><span>ivy-publish</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="771820024%2FProperties%2F-**********"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
                    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/kotlin_dsl" data-filterable-set=":docs/kotlin_dsl" data-active="" data-toggle=":docs/kotlin_dsl">DSL</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/kotlin_dsl"><div class="symbol monospace"><span class="token keyword"></span><span class="token keyword">val </span><a href="../../org.gradle.plugin.use/-plugin-dependencies-spec/index.html#552792532%2FMain%2F-**********">PluginDependenciesSpec</a><span class="token punctuation">.</span><a href="../ivy-publish.html">ivy-publish</a><span class="token operator">: </span><a href="../../org.gradle.plugin.use/-plugin-dependency-spec/index.html#-**********%2FMain%2F-**********">PluginDependencySpec</a></div><div class="brief "><p class="paragraph">The builtin Gradle plugin implemented by <span data-unresolved-link="org.gradle.api.publish.ivy.plugins/IvyPublishPlugin///PointingToDeclaration/">org.gradle.api.publish.ivy.plugins.IvyPublishPlugin</span>.</p></div></div>                  </div>
                </div>
              </div>
            </div>
          </div>
<a data-name="-1558440643%2FProperties%2F-**********" anchor-label="jacoco" id="-1558440643%2FProperties%2F-**********" data-filterable-set=":docs/kotlin_dsl"></a>
          <div class="table-row" data-togglable="EXTENSION_PROPERTY" data-filterable-current=":docs/kotlin_dsl" data-filterable-set=":docs/kotlin_dsl">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="../jacoco.html"><span><span>jacoco</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="-1558440643%2FProperties%2F-**********"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
                    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/kotlin_dsl" data-filterable-set=":docs/kotlin_dsl" data-active="" data-toggle=":docs/kotlin_dsl">DSL</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/kotlin_dsl"><div class="symbol monospace"><span class="token keyword"></span><span class="token keyword">val </span><a href="../../org.gradle.plugin.use/-plugin-dependencies-spec/index.html#552792532%2FMain%2F-**********">PluginDependenciesSpec</a><span class="token punctuation">.</span><a href="../jacoco.html">jacoco</a><span class="token operator">: </span><a href="../../org.gradle.plugin.use/-plugin-dependency-spec/index.html#-**********%2FMain%2F-**********">PluginDependencySpec</a></div><div class="brief "><p class="paragraph">The builtin Gradle plugin implemented by <span data-unresolved-link="org.gradle.testing.jacoco.plugins/JacocoPlugin///PointingToDeclaration/">org.gradle.testing.jacoco.plugins.JacocoPlugin</span>.</p></div></div>                  </div>
                </div>
              </div>
            </div>
          </div>
<a data-name="-918377153%2FProperties%2F-**********" anchor-label="jacoco-report-aggregation" id="-918377153%2FProperties%2F-**********" data-filterable-set=":docs/kotlin_dsl"></a>
          <div class="table-row" data-togglable="EXTENSION_PROPERTY" data-filterable-current=":docs/kotlin_dsl" data-filterable-set=":docs/kotlin_dsl">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="../jacoco-report-aggregation.html"><span><span>jacoco-report-aggregation</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="-918377153%2FProperties%2F-**********"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
                    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/kotlin_dsl" data-filterable-set=":docs/kotlin_dsl" data-active="" data-toggle=":docs/kotlin_dsl">DSL</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/kotlin_dsl"><div class="symbol monospace"><span class="token keyword"></span><span class="token keyword">val </span><a href="../../org.gradle.plugin.use/-plugin-dependencies-spec/index.html#552792532%2FMain%2F-**********">PluginDependenciesSpec</a><span class="token punctuation">.</span><a href="../jacoco-report-aggregation.html">jacoco-report-aggregation</a><span class="token operator">: </span><a href="../../org.gradle.plugin.use/-plugin-dependency-spec/index.html#-**********%2FMain%2F-**********">PluginDependencySpec</a></div><div class="brief "><p class="paragraph">The builtin Gradle plugin implemented by <span data-unresolved-link="org.gradle.testing.jacoco.plugins/JacocoReportAggregationPlugin///PointingToDeclaration/">org.gradle.testing.jacoco.plugins.JacocoReportAggregationPlugin</span>.</p></div></div>                  </div>
                </div>
              </div>
            </div>
          </div>
<a data-name="-1536364726%2FProperties%2F-**********" anchor-label="java" id="-1536364726%2FProperties%2F-**********" data-filterable-set=":docs/kotlin_dsl"></a>
          <div class="table-row" data-togglable="EXTENSION_PROPERTY" data-filterable-current=":docs/kotlin_dsl" data-filterable-set=":docs/kotlin_dsl">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="../java.html"><span><span>java</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="-1536364726%2FProperties%2F-**********"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
                    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/kotlin_dsl" data-filterable-set=":docs/kotlin_dsl" data-active="" data-toggle=":docs/kotlin_dsl">DSL</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/kotlin_dsl"><div class="symbol monospace"><span class="token keyword"></span><span class="token keyword">val </span><a href="../../org.gradle.plugin.use/-plugin-dependencies-spec/index.html#552792532%2FMain%2F-**********">PluginDependenciesSpec</a><span class="token punctuation">.</span><a href="../java.html">java</a><span class="token operator">: </span><a href="../../org.gradle.plugin.use/-plugin-dependency-spec/index.html#-**********%2FMain%2F-**********">PluginDependencySpec</a></div><div class="brief "><p class="paragraph">The builtin Gradle plugin implemented by <span data-unresolved-link="org.gradle.api.plugins/JavaPlugin///PointingToDeclaration/">org.gradle.api.plugins.JavaPlugin</span>.</p></div></div>                  </div>
                </div>
              </div>
            </div>
          </div>
<a data-name="1576808330%2FProperties%2F-**********" anchor-label="java-base" id="1576808330%2FProperties%2F-**********" data-filterable-set=":docs/kotlin_dsl"></a>
          <div class="table-row" data-togglable="EXTENSION_PROPERTY" data-filterable-current=":docs/kotlin_dsl" data-filterable-set=":docs/kotlin_dsl">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="../java-base.html"><span><span>java-base</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="1576808330%2FProperties%2F-**********"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
                    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/kotlin_dsl" data-filterable-set=":docs/kotlin_dsl" data-active="" data-toggle=":docs/kotlin_dsl">DSL</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/kotlin_dsl"><div class="symbol monospace"><span class="token keyword"></span><span class="token keyword">val </span><a href="../../org.gradle.plugin.use/-plugin-dependencies-spec/index.html#552792532%2FMain%2F-**********">PluginDependenciesSpec</a><span class="token punctuation">.</span><a href="../java-base.html">java-base</a><span class="token operator">: </span><a href="../../org.gradle.plugin.use/-plugin-dependency-spec/index.html#-**********%2FMain%2F-**********">PluginDependencySpec</a></div><div class="brief "><p class="paragraph">The builtin Gradle plugin implemented by <span data-unresolved-link="org.gradle.api.plugins/JavaBasePlugin///PointingToDeclaration/">org.gradle.api.plugins.JavaBasePlugin</span>.</p></div></div>                  </div>
                </div>
              </div>
            </div>
          </div>
<a data-name="-1232383842%2FProperties%2F-**********" anchor-label="java-gradle-plugin" id="-1232383842%2FProperties%2F-**********" data-filterable-set=":docs/kotlin_dsl"></a>
          <div class="table-row" data-togglable="EXTENSION_PROPERTY" data-filterable-current=":docs/kotlin_dsl" data-filterable-set=":docs/kotlin_dsl">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="../java-gradle-plugin.html"><span><span>java-gradle-plugin</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="-1232383842%2FProperties%2F-**********"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
                    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/kotlin_dsl" data-filterable-set=":docs/kotlin_dsl" data-active="" data-toggle=":docs/kotlin_dsl">DSL</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/kotlin_dsl"><div class="symbol monospace"><span class="token keyword"></span><span class="token keyword">val </span><a href="../../org.gradle.plugin.use/-plugin-dependencies-spec/index.html#552792532%2FMain%2F-**********">PluginDependenciesSpec</a><span class="token punctuation">.</span><a href="../java-gradle-plugin.html">java-gradle-plugin</a><span class="token operator">: </span><a href="../../org.gradle.plugin.use/-plugin-dependency-spec/index.html#-**********%2FMain%2F-**********">PluginDependencySpec</a></div><div class="brief "><p class="paragraph">The builtin Gradle plugin implemented by <span data-unresolved-link="org.gradle.plugin.devel.plugins/JavaGradlePluginPlugin///PointingToDeclaration/">org.gradle.plugin.devel.plugins.JavaGradlePluginPlugin</span>.</p></div></div>                  </div>
                </div>
              </div>
            </div>
          </div>
<a data-name="-115524836%2FProperties%2F-**********" anchor-label="java-library" id="-115524836%2FProperties%2F-**********" data-filterable-set=":docs/kotlin_dsl"></a>
          <div class="table-row" data-togglable="EXTENSION_PROPERTY" data-filterable-current=":docs/kotlin_dsl" data-filterable-set=":docs/kotlin_dsl">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="../java-library.html"><span><span>java-library</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="-115524836%2FProperties%2F-**********"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
                    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/kotlin_dsl" data-filterable-set=":docs/kotlin_dsl" data-active="" data-toggle=":docs/kotlin_dsl">DSL</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/kotlin_dsl"><div class="symbol monospace"><span class="token keyword"></span><span class="token keyword">val </span><a href="../../org.gradle.plugin.use/-plugin-dependencies-spec/index.html#552792532%2FMain%2F-**********">PluginDependenciesSpec</a><span class="token punctuation">.</span><a href="../java-library.html">java-library</a><span class="token operator">: </span><a href="../../org.gradle.plugin.use/-plugin-dependency-spec/index.html#-**********%2FMain%2F-**********">PluginDependencySpec</a></div><div class="brief "><p class="paragraph">The builtin Gradle plugin implemented by <span data-unresolved-link="org.gradle.api.plugins/JavaLibraryPlugin///PointingToDeclaration/">org.gradle.api.plugins.JavaLibraryPlugin</span>.</p></div></div>                  </div>
                </div>
              </div>
            </div>
          </div>
<a data-name="-1765609691%2FProperties%2F-**********" anchor-label="java-library-distribution" id="-1765609691%2FProperties%2F-**********" data-filterable-set=":docs/kotlin_dsl"></a>
          <div class="table-row" data-togglable="EXTENSION_PROPERTY" data-filterable-current=":docs/kotlin_dsl" data-filterable-set=":docs/kotlin_dsl">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="../java-library-distribution.html"><span><span>java-library-distribution</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="-1765609691%2FProperties%2F-**********"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
                    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/kotlin_dsl" data-filterable-set=":docs/kotlin_dsl" data-active="" data-toggle=":docs/kotlin_dsl">DSL</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/kotlin_dsl"><div class="symbol monospace"><span class="token keyword"></span><span class="token keyword">val </span><a href="../../org.gradle.plugin.use/-plugin-dependencies-spec/index.html#552792532%2FMain%2F-**********">PluginDependenciesSpec</a><span class="token punctuation">.</span><a href="../java-library-distribution.html">java-library-distribution</a><span class="token operator">: </span><a href="../../org.gradle.plugin.use/-plugin-dependency-spec/index.html#-**********%2FMain%2F-**********">PluginDependencySpec</a></div><div class="brief "><p class="paragraph">The builtin Gradle plugin implemented by <span data-unresolved-link="org.gradle.api.plugins/JavaLibraryDistributionPlugin///PointingToDeclaration/">org.gradle.api.plugins.JavaLibraryDistributionPlugin</span>.</p></div></div>                  </div>
                </div>
              </div>
            </div>
          </div>
<a data-name="-1623825880%2FProperties%2F-**********" anchor-label="java-platform" id="-1623825880%2FProperties%2F-**********" data-filterable-set=":docs/kotlin_dsl"></a>
          <div class="table-row" data-togglable="EXTENSION_PROPERTY" data-filterable-current=":docs/kotlin_dsl" data-filterable-set=":docs/kotlin_dsl">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="../java-platform.html"><span><span>java-platform</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="-1623825880%2FProperties%2F-**********"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
                    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/kotlin_dsl" data-filterable-set=":docs/kotlin_dsl" data-active="" data-toggle=":docs/kotlin_dsl">DSL</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/kotlin_dsl"><div class="symbol monospace"><span class="token keyword"></span><span class="token keyword">val </span><a href="../../org.gradle.plugin.use/-plugin-dependencies-spec/index.html#552792532%2FMain%2F-**********">PluginDependenciesSpec</a><span class="token punctuation">.</span><a href="../java-platform.html">java-platform</a><span class="token operator">: </span><a href="../../org.gradle.plugin.use/-plugin-dependency-spec/index.html#-**********%2FMain%2F-**********">PluginDependencySpec</a></div><div class="brief "><p class="paragraph">The builtin Gradle plugin implemented by <span data-unresolved-link="org.gradle.api.plugins/JavaPlatformPlugin///PointingToDeclaration/">org.gradle.api.plugins.JavaPlatformPlugin</span>.</p></div></div>                  </div>
                </div>
              </div>
            </div>
          </div>
<a data-name="-1378113230%2FProperties%2F-**********" anchor-label="java-test-fixtures" id="-1378113230%2FProperties%2F-**********" data-filterable-set=":docs/kotlin_dsl"></a>
          <div class="table-row" data-togglable="EXTENSION_PROPERTY" data-filterable-current=":docs/kotlin_dsl" data-filterable-set=":docs/kotlin_dsl">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="../java-test-fixtures.html"><span><span>java-test-fixtures</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="-1378113230%2FProperties%2F-**********"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
                    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/kotlin_dsl" data-filterable-set=":docs/kotlin_dsl" data-active="" data-toggle=":docs/kotlin_dsl">DSL</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/kotlin_dsl"><div class="symbol monospace"><span class="token keyword"></span><span class="token keyword">val </span><a href="../../org.gradle.plugin.use/-plugin-dependencies-spec/index.html#552792532%2FMain%2F-**********">PluginDependenciesSpec</a><span class="token punctuation">.</span><a href="../java-test-fixtures.html">java-test-fixtures</a><span class="token operator">: </span><a href="../../org.gradle.plugin.use/-plugin-dependency-spec/index.html#-**********%2FMain%2F-**********">PluginDependencySpec</a></div><div class="brief "><p class="paragraph">The builtin Gradle plugin implemented by <span data-unresolved-link="org.gradle.api.plugins/JavaTestFixturesPlugin///PointingToDeclaration/">org.gradle.api.plugins.JavaTestFixturesPlugin</span>.</p></div></div>                  </div>
                </div>
              </div>
            </div>
          </div>
<a data-name="-519429658%2FProperties%2F-**********" anchor-label="jdk-toolchains" id="-519429658%2FProperties%2F-**********" data-filterable-set=":docs/kotlin_dsl"></a>
          <div class="table-row" data-togglable="EXTENSION_PROPERTY" data-filterable-current=":docs/kotlin_dsl" data-filterable-set=":docs/kotlin_dsl">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="../jdk-toolchains.html"><span><span>jdk-toolchains</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="-519429658%2FProperties%2F-**********"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
                    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/kotlin_dsl" data-filterable-set=":docs/kotlin_dsl" data-active="" data-toggle=":docs/kotlin_dsl">DSL</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/kotlin_dsl"><div class="symbol monospace"><span class="token keyword"></span><span class="token keyword">val </span><a href="../../org.gradle.plugin.use/-plugin-dependencies-spec/index.html#552792532%2FMain%2F-**********">PluginDependenciesSpec</a><span class="token punctuation">.</span><a href="../jdk-toolchains.html">jdk-toolchains</a><span class="token operator">: </span><a href="../../org.gradle.plugin.use/-plugin-dependency-spec/index.html#-**********%2FMain%2F-**********">PluginDependencySpec</a></div><div class="brief "><p class="paragraph">The builtin Gradle plugin implemented by org.gradle.api.plugins.JdkToolchainsPlugin.</p></div></div>                  </div>
                </div>
              </div>
            </div>
          </div>
<a data-name="1068295378%2FProperties%2F-**********" anchor-label="jvm-ecosystem" id="1068295378%2FProperties%2F-**********" data-filterable-set=":docs/kotlin_dsl"></a>
          <div class="table-row" data-togglable="EXTENSION_PROPERTY" data-filterable-current=":docs/kotlin_dsl" data-filterable-set=":docs/kotlin_dsl">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="../jvm-ecosystem.html"><span><span>jvm-ecosystem</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="1068295378%2FProperties%2F-**********"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
                    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/kotlin_dsl" data-filterable-set=":docs/kotlin_dsl" data-active="" data-toggle=":docs/kotlin_dsl">DSL</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/kotlin_dsl"><div class="symbol monospace"><span class="token keyword"></span><span class="token keyword">val </span><a href="../../org.gradle.plugin.use/-plugin-dependencies-spec/index.html#552792532%2FMain%2F-**********">PluginDependenciesSpec</a><span class="token punctuation">.</span><a href="../jvm-ecosystem.html">jvm-ecosystem</a><span class="token operator">: </span><a href="../../org.gradle.plugin.use/-plugin-dependency-spec/index.html#-**********%2FMain%2F-**********">PluginDependencySpec</a></div><div class="brief "><p class="paragraph">The builtin Gradle plugin implemented by <span data-unresolved-link="org.gradle.api.plugins/JvmEcosystemPlugin///PointingToDeclaration/">org.gradle.api.plugins.JvmEcosystemPlugin</span>.</p></div></div>                  </div>
                </div>
              </div>
            </div>
          </div>
<a data-name="69803875%2FProperties%2F-**********" anchor-label="jvm-test-suite" id="69803875%2FProperties%2F-**********" data-filterable-set=":docs/kotlin_dsl"></a>
          <div class="table-row" data-togglable="EXTENSION_PROPERTY" data-filterable-current=":docs/kotlin_dsl" data-filterable-set=":docs/kotlin_dsl">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="../jvm-test-suite.html"><span><span>jvm-test-suite</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="69803875%2FProperties%2F-**********"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
                    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/kotlin_dsl" data-filterable-set=":docs/kotlin_dsl" data-active="" data-toggle=":docs/kotlin_dsl">DSL</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/kotlin_dsl"><div class="symbol monospace"><span class="token keyword"></span><span class="token keyword">val </span><a href="../../org.gradle.plugin.use/-plugin-dependencies-spec/index.html#552792532%2FMain%2F-**********">PluginDependenciesSpec</a><span class="token punctuation">.</span><a href="../jvm-test-suite.html">jvm-test-suite</a><span class="token operator">: </span><a href="../../org.gradle.plugin.use/-plugin-dependency-spec/index.html#-**********%2FMain%2F-**********">PluginDependencySpec</a></div><div class="brief "><p class="paragraph">The builtin Gradle plugin implemented by <span data-unresolved-link="org.gradle.api.plugins/JvmTestSuitePlugin///PointingToDeclaration/">org.gradle.api.plugins.JvmTestSuitePlugin</span>.</p></div></div>                  </div>
                </div>
              </div>
            </div>
          </div>
<a data-name="-1167443335%2FProperties%2F-**********" anchor-label="jvm-toolchain-management" id="-1167443335%2FProperties%2F-**********" data-filterable-set=":docs/kotlin_dsl"></a>
          <div class="table-row" data-togglable="EXTENSION_PROPERTY" data-filterable-current=":docs/kotlin_dsl" data-filterable-set=":docs/kotlin_dsl">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="../jvm-toolchain-management.html"><span><span>jvm-toolchain-management</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="-1167443335%2FProperties%2F-**********"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
                    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/kotlin_dsl" data-filterable-set=":docs/kotlin_dsl" data-active="" data-toggle=":docs/kotlin_dsl">DSL</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/kotlin_dsl"><div class="symbol monospace"><span class="token keyword"></span><span class="token keyword">val </span><a href="../../org.gradle.plugin.use/-plugin-dependencies-spec/index.html#552792532%2FMain%2F-**********">PluginDependenciesSpec</a><span class="token punctuation">.</span><a href="../jvm-toolchain-management.html">jvm-toolchain-management</a><span class="token operator">: </span><a href="../../org.gradle.plugin.use/-plugin-dependency-spec/index.html#-**********%2FMain%2F-**********">PluginDependencySpec</a></div><div class="brief "><p class="paragraph">The builtin Gradle plugin implemented by <span data-unresolved-link="org.gradle.api.plugins/JvmToolchainManagementPlugin///PointingToDeclaration/">org.gradle.api.plugins.JvmToolchainManagementPlugin</span>.</p></div></div>                  </div>
                </div>
              </div>
            </div>
          </div>
<a data-name="-361348074%2FProperties%2F-**********" anchor-label="jvm-toolchains" id="-361348074%2FProperties%2F-**********" data-filterable-set=":docs/kotlin_dsl"></a>
          <div class="table-row" data-togglable="EXTENSION_PROPERTY" data-filterable-current=":docs/kotlin_dsl" data-filterable-set=":docs/kotlin_dsl">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="../jvm-toolchains.html"><span><span>jvm-toolchains</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="-361348074%2FProperties%2F-**********"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
                    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/kotlin_dsl" data-filterable-set=":docs/kotlin_dsl" data-active="" data-toggle=":docs/kotlin_dsl">DSL</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/kotlin_dsl"><div class="symbol monospace"><span class="token keyword"></span><span class="token keyword">val </span><a href="../../org.gradle.plugin.use/-plugin-dependencies-spec/index.html#552792532%2FMain%2F-**********">PluginDependenciesSpec</a><span class="token punctuation">.</span><a href="../jvm-toolchains.html">jvm-toolchains</a><span class="token operator">: </span><a href="../../org.gradle.plugin.use/-plugin-dependency-spec/index.html#-**********%2FMain%2F-**********">PluginDependencySpec</a></div><div class="brief "><p class="paragraph">The builtin Gradle plugin implemented by <span data-unresolved-link="org.gradle.api.plugins/JvmToolchainsPlugin///PointingToDeclaration/">org.gradle.api.plugins.JvmToolchainsPlugin</span>.</p></div></div>                  </div>
                </div>
              </div>
            </div>
          </div>
<a data-name="-1664344677%2FProperties%2F-**********" anchor-label="kotlin-dsl" id="-1664344677%2FProperties%2F-**********" data-filterable-set=":docs/kotlin_dsl"></a>
          <div class="table-row" data-togglable="EXTENSION_PROPERTY" data-filterable-current=":docs/kotlin_dsl" data-filterable-set=":docs/kotlin_dsl">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="../kotlin-dsl.html"><span><span>kotlin-dsl</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="-1664344677%2FProperties%2F-**********"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
                    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/kotlin_dsl" data-filterable-set=":docs/kotlin_dsl" data-active="" data-toggle=":docs/kotlin_dsl">DSL</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/kotlin_dsl"><div class="symbol monospace"><span class="token keyword"></span><span class="token keyword">val </span><a href="../../org.gradle.plugin.use/-plugin-dependencies-spec/index.html#552792532%2FMain%2F-**********">PluginDependenciesSpec</a><span class="token punctuation">.</span><a href="../kotlin-dsl.html">kotlin-dsl</a><span class="token operator">: </span><a href="../../org.gradle.plugin.use/-plugin-dependency-spec/index.html#-**********%2FMain%2F-**********">PluginDependencySpec</a></div><div class="brief "><p class="paragraph">The <code class="lang-kotlin">kotlin-dsl</code> plugin.</p></div></div>                  </div>
                </div>
              </div>
            </div>
          </div>
<a data-name="-769504295%2FProperties%2F-**********" anchor-label="kotlin-dsl-base" id="-769504295%2FProperties%2F-**********" data-filterable-set=":docs/kotlin_dsl"></a>
          <div class="table-row" data-togglable="EXTENSION_PROPERTY" data-filterable-current=":docs/kotlin_dsl" data-filterable-set=":docs/kotlin_dsl">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="../kotlin-dsl-base.html"><span><span>kotlin-dsl-base</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="-769504295%2FProperties%2F-**********"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
                    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/kotlin_dsl" data-filterable-set=":docs/kotlin_dsl" data-active="" data-toggle=":docs/kotlin_dsl">DSL</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/kotlin_dsl"><div class="symbol monospace"><span class="token keyword"></span><span class="token keyword">val </span><a href="../../org.gradle.plugin.use/-plugin-dependencies-spec/index.html#552792532%2FMain%2F-**********">PluginDependenciesSpec</a><span class="token punctuation">.</span><a href="../kotlin-dsl-base.html">kotlin-dsl-base</a><span class="token operator">: </span><a href="../../org.gradle.plugin.use/-plugin-dependency-spec/index.html#-**********%2FMain%2F-**********">PluginDependencySpec</a></div><div class="brief "><p class="paragraph">The <code class="lang-kotlin">kotlin-dsl.base</code> plugin.</p></div></div>                  </div>
                </div>
              </div>
            </div>
          </div>
<a data-name="238465107%2FProperties%2F-**********" anchor-label="kotlin-dsl-precompiled-script-plugins" id="238465107%2FProperties%2F-**********" data-filterable-set=":docs/kotlin_dsl"></a>
          <div class="table-row" data-togglable="EXTENSION_PROPERTY" data-filterable-current=":docs/kotlin_dsl" data-filterable-set=":docs/kotlin_dsl">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="../kotlin-dsl-precompiled-script-plugins.html"><span><span>kotlin-dsl-precompiled-script-plugins</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="238465107%2FProperties%2F-**********"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
                    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/kotlin_dsl" data-filterable-set=":docs/kotlin_dsl" data-active="" data-toggle=":docs/kotlin_dsl">DSL</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/kotlin_dsl"><div class="symbol monospace"><span class="token keyword"></span><span class="token keyword">val </span><a href="../../org.gradle.plugin.use/-plugin-dependencies-spec/index.html#552792532%2FMain%2F-**********">PluginDependenciesSpec</a><span class="token punctuation">.</span><a href="../kotlin-dsl-precompiled-script-plugins.html">kotlin-dsl-precompiled-script-plugins</a><span class="token operator">: </span><a href="../../org.gradle.plugin.use/-plugin-dependency-spec/index.html#-**********%2FMain%2F-**********">PluginDependencySpec</a></div><div class="brief "><p class="paragraph">The <code class="lang-kotlin">kotlin-dsl.precompiled-script-plugins</code> plugin.</p></div></div>                  </div>
                </div>
              </div>
            </div>
          </div>
<a data-name="1124843200%2FProperties%2F-**********" anchor-label="language-base" id="1124843200%2FProperties%2F-**********" data-filterable-set=":docs/kotlin_dsl"></a>
          <div class="table-row" data-togglable="EXTENSION_PROPERTY" data-filterable-current=":docs/kotlin_dsl" data-filterable-set=":docs/kotlin_dsl">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="../language-base.html"><span><span>language-base</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="1124843200%2FProperties%2F-**********"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
                    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/kotlin_dsl" data-filterable-set=":docs/kotlin_dsl" data-active="" data-toggle=":docs/kotlin_dsl">DSL</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/kotlin_dsl"><div class="symbol monospace"><span class="token keyword"></span><span class="token keyword">val </span><a href="../../org.gradle.plugin.use/-plugin-dependencies-spec/index.html#552792532%2FMain%2F-**********">PluginDependenciesSpec</a><span class="token punctuation">.</span><a href="../language-base.html">language-base</a><span class="token operator">: </span><a href="../../org.gradle.plugin.use/-plugin-dependency-spec/index.html#-**********%2FMain%2F-**********">PluginDependencySpec</a></div><div class="brief "><p class="paragraph">The builtin Gradle plugin implemented by <span data-unresolved-link="org.gradle.language.base.plugins/LanguageBasePlugin///PointingToDeclaration/">org.gradle.language.base.plugins.LanguageBasePlugin</span>.</p></div></div>                  </div>
                </div>
              </div>
            </div>
          </div>
<a data-name="312418712%2FProperties%2F-**********" anchor-label="lifecycle-base" id="312418712%2FProperties%2F-**********" data-filterable-set=":docs/kotlin_dsl"></a>
          <div class="table-row" data-togglable="EXTENSION_PROPERTY" data-filterable-current=":docs/kotlin_dsl" data-filterable-set=":docs/kotlin_dsl">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="../lifecycle-base.html"><span><span>lifecycle-base</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="312418712%2FProperties%2F-**********"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
                    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/kotlin_dsl" data-filterable-set=":docs/kotlin_dsl" data-active="" data-toggle=":docs/kotlin_dsl">DSL</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/kotlin_dsl"><div class="symbol monospace"><span class="token keyword"></span><span class="token keyword">val </span><a href="../../org.gradle.plugin.use/-plugin-dependencies-spec/index.html#552792532%2FMain%2F-**********">PluginDependenciesSpec</a><span class="token punctuation">.</span><a href="../lifecycle-base.html">lifecycle-base</a><span class="token operator">: </span><a href="../../org.gradle.plugin.use/-plugin-dependency-spec/index.html#-**********%2FMain%2F-**********">PluginDependencySpec</a></div><div class="brief "><p class="paragraph">The builtin Gradle plugin implemented by <span data-unresolved-link="org.gradle.language.base.plugins/LifecycleBasePlugin///PointingToDeclaration/">org.gradle.language.base.plugins.LifecycleBasePlugin</span>.</p></div></div>                  </div>
                </div>
              </div>
            </div>
          </div>
<a data-name="76565625%2FProperties%2F-**********" anchor-label="maven-publish" id="76565625%2FProperties%2F-**********" data-filterable-set=":docs/kotlin_dsl"></a>
          <div class="table-row" data-togglable="EXTENSION_PROPERTY" data-filterable-current=":docs/kotlin_dsl" data-filterable-set=":docs/kotlin_dsl">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="../maven-publish.html"><span><span>maven-publish</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="76565625%2FProperties%2F-**********"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
                    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/kotlin_dsl" data-filterable-set=":docs/kotlin_dsl" data-active="" data-toggle=":docs/kotlin_dsl">DSL</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/kotlin_dsl"><div class="symbol monospace"><span class="token keyword"></span><span class="token keyword">val </span><a href="../../org.gradle.plugin.use/-plugin-dependencies-spec/index.html#552792532%2FMain%2F-**********">PluginDependenciesSpec</a><span class="token punctuation">.</span><a href="../maven-publish.html">maven-publish</a><span class="token operator">: </span><a href="../../org.gradle.plugin.use/-plugin-dependency-spec/index.html#-**********%2FMain%2F-**********">PluginDependencySpec</a></div><div class="brief "><p class="paragraph">The builtin Gradle plugin implemented by <span data-unresolved-link="org.gradle.api.publish.maven.plugins/MavenPublishPlugin///PointingToDeclaration/">org.gradle.api.publish.maven.plugins.MavenPublishPlugin</span>.</p></div></div>                  </div>
                </div>
              </div>
            </div>
          </div>
<a data-name="-1372217361%2FProperties%2F-**********" anchor-label="microsoft-visual-cpp-compiler" id="-1372217361%2FProperties%2F-**********" data-filterable-set=":docs/kotlin_dsl"></a>
          <div class="table-row" data-togglable="EXTENSION_PROPERTY" data-filterable-current=":docs/kotlin_dsl" data-filterable-set=":docs/kotlin_dsl">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="../microsoft-visual-cpp-compiler.html"><span><span>microsoft-visual-cpp-compiler</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="-1372217361%2FProperties%2F-**********"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
                    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/kotlin_dsl" data-filterable-set=":docs/kotlin_dsl" data-active="" data-toggle=":docs/kotlin_dsl">DSL</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/kotlin_dsl"><div class="symbol monospace"><span class="token keyword"></span><span class="token keyword">val </span><a href="../../org.gradle.plugin.use/-plugin-dependencies-spec/index.html#552792532%2FMain%2F-**********">PluginDependenciesSpec</a><span class="token punctuation">.</span><a href="../microsoft-visual-cpp-compiler.html">microsoft-visual-cpp-compiler</a><span class="token operator">: </span><a href="../../org.gradle.plugin.use/-plugin-dependency-spec/index.html#-**********%2FMain%2F-**********">PluginDependencySpec</a></div><div class="brief "><p class="paragraph">The builtin Gradle plugin implemented by <span data-unresolved-link="org.gradle.nativeplatform.toolchain.plugins/MicrosoftVisualCppCompilerPlugin///PointingToDeclaration/">org.gradle.nativeplatform.toolchain.plugins.MicrosoftVisualCppCompilerPlugin</span>.</p></div></div>                  </div>
                </div>
              </div>
            </div>
          </div>
<a data-name="-995504827%2FProperties%2F-**********" anchor-label="native-component" id="-995504827%2FProperties%2F-**********" data-filterable-set=":docs/kotlin_dsl"></a>
          <div class="table-row" data-togglable="EXTENSION_PROPERTY" data-filterable-current=":docs/kotlin_dsl" data-filterable-set=":docs/kotlin_dsl">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="../native-component.html"><span><span>native-component</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="-995504827%2FProperties%2F-**********"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
                    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/kotlin_dsl" data-filterable-set=":docs/kotlin_dsl" data-active="" data-toggle=":docs/kotlin_dsl">DSL</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/kotlin_dsl"><div class="symbol monospace"><span class="token keyword"></span><span class="token keyword">val </span><a href="../../org.gradle.plugin.use/-plugin-dependencies-spec/index.html#552792532%2FMain%2F-**********">PluginDependenciesSpec</a><span class="token punctuation">.</span><a href="../native-component.html">native-component</a><span class="token operator">: </span><a href="../../org.gradle.plugin.use/-plugin-dependency-spec/index.html#-**********%2FMain%2F-**********">PluginDependencySpec</a></div><div class="brief "><p class="paragraph">The builtin Gradle plugin implemented by <span data-unresolved-link="org.gradle.nativeplatform.plugins/NativeComponentPlugin///PointingToDeclaration/">org.gradle.nativeplatform.plugins.NativeComponentPlugin</span>.</p></div></div>                  </div>
                </div>
              </div>
            </div>
          </div>
<a data-name="-1313290583%2FProperties%2F-**********" anchor-label="native-component-model" id="-1313290583%2FProperties%2F-**********" data-filterable-set=":docs/kotlin_dsl"></a>
          <div class="table-row" data-togglable="EXTENSION_PROPERTY" data-filterable-current=":docs/kotlin_dsl" data-filterable-set=":docs/kotlin_dsl">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="../native-component-model.html"><span><span>native-component-model</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="-1313290583%2FProperties%2F-**********"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
                    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/kotlin_dsl" data-filterable-set=":docs/kotlin_dsl" data-active="" data-toggle=":docs/kotlin_dsl">DSL</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/kotlin_dsl"><div class="symbol monospace"><span class="token keyword"></span><span class="token keyword">val </span><a href="../../org.gradle.plugin.use/-plugin-dependencies-spec/index.html#552792532%2FMain%2F-**********">PluginDependenciesSpec</a><span class="token punctuation">.</span><a href="../native-component-model.html">native-component-model</a><span class="token operator">: </span><a href="../../org.gradle.plugin.use/-plugin-dependency-spec/index.html#-**********%2FMain%2F-**********">PluginDependencySpec</a></div><div class="brief "><p class="paragraph">The builtin Gradle plugin implemented by <span data-unresolved-link="org.gradle.nativeplatform.plugins/NativeComponentModelPlugin///PointingToDeclaration/">org.gradle.nativeplatform.plugins.NativeComponentModelPlugin</span>.</p></div></div>                  </div>
                </div>
              </div>
            </div>
          </div>
<a data-name="902711031%2FProperties%2F-**********" anchor-label="objective-c" id="902711031%2FProperties%2F-**********" data-filterable-set=":docs/kotlin_dsl"></a>
          <div class="table-row" data-togglable="EXTENSION_PROPERTY" data-filterable-current=":docs/kotlin_dsl" data-filterable-set=":docs/kotlin_dsl">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="../objective-c.html"><span><span>objective-c</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="902711031%2FProperties%2F-**********"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
                    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/kotlin_dsl" data-filterable-set=":docs/kotlin_dsl" data-active="" data-toggle=":docs/kotlin_dsl">DSL</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/kotlin_dsl"><div class="symbol monospace"><span class="token keyword"></span><span class="token keyword">val </span><a href="../../org.gradle.plugin.use/-plugin-dependencies-spec/index.html#552792532%2FMain%2F-**********">PluginDependenciesSpec</a><span class="token punctuation">.</span><a href="../objective-c.html">objective-c</a><span class="token operator">: </span><a href="../../org.gradle.plugin.use/-plugin-dependency-spec/index.html#-**********%2FMain%2F-**********">PluginDependencySpec</a></div><div class="brief "><p class="paragraph">The builtin Gradle plugin implemented by <span data-unresolved-link="org.gradle.language.objectivec.plugins/ObjectiveCPlugin///PointingToDeclaration/">org.gradle.language.objectivec.plugins.ObjectiveCPlugin</span>.</p></div></div>                  </div>
                </div>
              </div>
            </div>
          </div>
<a data-name="-429071616%2FProperties%2F-**********" anchor-label="objective-c-lang" id="-429071616%2FProperties%2F-**********" data-filterable-set=":docs/kotlin_dsl"></a>
          <div class="table-row" data-togglable="EXTENSION_PROPERTY" data-filterable-current=":docs/kotlin_dsl" data-filterable-set=":docs/kotlin_dsl">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="../objective-c-lang.html"><span><span>objective-c-lang</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="-429071616%2FProperties%2F-**********"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
                    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/kotlin_dsl" data-filterable-set=":docs/kotlin_dsl" data-active="" data-toggle=":docs/kotlin_dsl">DSL</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/kotlin_dsl"><div class="symbol monospace"><span class="token keyword"></span><span class="token keyword">val </span><a href="../../org.gradle.plugin.use/-plugin-dependencies-spec/index.html#552792532%2FMain%2F-**********">PluginDependenciesSpec</a><span class="token punctuation">.</span><a href="../objective-c-lang.html">objective-c-lang</a><span class="token operator">: </span><a href="../../org.gradle.plugin.use/-plugin-dependency-spec/index.html#-**********%2FMain%2F-**********">PluginDependencySpec</a></div><div class="brief "><p class="paragraph">The builtin Gradle plugin implemented by <span data-unresolved-link="org.gradle.language.objectivec.plugins/ObjectiveCLangPlugin///PointingToDeclaration/">org.gradle.language.objectivec.plugins.ObjectiveCLangPlugin</span>.</p></div></div>                  </div>
                </div>
              </div>
            </div>
          </div>
<a data-name="-915738249%2FProperties%2F-**********" anchor-label="objective-cpp" id="-915738249%2FProperties%2F-**********" data-filterable-set=":docs/kotlin_dsl"></a>
          <div class="table-row" data-togglable="EXTENSION_PROPERTY" data-filterable-current=":docs/kotlin_dsl" data-filterable-set=":docs/kotlin_dsl">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="../objective-cpp.html"><span><span>objective-cpp</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="-915738249%2FProperties%2F-**********"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
                    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/kotlin_dsl" data-filterable-set=":docs/kotlin_dsl" data-active="" data-toggle=":docs/kotlin_dsl">DSL</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/kotlin_dsl"><div class="symbol monospace"><span class="token keyword"></span><span class="token keyword">val </span><a href="../../org.gradle.plugin.use/-plugin-dependencies-spec/index.html#552792532%2FMain%2F-**********">PluginDependenciesSpec</a><span class="token punctuation">.</span><a href="../objective-cpp.html">objective-cpp</a><span class="token operator">: </span><a href="../../org.gradle.plugin.use/-plugin-dependency-spec/index.html#-**********%2FMain%2F-**********">PluginDependencySpec</a></div><div class="brief "><p class="paragraph">The builtin Gradle plugin implemented by <span data-unresolved-link="org.gradle.language.objectivecpp.plugins/ObjectiveCppPlugin///PointingToDeclaration/">org.gradle.language.objectivecpp.plugins.ObjectiveCppPlugin</span>.</p></div></div>                  </div>
                </div>
              </div>
            </div>
          </div>
<a data-name="647415936%2FProperties%2F-**********" anchor-label="objective-cpp-lang" id="647415936%2FProperties%2F-**********" data-filterable-set=":docs/kotlin_dsl"></a>
          <div class="table-row" data-togglable="EXTENSION_PROPERTY" data-filterable-current=":docs/kotlin_dsl" data-filterable-set=":docs/kotlin_dsl">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="../objective-cpp-lang.html"><span><span>objective-cpp-lang</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="647415936%2FProperties%2F-**********"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
                    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/kotlin_dsl" data-filterable-set=":docs/kotlin_dsl" data-active="" data-toggle=":docs/kotlin_dsl">DSL</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/kotlin_dsl"><div class="symbol monospace"><span class="token keyword"></span><span class="token keyword">val </span><a href="../../org.gradle.plugin.use/-plugin-dependencies-spec/index.html#552792532%2FMain%2F-**********">PluginDependenciesSpec</a><span class="token punctuation">.</span><a href="../objective-cpp-lang.html">objective-cpp-lang</a><span class="token operator">: </span><a href="../../org.gradle.plugin.use/-plugin-dependency-spec/index.html#-**********%2FMain%2F-**********">PluginDependencySpec</a></div><div class="brief "><p class="paragraph">The builtin Gradle plugin implemented by <span data-unresolved-link="org.gradle.language.objectivecpp.plugins/ObjectiveCppLangPlugin///PointingToDeclaration/">org.gradle.language.objectivecpp.plugins.ObjectiveCppLangPlugin</span>.</p></div></div>                  </div>
                </div>
              </div>
            </div>
          </div>
<a data-name="82176447%2FProperties%2F-**********" anchor-label="pmd" id="82176447%2FProperties%2F-**********" data-filterable-set=":docs/kotlin_dsl"></a>
          <div class="table-row" data-togglable="EXTENSION_PROPERTY" data-filterable-current=":docs/kotlin_dsl" data-filterable-set=":docs/kotlin_dsl">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="../pmd.html"><span><span>pmd</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="82176447%2FProperties%2F-**********"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
                    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/kotlin_dsl" data-filterable-set=":docs/kotlin_dsl" data-active="" data-toggle=":docs/kotlin_dsl">DSL</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/kotlin_dsl"><div class="symbol monospace"><span class="token keyword"></span><span class="token keyword">val </span><a href="../../org.gradle.plugin.use/-plugin-dependencies-spec/index.html#552792532%2FMain%2F-**********">PluginDependenciesSpec</a><span class="token punctuation">.</span><a href="../pmd.html">pmd</a><span class="token operator">: </span><a href="../../org.gradle.plugin.use/-plugin-dependency-spec/index.html#-**********%2FMain%2F-**********">PluginDependencySpec</a></div><div class="brief "><p class="paragraph">The builtin Gradle plugin implemented by <span data-unresolved-link="org.gradle.api.plugins.quality/PmdPlugin///PointingToDeclaration/">org.gradle.api.plugins.quality.PmdPlugin</span>.</p></div></div>                  </div>
                </div>
              </div>
            </div>
          </div>
<a data-name="508324612%2FProperties%2F-**********" anchor-label="project-report" id="508324612%2FProperties%2F-**********" data-filterable-set=":docs/kotlin_dsl"></a>
          <div class="table-row" data-togglable="EXTENSION_PROPERTY" data-filterable-current=":docs/kotlin_dsl" data-filterable-set=":docs/kotlin_dsl">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="../project-report.html"><span><span>project-report</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="508324612%2FProperties%2F-**********"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
                    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/kotlin_dsl" data-filterable-set=":docs/kotlin_dsl" data-active="" data-toggle=":docs/kotlin_dsl">DSL</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/kotlin_dsl"><div class="symbol monospace"><span class="token keyword"></span><span class="token keyword">val </span><a href="../../org.gradle.plugin.use/-plugin-dependencies-spec/index.html#552792532%2FMain%2F-**********">PluginDependenciesSpec</a><span class="token punctuation">.</span><a href="../project-report.html">project-report</a><span class="token operator">: </span><a href="../../org.gradle.plugin.use/-plugin-dependency-spec/index.html#-**********%2FMain%2F-**********">PluginDependencySpec</a></div><div class="brief "><p class="paragraph">The builtin Gradle plugin implemented by <span data-unresolved-link="org.gradle.api.plugins/ProjectReportsPlugin///PointingToDeclaration/">org.gradle.api.plugins.ProjectReportsPlugin</span>.</p></div></div>                  </div>
                </div>
              </div>
            </div>
          </div>
<a data-name="-2074956869%2FProperties%2F-**********" anchor-label="project-reports" id="-2074956869%2FProperties%2F-**********" data-filterable-set=":docs/kotlin_dsl"></a>
          <div class="table-row" data-togglable="EXTENSION_PROPERTY" data-filterable-current=":docs/kotlin_dsl" data-filterable-set=":docs/kotlin_dsl">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="../project-reports.html"><span><span>project-reports</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="-2074956869%2FProperties%2F-**********"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
                    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/kotlin_dsl" data-filterable-set=":docs/kotlin_dsl" data-active="" data-toggle=":docs/kotlin_dsl">DSL</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/kotlin_dsl"><div class="symbol monospace"><span class="token keyword"></span><span class="token keyword">val </span><a href="../../org.gradle.plugin.use/-plugin-dependencies-spec/index.html#552792532%2FMain%2F-**********">PluginDependenciesSpec</a><span class="token punctuation">.</span><a href="../project-reports.html">project-reports</a><span class="token operator">: </span><a href="../../org.gradle.plugin.use/-plugin-dependency-spec/index.html#-**********%2FMain%2F-**********">PluginDependencySpec</a></div><div class="brief "><p class="paragraph">The builtin Gradle plugin implemented by <span data-unresolved-link="org.gradle.api.plugins/ProjectReportsPlugin///PointingToDeclaration/">org.gradle.api.plugins.ProjectReportsPlugin</span>.</p></div></div>                  </div>
                </div>
              </div>
            </div>
          </div>
<a data-name="-1421357735%2FProperties%2F-**********" anchor-label="publishing" id="-1421357735%2FProperties%2F-**********" data-filterable-set=":docs/kotlin_dsl"></a>
          <div class="table-row" data-togglable="EXTENSION_PROPERTY" data-filterable-current=":docs/kotlin_dsl" data-filterable-set=":docs/kotlin_dsl">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="../publishing.html"><span><span>publishing</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="-1421357735%2FProperties%2F-**********"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
                    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/kotlin_dsl" data-filterable-set=":docs/kotlin_dsl" data-active="" data-toggle=":docs/kotlin_dsl">DSL</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/kotlin_dsl"><div class="symbol monospace"><span class="token keyword"></span><span class="token keyword">val </span><a href="../../org.gradle.plugin.use/-plugin-dependencies-spec/index.html#552792532%2FMain%2F-**********">PluginDependenciesSpec</a><span class="token punctuation">.</span><a href="../publishing.html">publishing</a><span class="token operator">: </span><a href="../../org.gradle.plugin.use/-plugin-dependency-spec/index.html#-**********%2FMain%2F-**********">PluginDependencySpec</a></div><div class="brief "><p class="paragraph">The builtin Gradle plugin implemented by <span data-unresolved-link="org.gradle.api.publish.plugins/PublishingPlugin///PointingToDeclaration/">org.gradle.api.publish.plugins.PublishingPlugin</span>.</p></div></div>                  </div>
                </div>
              </div>
            </div>
          </div>
<a data-name="-1816118436%2FProperties%2F-**********" anchor-label="reporting-base" id="-1816118436%2FProperties%2F-**********" data-filterable-set=":docs/kotlin_dsl"></a>
          <div class="table-row" data-togglable="EXTENSION_PROPERTY" data-filterable-current=":docs/kotlin_dsl" data-filterable-set=":docs/kotlin_dsl">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="../reporting-base.html"><span><span>reporting-base</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="-1816118436%2FProperties%2F-**********"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
                    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/kotlin_dsl" data-filterable-set=":docs/kotlin_dsl" data-active="" data-toggle=":docs/kotlin_dsl">DSL</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/kotlin_dsl"><div class="symbol monospace"><span class="token keyword"></span><span class="token keyword">val </span><a href="../../org.gradle.plugin.use/-plugin-dependencies-spec/index.html#552792532%2FMain%2F-**********">PluginDependenciesSpec</a><span class="token punctuation">.</span><a href="../reporting-base.html">reporting-base</a><span class="token operator">: </span><a href="../../org.gradle.plugin.use/-plugin-dependency-spec/index.html#-**********%2FMain%2F-**********">PluginDependencySpec</a></div><div class="brief "><p class="paragraph">The builtin Gradle plugin implemented by <span data-unresolved-link="org.gradle.api.plugins/ReportingBasePlugin///PointingToDeclaration/">org.gradle.api.plugins.ReportingBasePlugin</span>.</p></div></div>                  </div>
                </div>
              </div>
            </div>
          </div>
<a data-name="1363956384%2FProperties%2F-**********" anchor-label="scala" id="1363956384%2FProperties%2F-**********" data-filterable-set=":docs/kotlin_dsl"></a>
          <div class="table-row" data-togglable="EXTENSION_PROPERTY" data-filterable-current=":docs/kotlin_dsl" data-filterable-set=":docs/kotlin_dsl">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="../scala.html"><span><span>scala</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="1363956384%2FProperties%2F-**********"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
                    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/kotlin_dsl" data-filterable-set=":docs/kotlin_dsl" data-active="" data-toggle=":docs/kotlin_dsl">DSL</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/kotlin_dsl"><div class="symbol monospace"><span class="token keyword"></span><span class="token keyword">val </span><a href="../../org.gradle.plugin.use/-plugin-dependencies-spec/index.html#552792532%2FMain%2F-**********">PluginDependenciesSpec</a><span class="token punctuation">.</span><a href="../scala.html">scala</a><span class="token operator">: </span><a href="../../org.gradle.plugin.use/-plugin-dependency-spec/index.html#-**********%2FMain%2F-**********">PluginDependencySpec</a></div><div class="brief "><p class="paragraph">The builtin Gradle plugin implemented by <span data-unresolved-link="org.gradle.api.plugins.scala/ScalaPlugin///PointingToDeclaration/">org.gradle.api.plugins.scala.ScalaPlugin</span>.</p></div></div>                  </div>
                </div>
              </div>
            </div>
          </div>
<a data-name="1728279028%2FProperties%2F-**********" anchor-label="scala-base" id="1728279028%2FProperties%2F-**********" data-filterable-set=":docs/kotlin_dsl"></a>
          <div class="table-row" data-togglable="EXTENSION_PROPERTY" data-filterable-current=":docs/kotlin_dsl" data-filterable-set=":docs/kotlin_dsl">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="../scala-base.html"><span><span>scala-base</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="1728279028%2FProperties%2F-**********"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
                    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/kotlin_dsl" data-filterable-set=":docs/kotlin_dsl" data-active="" data-toggle=":docs/kotlin_dsl">DSL</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/kotlin_dsl"><div class="symbol monospace"><span class="token keyword"></span><span class="token keyword">val </span><a href="../../org.gradle.plugin.use/-plugin-dependencies-spec/index.html#552792532%2FMain%2F-**********">PluginDependenciesSpec</a><span class="token punctuation">.</span><a href="../scala-base.html">scala-base</a><span class="token operator">: </span><a href="../../org.gradle.plugin.use/-plugin-dependency-spec/index.html#-**********%2FMain%2F-**********">PluginDependencySpec</a></div><div class="brief "><p class="paragraph">The builtin Gradle plugin implemented by <span data-unresolved-link="org.gradle.api.plugins.scala/ScalaBasePlugin///PointingToDeclaration/">org.gradle.api.plugins.scala.ScalaBasePlugin</span>.</p></div></div>                  </div>
                </div>
              </div>
            </div>
          </div>
<a data-name="-1400752383%2FProperties%2F-**********" anchor-label="signing" id="-1400752383%2FProperties%2F-**********" data-filterable-set=":docs/kotlin_dsl"></a>
          <div class="table-row" data-togglable="EXTENSION_PROPERTY" data-filterable-current=":docs/kotlin_dsl" data-filterable-set=":docs/kotlin_dsl">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="../signing.html"><span><span>signing</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="-1400752383%2FProperties%2F-**********"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
                    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/kotlin_dsl" data-filterable-set=":docs/kotlin_dsl" data-active="" data-toggle=":docs/kotlin_dsl">DSL</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/kotlin_dsl"><div class="symbol monospace"><span class="token keyword"></span><span class="token keyword">val </span><a href="../../org.gradle.plugin.use/-plugin-dependencies-spec/index.html#552792532%2FMain%2F-**********">PluginDependenciesSpec</a><span class="token punctuation">.</span><a href="../signing.html">signing</a><span class="token operator">: </span><a href="../../org.gradle.plugin.use/-plugin-dependency-spec/index.html#-**********%2FMain%2F-**********">PluginDependencySpec</a></div><div class="brief "><p class="paragraph">The builtin Gradle plugin implemented by <span data-unresolved-link="org.gradle.plugins.signing/SigningPlugin///PointingToDeclaration/">org.gradle.plugins.signing.SigningPlugin</span>.</p></div></div>                  </div>
                </div>
              </div>
            </div>
          </div>
<a data-name="642168821%2FProperties%2F-**********" anchor-label="standard-tool-chains" id="642168821%2FProperties%2F-**********" data-filterable-set=":docs/kotlin_dsl"></a>
          <div class="table-row" data-togglable="EXTENSION_PROPERTY" data-filterable-current=":docs/kotlin_dsl" data-filterable-set=":docs/kotlin_dsl">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="../standard-tool-chains.html"><span><span>standard-tool-chains</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="642168821%2FProperties%2F-**********"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
                    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/kotlin_dsl" data-filterable-set=":docs/kotlin_dsl" data-active="" data-toggle=":docs/kotlin_dsl">DSL</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/kotlin_dsl"><div class="symbol monospace"><span class="token keyword"></span><span class="token keyword">val </span><a href="../../org.gradle.plugin.use/-plugin-dependencies-spec/index.html#552792532%2FMain%2F-**********">PluginDependenciesSpec</a><span class="token punctuation">.</span><a href="../standard-tool-chains.html">standard-tool-chains</a><span class="token operator">: </span><a href="../../org.gradle.plugin.use/-plugin-dependency-spec/index.html#-**********%2FMain%2F-**********">PluginDependencySpec</a></div><div class="brief "><p class="paragraph">The builtin Gradle plugin implemented by <span data-unresolved-link="org.gradle.nativeplatform.toolchain.internal.plugins/StandardToolChainsPlugin///PointingToDeclaration/">org.gradle.nativeplatform.toolchain.internal.plugins.StandardToolChainsPlugin</span>.</p></div></div>                  </div>
                </div>
              </div>
            </div>
          </div>
<a data-name="-672284208%2FProperties%2F-**********" anchor-label="swift-application" id="-672284208%2FProperties%2F-**********" data-filterable-set=":docs/kotlin_dsl"></a>
          <div class="table-row" data-togglable="EXTENSION_PROPERTY" data-filterable-current=":docs/kotlin_dsl" data-filterable-set=":docs/kotlin_dsl">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="../swift-application.html"><span><span>swift-application</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="-672284208%2FProperties%2F-**********"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
                    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/kotlin_dsl" data-filterable-set=":docs/kotlin_dsl" data-active="" data-toggle=":docs/kotlin_dsl">DSL</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/kotlin_dsl"><div class="symbol monospace"><span class="token keyword"></span><span class="token keyword">val </span><a href="../../org.gradle.plugin.use/-plugin-dependencies-spec/index.html#552792532%2FMain%2F-**********">PluginDependenciesSpec</a><span class="token punctuation">.</span><a href="../swift-application.html">swift-application</a><span class="token operator">: </span><a href="../../org.gradle.plugin.use/-plugin-dependency-spec/index.html#-**********%2FMain%2F-**********">PluginDependencySpec</a></div><div class="brief "><p class="paragraph">The builtin Gradle plugin implemented by <span data-unresolved-link="org.gradle.language.swift.plugins/SwiftApplicationPlugin///PointingToDeclaration/">org.gradle.language.swift.plugins.SwiftApplicationPlugin</span>.</p></div></div>                  </div>
                </div>
              </div>
            </div>
          </div>
<a data-name="-970944315%2FProperties%2F-**********" anchor-label="swift-library" id="-970944315%2FProperties%2F-**********" data-filterable-set=":docs/kotlin_dsl"></a>
          <div class="table-row" data-togglable="EXTENSION_PROPERTY" data-filterable-current=":docs/kotlin_dsl" data-filterable-set=":docs/kotlin_dsl">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="../swift-library.html"><span><span>swift-library</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="-970944315%2FProperties%2F-**********"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
                    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/kotlin_dsl" data-filterable-set=":docs/kotlin_dsl" data-active="" data-toggle=":docs/kotlin_dsl">DSL</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/kotlin_dsl"><div class="symbol monospace"><span class="token keyword"></span><span class="token keyword">val </span><a href="../../org.gradle.plugin.use/-plugin-dependencies-spec/index.html#552792532%2FMain%2F-**********">PluginDependenciesSpec</a><span class="token punctuation">.</span><a href="../swift-library.html">swift-library</a><span class="token operator">: </span><a href="../../org.gradle.plugin.use/-plugin-dependency-spec/index.html#-**********%2FMain%2F-**********">PluginDependencySpec</a></div><div class="brief "><p class="paragraph">The builtin Gradle plugin implemented by <span data-unresolved-link="org.gradle.language.swift.plugins/SwiftLibraryPlugin///PointingToDeclaration/">org.gradle.language.swift.plugins.SwiftLibraryPlugin</span>.</p></div></div>                  </div>
                </div>
              </div>
            </div>
          </div>
<a data-name="1156362811%2FProperties%2F-**********" anchor-label="swiftpm-export" id="1156362811%2FProperties%2F-**********" data-filterable-set=":docs/kotlin_dsl"></a>
          <div class="table-row" data-togglable="EXTENSION_PROPERTY" data-filterable-current=":docs/kotlin_dsl" data-filterable-set=":docs/kotlin_dsl">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="../swiftpm-export.html"><span><span>swiftpm-export</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="1156362811%2FProperties%2F-**********"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
                    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/kotlin_dsl" data-filterable-set=":docs/kotlin_dsl" data-active="" data-toggle=":docs/kotlin_dsl">DSL</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/kotlin_dsl"><div class="symbol monospace"><span class="token keyword"></span><span class="token keyword">val </span><a href="../../org.gradle.plugin.use/-plugin-dependencies-spec/index.html#552792532%2FMain%2F-**********">PluginDependenciesSpec</a><span class="token punctuation">.</span><a href="../swiftpm-export.html">swiftpm-export</a><span class="token operator">: </span><a href="../../org.gradle.plugin.use/-plugin-dependency-spec/index.html#-**********%2FMain%2F-**********">PluginDependencySpec</a></div><div class="brief "><p class="paragraph">The builtin Gradle plugin implemented by <span data-unresolved-link="org.gradle.swiftpm.plugins/SwiftPackageManagerExportPlugin///PointingToDeclaration/">org.gradle.swiftpm.plugins.SwiftPackageManagerExportPlugin</span>.</p></div></div>                  </div>
                </div>
              </div>
            </div>
          </div>
<a data-name="1839480962%2FProperties%2F-**********" anchor-label="test-report-aggregation" id="1839480962%2FProperties%2F-**********" data-filterable-set=":docs/kotlin_dsl"></a>
          <div class="table-row" data-togglable="EXTENSION_PROPERTY" data-filterable-current=":docs/kotlin_dsl" data-filterable-set=":docs/kotlin_dsl">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="../test-report-aggregation.html"><span><span>test-report-aggregation</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="1839480962%2FProperties%2F-**********"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
                    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/kotlin_dsl" data-filterable-set=":docs/kotlin_dsl" data-active="" data-toggle=":docs/kotlin_dsl">DSL</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/kotlin_dsl"><div class="symbol monospace"><span class="token keyword"></span><span class="token keyword">val </span><a href="../../org.gradle.plugin.use/-plugin-dependencies-spec/index.html#552792532%2FMain%2F-**********">PluginDependenciesSpec</a><span class="token punctuation">.</span><a href="../test-report-aggregation.html">test-report-aggregation</a><span class="token operator">: </span><a href="../../org.gradle.plugin.use/-plugin-dependency-spec/index.html#-**********%2FMain%2F-**********">PluginDependencySpec</a></div><div class="brief "><p class="paragraph">The builtin Gradle plugin implemented by <span data-unresolved-link="org.gradle.api.plugins/TestReportAggregationPlugin///PointingToDeclaration/">org.gradle.api.plugins.TestReportAggregationPlugin</span>.</p></div></div>                  </div>
                </div>
              </div>
            </div>
          </div>
<a data-name="-1478796315%2FProperties%2F-**********" anchor-label="test-suite-base" id="-1478796315%2FProperties%2F-**********" data-filterable-set=":docs/kotlin_dsl"></a>
          <div class="table-row" data-togglable="EXTENSION_PROPERTY" data-filterable-current=":docs/kotlin_dsl" data-filterable-set=":docs/kotlin_dsl">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="../test-suite-base.html"><span><span>test-suite-base</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="-1478796315%2FProperties%2F-**********"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
                    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/kotlin_dsl" data-filterable-set=":docs/kotlin_dsl" data-active="" data-toggle=":docs/kotlin_dsl">DSL</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/kotlin_dsl"><div class="symbol monospace"><span class="token keyword"></span><span class="token keyword">val </span><a href="../../org.gradle.plugin.use/-plugin-dependencies-spec/index.html#552792532%2FMain%2F-**********">PluginDependenciesSpec</a><span class="token punctuation">.</span><a href="../test-suite-base.html">test-suite-base</a><span class="token operator">: </span><a href="../../org.gradle.plugin.use/-plugin-dependency-spec/index.html#-**********%2FMain%2F-**********">PluginDependencySpec</a></div><div class="brief "><p class="paragraph">The builtin Gradle plugin implemented by <span data-unresolved-link="org.gradle.testing.base.plugins/TestSuiteBasePlugin///PointingToDeclaration/">org.gradle.testing.base.plugins.TestSuiteBasePlugin</span>.</p></div></div>                  </div>
                </div>
              </div>
            </div>
          </div>
<a data-name="200739394%2FProperties%2F-**********" anchor-label="version-catalog" id="200739394%2FProperties%2F-**********" data-filterable-set=":docs/kotlin_dsl"></a>
          <div class="table-row" data-togglable="EXTENSION_PROPERTY" data-filterable-current=":docs/kotlin_dsl" data-filterable-set=":docs/kotlin_dsl">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="../version-catalog.html"><span><span>version-catalog</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="200739394%2FProperties%2F-**********"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
                    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/kotlin_dsl" data-filterable-set=":docs/kotlin_dsl" data-active="" data-toggle=":docs/kotlin_dsl">DSL</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/kotlin_dsl"><div class="symbol monospace"><span class="token keyword"></span><span class="token keyword">val </span><a href="../../org.gradle.plugin.use/-plugin-dependencies-spec/index.html#552792532%2FMain%2F-**********">PluginDependenciesSpec</a><span class="token punctuation">.</span><a href="../version-catalog.html">version-catalog</a><span class="token operator">: </span><a href="../../org.gradle.plugin.use/-plugin-dependency-spec/index.html#-**********%2FMain%2F-**********">PluginDependencySpec</a></div><div class="brief "><p class="paragraph">The builtin Gradle plugin implemented by <span data-unresolved-link="org.gradle.api.plugins.catalog/VersionCatalogPlugin///PointingToDeclaration/">org.gradle.api.plugins.catalog.VersionCatalogPlugin</span>.</p></div></div>                  </div>
                </div>
              </div>
            </div>
          </div>
<a data-name="-1108135933%2FProperties%2F-**********" anchor-label="visual-studio" id="-1108135933%2FProperties%2F-**********" data-filterable-set=":docs/kotlin_dsl"></a>
          <div class="table-row" data-togglable="EXTENSION_PROPERTY" data-filterable-current=":docs/kotlin_dsl" data-filterable-set=":docs/kotlin_dsl">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="../visual-studio.html"><span><span>visual-studio</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="-1108135933%2FProperties%2F-**********"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
                    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/kotlin_dsl" data-filterable-set=":docs/kotlin_dsl" data-active="" data-toggle=":docs/kotlin_dsl">DSL</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/kotlin_dsl"><div class="symbol monospace"><span class="token keyword"></span><span class="token keyword">val </span><a href="../../org.gradle.plugin.use/-plugin-dependencies-spec/index.html#552792532%2FMain%2F-**********">PluginDependenciesSpec</a><span class="token punctuation">.</span><a href="../visual-studio.html">visual-studio</a><span class="token operator">: </span><a href="../../org.gradle.plugin.use/-plugin-dependency-spec/index.html#-**********%2FMain%2F-**********">PluginDependencySpec</a></div><div class="brief "><p class="paragraph">The builtin Gradle plugin implemented by <span data-unresolved-link="org.gradle.ide.visualstudio.plugins/VisualStudioPlugin///PointingToDeclaration/">org.gradle.ide.visualstudio.plugins.VisualStudioPlugin</span>.</p></div></div>                  </div>
                </div>
              </div>
            </div>
          </div>
<a data-name="187284862%2FProperties%2F-**********" anchor-label="war" id="187284862%2FProperties%2F-**********" data-filterable-set=":docs/kotlin_dsl"></a>
          <div class="table-row" data-togglable="EXTENSION_PROPERTY" data-filterable-current=":docs/kotlin_dsl" data-filterable-set=":docs/kotlin_dsl">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="../war.html"><span><span>war</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="187284862%2FProperties%2F-**********"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
                    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/kotlin_dsl" data-filterable-set=":docs/kotlin_dsl" data-active="" data-toggle=":docs/kotlin_dsl">DSL</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/kotlin_dsl"><div class="symbol monospace"><span class="token keyword"></span><span class="token keyword">val </span><a href="../../org.gradle.plugin.use/-plugin-dependencies-spec/index.html#552792532%2FMain%2F-**********">PluginDependenciesSpec</a><span class="token punctuation">.</span><a href="../war.html">war</a><span class="token operator">: </span><a href="../../org.gradle.plugin.use/-plugin-dependency-spec/index.html#-**********%2FMain%2F-**********">PluginDependencySpec</a></div><div class="brief "><p class="paragraph">The builtin Gradle plugin implemented by <span data-unresolved-link="org.gradle.api.plugins/WarPlugin///PointingToDeclaration/">org.gradle.api.plugins.WarPlugin</span>.</p></div></div>                  </div>
                </div>
              </div>
            </div>
          </div>
<a data-name="545303238%2FProperties%2F-**********" anchor-label="windows-resource-script" id="545303238%2FProperties%2F-**********" data-filterable-set=":docs/kotlin_dsl"></a>
          <div class="table-row" data-togglable="EXTENSION_PROPERTY" data-filterable-current=":docs/kotlin_dsl" data-filterable-set=":docs/kotlin_dsl">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="../windows-resource-script.html"><span><span>windows-resource-script</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="545303238%2FProperties%2F-**********"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
                    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/kotlin_dsl" data-filterable-set=":docs/kotlin_dsl" data-active="" data-toggle=":docs/kotlin_dsl">DSL</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/kotlin_dsl"><div class="symbol monospace"><span class="token keyword"></span><span class="token keyword">val </span><a href="../../org.gradle.plugin.use/-plugin-dependencies-spec/index.html#552792532%2FMain%2F-**********">PluginDependenciesSpec</a><span class="token punctuation">.</span><a href="../windows-resource-script.html">windows-resource-script</a><span class="token operator">: </span><a href="../../org.gradle.plugin.use/-plugin-dependency-spec/index.html#-**********%2FMain%2F-**********">PluginDependencySpec</a></div><div class="brief "><p class="paragraph">The builtin Gradle plugin implemented by <span data-unresolved-link="org.gradle.language.rc.plugins/WindowsResourceScriptPlugin///PointingToDeclaration/">org.gradle.language.rc.plugins.WindowsResourceScriptPlugin</span>.</p></div></div>                  </div>
                </div>
              </div>
            </div>
          </div>
<a data-name="-1935216085%2FProperties%2F-**********" anchor-label="windows-resources" id="-1935216085%2FProperties%2F-**********" data-filterable-set=":docs/kotlin_dsl"></a>
          <div class="table-row" data-togglable="EXTENSION_PROPERTY" data-filterable-current=":docs/kotlin_dsl" data-filterable-set=":docs/kotlin_dsl">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="../windows-resources.html"><span><span>windows-resources</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="-1935216085%2FProperties%2F-**********"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
                    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/kotlin_dsl" data-filterable-set=":docs/kotlin_dsl" data-active="" data-toggle=":docs/kotlin_dsl">DSL</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/kotlin_dsl"><div class="symbol monospace"><span class="token keyword"></span><span class="token keyword">val </span><a href="../../org.gradle.plugin.use/-plugin-dependencies-spec/index.html#552792532%2FMain%2F-**********">PluginDependenciesSpec</a><span class="token punctuation">.</span><a href="../windows-resources.html">windows-resources</a><span class="token operator">: </span><a href="../../org.gradle.plugin.use/-plugin-dependency-spec/index.html#-**********%2FMain%2F-**********">PluginDependencySpec</a></div><div class="brief "><p class="paragraph">The builtin Gradle plugin implemented by <span data-unresolved-link="org.gradle.language.rc.plugins/WindowsResourcesPlugin///PointingToDeclaration/">org.gradle.language.rc.plugins.WindowsResourcesPlugin</span>.</p></div></div>                  </div>
                </div>
              </div>
            </div>
          </div>
<a data-name="2056663091%2FProperties%2F-**********" anchor-label="wrapper" id="2056663091%2FProperties%2F-**********" data-filterable-set=":docs/kotlin_dsl"></a>
          <div class="table-row" data-togglable="EXTENSION_PROPERTY" data-filterable-current=":docs/kotlin_dsl" data-filterable-set=":docs/kotlin_dsl">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="../wrapper.html"><span><span>wrapper</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="2056663091%2FProperties%2F-**********"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
                    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/kotlin_dsl" data-filterable-set=":docs/kotlin_dsl" data-active="" data-toggle=":docs/kotlin_dsl">DSL</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/kotlin_dsl"><div class="symbol monospace"><span class="token keyword"></span><span class="token keyword">val </span><a href="../../org.gradle.plugin.use/-plugin-dependencies-spec/index.html#552792532%2FMain%2F-**********">PluginDependenciesSpec</a><span class="token punctuation">.</span><a href="../wrapper.html">wrapper</a><span class="token operator">: </span><a href="../../org.gradle.plugin.use/-plugin-dependency-spec/index.html#-**********%2FMain%2F-**********">PluginDependencySpec</a></div><div class="brief "><p class="paragraph">The builtin Gradle plugin implemented by <span data-unresolved-link="org.gradle.buildinit.plugins/WrapperPlugin///PointingToDeclaration/">org.gradle.buildinit.plugins.WrapperPlugin</span>.</p></div></div>                  </div>
                </div>
              </div>
            </div>
          </div>
<a data-name="-1078264607%2FProperties%2F-**********" anchor-label="xcode" id="-1078264607%2FProperties%2F-**********" data-filterable-set=":docs/kotlin_dsl"></a>
          <div class="table-row" data-togglable="EXTENSION_PROPERTY" data-filterable-current=":docs/kotlin_dsl" data-filterable-set=":docs/kotlin_dsl">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="../xcode.html"><span><span>xcode</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="-1078264607%2FProperties%2F-**********"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
                    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/kotlin_dsl" data-filterable-set=":docs/kotlin_dsl" data-active="" data-toggle=":docs/kotlin_dsl">DSL</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/kotlin_dsl"><div class="symbol monospace"><span class="token keyword"></span><span class="token keyword">val </span><a href="../../org.gradle.plugin.use/-plugin-dependencies-spec/index.html#552792532%2FMain%2F-**********">PluginDependenciesSpec</a><span class="token punctuation">.</span><a href="../xcode.html">xcode</a><span class="token operator">: </span><a href="../../org.gradle.plugin.use/-plugin-dependency-spec/index.html#-**********%2FMain%2F-**********">PluginDependencySpec</a></div><div class="brief "><p class="paragraph">The builtin Gradle plugin implemented by <span data-unresolved-link="org.gradle.ide.xcode.plugins/XcodePlugin///PointingToDeclaration/">org.gradle.ide.xcode.plugins.XcodePlugin</span>.</p></div></div>                  </div>
                </div>
              </div>
            </div>
          </div>
<a data-name="461421455%2FProperties%2F-**********" anchor-label="xctest" id="461421455%2FProperties%2F-**********" data-filterable-set=":docs/kotlin_dsl"></a>
          <div class="table-row" data-togglable="EXTENSION_PROPERTY" data-filterable-current=":docs/kotlin_dsl" data-filterable-set=":docs/kotlin_dsl">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="../xctest.html"><span><span>xctest</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="461421455%2FProperties%2F-**********"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
                    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/kotlin_dsl" data-filterable-set=":docs/kotlin_dsl" data-active="" data-toggle=":docs/kotlin_dsl">DSL</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/kotlin_dsl"><div class="symbol monospace"><span class="token keyword"></span><span class="token keyword">val </span><a href="../../org.gradle.plugin.use/-plugin-dependencies-spec/index.html#552792532%2FMain%2F-**********">PluginDependenciesSpec</a><span class="token punctuation">.</span><a href="../xctest.html">xctest</a><span class="token operator">: </span><a href="../../org.gradle.plugin.use/-plugin-dependency-spec/index.html#-**********%2FMain%2F-**********">PluginDependencySpec</a></div><div class="brief "><p class="paragraph">The builtin Gradle plugin implemented by <span data-unresolved-link="org.gradle.nativeplatform.test.xctest.plugins/XCTestConventionPlugin///PointingToDeclaration/">org.gradle.nativeplatform.test.xctest.plugins.XCTestConventionPlugin</span>.</p></div></div>                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div data-togglable="FUNCTION">
        <h2 class="">Functions</h2>
        <div class="table"><a data-name="1908226419%2FFunctions%2F-**********" anchor-label="alias" id="1908226419%2FFunctions%2F-**********" data-filterable-set=":docs/kotlin_dsl"></a>
          <div class="table-row" data-filterable-current=":docs/kotlin_dsl" data-filterable-set=":docs/kotlin_dsl">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="alias.html"><span><span>alias</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="1908226419%2FFunctions%2F-**********"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
                    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/kotlin_dsl" data-filterable-set=":docs/kotlin_dsl" data-active="" data-toggle=":docs/kotlin_dsl">DSL</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/kotlin_dsl"><div class="symbol monospace"><span class="token keyword">open </span><span class="token keyword">override </span><span class="token keyword">fun </span><a href="alias.html"><span class="token function">alias</span></a><span class="token punctuation">(</span><span class="parameters "><span class="parameter ">notation<span class="token operator">: </span><span data-unresolved-link="org.gradle.api.provider/ProviderConvertible///PointingToDeclaration/">ProviderConvertible</span><span class="token operator">&lt;</span><span class="token keyword"></span><span data-unresolved-link="org.gradle.plugin.use/PluginDependency///PointingToDeclaration/">PluginDependency</span><span class="token operator">&gt;</span></span></span><span class="token punctuation">)</span><span class="token operator">: </span><a href="../../org.gradle.plugin.use/-plugin-dependency-spec/index.html#-**********%2FMain%2F-**********">PluginDependencySpec</a></div><div class="symbol monospace"><span class="token keyword">open </span><span class="token keyword">override </span><span class="token keyword">fun </span><a href="alias.html"><span class="token function">alias</span></a><span class="token punctuation">(</span><span class="parameters "><span class="parameter ">notation<span class="token operator">: </span><span data-unresolved-link="org.gradle.api.provider/Provider///PointingToDeclaration/">Provider</span><span class="token operator">&lt;</span><span class="token keyword"></span><span data-unresolved-link="org.gradle.plugin.use/PluginDependency///PointingToDeclaration/">PluginDependency</span><span class="token operator">&gt;</span></span></span><span class="token punctuation">)</span><span class="token operator">: </span><a href="../../org.gradle.plugin.use/-plugin-dependency-spec/index.html#-**********%2FMain%2F-**********">PluginDependencySpec</a></div></div>                  </div>
                </div>
              </div>
            </div>
          </div>
<a data-name="919954307%2FFunctions%2F-**********" anchor-label="embeddedKotlin" id="919954307%2FFunctions%2F-**********" data-filterable-set=":docs/kotlin_dsl"></a>
          <div class="table-row" data-togglable="EXTENSION_FUNCTION" data-filterable-current=":docs/kotlin_dsl" data-filterable-set=":docs/kotlin_dsl">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="../embedded-kotlin.html"><span>embedded</span><wbr></wbr><span><span>Kotlin</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="919954307%2FFunctions%2F-**********"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
                    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/kotlin_dsl" data-filterable-set=":docs/kotlin_dsl" data-active="" data-toggle=":docs/kotlin_dsl">DSL</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/kotlin_dsl"><div class="symbol monospace"><div class="block"><div class="block"><span class="token annotation builtin">@</span><span data-unresolved-link="org.gradle.api/Incubating///PointingToDeclaration/"><span class="token annotation builtin">Incubating</span></span></div></div><span class="token keyword"></span><span class="token keyword">fun </span><a href="../../org.gradle.plugin.use/-plugin-dependencies-spec/index.html#552792532%2FMain%2F-**********">PluginDependenciesSpec</a><span class="token punctuation">.</span><a href="../embedded-kotlin.html"><span class="token function">embeddedKotlin</span></a><span class="token punctuation">(</span><span class="parameters "><span class="parameter ">module<span class="token operator">: </span><a href="https://kotlinlang.org/api/latest/jvm/stdlib/kotlin/-string/index.html">String</a></span></span><span class="token punctuation">)</span><span class="token operator">: </span><a href="../../org.gradle.plugin.use/-plugin-dependency-spec/index.html#-**********%2FMain%2F-**********">PluginDependencySpec</a></div><div class="brief "><p class="paragraph">Applies the given Kotlin plugin <a href="../embedded-kotlin.html">module</a> at the embedded version (currently <i>1.9.20</i>).</p></div></div>                  </div>
                </div>
              </div>
            </div>
          </div>
<a data-name="452416118%2FFunctions%2F-**********" anchor-label="id" id="452416118%2FFunctions%2F-**********" data-filterable-set=":docs/kotlin_dsl"></a>
          <div class="table-row" data-filterable-current=":docs/kotlin_dsl" data-filterable-set=":docs/kotlin_dsl">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="id.html"><span><span>id</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="452416118%2FFunctions%2F-**********"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
                    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/kotlin_dsl" data-filterable-set=":docs/kotlin_dsl" data-active="" data-toggle=":docs/kotlin_dsl">DSL</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/kotlin_dsl"><div class="symbol monospace"><span class="token keyword">open </span><span class="token keyword">override </span><span class="token keyword">fun </span><a href="id.html"><span class="token function">id</span></a><span class="token punctuation">(</span><span class="parameters "><span class="parameter ">id<span class="token operator">: </span><a href="https://kotlinlang.org/api/latest/jvm/stdlib/kotlin/-string/index.html">String</a></span></span><span class="token punctuation">)</span><span class="token operator">: </span><a href="../../org.gradle.plugin.use/-plugin-dependency-spec/index.html#-**********%2FMain%2F-**********">PluginDependencySpec</a></div></div>                  </div>
                </div>
              </div>
            </div>
          </div>
<a data-name="-753815591%2FFunctions%2F-**********" anchor-label="kotlin" id="-753815591%2FFunctions%2F-**********" data-filterable-set=":docs/kotlin_dsl"></a>
          <div class="table-row" data-togglable="EXTENSION_FUNCTION" data-filterable-current=":docs/kotlin_dsl" data-filterable-set=":docs/kotlin_dsl">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="../kotlin.html"><span><span>kotlin</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="-753815591%2FFunctions%2F-**********"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
                    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/kotlin_dsl" data-filterable-set=":docs/kotlin_dsl" data-active="" data-toggle=":docs/kotlin_dsl">DSL</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/kotlin_dsl"><div class="symbol monospace"><span class="token keyword"></span><span class="token keyword">fun </span><a href="../../org.gradle.plugin.use/-plugin-dependencies-spec/index.html#552792532%2FMain%2F-**********">PluginDependenciesSpec</a><span class="token punctuation">.</span><a href="../kotlin.html"><span class="token function">kotlin</span></a><span class="token punctuation">(</span><span class="parameters "><span class="parameter ">module<span class="token operator">: </span><a href="https://kotlinlang.org/api/latest/jvm/stdlib/kotlin/-string/index.html">String</a></span></span><span class="token punctuation">)</span><span class="token operator">: </span><a href="../../org.gradle.plugin.use/-plugin-dependency-spec/index.html#-**********%2FMain%2F-**********">PluginDependencySpec</a></div><div class="brief "><p class="paragraph">Applies the given Kotlin plugin <a href="../kotlin.html">module</a>.</p></div></div>                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
      <div class="footer">
        <span class="go-to-top-icon"><a href="#content" id="go-to-top-link"></a></span><span>Gradle Kotlin DSL Reference</span><span
                class="pull-right"><span>Generated by </span><a
                href="https://github.com/Kotlin/dokka"><span>dokka</span><span class="padded-icon"></span></a></span>
      </div>
    </div>
</div>
</body>
</html>
