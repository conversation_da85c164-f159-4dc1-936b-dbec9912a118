<!DOCTYPE html>
<html>
<head>
    <meta name="viewport" content="width=device-width, initial-scale=1" charset="UTF-8">
    <title>exclude</title>
    <link href="../../images/logo-icon.svg" rel="icon" type="image/svg">
    <script>var pathToRoot = "../../";</script>
    <script>const storage = localStorage.getItem("dokka-dark-mode")
    if (storage == null) {
        const osDarkSchemePreferred = window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches
        if (osDarkSchemePreferred === true) {
            document.getElementsByTagName("html")[0].classList.add("theme-dark")
        }
    } else {
        const savedDarkMode = JSON.parse(storage)
        if(savedDarkMode === true) {
            document.getElementsByTagName("html")[0].classList.add("theme-dark")
        }
    }
    </script>
<script type="text/javascript" src="../../scripts/sourceset_dependencies.js" async="async"></script>
<link href="../../styles/style.css" rel="Stylesheet">
<link href="../../styles/jetbrains-mono.css" rel="Stylesheet">
<link href="../../styles/main.css" rel="Stylesheet">
<link href="../../styles/prism.css" rel="Stylesheet">
<link href="../../styles/logo-styles.css" rel="Stylesheet">
<script type="text/javascript" src="../../scripts/clipboard.js" async="async"></script>
<script type="text/javascript" src="../../scripts/navigation-loader.js" async="async"></script>
<script type="text/javascript" src="../../scripts/platform-content-handler.js" async="async"></script>
<script type="text/javascript" src="../../scripts/main.js" defer="defer"></script>
<script type="text/javascript" src="../../scripts/prism.js" async="async"></script>
<script type="text/javascript" src="../../scripts/symbol-parameters-wrapper_deferred.js" defer="defer"></script>
<link href="../../images/gradle-logo.svg">
<link href="../../styles/gradle.css" rel="Stylesheet">
</head>
<body>
<div class="navigation-wrapper" id="navigation-wrapper">
    <div id="leftToggler"><span class="icon-toggler"></span></div>
    <div class="library-name">
            <a href="../../index.html">
                    <span>gradle</span>
            </a>
    </div>
    <div>
8.5    </div>
    <div class="pull-right d-flex">
        <div class="filter-section" id="filter-section">
                <button class="platform-tag platform-selector jvm-like" data-active="" data-filter=":docs/kotlin_dsl">DSL</button>
        </div>
        <button id="theme-toggle-button"><span id="theme-toggle"></span></button>
        <div id="searchBar"></div>
    </div>
</div>
<div id="container">
    <div id="leftColumn">
        <div id="sideMenu"></div>
    </div>
    <div id="main">
<div class="main-content" data-page-type="member" id="content" pageIds="gradle::org.gradle.kotlin.dsl//exclude/org.gradle.api.artifacts.Configuration#kotlin.String?#kotlin.String?/PointingToDeclaration//-1867656071">
  <div class="breadcrumbs"><a href="../../index.html">gradle</a><span class="delimiter">/</span><a href="index.html">org.gradle.kotlin.dsl</a><span class="delimiter">/</span><span class="current">exclude</span></div>
  <div class="cover ">
    <h1 class="cover"><span><span>exclude</span></span></h1>
  </div>
  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/kotlin_dsl" data-filterable-set=":docs/kotlin_dsl" data-active="" data-toggle=":docs/kotlin_dsl">DSL</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/kotlin_dsl"><div class="symbol monospace"><span class="token keyword"></span><span class="token keyword">fun </span><a href="../org.gradle.api.artifacts/-configuration/index.html#-1825876070%2FMain%2F-1867656071">Configuration</a><span class="token punctuation">.</span><a href="exclude.html"><span class="token function">exclude</span></a><span class="token punctuation">(</span><span class="parameters "><span class="parameter ">group<span class="token operator">: </span><a href="https://kotlinlang.org/api/latest/jvm/stdlib/kotlin/-string/index.html">String</a><span class="token operator">?</span><span class="token operator"> = </span>null<span class="token punctuation">, </span></span><span class="parameter ">module<span class="token operator">: </span><a href="https://kotlinlang.org/api/latest/jvm/stdlib/kotlin/-string/index.html">String</a><span class="token operator">?</span><span class="token operator"> = </span>null</span></span><span class="token punctuation">)</span><span class="token operator">: </span><a href="../org.gradle.api.artifacts/-configuration/index.html#-1825876070%2FMain%2F-1867656071">Configuration</a><span class="clearfix"><span class="floating-right">(<a href="https://github.com/gradle/gradle/blob/28aca86a7180baa17117e0e5ba01d8ea9feca598/platforms/core-configuration/kotlin-dsl/src/main/kotlin/org/gradle/kotlin/dsl/ConfigurationExtensions.kt#L33">source</a>)</span></span></div><p class="paragraph">Adds an exclude rule to exclude transitive dependencies for all dependencies of this configuration. You can also add exclude rules per-dependency. See <span data-unresolved-link="org.gradle.api.artifacts/ModuleDependency/exclude/#kotlin.collections.MutableMap[kotlin.String,kotlin.String]/PointingToDeclaration/">ModuleDependency.exclude</span>.</p><span class="kdoc-tag"><h4 class="">Return</h4><p class="paragraph">this</p></span><h4 class="">Parameters</h4><div class="table"><div class="table-row" data-filterable-current=":docs/kotlin_dsl" data-filterable-set=":docs/kotlin_dsl"><div class="main-subrow keyValue "><div class=""><span class="inline-flex"><div><u><span><span>group</span></span></u></div></span></div><div><div class="title"><p class="paragraph">the optional group identifying the dependencies to be excluded.</p></div></div></div></div><div class="table-row" data-filterable-current=":docs/kotlin_dsl" data-filterable-set=":docs/kotlin_dsl"><div class="main-subrow keyValue "><div class=""><span class="inline-flex"><div><u><span><span>module</span></span></u></div></span></div><div><div class="title"><p class="paragraph">the optional module name identifying the dependencies to be excluded.</p></div></div></div></div></div><hr><div class="symbol monospace"><span class="token keyword"></span><span class="token keyword">fun </span><span class="token operator">&lt;</span><span class="token keyword"></span><a href="exclude.html">T</a><span class="token operator"> : </span><span data-unresolved-link="org.gradle.api.artifacts/ModuleDependency///PointingToDeclaration/">ModuleDependency</span><span class="token operator">&gt; </span><a href="exclude.html">T</a><span class="token punctuation">.</span><a href="exclude.html"><span class="token function">exclude</span></a><span class="token punctuation">(</span><span class="parameters "><span class="parameter ">group<span class="token operator">: </span><a href="https://kotlinlang.org/api/latest/jvm/stdlib/kotlin/-string/index.html">String</a><span class="token operator">?</span><span class="token operator"> = </span>null<span class="token punctuation">, </span></span><span class="parameter ">module<span class="token operator">: </span><a href="https://kotlinlang.org/api/latest/jvm/stdlib/kotlin/-string/index.html">String</a><span class="token operator">?</span><span class="token operator"> = </span>null</span></span><span class="token punctuation">)</span><span class="token operator">: </span><a href="exclude.html">T</a><span class="clearfix"><span class="floating-right">(<a href="https://github.com/gradle/gradle/blob/28aca86a7180baa17117e0e5ba01d8ea9feca598/platforms/core-configuration/kotlin-dsl/src/main/kotlin/org/gradle/kotlin/dsl/DependencyHandlerExtensions.kt#L321">source</a>)</span></span></div><p class="paragraph">Adds an exclude rule to exclude transitive dependencies of this dependency.</p><p class="paragraph">Excluding a particular transitive dependency does not guarantee that it does not show up in the dependencies of a given configuration. For example, some other dependency, which does not have any exclude rules, might pull in exactly the same transitive dependency. To guarantee that the transitive dependency is excluded from the entire configuration please use per-configuration exclude rules: <span data-unresolved-link="org.gradle.api.artifacts/Configuration/getExcludeRules/#/PointingToDeclaration/">Configuration.getExcludeRules</span>. In fact, in majority of cases the actual intention of configuring per-dependency exclusions is really excluding a dependency from the entire configuration (or classpath).</p><p class="paragraph">If your intention is to exclude a particular transitive dependency because you don't like the version it pulls in to the configuration then consider using the forced versions feature: <span data-unresolved-link="org.gradle.api.artifacts/ResolutionStrategy/force/#kotlin.Array[kotlin.Any]/PointingToDeclaration/">ResolutionStrategy.force</span>.</p><span class="kdoc-tag"><h4 class="">Return</h4><p class="paragraph">this</p></span><h4 class="">Parameters</h4><div class="table"><div class="table-row" data-filterable-current=":docs/kotlin_dsl" data-filterable-set=":docs/kotlin_dsl"><div class="main-subrow keyValue "><div class=""><span class="inline-flex"><div><u><span><span>group</span></span></u></div></span></div><div><div class="title"><p class="paragraph">the optional group identifying the dependencies to be excluded.</p></div></div></div></div><div class="table-row" data-filterable-current=":docs/kotlin_dsl" data-filterable-set=":docs/kotlin_dsl"><div class="main-subrow keyValue "><div class=""><span class="inline-flex"><div><u><span><span>module</span></span></u></div></span></div><div><div class="title"><p class="paragraph">the optional module name identifying the dependencies to be excluded.</p></div></div></div></div></div><h4 class="">See also</h4><div class="table"><div class="table-row" data-filterable-current=":docs/kotlin_dsl" data-filterable-set=":docs/kotlin_dsl"><div class="main-subrow keyValue "><div class=""><span class="inline-flex"><div><span data-unresolved-link="org.gradle.api.artifacts/ModuleDependency/exclude/#kotlin.collections.MutableMap[kotlin.String,kotlin.String]/PointingToDeclaration/"><span>Module</span><wbr></wbr><span>Dependency.</span><wbr></wbr><span>exclude</span></span></div></span></div><div></div></div></div></div></div>  </div>
</div>
      <div class="footer">
        <span class="go-to-top-icon"><a href="#content" id="go-to-top-link"></a></span><span>Gradle Kotlin DSL Reference</span><span
                class="pull-right"><span>Generated by </span><a
                href="https://github.com/Kotlin/dokka"><span>dokka</span><span class="padded-icon"></span></a></span>
      </div>
    </div>
</div>
</body>
</html>
