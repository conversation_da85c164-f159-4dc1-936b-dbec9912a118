<!DOCTYPE html>
<html>
<head>
    <meta name="viewport" content="width=device-width, initial-scale=1" charset="UTF-8">
    <title>create</title>
    <link href="../../../images/logo-icon.svg" rel="icon" type="image/svg">
    <script>var pathToRoot = "../../../";</script>
    <script>const storage = localStorage.getItem("dokka-dark-mode")
    if (storage == null) {
        const osDarkSchemePreferred = window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches
        if (osDarkSchemePreferred === true) {
            document.getElementsByTagName("html")[0].classList.add("theme-dark")
        }
    } else {
        const savedDarkMode = JSON.parse(storage)
        if(savedDarkMode === true) {
            document.getElementsByTagName("html")[0].classList.add("theme-dark")
        }
    }
    </script>
<script type="text/javascript" src="../../../scripts/sourceset_dependencies.js" async="async"></script>
<link href="../../../styles/style.css" rel="Stylesheet">
<link href="../../../styles/jetbrains-mono.css" rel="Stylesheet">
<link href="../../../styles/main.css" rel="Stylesheet">
<link href="../../../styles/prism.css" rel="Stylesheet">
<link href="../../../styles/logo-styles.css" rel="Stylesheet">
<script type="text/javascript" src="../../../scripts/clipboard.js" async="async"></script>
<script type="text/javascript" src="../../../scripts/navigation-loader.js" async="async"></script>
<script type="text/javascript" src="../../../scripts/platform-content-handler.js" async="async"></script>
<script type="text/javascript" src="../../../scripts/main.js" defer="defer"></script>
<script type="text/javascript" src="../../../scripts/prism.js" async="async"></script>
<script type="text/javascript" src="../../../scripts/symbol-parameters-wrapper_deferred.js" defer="defer"></script>
<link href="../../../images/gradle-logo.svg">
<link href="../../../styles/gradle.css" rel="Stylesheet">
</head>
<body>
<div class="navigation-wrapper" id="navigation-wrapper">
    <div id="leftToggler"><span class="icon-toggler"></span></div>
    <div class="library-name">
            <a href="../../../index.html">
                    <span>gradle</span>
            </a>
    </div>
    <div>
8.5    </div>
    <div class="pull-right d-flex">
        <div class="filter-section" id="filter-section">
                <button class="platform-tag platform-selector jvm-like" data-active="" data-filter=":docs/java_api">API</button>
        </div>
        <button id="theme-toggle-button"><span id="theme-toggle"></span></button>
        <div id="searchBar"></div>
    </div>
</div>
<div id="container">
    <div id="leftColumn">
        <div id="sideMenu"></div>
    </div>
    <div id="main">
<div class="main-content" data-page-type="member" id="content" pageIds="gradle::org.gradle.api.tasks/TaskContainer/create/#java.util.Map&lt;java.lang.String,?&gt;/PointingToDeclaration//-1793262594">
  <div class="breadcrumbs"><a href="../../../index.html">gradle</a><span class="delimiter">/</span><a href="../index.html">org.gradle.api.tasks</a><span class="delimiter">/</span><a href="index.html">TaskContainer</a><span class="delimiter">/</span><span class="current">create</span></div>
  <div class="cover ">
    <h1 class="cover"><span><span>create</span></span></h1>
  </div>
  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api" data-active="" data-toggle=":docs/java_api">API</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/java_api"><div class="symbol monospace"><span class="token keyword">abstract </span><span class="token keyword"></span><span class="token keyword">fun </span><a href="create.html"><span class="token function">create</span></a><span class="token punctuation">(</span><span class="parameters "><span class="parameter ">options<span class="token operator">: </span><a href="https://docs.oracle.com/en/java/javase/11/docs/api/java.base/java/util/Map.html">Map</a><span class="token operator">&lt;</span><a href="https://docs.oracle.com/en/java/javase/11/docs/api/java.base/java/lang/String.html">String</a><span class="token punctuation">, </span><span class="token keyword">out </span><a href="https://kotlinlang.org/api/latest/jvm/stdlib/kotlin/-any/index.html">Any</a><span class="token operator">&gt;</span></span></span><span class="token punctuation">)</span><span class="token operator">: </span><a href="../../org.gradle.api/-task/index.html">Task</a><span class="clearfix"><span class="floating-right">(<a href="https://github.com/gradle/gradle/blob/28aca86a7180baa17117e0e5ba01d8ea9feca598/subprojects/core-api/src/main/java/org/gradle/api/tasks/TaskContainer.java#L108">source</a>)</span></span></div><p class="paragraph">Creates a <a href="../../org.gradle.api/-task/index.html">Task</a> and adds it to this container. A map of creation options can be passed to this method to control how the task is created. The following options are available:</p><table><thead></thead><tbody><tr><td></td><td></td><td></td></tr><tr><td><code class="org.jetbrains.dokka.pages.commenttable@4185ff28 lang-kotlin">{@value org.gradle.api.Task#TASK_NAME}</code></td><td>The name of the task to create.</td><td>None. Must be specified.</td></tr><tr><td><code class="org.jetbrains.dokka.pages.commenttable@4185ff28 lang-kotlin">{@value org.gradle.api.Task#TASK_TYPE}</code></td><td>The class of the task to create.</td><td><a href="../../org.gradle.api/-default-task/index.html">org.gradle.api.DefaultTask</a></td></tr><tr><td><code class="org.jetbrains.dokka.pages.commenttable@4185ff28 lang-kotlin">{@value org.gradle.api.Task#TASK_ACTION}</code></td><td>The closure or <a href="../../org.gradle.api/-action/index.html">Action</a> to execute when the task executes. See <a href="../../org.gradle.api/-task/do-first.html">doFirst</a>.</td><td><code class="org.jetbrains.dokka.pages.commenttable@4185ff28 lang-kotlin">null</code></td></tr><tr><td><code class="org.jetbrains.dokka.pages.commenttable@4185ff28 lang-kotlin">{@value org.gradle.api.Task#TASK_OVERWRITE}</code></td><td>Replace an existing task?</td><td><code class="org.jetbrains.dokka.pages.commenttable@4185ff28 lang-kotlin">false</code></td></tr><tr><td><code class="org.jetbrains.dokka.pages.commenttable@4185ff28 lang-kotlin">{@value org.gradle.api.Task#TASK_DEPENDS_ON}</code></td><td>The dependencies of the task. See <a href="../Task.html#dependencies">here</a> for more details.</td><td><code class="org.jetbrains.dokka.pages.commenttable@4185ff28 lang-kotlin">[]</code></td></tr><tr><td><code class="org.jetbrains.dokka.pages.commenttable@4185ff28 lang-kotlin">{@value org.gradle.api.Task#TASK_GROUP}</code></td><td>The group of the task.</td><td><code class="org.jetbrains.dokka.pages.commenttable@4185ff28 lang-kotlin">null 
     </code></td></tr><tr><td><code class="org.jetbrains.dokka.pages.commenttable@4185ff28 lang-kotlin">{@value org.gradle.api.Task#TASK_DESCRIPTION}</code></td><td>The description of the task.</td><td><code class="org.jetbrains.dokka.pages.commenttable@4185ff28 lang-kotlin">null</code></td></tr><tr><td><code class="org.jetbrains.dokka.pages.commenttable@4185ff28 lang-kotlin">{@value org.gradle.api.Task#TASK_CONSTRUCTOR_ARGS}</code></td><td>The arguments to pass to the task class constructor.</td><td><code class="org.jetbrains.dokka.pages.commenttable@4185ff28 lang-kotlin">null</code></td></tr></tbody></table><p class="paragraph">After the task is added, it is made available as a property of the project, so that you can reference the task by name in your build file.</p><p class="paragraph">If a task with the given name already exists in this container and the <code class="lang-kotlin">{@value org.gradle.api.Task#TASK_OVERWRITE}</code> option is not set to true, an exception is thrown.</p><span class="kdoc-tag"><h4 class="">Return</h4><p class="paragraph">The newly created task object</p></span><h4 class="">Parameters</h4><div class="table"><div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api"><div class="main-subrow keyValue "><div class=""><span class="inline-flex"><div><u><span><span>options</span></span></u></div></span></div><div><div class="title"><p class="paragraph">The task creation options.</p></div></div></div></div></div><h4 class="">See also</h4><div class="table"><div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api"><div class="main-subrow keyValue "><div class=""><span class="inline-flex"><div><a href="../../org.gradle.api/-project/get-properties.html"><span><span>Project</span></span></a></div></span></div><div><div class="title"><p class="paragraph">More information about how tasks are exposed by name in build scripts</p></div></div></div></div></div><h4 class="">Throws</h4><div class="table"><div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api"><div class="main-subrow keyValue "><div class=""><span class="inline-flex"><div><a href="../../org.gradle.api/-invalid-user-data-exception/index.html"><span>Invalid</span><wbr></wbr><span>User</span><wbr></wbr><span>Data</span><wbr></wbr><span><span>Exception</span></span></a></div></span></div><div><div class="title"><p class="paragraph">If a task with the given name already exists in this project.</p></div></div></div></div><div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api"><div class="main-subrow keyValue "><div class=""><span class="inline-flex"><div><a href="https://docs.oracle.com/en/java/javase/11/docs/api/java.base/java/lang/NullPointerException.html"><span>Null</span><wbr></wbr><span>Pointer</span><wbr></wbr><span><span>Exception</span></span></a></div></span></div><div><div class="title"><p class="paragraph">If any of the values in <code class="lang-kotlin">{@value org.gradle.api.Task#TASK_CONSTRUCTOR_ARGS}</code> is null.</p></div></div></div></div></div><hr><div class="symbol monospace"><span class="token keyword">abstract </span><span class="token keyword"></span><span class="token keyword">fun </span><a href="create.html"><span class="token function">create</span></a><span class="token punctuation">(</span><span class="parameters "><span class="parameter ">options<span class="token operator">: </span><a href="https://docs.oracle.com/en/java/javase/11/docs/api/java.base/java/util/Map.html">Map</a><span class="token operator">&lt;</span><a href="https://docs.oracle.com/en/java/javase/11/docs/api/java.base/java/lang/String.html">String</a><span class="token punctuation">, </span><span class="token keyword">out </span><a href="https://kotlinlang.org/api/latest/jvm/stdlib/kotlin/-any/index.html">Any</a><span class="token operator">&gt;</span><span class="token punctuation">, </span></span><span class="parameter ">configureClosure<span class="token operator">: </span><span data-unresolved-link="groovy.lang/Closure///PointingToDeclaration/">Closure</span></span></span><span class="token punctuation">)</span><span class="token operator">: </span><a href="../../org.gradle.api/-task/index.html">Task</a><span class="clearfix"><span class="floating-right">(<a href="https://github.com/gradle/gradle/blob/28aca86a7180baa17117e0e5ba01d8ea9feca598/subprojects/core-api/src/main/java/org/gradle/api/tasks/TaskContainer.java#L124">source</a>)</span></span></div><p class="paragraph">Creates a <a href="../../org.gradle.api/-task/index.html">Task</a> adds it to this container. A map of creation options can be passed to this method to control how the task is created. See <a href="create.html">create</a> for the list of options available. The given closure is used to configure the task before it is returned by this method.</p><p class="paragraph">After the task is added, it is made available as a property of the project, so that you can reference the task by name in your build file.</p><span class="kdoc-tag"><h4 class="">Return</h4><p class="paragraph">The newly created task object</p></span><h4 class="">Parameters</h4><div class="table"><div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api"><div class="main-subrow keyValue "><div class=""><span class="inline-flex"><div><u><span><span>options</span></span></u></div></span></div><div><div class="title"><p class="paragraph">The task creation options.</p></div></div></div></div><div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api"><div class="main-subrow keyValue "><div class=""><span class="inline-flex"><div><u><span>configure</span><wbr></wbr><span><span>Closure</span></span></u></div></span></div><div><div class="title"><p class="paragraph">The closure to use to configure the task.</p></div></div></div></div></div><h4 class="">See also</h4><div class="table"><div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api"><div class="main-subrow keyValue "><div class=""><span class="inline-flex"><div><a href="../../org.gradle.api/-project/get-properties.html"><span><span>Project</span></span></a></div></span></div><div><div class="title"><p class="paragraph">More information about how tasks are exposed by name in build scripts</p></div></div></div></div></div><h4 class="">Throws</h4><div class="table"><div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api"><div class="main-subrow keyValue "><div class=""><span class="inline-flex"><div><a href="../../org.gradle.api/-invalid-user-data-exception/index.html"><span>Invalid</span><wbr></wbr><span>User</span><wbr></wbr><span>Data</span><wbr></wbr><span><span>Exception</span></span></a></div></span></div><div><div class="title"><p class="paragraph">If a task with the given name already exists in this project.</p></div></div></div></div></div><hr><div class="symbol monospace"><span class="token keyword">abstract </span><span class="token keyword"></span><span class="token keyword">fun </span><a href="create.html"><span class="token function">create</span></a><span class="token punctuation">(</span><span class="parameters "><span class="parameter ">name<span class="token operator">: </span><a href="https://docs.oracle.com/en/java/javase/11/docs/api/java.base/java/lang/String.html">String</a><span class="token punctuation">, </span></span><span class="parameter ">configureClosure<span class="token operator">: </span><span data-unresolved-link="groovy.lang/Closure///PointingToDeclaration/">Closure</span></span></span><span class="token punctuation">)</span><span class="token operator">: </span><a href="../../org.gradle.api/-task/index.html">Task</a><span class="clearfix"><span class="floating-right">(<a href="https://github.com/gradle/gradle/blob/28aca86a7180baa17117e0e5ba01d8ea9feca598/subprojects/core-api/src/main/java/org/gradle/api/tasks/TaskContainer.java#L140">source</a>)</span></span></div><p class="paragraph">Creates a <a href="../../org.gradle.api/-task/index.html">Task</a> with the given name adds it to this container. The given closure is used to configure the task before it is returned by this method.</p><p class="paragraph">After the task is added, it is made available as a property of the project, so that you can reference the task by name in your build file.</p><span class="kdoc-tag"><h4 class="">Return</h4><p class="paragraph">The newly created task object</p></span><h4 class="">Parameters</h4><div class="table"><div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api"><div class="main-subrow keyValue "><div class=""><span class="inline-flex"><div><u><span><span>name</span></span></u></div></span></div><div><div class="title"><p class="paragraph">The name of the task to be created</p></div></div></div></div><div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api"><div class="main-subrow keyValue "><div class=""><span class="inline-flex"><div><u><span>configure</span><wbr></wbr><span><span>Closure</span></span></u></div></span></div><div><div class="title"><p class="paragraph">The closure to use to configure the task.</p></div></div></div></div></div><h4 class="">See also</h4><div class="table"><div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api"><div class="main-subrow keyValue "><div class=""><span class="inline-flex"><div><a href="../../org.gradle.api/-project/get-properties.html"><span><span>Project</span></span></a></div></span></div><div><div class="title"><p class="paragraph">More information about how tasks are exposed by name in build scripts</p></div></div></div></div></div><h4 class="">Throws</h4><div class="table"><div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api"><div class="main-subrow keyValue "><div class=""><span class="inline-flex"><div><a href="../../org.gradle.api/-invalid-user-data-exception/index.html"><span>Invalid</span><wbr></wbr><span>User</span><wbr></wbr><span>Data</span><wbr></wbr><span><span>Exception</span></span></a></div></span></div><div><div class="title"><p class="paragraph">If a task with the given name already exists in this project.</p></div></div></div></div></div><hr><div class="symbol monospace"><span class="token keyword">abstract </span><span class="token keyword"></span><span class="token keyword">fun </span><a href="create.html"><span class="token function">create</span></a><span class="token punctuation">(</span><span class="parameters "><span class="parameter ">name<span class="token operator">: </span><a href="https://docs.oracle.com/en/java/javase/11/docs/api/java.base/java/lang/String.html">String</a></span></span><span class="token punctuation">)</span><span class="token operator">: </span><a href="../../org.gradle.api/-task/index.html">Task</a><span class="clearfix"><span class="floating-right">(<a href="https://github.com/gradle/gradle/blob/28aca86a7180baa17117e0e5ba01d8ea9feca598/subprojects/core-api/src/main/java/org/gradle/api/tasks/TaskContainer.java#L154">source</a>)</span></span></div><p class="paragraph">Creates a <a href="../../org.gradle.api/-task/index.html">Task</a> with the given name and adds it to this container.</p><p class="paragraph">After the task is added, it is made available as a property of the project, so that you can reference the task by name in your build file.</p><span class="kdoc-tag"><h4 class="">Return</h4><p class="paragraph">The newly created task object</p></span><h4 class="">Parameters</h4><div class="table"><div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api"><div class="main-subrow keyValue "><div class=""><span class="inline-flex"><div><u><span><span>name</span></span></u></div></span></div><div><div class="title"><p class="paragraph">The name of the task to be created</p></div></div></div></div></div><h4 class="">See also</h4><div class="table"><div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api"><div class="main-subrow keyValue "><div class=""><span class="inline-flex"><div><a href="../../org.gradle.api/-project/get-properties.html"><span><span>Project</span></span></a></div></span></div><div><div class="title"><p class="paragraph">More information about how tasks are exposed by name in build scripts</p></div></div></div></div></div><h4 class="">Throws</h4><div class="table"><div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api"><div class="main-subrow keyValue "><div class=""><span class="inline-flex"><div><a href="../../org.gradle.api/-invalid-user-data-exception/index.html"><span>Invalid</span><wbr></wbr><span>User</span><wbr></wbr><span>Data</span><wbr></wbr><span><span>Exception</span></span></a></div></span></div><div><div class="title"><p class="paragraph">If a task with the given name already exists in this project.</p></div></div></div></div></div><hr><div class="symbol monospace"><span class="token keyword">abstract </span><span class="token keyword"></span><span class="token keyword">fun </span><span class="token operator">&lt;</span><span class="token keyword"></span><a href="create.html">T</a><span class="token operator"> : </span><a href="../../org.gradle.api/-task/index.html">Task</a><span class="token operator">?</span><span class="token operator">&gt; </span><a href="create.html"><span class="token function">create</span></a><span class="token punctuation">(</span><span class="parameters "><span class="parameter ">name<span class="token operator">: </span><a href="https://docs.oracle.com/en/java/javase/11/docs/api/java.base/java/lang/String.html">String</a><span class="token punctuation">, </span></span><span class="parameter ">type<span class="token operator">: </span><a href="https://docs.oracle.com/en/java/javase/11/docs/api/java.base/java/lang/Class.html">Class</a><span class="token operator">&lt;</span><a href="create.html">T</a><span class="token operator">&gt;</span></span></span><span class="token punctuation">)</span><span class="token operator">: </span><a href="create.html">T</a><span class="clearfix"><span class="floating-right">(<a href="https://github.com/gradle/gradle/blob/28aca86a7180baa17117e0e5ba01d8ea9feca598/subprojects/core-api/src/main/java/org/gradle/api/tasks/TaskContainer.java#L169">source</a>)</span></span></div><p class="paragraph">Creates a <a href="../../org.gradle.api/-task/index.html">Task</a> with the given name and type, and adds it to this container.</p><p class="paragraph">After the task is added, it is made available as a property of the project, so that you can reference the task by name in your build file.</p><span class="kdoc-tag"><h4 class="">Return</h4><p class="paragraph">The newly created task object</p></span><h4 class="">Parameters</h4><div class="table"><div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api"><div class="main-subrow keyValue "><div class=""><span class="inline-flex"><div><u><span><span>name</span></span></u></div></span></div><div><div class="title"><p class="paragraph">The name of the task to be created.</p></div></div></div></div><div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api"><div class="main-subrow keyValue "><div class=""><span class="inline-flex"><div><u><span><span>type</span></span></u></div></span></div><div><div class="title"><p class="paragraph">The type of task to create.</p></div></div></div></div></div><h4 class="">See also</h4><div class="table"><div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api"><div class="main-subrow keyValue "><div class=""><span class="inline-flex"><div><a href="../../org.gradle.api/-project/get-properties.html"><span><span>Project</span></span></a></div></span></div><div><div class="title"><p class="paragraph">More information about how tasks are exposed by name in build scripts</p></div></div></div></div></div><h4 class="">Throws</h4><div class="table"><div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api"><div class="main-subrow keyValue "><div class=""><span class="inline-flex"><div><a href="../../org.gradle.api/-invalid-user-data-exception/index.html"><span>Invalid</span><wbr></wbr><span>User</span><wbr></wbr><span>Data</span><wbr></wbr><span><span>Exception</span></span></a></div></span></div><div><div class="title"><p class="paragraph">If a task with the given name already exists in this project.</p></div></div></div></div></div><hr><div class="symbol monospace"><span class="token keyword">abstract </span><span class="token keyword"></span><span class="token keyword">fun </span><span class="token operator">&lt;</span><span class="token keyword"></span><a href="create.html">T</a><span class="token operator"> : </span><a href="../../org.gradle.api/-task/index.html">Task</a><span class="token operator">?</span><span class="token operator">&gt; </span><a href="create.html"><span class="token function">create</span></a><span class="token punctuation">(</span><span class="parameters "><span class="parameter ">name<span class="token operator">: </span><a href="https://docs.oracle.com/en/java/javase/11/docs/api/java.base/java/lang/String.html">String</a><span class="token punctuation">, </span></span><span class="parameter ">type<span class="token operator">: </span><a href="https://docs.oracle.com/en/java/javase/11/docs/api/java.base/java/lang/Class.html">Class</a><span class="token operator">&lt;</span><a href="create.html">T</a><span class="token operator">&gt;</span><span class="token punctuation">, </span></span><span class="parameter ">constructorArgs<span class="token operator">: </span><a href="https://kotlinlang.org/api/latest/jvm/stdlib/kotlin/-array/index.html">Array</a><span class="token operator">&lt;</span><a href="https://kotlinlang.org/api/latest/jvm/stdlib/kotlin/-any/index.html">Any</a><span class="token operator">&gt;</span></span></span><span class="token punctuation">)</span><span class="token operator">: </span><a href="create.html">T</a><span class="clearfix"><span class="floating-right">(<a href="https://github.com/gradle/gradle/blob/28aca86a7180baa17117e0e5ba01d8ea9feca598/subprojects/core-api/src/main/java/org/gradle/api/tasks/TaskContainer.java#L188">source</a>)</span></span></div><p class="paragraph">Creates a <a href="../../org.gradle.api/-task/index.html">Task</a> with the given name and type, passing the given arguments to the <code class="lang-kotlin">@Inject</code>-annotated constructor, and adds it to this container. All values passed to the task constructor must be non-null; otherwise a <code class="lang-kotlin">NullPointerException</code> will be thrown</p><p class="paragraph">After the task is added, it is made available as a property of the project, so that you can reference the task by name in your build file.</p><span class="kdoc-tag"><h4 class="">Return</h4><p class="paragraph">The newly created task object</p></span><h4 class="">Parameters</h4><div class="table"><div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api"><div class="main-subrow keyValue "><div class=""><span class="inline-flex"><div><u><span><span>name</span></span></u></div></span></div><div><div class="title"><p class="paragraph">The name of the task to be created.</p></div></div></div></div><div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api"><div class="main-subrow keyValue "><div class=""><span class="inline-flex"><div><u><span><span>type</span></span></u></div></span></div><div><div class="title"><p class="paragraph">The type of task to create.</p></div></div></div></div><div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api"><div class="main-subrow keyValue "><div class=""><span class="inline-flex"><div><u><span>constructor</span><wbr></wbr><span><span>Args</span></span></u></div></span></div><div><div class="title"><p class="paragraph">The arguments to pass to the task constructor</p></div></div></div></div></div><h4 class="">See also</h4><div class="table"><div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api"><div class="main-subrow keyValue "><div class=""><span class="inline-flex"><div><a href="../../org.gradle.api/-project/get-properties.html"><span><span>Project</span></span></a></div></span></div><div><div class="title"><p class="paragraph">More information about how tasks are exposed by name in build scripts</p></div></div></div></div></div><h4 class="">Throws</h4><div class="table"><div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api"><div class="main-subrow keyValue "><div class=""><span class="inline-flex"><div><a href="../../org.gradle.api/-invalid-user-data-exception/index.html"><span>Invalid</span><wbr></wbr><span>User</span><wbr></wbr><span>Data</span><wbr></wbr><span><span>Exception</span></span></a></div></span></div><div><div class="title"><p class="paragraph">If a task with the given name already exists in this project.</p></div></div></div></div><div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api"><div class="main-subrow keyValue "><div class=""><span class="inline-flex"><div><a href="https://docs.oracle.com/en/java/javase/11/docs/api/java.base/java/lang/NullPointerException.html"><span>Null</span><wbr></wbr><span>Pointer</span><wbr></wbr><span><span>Exception</span></span></a></div></span></div><div><div class="title"><p class="paragraph">If any of the values in <code class="lang-kotlin">constructorArgs</code> is null.</p></div></div></div></div></div><hr><div class="symbol monospace"><span class="token keyword">abstract </span><span class="token keyword"></span><span class="token keyword">fun </span><span class="token operator">&lt;</span><span class="token keyword"></span><a href="create.html">T</a><span class="token operator"> : </span><a href="../../org.gradle.api/-task/index.html">Task</a><span class="token operator">?</span><span class="token operator">&gt; </span><a href="create.html"><span class="token function">create</span></a><span class="token punctuation">(</span><span class="parameters "><span class="parameter ">name<span class="token operator">: </span><a href="https://docs.oracle.com/en/java/javase/11/docs/api/java.base/java/lang/String.html">String</a><span class="token punctuation">, </span></span><span class="parameter ">type<span class="token operator">: </span><a href="https://docs.oracle.com/en/java/javase/11/docs/api/java.base/java/lang/Class.html">Class</a><span class="token operator">&lt;</span><a href="create.html">T</a><span class="token operator">&gt;</span><span class="token punctuation">, </span></span><span class="parameter ">configuration<span class="token operator">: </span><a href="../../org.gradle.api/-action/index.html">Action</a><span class="token operator">&lt;</span><span class="token keyword">out </span><a href="https://kotlinlang.org/api/latest/jvm/stdlib/kotlin/-any/index.html">Any</a><span class="token operator">&gt;</span></span></span><span class="token punctuation">)</span><span class="token operator">: </span><a href="create.html">T</a><span class="clearfix"><span class="floating-right">(<a href="https://github.com/gradle/gradle/blob/28aca86a7180baa17117e0e5ba01d8ea9feca598/subprojects/core-api/src/main/java/org/gradle/api/tasks/TaskContainer.java#L204">source</a>)</span></span></div><p class="paragraph">Creates a <a href="../../org.gradle.api/-task/index.html">Task</a> with the given name and type, configures it with the given action, and adds it to this container.</p><p class="paragraph">After the task is added, it is made available as a property of the project, so that you can reference the task by name in your build file.</p><span class="kdoc-tag"><h4 class="">Return</h4><p class="paragraph">The newly created task object.</p></span><h4 class="">Parameters</h4><div class="table"><div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api"><div class="main-subrow keyValue "><div class=""><span class="inline-flex"><div><u><span><span>name</span></span></u></div></span></div><div><div class="title"><p class="paragraph">The name of the task to be created.</p></div></div></div></div><div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api"><div class="main-subrow keyValue "><div class=""><span class="inline-flex"><div><u><span><span>type</span></span></u></div></span></div><div><div class="title"><p class="paragraph">The type of task to create.</p></div></div></div></div><div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api"><div class="main-subrow keyValue "><div class=""><span class="inline-flex"><div><u><span><span>configuration</span></span></u></div></span></div><div><div class="title"><p class="paragraph">The action to configure the task with.</p></div></div></div></div></div><h4 class="">See also</h4><div class="table"><div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api"><div class="main-subrow keyValue "><div class=""><span class="inline-flex"><div><a href="../../org.gradle.api/-project/get-properties.html"><span><span>Project</span></span></a></div></span></div><div><div class="title"><p class="paragraph">More information about how tasks are exposed by name in build scripts</p></div></div></div></div></div><h4 class="">Throws</h4><div class="table"><div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api"><div class="main-subrow keyValue "><div class=""><span class="inline-flex"><div><a href="../../org.gradle.api/-invalid-user-data-exception/index.html"><span>Invalid</span><wbr></wbr><span>User</span><wbr></wbr><span>Data</span><wbr></wbr><span><span>Exception</span></span></a></div></span></div><div><div class="title"><p class="paragraph">If a task with the given name already exists in this project.</p></div></div></div></div></div></div>  </div>
</div>
      <div class="footer">
        <span class="go-to-top-icon"><a href="#content" id="go-to-top-link"></a></span><span>Gradle Kotlin DSL Reference</span><span
                class="pull-right"><span>Generated by </span><a
                href="https://github.com/Kotlin/dokka"><span>dokka</span><span class="padded-icon"></span></a></span>
      </div>
    </div>
</div>
</body>
</html>
