<!DOCTYPE html>
<html>
<head>
    <meta name="viewport" content="width=device-width, initial-scale=1" charset="UTF-8">
    <title>org.gradle.api.artifacts</title>
    <link href="../../images/logo-icon.svg" rel="icon" type="image/svg">
    <script>var pathToRoot = "../../";</script>
    <script>const storage = localStorage.getItem("dokka-dark-mode")
    if (storage == null) {
        const osDarkSchemePreferred = window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches
        if (osDarkSchemePreferred === true) {
            document.getElementsByTagName("html")[0].classList.add("theme-dark")
        }
    } else {
        const savedDarkMode = JSON.parse(storage)
        if(savedDarkMode === true) {
            document.getElementsByTagName("html")[0].classList.add("theme-dark")
        }
    }
    </script>
<script type="text/javascript" src="../../scripts/sourceset_dependencies.js" async="async"></script>
<link href="../../styles/style.css" rel="Stylesheet">
<link href="../../styles/jetbrains-mono.css" rel="Stylesheet">
<link href="../../styles/main.css" rel="Stylesheet">
<link href="../../styles/prism.css" rel="Stylesheet">
<link href="../../styles/logo-styles.css" rel="Stylesheet">
<script type="text/javascript" src="../../scripts/clipboard.js" async="async"></script>
<script type="text/javascript" src="../../scripts/navigation-loader.js" async="async"></script>
<script type="text/javascript" src="../../scripts/platform-content-handler.js" async="async"></script>
<script type="text/javascript" src="../../scripts/main.js" defer="defer"></script>
<script type="text/javascript" src="../../scripts/prism.js" async="async"></script>
<script type="text/javascript" src="../../scripts/symbol-parameters-wrapper_deferred.js" defer="defer"></script>
<link href="../../images/gradle-logo.svg">
<link href="../../styles/gradle.css" rel="Stylesheet">
</head>
<body>
<div class="navigation-wrapper" id="navigation-wrapper">
    <div id="leftToggler"><span class="icon-toggler"></span></div>
    <div class="library-name">
            <a href="../../index.html">
                    <span>gradle</span>
            </a>
    </div>
    <div>
8.5    </div>
    <div class="pull-right d-flex">
        <div class="filter-section" id="filter-section">
                <button class="platform-tag platform-selector jvm-like" data-active="" data-filter=":docs/java_api">API</button>
        </div>
        <button id="theme-toggle-button"><span id="theme-toggle"></span></button>
        <div id="searchBar"></div>
    </div>
</div>
<div id="container">
    <div id="leftColumn">
        <div id="sideMenu"></div>
    </div>
    <div id="main">
<div class="main-content" data-page-type="package" id="content" pageIds="gradle::org.gradle.api.artifacts////PointingToDeclaration//-1793262594">
  <div class="breadcrumbs"><a href="../../index.html">gradle</a><span class="delimiter">/</span><span class="current">org.gradle.api.artifacts</span></div>
  <div class="cover ">
    <h1 class="cover"><span><span>Package-level</span></span> <span><span>declarations</span></span></h1>
    <div class="platform-hinted UnderCoverText with-platform-tabs" data-platform-hinted="data-platform-hinted">
      <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api" data-active="" data-toggle=":docs/java_api">API</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/java_api"><p class="paragraph">Classes for declaring and using artifacts and artifact dependencies.</p></div>    </div>
  </div>
  <div class="tabbedcontent">
    <div class="tabs-section" tabs-section="tabs-section"><button class="section-tab" data-active="" data-togglable="TYPE">Types</button></div>
    <div class="tabs-section-body">
      <div data-togglable="TYPE">
        <h2 class="">Types</h2>
        <div class="table"><a data-name="-1512739870%2FClasslikes%2F-1793262594" anchor-label="ArtifactCollection" id="-1512739870%2FClasslikes%2F-1793262594" data-filterable-set=":docs/java_api"></a>
          <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="-artifact-collection/index.html"><span>Artifact</span><wbr></wbr><span><span>Collection</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="-1512739870%2FClasslikes%2F-1793262594"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
                    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api" data-active="" data-toggle=":docs/java_api">API</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/java_api"><div class="symbol monospace"><span class="token keyword">interface </span><a href="-artifact-collection/index.html">ArtifactCollection</a> : <a href="https://docs.oracle.com/en/java/javase/11/docs/api/java.base/java/lang/Iterable.html">Iterable</a><span class="token operator">&lt;</span><a href="https://docs.oracle.com/en/java/javase/11/docs/api/java.base/java/lang/Iterable.html">T</a><span class="token operator">&gt; </span></div><div class="brief ">A collection of artifacts resolved for a configuration.</div></div>                  </div>
                </div>
              </div>
            </div>
          </div>
<a data-name="-745357257%2FClasslikes%2F-1793262594" anchor-label="ArtifactIdentifier" id="-745357257%2FClasslikes%2F-1793262594" data-filterable-set=":docs/java_api"></a>
          <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="-artifact-identifier/index.html"><span>Artifact</span><wbr></wbr><span><span>Identifier</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="-745357257%2FClasslikes%2F-1793262594"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
                    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api" data-active="" data-toggle=":docs/java_api">API</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/java_api"><div class="symbol monospace"><span class="token keyword">interface </span><a href="-artifact-identifier/index.html">ArtifactIdentifier</a></div><div class="brief ">The identifier for a module artifact.</div></div>                  </div>
                </div>
              </div>
            </div>
          </div>
<a data-name="-2136232949%2FClasslikes%2F-1793262594" anchor-label="ArtifactRepositoryContainer" id="-2136232949%2FClasslikes%2F-1793262594" data-filterable-set=":docs/java_api"></a>
          <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="-artifact-repository-container/index.html"><span>Artifact</span><wbr></wbr><span>Repository</span><wbr></wbr><span><span>Container</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="-2136232949%2FClasslikes%2F-1793262594"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
                    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api" data-active="" data-toggle=":docs/java_api">API</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/java_api"><div class="symbol monospace"><span class="token keyword">interface </span><a href="-artifact-repository-container/index.html">ArtifactRepositoryContainer</a> : <a href="../org.gradle.api/-named-domain-object-list/index.html">NamedDomainObjectList</a><span class="token operator">&lt;</span><a href="../org.gradle.api/-named-domain-object-list/index.html">T</a><span class="token operator">&gt; </span>, <a href="../org.gradle.util/-configurable/index.html">Configurable</a><span class="token operator">&lt;</span><a href="../org.gradle.util/-configurable/index.html">T</a><span class="token operator">&gt; </span></div><div class="brief ">A <code class="lang-kotlin">ResolverContainer</code> is responsible for managing a set of <a href="../org.gradle.api.artifacts.repositories/-artifact-repository/index.html">ArtifactRepository</a> instances.</div></div>                  </div>
                </div>
              </div>
            </div>
          </div>
<a data-name="-1246845142%2FClasslikes%2F-1793262594" anchor-label="ArtifactSelectionDetails" id="-1246845142%2FClasslikes%2F-1793262594" data-filterable-set=":docs/java_api"></a>
          <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="-artifact-selection-details/index.html"><span>Artifact</span><wbr></wbr><span>Selection</span><wbr></wbr><span><span>Details</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="-1246845142%2FClasslikes%2F-1793262594"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
                    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api" data-active="" data-toggle=":docs/java_api">API</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/java_api"><div class="symbol monospace"><span class="token keyword">interface </span><a href="-artifact-selection-details/index.html">ArtifactSelectionDetails</a></div><div class="brief ">Details about artifact dependency substitution: this class gives access to the original dependency requested artifacts, if any, and gives the opportunity to replace the original requested artifacts with other artifacts.</div></div>                  </div>
                </div>
              </div>
            </div>
          </div>
<a data-name="1825471739%2FClasslikes%2F-1793262594" anchor-label="ArtifactView" id="1825471739%2FClasslikes%2F-1793262594" data-filterable-set=":docs/java_api"></a>
          <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="-artifact-view/index.html"><span>Artifact</span><wbr></wbr><span><span>View</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="1825471739%2FClasslikes%2F-1793262594"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
                    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api" data-active="" data-toggle=":docs/java_api">API</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/java_api"><div class="symbol monospace"><span class="token keyword">interface </span><a href="-artifact-view/index.html">ArtifactView</a> : <a href="../org.gradle.api.attributes/-has-attributes/index.html">HasAttributes</a></div><div class="brief ">A view over the artifacts resolved for this set of dependencies.</div></div>                  </div>
                </div>
              </div>
            </div>
          </div>
<a data-name="-508788264%2FClasslikes%2F-1793262594" anchor-label="CacheableRule" id="-508788264%2FClasslikes%2F-1793262594" data-filterable-set=":docs/java_api"></a>
          <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="-cacheable-rule/index.html"><span>Cacheable</span><wbr></wbr><span><span>Rule</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="-508788264%2FClasslikes%2F-1793262594"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
                    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api" data-active="" data-toggle=":docs/java_api">API</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/java_api"><div class="symbol monospace"><div class="block"><div class="block"><span class="token annotation builtin">@</span><a href="https://docs.oracle.com/en/java/javase/11/docs/api/java.base/java/lang/annotation/Documented.html"><span class="token annotation builtin">Documented</span></a></div><div class="block"><span class="token annotation builtin">@</span><a href="https://docs.oracle.com/en/java/javase/11/docs/api/java.base/java/lang/annotation/Retention.html"><span class="token annotation builtin">Retention</span></a><span class="token punctuation">(</span><span>value<span class="token operator"> = </span><a href="https://docs.oracle.com/en/java/javase/11/docs/api/java.base/java/lang/annotation/RetentionPolicy.html">RetentionPolicy.RUNTIME</a></span><wbr></wbr><span class="token punctuation">)</span></div><div class="block"><span class="token annotation builtin">@</span><a href="https://docs.oracle.com/en/java/javase/11/docs/api/java.base/java/lang/annotation/Target.html"><span class="token annotation builtin">Target</span></a><span class="token punctuation">(</span><span>value<span class="token operator"> = </span><span class="token punctuation">[</span><span><a href="https://docs.oracle.com/en/java/javase/11/docs/api/java.base/java/lang/annotation/ElementType.html">ElementType.TYPE</a></span><wbr></wbr><span class="token punctuation">]</span></span><wbr></wbr><span class="token punctuation">)</span></div></div><span class="token keyword">annotation class </span><a href="-cacheable-rule/index.html">CacheableRule</a></div><div class="brief ">Declares a rule eligible for caching.</div></div>                  </div>
                </div>
              </div>
            </div>
          </div>
<a data-name="635908272%2FClasslikes%2F-1793262594" anchor-label="CapabilitiesResolution" id="635908272%2FClasslikes%2F-1793262594" data-filterable-set=":docs/java_api"></a>
          <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="-capabilities-resolution/index.html"><span>Capabilities</span><wbr></wbr><span><span>Resolution</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="635908272%2FClasslikes%2F-1793262594"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
                    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api" data-active="" data-toggle=":docs/java_api">API</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/java_api"><div class="symbol monospace"><span class="token keyword">interface </span><a href="-capabilities-resolution/index.html">CapabilitiesResolution</a></div><div class="brief ">Allows configuring the capabilities resolution strategy.</div></div>                  </div>
                </div>
              </div>
            </div>
          </div>
<a data-name="1647498642%2FClasslikes%2F-1793262594" anchor-label="CapabilityResolutionDetails" id="1647498642%2FClasslikes%2F-1793262594" data-filterable-set=":docs/java_api"></a>
          <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="-capability-resolution-details/index.html"><span>Capability</span><wbr></wbr><span>Resolution</span><wbr></wbr><span><span>Details</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="1647498642%2FClasslikes%2F-1793262594"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
                    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api" data-active="" data-toggle=":docs/java_api">API</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/java_api"><div class="symbol monospace"><span class="token keyword">interface </span><a href="-capability-resolution-details/index.html">CapabilityResolutionDetails</a></div><div class="brief ">Gives access to the resolution details of a single capability conflict.</div></div>                  </div>
                </div>
              </div>
            </div>
          </div>
<a data-name="-1310718341%2FClasslikes%2F-1793262594" anchor-label="ClientModule" id="-1310718341%2FClasslikes%2F-1793262594" data-filterable-set=":docs/java_api"></a>
          <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="-client-module/index.html"><span>Client</span><wbr></wbr><span><span>Module</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="-1310718341%2FClasslikes%2F-1793262594"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
                    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api" data-active="" data-toggle=":docs/java_api">API</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/java_api"><div class="symbol monospace"><span class="token keyword">interface </span><a href="-client-module/index.html"><strike>ClientModule</strike></a> : <a href="-external-module-dependency/index.html">ExternalModuleDependency</a></div><div class="brief ">To model a module in your dependency declarations.</div></div>                  </div>
                </div>
              </div>
            </div>
          </div>
<a data-name="1051157124%2FClasslikes%2F-1793262594" anchor-label="ComponentMetadata" id="1051157124%2FClasslikes%2F-1793262594" data-filterable-set=":docs/java_api"></a>
          <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="-component-metadata/index.html"><span>Component</span><wbr></wbr><span><span>Metadata</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="1051157124%2FClasslikes%2F-1793262594"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
                    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api" data-active="" data-toggle=":docs/java_api">API</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/java_api"><div class="symbol monospace"><span class="token keyword">interface </span><a href="-component-metadata/index.html">ComponentMetadata</a> : <a href="../org.gradle.api.attributes/-has-attributes/index.html">HasAttributes</a></div><div class="brief ">Provides a read-only view of a resolved component's metadata, which typically originates from a component descriptor (Ivy file, Maven POM).</div></div>                  </div>
                </div>
              </div>
            </div>
          </div>
<a data-name="-479355037%2FClasslikes%2F-1793262594" anchor-label="ComponentMetadataBuilder" id="-479355037%2FClasslikes%2F-1793262594" data-filterable-set=":docs/java_api"></a>
          <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="-component-metadata-builder/index.html"><span>Component</span><wbr></wbr><span>Metadata</span><wbr></wbr><span><span>Builder</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="-479355037%2FClasslikes%2F-1793262594"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
                    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api" data-active="" data-toggle=":docs/java_api">API</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/java_api"><div class="symbol monospace"><span class="token keyword">interface </span><a href="-component-metadata-builder/index.html">ComponentMetadataBuilder</a></div><div class="brief ">A component metadata builder.</div></div>                  </div>
                </div>
              </div>
            </div>
          </div>
<a data-name="1905235919%2FClasslikes%2F-1793262594" anchor-label="ComponentMetadataContext" id="1905235919%2FClasslikes%2F-1793262594" data-filterable-set=":docs/java_api"></a>
          <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="-component-metadata-context/index.html"><span>Component</span><wbr></wbr><span>Metadata</span><wbr></wbr><span><span>Context</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="1905235919%2FClasslikes%2F-1793262594"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
                    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api" data-active="" data-toggle=":docs/java_api">API</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/java_api"><div class="symbol monospace"><span class="token keyword">interface </span><a href="-component-metadata-context/index.html">ComponentMetadataContext</a></div><div class="brief ">Provides access to component metadata from a <a href="-component-metadata-rule/index.html">ComponentMetadataRule</a>.</div></div>                  </div>
                </div>
              </div>
            </div>
          </div>
<a data-name="761534108%2FClasslikes%2F-1793262594" anchor-label="ComponentMetadataDetails" id="761534108%2FClasslikes%2F-1793262594" data-filterable-set=":docs/java_api"></a>
          <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="-component-metadata-details/index.html"><span>Component</span><wbr></wbr><span>Metadata</span><wbr></wbr><span><span>Details</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="761534108%2FClasslikes%2F-1793262594"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
                    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api" data-active="" data-toggle=":docs/java_api">API</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/java_api"><div class="symbol monospace"><div class="block"><div class="block"><span class="token annotation builtin">@</span><a href="../org.gradle.api/-non-extensible/index.html"><span class="token annotation builtin">NonExtensible</span></a></div></div><span class="token keyword">interface </span><a href="-component-metadata-details/index.html">ComponentMetadataDetails</a> : <a href="-component-metadata/index.html">ComponentMetadata</a>, <a href="../org.gradle.api.attributes/-has-configurable-attributes/index.html">HasConfigurableAttributes</a><span class="token operator">&lt;</span><a href="../org.gradle.api.attributes/-has-configurable-attributes/index.html">SELF</a><span class="token operator">&gt; </span></div><div class="brief ">Describes a resolved component's metadata, which typically originates from a component descriptor (Ivy file, Maven POM).</div></div>                  </div>
                </div>
              </div>
            </div>
          </div>
<a data-name="972519111%2FClasslikes%2F-1793262594" anchor-label="ComponentMetadataListerDetails" id="972519111%2FClasslikes%2F-1793262594" data-filterable-set=":docs/java_api"></a>
          <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="-component-metadata-lister-details/index.html"><span>Component</span><wbr></wbr><span>Metadata</span><wbr></wbr><span>Lister</span><wbr></wbr><span><span>Details</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="972519111%2FClasslikes%2F-1793262594"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
                    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api" data-active="" data-toggle=":docs/java_api">API</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/java_api"><div class="symbol monospace"><span class="token keyword">interface </span><a href="-component-metadata-lister-details/index.html">ComponentMetadataListerDetails</a></div><div class="brief ">Allows a custom version lister to specify the list of versions known for a specific module.</div></div>                  </div>
                </div>
              </div>
            </div>
          </div>
<a data-name="2044947816%2FClasslikes%2F-1793262594" anchor-label="ComponentMetadataRule" id="2044947816%2FClasslikes%2F-1793262594" data-filterable-set=":docs/java_api"></a>
          <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="-component-metadata-rule/index.html"><span>Component</span><wbr></wbr><span>Metadata</span><wbr></wbr><span><span>Rule</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="2044947816%2FClasslikes%2F-1793262594"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
                    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api" data-active="" data-toggle=":docs/java_api">API</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/java_api"><div class="symbol monospace"><span class="token keyword">interface </span><a href="-component-metadata-rule/index.html">ComponentMetadataRule</a> : <a href="../org.gradle.api/-action/index.html">Action</a><span class="token operator">&lt;</span><a href="../org.gradle.api/-action/index.html">T</a><span class="token operator">&gt; </span></div><div class="brief ">A rule that modify <a href="-component-metadata-details/index.html">component metadata</a>.</div></div>                  </div>
                </div>
              </div>
            </div>
          </div>
<a data-name="-1734327528%2FClasslikes%2F-1793262594" anchor-label="ComponentMetadataSupplier" id="-1734327528%2FClasslikes%2F-1793262594" data-filterable-set=":docs/java_api"></a>
          <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="-component-metadata-supplier/index.html"><span>Component</span><wbr></wbr><span>Metadata</span><wbr></wbr><span><span>Supplier</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="-1734327528%2FClasslikes%2F-1793262594"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
                    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api" data-active="" data-toggle=":docs/java_api">API</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/java_api"><div class="symbol monospace"><span class="token keyword">interface </span><a href="-component-metadata-supplier/index.html">ComponentMetadataSupplier</a> : <a href="../org.gradle.api/-action/index.html">Action</a><span class="token operator">&lt;</span><a href="../org.gradle.api/-action/index.html">T</a><span class="token operator">&gt; </span></div><div class="brief ">A component metadata rule is responsible for providing the initial metadata of a component from a remote repository, in place of parsing the descriptor.</div></div>                  </div>
                </div>
              </div>
            </div>
          </div>
<a data-name="1105012616%2FClasslikes%2F-1793262594" anchor-label="ComponentMetadataSupplierDetails" id="1105012616%2FClasslikes%2F-1793262594" data-filterable-set=":docs/java_api"></a>
          <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="-component-metadata-supplier-details/index.html"><span>Component</span><wbr></wbr><span>Metadata</span><wbr></wbr><span>Supplier</span><wbr></wbr><span><span>Details</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="1105012616%2FClasslikes%2F-1793262594"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
                    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api" data-active="" data-toggle=":docs/java_api">API</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/java_api"><div class="symbol monospace"><span class="token keyword">interface </span><a href="-component-metadata-supplier-details/index.html">ComponentMetadataSupplierDetails</a></div><div class="brief ">A component metadata rule details, giving access to the identifier of the component being resolved, the metadata builder, and the repository resource accessor for this.</div></div>                  </div>
                </div>
              </div>
            </div>
          </div>
<a data-name="1270763899%2FClasslikes%2F-1793262594" anchor-label="ComponentMetadataVersionLister" id="1270763899%2FClasslikes%2F-1793262594" data-filterable-set=":docs/java_api"></a>
          <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="-component-metadata-version-lister/index.html"><span>Component</span><wbr></wbr><span>Metadata</span><wbr></wbr><span>Version</span><wbr></wbr><span><span>Lister</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="1270763899%2FClasslikes%2F-1793262594"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
                    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api" data-active="" data-toggle=":docs/java_api">API</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/java_api"><div class="symbol monospace"><span class="token keyword">interface </span><a href="-component-metadata-version-lister/index.html">ComponentMetadataVersionLister</a> : <a href="../org.gradle.api/-action/index.html">Action</a><span class="token operator">&lt;</span><a href="../org.gradle.api/-action/index.html">T</a><span class="token operator">&gt; </span></div><div class="brief ">Interface for custom version listers.</div></div>                  </div>
                </div>
              </div>
            </div>
          </div>
<a data-name="2140026872%2FClasslikes%2F-1793262594" anchor-label="ComponentModuleMetadata" id="2140026872%2FClasslikes%2F-1793262594" data-filterable-set=":docs/java_api"></a>
          <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="-component-module-metadata/index.html"><span>Component</span><wbr></wbr><span>Module</span><wbr></wbr><span><span>Metadata</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="2140026872%2FClasslikes%2F-1793262594"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
                    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api" data-active="" data-toggle=":docs/java_api">API</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/java_api"><div class="symbol monospace"><span class="token keyword">interface </span><a href="-component-module-metadata/index.html">ComponentModuleMetadata</a></div><div class="brief ">Contains immutable component module metadata information.</div></div>                  </div>
                </div>
              </div>
            </div>
          </div>
<a data-name="-1441007960%2FClasslikes%2F-1793262594" anchor-label="ComponentModuleMetadataDetails" id="-1441007960%2FClasslikes%2F-1793262594" data-filterable-set=":docs/java_api"></a>
          <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="-component-module-metadata-details/index.html"><span>Component</span><wbr></wbr><span>Module</span><wbr></wbr><span>Metadata</span><wbr></wbr><span><span>Details</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="-1441007960%2FClasslikes%2F-1793262594"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
                    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api" data-active="" data-toggle=":docs/java_api">API</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/java_api"><div class="symbol monospace"><span class="token keyword">interface </span><a href="-component-module-metadata-details/index.html">ComponentModuleMetadataDetails</a> : <a href="-component-module-metadata/index.html">ComponentModuleMetadata</a></div><div class="brief ">Contains and allows configuring component module metadata information.</div></div>                  </div>
                </div>
              </div>
            </div>
          </div>
<a data-name="-490395197%2FClasslikes%2F-1793262594" anchor-label="ComponentSelection" id="-490395197%2FClasslikes%2F-1793262594" data-filterable-set=":docs/java_api"></a>
          <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="-component-selection/index.html"><span>Component</span><wbr></wbr><span><span>Selection</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="-490395197%2FClasslikes%2F-1793262594"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
                    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api" data-active="" data-toggle=":docs/java_api">API</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/java_api"><div class="symbol monospace"><span class="token keyword">interface </span><a href="-component-selection/index.html">ComponentSelection</a></div><div class="brief ">Represents a tuple of the component selector of a module and a candidate version to be evaluated in a component selection rule.</div></div>                  </div>
                </div>
              </div>
            </div>
          </div>
<a data-name="597452008%2FClasslikes%2F-1793262594" anchor-label="ComponentSelectionRules" id="597452008%2FClasslikes%2F-1793262594" data-filterable-set=":docs/java_api"></a>
          <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="-component-selection-rules/index.html"><span>Component</span><wbr></wbr><span>Selection</span><wbr></wbr><span><span>Rules</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="597452008%2FClasslikes%2F-1793262594"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
                    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api" data-active="" data-toggle=":docs/java_api">API</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/java_api"><div class="symbol monospace"><span class="token keyword">interface </span><a href="-component-selection-rules/index.html">ComponentSelectionRules</a></div><div class="brief ">Represents a container for component selection rules.</div></div>                  </div>
                </div>
              </div>
            </div>
          </div>
<a data-name="652377217%2FClasslikes%2F-1793262594" anchor-label="ComponentVariantIdentifier" id="652377217%2FClasslikes%2F-1793262594" data-filterable-set=":docs/java_api"></a>
          <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="-component-variant-identifier/index.html"><span>Component</span><wbr></wbr><span>Variant</span><wbr></wbr><span><span>Identifier</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="652377217%2FClasslikes%2F-1793262594"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
                    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api" data-active="" data-toggle=":docs/java_api">API</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/java_api"><div class="symbol monospace"><span class="token keyword">interface </span><a href="-component-variant-identifier/index.html">ComponentVariantIdentifier</a></div><div class="brief ">Identifies a variant of a component by module identifier and variant name.</div></div>                  </div>
                </div>
              </div>
            </div>
          </div>
<a data-name="-148602104%2FClasslikes%2F-1793262594" anchor-label="ConfigurablePublishArtifact" id="-148602104%2FClasslikes%2F-1793262594" data-filterable-set=":docs/java_api"></a>
          <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="-configurable-publish-artifact/index.html"><span>Configurable</span><wbr></wbr><span>Publish</span><wbr></wbr><span><span>Artifact</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="-148602104%2FClasslikes%2F-1793262594"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
                    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api" data-active="" data-toggle=":docs/java_api">API</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/java_api"><div class="symbol monospace"><span class="token keyword">interface </span><a href="-configurable-publish-artifact/index.html">ConfigurablePublishArtifact</a> : <a href="-publish-artifact/index.html">PublishArtifact</a></div><div class="brief ">A <a href="-publish-artifact/index.html">PublishArtifact</a> whose properties can be modified.</div></div>                  </div>
                </div>
              </div>
            </div>
          </div>
<a data-name="-1825876070%2FClasslikes%2F-1793262594" anchor-label="Configuration" id="-1825876070%2FClasslikes%2F-1793262594" data-filterable-set=":docs/java_api"></a>
          <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="-configuration/index.html"><span><span>Configuration</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="-1825876070%2FClasslikes%2F-1793262594"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
                    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api" data-active="" data-toggle=":docs/java_api">API</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/java_api"><div class="symbol monospace"><span class="token keyword">interface </span><a href="-configuration/index.html">Configuration</a> : <a href="../org.gradle.api.file/-file-collection/index.html">FileCollection</a>, <a href="../org.gradle.api.attributes/-has-configurable-attributes/index.html">HasConfigurableAttributes</a><span class="token operator">&lt;</span><a href="../org.gradle.api.attributes/-has-configurable-attributes/index.html">SELF</a><span class="token operator">&gt; </span>, <a href="../org.gradle.api/-named/index.html">Named</a></div><div class="brief ">A <code class="lang-kotlin">Configuration</code> represents a group of artifacts and their dependencies.</div></div>                  </div>
                </div>
              </div>
            </div>
          </div>
<a data-name="-802132569%2FClasslikes%2F-1793262594" anchor-label="ConfigurationContainer" id="-802132569%2FClasslikes%2F-1793262594" data-filterable-set=":docs/java_api"></a>
          <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="-configuration-container/index.html"><span>Configuration</span><wbr></wbr><span><span>Container</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="-802132569%2FClasslikes%2F-1793262594"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
                    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api" data-active="" data-toggle=":docs/java_api">API</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/java_api"><div class="symbol monospace"><span class="token keyword">interface </span><a href="-configuration-container/index.html">ConfigurationContainer</a> : <a href="../org.gradle.api/-named-domain-object-container/index.html">NamedDomainObjectContainer</a><span class="token operator">&lt;</span><a href="../org.gradle.api/-named-domain-object-container/index.html">T</a><span class="token operator">&gt; </span></div><div class="brief ">A <code class="lang-kotlin">ConfigurationContainer</code> is responsible for declaring and managing configurations.</div></div>                  </div>
                </div>
              </div>
            </div>
          </div>
<a data-name="-761697261%2FClasslikes%2F-1793262594" anchor-label="ConfigurationPublications" id="-761697261%2FClasslikes%2F-1793262594" data-filterable-set=":docs/java_api"></a>
          <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="-configuration-publications/index.html"><span>Configuration</span><wbr></wbr><span><span>Publications</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="-761697261%2FClasslikes%2F-1793262594"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
                    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api" data-active="" data-toggle=":docs/java_api">API</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/java_api"><div class="symbol monospace"><span class="token keyword">interface </span><a href="-configuration-publications/index.html">ConfigurationPublications</a> : <a href="../org.gradle.api.attributes/-has-configurable-attributes/index.html">HasConfigurableAttributes</a><span class="token operator">&lt;</span><a href="../org.gradle.api.attributes/-has-configurable-attributes/index.html">SELF</a><span class="token operator">&gt; </span></div><div class="brief ">Represents the outgoing artifacts associated with a configuration.</div></div>                  </div>
                </div>
              </div>
            </div>
          </div>
<a data-name="866566115%2FClasslikes%2F-1793262594" anchor-label="ConfigurationVariant" id="866566115%2FClasslikes%2F-1793262594" data-filterable-set=":docs/java_api"></a>
          <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="-configuration-variant/index.html"><span>Configuration</span><wbr></wbr><span><span>Variant</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="866566115%2FClasslikes%2F-1793262594"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
                    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api" data-active="" data-toggle=":docs/java_api">API</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/java_api"><div class="symbol monospace"><span class="token keyword">interface </span><a href="-configuration-variant/index.html">ConfigurationVariant</a> : <a href="../org.gradle.api/-named/index.html">Named</a>, <a href="../org.gradle.api.attributes/-has-configurable-attributes/index.html">HasConfigurableAttributes</a><span class="token operator">&lt;</span><a href="../org.gradle.api.attributes/-has-configurable-attributes/index.html">SELF</a><span class="token operator">&gt; </span></div><div class="brief ">Represents some variant of an outgoing configuration.</div></div>                  </div>
                </div>
              </div>
            </div>
          </div>
<a data-name="-787806947%2FClasslikes%2F-1793262594" anchor-label="ConsumableConfiguration" id="-787806947%2FClasslikes%2F-1793262594" data-filterable-set=":docs/java_api"></a>
          <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="-consumable-configuration/index.html"><span>Consumable</span><wbr></wbr><span><span>Configuration</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="-787806947%2FClasslikes%2F-1793262594"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
                    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api" data-active="" data-toggle=":docs/java_api">API</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/java_api"><div class="symbol monospace"><div class="block"><div class="block"><span class="token annotation builtin">@</span><a href="../org.gradle.api/-incubating/index.html"><span class="token annotation builtin">Incubating</span></a></div></div><span class="token keyword">interface </span><a href="-consumable-configuration/index.html">ConsumableConfiguration</a> : <a href="-configuration/index.html">Configuration</a></div><div class="brief ">A <a href="-configuration/index.html">Configuration</a> which can be consumed via Publishing and Dependency Management.</div></div>                  </div>
                </div>
              </div>
            </div>
          </div>
<a data-name="-1001114822%2FClasslikes%2F-1793262594" anchor-label="DependenciesMetadata" id="-1001114822%2FClasslikes%2F-1793262594" data-filterable-set=":docs/java_api"></a>
          <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="-dependencies-metadata/index.html"><span>Dependencies</span><wbr></wbr><span><span>Metadata</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="-1001114822%2FClasslikes%2F-1793262594"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
                    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api" data-active="" data-toggle=":docs/java_api">API</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/java_api"><div class="symbol monospace"><span class="token keyword">interface </span><a href="-dependencies-metadata/index.html">DependenciesMetadata</a><span class="token operator">&lt;</span><span class="token keyword"></span><a href="-dependencies-metadata/index.html">T</a><span class="token operator"> : </span><a href="-dependency-metadata/index.html">DependencyMetadata</a><span class="token operator">?</span><span class="token operator">&gt;</span> : <a href="https://docs.oracle.com/en/java/javase/11/docs/api/java.base/java/util/Collection.html">Collection</a><span class="token operator">&lt;</span><a href="https://docs.oracle.com/en/java/javase/11/docs/api/java.base/java/util/Collection.html">E</a><span class="token operator">&gt; </span></div><div class="brief ">Describes metadata about a dependency - direct dependencies or dependency constraints - declared in a resolved component's metadata.</div></div>                  </div>
                </div>
              </div>
            </div>
          </div>
<a data-name="1344021511%2FClasslikes%2F-1793262594" anchor-label="Dependency" id="1344021511%2FClasslikes%2F-1793262594" data-filterable-set=":docs/java_api"></a>
          <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="-dependency/index.html"><span><span>Dependency</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="1344021511%2FClasslikes%2F-1793262594"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
                    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api" data-active="" data-toggle=":docs/java_api">API</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/java_api"><div class="symbol monospace"><span class="token keyword">interface </span><a href="-dependency/index.html">Dependency</a></div><div class="brief ">A <code class="lang-kotlin">Dependency</code> represents a dependency on the artifacts from a particular source.</div></div>                  </div>
                </div>
              </div>
            </div>
          </div>
<a data-name="1554004053%2FClasslikes%2F-1793262594" anchor-label="DependencyArtifact" id="1554004053%2FClasslikes%2F-1793262594" data-filterable-set=":docs/java_api"></a>
          <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="-dependency-artifact/index.html"><span>Dependency</span><wbr></wbr><span><span>Artifact</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="1554004053%2FClasslikes%2F-1793262594"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
                    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api" data-active="" data-toggle=":docs/java_api">API</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/java_api"><div class="symbol monospace"><span class="token keyword">interface </span><a href="-dependency-artifact/index.html">DependencyArtifact</a></div><div class="brief ">An <code class="lang-kotlin">Artifact</code> represents an artifact included in a <a href="-dependency/index.html">org.gradle.api.artifacts.Dependency</a>.</div></div>                  </div>
                </div>
              </div>
            </div>
          </div>
<a data-name="2132224310%2FClasslikes%2F-1793262594" anchor-label="DependencyArtifactSelector" id="2132224310%2FClasslikes%2F-1793262594" data-filterable-set=":docs/java_api"></a>
          <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="-dependency-artifact-selector/index.html"><span>Dependency</span><wbr></wbr><span>Artifact</span><wbr></wbr><span><span>Selector</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="2132224310%2FClasslikes%2F-1793262594"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
                    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api" data-active="" data-toggle=":docs/java_api">API</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/java_api"><div class="symbol monospace"><span class="token keyword">interface </span><a href="-dependency-artifact-selector/index.html">DependencyArtifactSelector</a></div><div class="brief ">Details about an artifact selection in the context of a dependency substitution.</div></div>                  </div>
                </div>
              </div>
            </div>
          </div>
<a data-name="-138631894%2FClasslikes%2F-1793262594" anchor-label="DependencyConstraint" id="-138631894%2FClasslikes%2F-1793262594" data-filterable-set=":docs/java_api"></a>
          <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="-dependency-constraint/index.html"><span>Dependency</span><wbr></wbr><span><span>Constraint</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="-138631894%2FClasslikes%2F-1793262594"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
                    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api" data-active="" data-toggle=":docs/java_api">API</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/java_api"><div class="symbol monospace"><span class="token keyword">interface </span><a href="-dependency-constraint/index.html">DependencyConstraint</a> : <a href="-module-version-selector/index.html">ModuleVersionSelector</a>, <a href="../org.gradle.api.attributes/-has-configurable-attributes/index.html">HasConfigurableAttributes</a><span class="token operator">&lt;</span><a href="../org.gradle.api.attributes/-has-configurable-attributes/index.html">SELF</a><span class="token operator">&gt; </span></div><div class="brief ">Represents a constraints over all, including transitive, dependencies.</div></div>                  </div>
                </div>
              </div>
            </div>
          </div>
<a data-name="1953514427%2FClasslikes%2F-1793262594" anchor-label="DependencyConstraintMetadata" id="1953514427%2FClasslikes%2F-1793262594" data-filterable-set=":docs/java_api"></a>
          <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="-dependency-constraint-metadata/index.html"><span>Dependency</span><wbr></wbr><span>Constraint</span><wbr></wbr><span><span>Metadata</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="1953514427%2FClasslikes%2F-1793262594"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
                    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api" data-active="" data-toggle=":docs/java_api">API</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/java_api"><div class="symbol monospace"><span class="token keyword">interface </span><a href="-dependency-constraint-metadata/index.html">DependencyConstraintMetadata</a> : <a href="-dependency-metadata/index.html">DependencyMetadata</a><span class="token operator">&lt;</span><a href="-dependency-metadata/index.html">SELF</a><span class="token operator">&gt; </span></div><div class="brief ">Describes a dependency constraint declared in a resolved component's metadata, which typically originates from a component descriptor (Gradle metadata file).</div></div>                  </div>
                </div>
              </div>
            </div>
          </div>
<a data-name="1845043862%2FClasslikes%2F-1793262594" anchor-label="DependencyConstraintSet" id="1845043862%2FClasslikes%2F-1793262594" data-filterable-set=":docs/java_api"></a>
          <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="-dependency-constraint-set/index.html"><span>Dependency</span><wbr></wbr><span>Constraint</span><wbr></wbr><span><span>Set</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="1845043862%2FClasslikes%2F-1793262594"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
                    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api" data-active="" data-toggle=":docs/java_api">API</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/java_api"><div class="symbol monospace"><span class="token keyword">interface </span><a href="-dependency-constraint-set/index.html">DependencyConstraintSet</a> : <a href="../org.gradle.api/-domain-object-set/index.html">DomainObjectSet</a><span class="token operator">&lt;</span><a href="../org.gradle.api/-domain-object-set/index.html">T</a><span class="token operator">&gt; </span></div><div class="brief ">A set of dependency constraint definitions that are associated with a configuration.</div></div>                  </div>
                </div>
              </div>
            </div>
          </div>
<a data-name="-1479175978%2FClasslikes%2F-1793262594" anchor-label="DependencyConstraintsMetadata" id="-1479175978%2FClasslikes%2F-1793262594" data-filterable-set=":docs/java_api"></a>
          <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="-dependency-constraints-metadata/index.html"><span>Dependency</span><wbr></wbr><span>Constraints</span><wbr></wbr><span><span>Metadata</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="-1479175978%2FClasslikes%2F-1793262594"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
                    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api" data-active="" data-toggle=":docs/java_api">API</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/java_api"><div class="symbol monospace"><span class="token keyword">interface </span><a href="-dependency-constraints-metadata/index.html">DependencyConstraintsMetadata</a> : <a href="-dependencies-metadata/index.html">DependenciesMetadata</a><span class="token operator">&lt;</span><a href="-dependencies-metadata/index.html">T</a><span class="token operator">&gt; </span></div><div class="brief ">Describes the dependency constraints of a variant declared in a resolved component's metadata, which typically originate from a component descriptor (Gradle metadata file).</div></div>                  </div>
                </div>
              </div>
            </div>
          </div>
<a data-name="-1417308776%2FClasslikes%2F-1793262594" anchor-label="DependencyMetadata" id="-1417308776%2FClasslikes%2F-1793262594" data-filterable-set=":docs/java_api"></a>
          <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="-dependency-metadata/index.html"><span>Dependency</span><wbr></wbr><span><span>Metadata</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="-1417308776%2FClasslikes%2F-1793262594"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
                    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api" data-active="" data-toggle=":docs/java_api">API</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/java_api"><div class="symbol monospace"><span class="token keyword">interface </span><a href="-dependency-metadata/index.html">DependencyMetadata</a><span class="token operator">&lt;</span><span class="token keyword"></span><a href="-dependency-metadata/index.html">SELF</a><span class="token operator"> : </span><a href="-dependency-metadata/index.html">DependencyMetadata</a><span class="token operator">?</span><span class="token operator">&gt;</span></div><div class="brief ">Describes a metadata about a dependency - direct dependency or dependency constraint - declared in a resolved component's metadata.</div></div>                  </div>
                </div>
              </div>
            </div>
          </div>
<a data-name="132562151%2FClasslikes%2F-1793262594" anchor-label="DependencyResolutionListener" id="132562151%2FClasslikes%2F-1793262594" data-filterable-set=":docs/java_api"></a>
          <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="-dependency-resolution-listener/index.html"><span>Dependency</span><wbr></wbr><span>Resolution</span><wbr></wbr><span><span>Listener</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="132562151%2FClasslikes%2F-1793262594"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
                    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api" data-active="" data-toggle=":docs/java_api">API</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/java_api"><div class="symbol monospace"><span class="token keyword">interface </span><a href="-dependency-resolution-listener/index.html">DependencyResolutionListener</a></div><div class="brief ">A <code class="lang-kotlin">DependencyResolutionListener</code> is notified as dependencies are resolved.</div></div>                  </div>
                </div>
              </div>
            </div>
          </div>
<a data-name="1551753073%2FClasslikes%2F-1793262594" anchor-label="DependencyResolveDetails" id="1551753073%2FClasslikes%2F-1793262594" data-filterable-set=":docs/java_api"></a>
          <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="-dependency-resolve-details/index.html"><span>Dependency</span><wbr></wbr><span>Resolve</span><wbr></wbr><span><span>Details</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="1551753073%2FClasslikes%2F-1793262594"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
                    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api" data-active="" data-toggle=":docs/java_api">API</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/java_api"><div class="symbol monospace"><span class="token keyword">interface </span><a href="-dependency-resolve-details/index.html">DependencyResolveDetails</a></div><div class="brief ">Provides details about a dependency when it is resolved.</div></div>                  </div>
                </div>
              </div>
            </div>
          </div>
<a data-name="154270117%2FClasslikes%2F-1793262594" anchor-label="DependencyScopeConfiguration" id="154270117%2FClasslikes%2F-1793262594" data-filterable-set=":docs/java_api"></a>
          <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="-dependency-scope-configuration/index.html"><span>Dependency</span><wbr></wbr><span>Scope</span><wbr></wbr><span><span>Configuration</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="154270117%2FClasslikes%2F-1793262594"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
                    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api" data-active="" data-toggle=":docs/java_api">API</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/java_api"><div class="symbol monospace"><div class="block"><div class="block"><span class="token annotation builtin">@</span><a href="../org.gradle.api/-incubating/index.html"><span class="token annotation builtin">Incubating</span></a></div></div><span class="token keyword">interface </span><a href="-dependency-scope-configuration/index.html">DependencyScopeConfiguration</a> : <a href="-configuration/index.html">Configuration</a></div><div class="brief ">A <a href="-configuration/index.html">Configuration</a> which collects dependencies, dependency constraints, and exclude rules.</div></div>                  </div>
                </div>
              </div>
            </div>
          </div>
<a data-name="2128960153%2FClasslikes%2F-1793262594" anchor-label="DependencySet" id="2128960153%2FClasslikes%2F-1793262594" data-filterable-set=":docs/java_api"></a>
          <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="-dependency-set/index.html"><span>Dependency</span><wbr></wbr><span><span>Set</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="2128960153%2FClasslikes%2F-1793262594"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
                    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api" data-active="" data-toggle=":docs/java_api">API</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/java_api"><div class="symbol monospace"><span class="token keyword">interface </span><a href="-dependency-set/index.html">DependencySet</a> : <a href="../org.gradle.api/-domain-object-set/index.html">DomainObjectSet</a><span class="token operator">&lt;</span><a href="../org.gradle.api/-domain-object-set/index.html">T</a><span class="token operator">&gt; </span>, <a href="../org.gradle.api/-buildable/index.html">Buildable</a></div><div class="brief ">A set of artifact dependencies.</div></div>                  </div>
                </div>
              </div>
            </div>
          </div>
<a data-name="-28506486%2FClasslikes%2F-1793262594" anchor-label="DependencySubstitution" id="-28506486%2FClasslikes%2F-1793262594" data-filterable-set=":docs/java_api"></a>
          <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="-dependency-substitution/index.html"><span>Dependency</span><wbr></wbr><span><span>Substitution</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="-28506486%2FClasslikes%2F-1793262594"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
                    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api" data-active="" data-toggle=":docs/java_api">API</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/java_api"><div class="symbol monospace"><span class="token keyword">interface </span><a href="-dependency-substitution/index.html">DependencySubstitution</a></div><div class="brief ">Provides means to substitute a different dependency during resolution.</div></div>                  </div>
                </div>
              </div>
            </div>
          </div>
<a data-name="-964818971%2FClasslikes%2F-1793262594" anchor-label="DependencySubstitutions" id="-964818971%2FClasslikes%2F-1793262594" data-filterable-set=":docs/java_api"></a>
          <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="-dependency-substitutions/index.html"><span>Dependency</span><wbr></wbr><span><span>Substitutions</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="-964818971%2FClasslikes%2F-1793262594"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
                    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api" data-active="" data-toggle=":docs/java_api">API</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/java_api"><div class="symbol monospace"><span class="token keyword">interface </span><a href="-dependency-substitutions/index.html">DependencySubstitutions</a></div><div class="brief ">Allows replacing dependencies with other dependencies.</div></div>                  </div>
                </div>
              </div>
            </div>
          </div>
<a data-name="495659281%2FClasslikes%2F-1793262594" anchor-label="DirectDependenciesMetadata" id="495659281%2FClasslikes%2F-1793262594" data-filterable-set=":docs/java_api"></a>
          <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="-direct-dependencies-metadata/index.html"><span>Direct</span><wbr></wbr><span>Dependencies</span><wbr></wbr><span><span>Metadata</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="495659281%2FClasslikes%2F-1793262594"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
                    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api" data-active="" data-toggle=":docs/java_api">API</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/java_api"><div class="symbol monospace"><span class="token keyword">interface </span><a href="-direct-dependencies-metadata/index.html">DirectDependenciesMetadata</a> : <a href="-dependencies-metadata/index.html">DependenciesMetadata</a><span class="token operator">&lt;</span><a href="-dependencies-metadata/index.html">T</a><span class="token operator">&gt; </span></div><div class="brief ">Describes the dependencies of a variant declared in a resolved component's metadata, which typically originate from a component descriptor (Gradle metadata file, Ivy file, Maven POM).</div></div>                  </div>
                </div>
              </div>
            </div>
          </div>
<a data-name="-119663313%2FClasslikes%2F-1793262594" anchor-label="DirectDependencyMetadata" id="-119663313%2FClasslikes%2F-1793262594" data-filterable-set=":docs/java_api"></a>
          <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="-direct-dependency-metadata/index.html"><span>Direct</span><wbr></wbr><span>Dependency</span><wbr></wbr><span><span>Metadata</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="-119663313%2FClasslikes%2F-1793262594"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
                    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api" data-active="" data-toggle=":docs/java_api">API</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/java_api"><div class="symbol monospace"><span class="token keyword">interface </span><a href="-direct-dependency-metadata/index.html">DirectDependencyMetadata</a> : <a href="-dependency-metadata/index.html">DependencyMetadata</a><span class="token operator">&lt;</span><a href="-dependency-metadata/index.html">SELF</a><span class="token operator">&gt; </span></div><div class="brief ">Describes a dependency declared in a resolved component's metadata, which typically originates from a component descriptor (Gradle metadata file, Ivy file, Maven POM).</div></div>                  </div>
                </div>
              </div>
            </div>
          </div>
<a data-name="997727322%2FClasslikes%2F-1793262594" anchor-label="ExcludeRule" id="997727322%2FClasslikes%2F-1793262594" data-filterable-set=":docs/java_api"></a>
          <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="-exclude-rule/index.html"><span>Exclude</span><wbr></wbr><span><span>Rule</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="997727322%2FClasslikes%2F-1793262594"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
                    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api" data-active="" data-toggle=":docs/java_api">API</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/java_api"><div class="symbol monospace"><span class="token keyword">interface </span><a href="-exclude-rule/index.html">ExcludeRule</a></div><div class="brief ">An <code class="lang-kotlin">ExcludeRule</code> is used to describe transitive dependencies that should be excluded when resolving dependencies.</div></div>                  </div>
                </div>
              </div>
            </div>
          </div>
<a data-name="789905639%2FClasslikes%2F-1793262594" anchor-label="ExcludeRuleContainer" id="789905639%2FClasslikes%2F-1793262594" data-filterable-set=":docs/java_api"></a>
          <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="-exclude-rule-container/index.html"><span>Exclude</span><wbr></wbr><span>Rule</span><wbr></wbr><span><span>Container</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="789905639%2FClasslikes%2F-1793262594"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
                    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api" data-active="" data-toggle=":docs/java_api">API</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/java_api"><div class="symbol monospace"><span class="token keyword">interface </span><a href="-exclude-rule-container/index.html">ExcludeRuleContainer</a></div><div class="brief ">A container for adding exclude rules for dependencies.</div></div>                  </div>
                </div>
              </div>
            </div>
          </div>
<a data-name="1244351292%2FClasslikes%2F-1793262594" anchor-label="ExternalDependency" id="1244351292%2FClasslikes%2F-1793262594" data-filterable-set=":docs/java_api"></a>
          <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="-external-dependency/index.html"><span>External</span><wbr></wbr><span><span>Dependency</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="1244351292%2FClasslikes%2F-1793262594"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
                    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api" data-active="" data-toggle=":docs/java_api">API</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/java_api"><div class="symbol monospace"><span class="token keyword">interface </span><a href="-external-dependency/index.html">ExternalDependency</a> : <a href="-module-dependency/index.html">ModuleDependency</a>, <a href="-module-version-selector/index.html">ModuleVersionSelector</a></div><div class="brief ">An <code class="lang-kotlin">ExternalDependency</code> is a <a href="-dependency/index.html">Dependency</a> on a source outside the current project hierarchy.</div></div>                  </div>
                </div>
              </div>
            </div>
          </div>
<a data-name="1839260848%2FClasslikes%2F-1793262594" anchor-label="ExternalModuleDependency" id="1839260848%2FClasslikes%2F-1793262594" data-filterable-set=":docs/java_api"></a>
          <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="-external-module-dependency/index.html"><span>External</span><wbr></wbr><span>Module</span><wbr></wbr><span><span>Dependency</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="1839260848%2FClasslikes%2F-1793262594"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
                    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api" data-active="" data-toggle=":docs/java_api">API</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/java_api"><div class="symbol monospace"><span class="token keyword">interface </span><a href="-external-module-dependency/index.html">ExternalModuleDependency</a> : <a href="-external-dependency/index.html">ExternalDependency</a></div><div class="brief ">A <code class="lang-kotlin">ModuleDependency</code> is a <a href="-dependency/index.html">Dependency</a> on a module outside the current project hierarchy.</div></div>                  </div>
                </div>
              </div>
            </div>
          </div>
<a data-name="-1374697714%2FClasslikes%2F-1793262594" anchor-label="ExternalModuleDependencyBundle" id="-1374697714%2FClasslikes%2F-1793262594" data-filterable-set=":docs/java_api"></a>
          <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="-external-module-dependency-bundle/index.html"><span>External</span><wbr></wbr><span>Module</span><wbr></wbr><span>Dependency</span><wbr></wbr><span><span>Bundle</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="-1374697714%2FClasslikes%2F-1793262594"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
                    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api" data-active="" data-toggle=":docs/java_api">API</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/java_api"><div class="symbol monospace"><span class="token keyword">interface </span><a href="-external-module-dependency-bundle/index.html">ExternalModuleDependencyBundle</a> : <a href="https://docs.oracle.com/en/java/javase/11/docs/api/java.base/java/util/List.html">List</a><span class="token operator">&lt;</span><a href="https://docs.oracle.com/en/java/javase/11/docs/api/java.base/java/util/List.html">E</a><span class="token operator">&gt; </span></div><div class="brief ">A bundle is a list of dependencies which are always added together.</div></div>                  </div>
                </div>
              </div>
            </div>
          </div>
<a data-name="45061805%2FClasslikes%2F-1793262594" anchor-label="FileCollectionDependency" id="45061805%2FClasslikes%2F-1793262594" data-filterable-set=":docs/java_api"></a>
          <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="-file-collection-dependency/index.html"><span>File</span><wbr></wbr><span>Collection</span><wbr></wbr><span><span>Dependency</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="45061805%2FClasslikes%2F-1793262594"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
                    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api" data-active="" data-toggle=":docs/java_api">API</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/java_api"><div class="symbol monospace"><span class="token keyword">interface </span><a href="-file-collection-dependency/index.html">FileCollectionDependency</a> : <a href="-self-resolving-dependency/index.html">SelfResolvingDependency</a></div><div class="brief ">A <code class="lang-kotlin">FileCollectionDependency</code> is a <a href="-dependency/index.html">Dependency</a> on a collection of local files which are not stored in a repository.</div></div>                  </div>
                </div>
              </div>
            </div>
          </div>
<a data-name="988764627%2FClasslikes%2F-1793262594" anchor-label="LenientConfiguration" id="988764627%2FClasslikes%2F-1793262594" data-filterable-set=":docs/java_api"></a>
          <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="-lenient-configuration/index.html"><span>Lenient</span><wbr></wbr><span><span>Configuration</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="988764627%2FClasslikes%2F-1793262594"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
                    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api" data-active="" data-toggle=":docs/java_api">API</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/java_api"><div class="symbol monospace"><span class="token keyword">interface </span><a href="-lenient-configuration/index.html">LenientConfiguration</a></div><div class="brief ">Resolved configuration that does not fail eagerly when some dependencies are not resolved, or some artifacts do not exist.</div></div>                  </div>
                </div>
              </div>
            </div>
          </div>
<a data-name="925121517%2FClasslikes%2F-1793262594" anchor-label="MinimalExternalModuleDependency" id="925121517%2FClasslikes%2F-1793262594" data-filterable-set=":docs/java_api"></a>
          <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="-minimal-external-module-dependency/index.html"><span>Minimal</span><wbr></wbr><span>External</span><wbr></wbr><span>Module</span><wbr></wbr><span><span>Dependency</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="925121517%2FClasslikes%2F-1793262594"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
                    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api" data-active="" data-toggle=":docs/java_api">API</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/java_api"><div class="symbol monospace"><span class="token keyword">interface </span><a href="-minimal-external-module-dependency/index.html">MinimalExternalModuleDependency</a> : <a href="-external-module-dependency/index.html">ExternalModuleDependency</a></div><div class="brief ">The minimal information Gradle needs to address an external module.</div></div>                  </div>
                </div>
              </div>
            </div>
          </div>
<a data-name="1948301627%2FClasslikes%2F-1793262594" anchor-label="ModuleDependency" id="1948301627%2FClasslikes%2F-1793262594" data-filterable-set=":docs/java_api"></a>
          <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="-module-dependency/index.html"><span>Module</span><wbr></wbr><span><span>Dependency</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="1948301627%2FClasslikes%2F-1793262594"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
                    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api" data-active="" data-toggle=":docs/java_api">API</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/java_api"><div class="symbol monospace"><span class="token keyword">interface </span><a href="-module-dependency/index.html">ModuleDependency</a> : <a href="-dependency/index.html">Dependency</a>, <a href="../org.gradle.api.attributes/-has-configurable-attributes/index.html">HasConfigurableAttributes</a><span class="token operator">&lt;</span><a href="../org.gradle.api.attributes/-has-configurable-attributes/index.html">SELF</a><span class="token operator">&gt; </span></div><div class="brief ">A <code class="lang-kotlin">ModuleDependency</code> is a <a href="-dependency/index.html">org.gradle.api.artifacts.Dependency</a> on a module outside the current project.</div></div>                  </div>
                </div>
              </div>
            </div>
          </div>
<a data-name="175608531%2FClasslikes%2F-1793262594" anchor-label="ModuleDependencyCapabilitiesHandler" id="175608531%2FClasslikes%2F-1793262594" data-filterable-set=":docs/java_api"></a>
          <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="-module-dependency-capabilities-handler/index.html"><span>Module</span><wbr></wbr><span>Dependency</span><wbr></wbr><span>Capabilities</span><wbr></wbr><span><span>Handler</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="175608531%2FClasslikes%2F-1793262594"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
                    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api" data-active="" data-toggle=":docs/java_api">API</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/java_api"><div class="symbol monospace"><span class="token keyword">interface </span><a href="-module-dependency-capabilities-handler/index.html">ModuleDependencyCapabilitiesHandler</a></div><div class="brief ">The capabilities requested for a dependency.</div></div>                  </div>
                </div>
              </div>
            </div>
          </div>
<a data-name="-160856675%2FClasslikes%2F-1793262594" anchor-label="ModuleIdentifier" id="-160856675%2FClasslikes%2F-1793262594" data-filterable-set=":docs/java_api"></a>
          <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="-module-identifier/index.html"><span>Module</span><wbr></wbr><span><span>Identifier</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="-160856675%2FClasslikes%2F-1793262594"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
                    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api" data-active="" data-toggle=":docs/java_api">API</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/java_api"><div class="symbol monospace"><span class="token keyword">interface </span><a href="-module-identifier/index.html">ModuleIdentifier</a> : <a href="https://docs.oracle.com/en/java/javase/11/docs/api/java.base/java/io/Serializable.html">Serializable</a></div><div class="brief ">The identifier of a module.</div></div>                  </div>
                </div>
              </div>
            </div>
          </div>
<a data-name="-22995941%2FClasslikes%2F-1793262594" anchor-label="ModuleVersionIdentifier" id="-22995941%2FClasslikes%2F-1793262594" data-filterable-set=":docs/java_api"></a>
          <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="-module-version-identifier/index.html"><span>Module</span><wbr></wbr><span>Version</span><wbr></wbr><span><span>Identifier</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="-22995941%2FClasslikes%2F-1793262594"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
                    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api" data-active="" data-toggle=":docs/java_api">API</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/java_api"><div class="symbol monospace"><span class="token keyword">interface </span><a href="-module-version-identifier/index.html">ModuleVersionIdentifier</a> : <a href="https://docs.oracle.com/en/java/javase/11/docs/api/java.base/java/io/Serializable.html">Serializable</a></div><div class="brief ">The identifier of a module version.</div></div>                  </div>
                </div>
              </div>
            </div>
          </div>
<a data-name="-387891611%2FClasslikes%2F-1793262594" anchor-label="ModuleVersionSelector" id="-387891611%2FClasslikes%2F-1793262594" data-filterable-set=":docs/java_api"></a>
          <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="-module-version-selector/index.html"><span>Module</span><wbr></wbr><span>Version</span><wbr></wbr><span><span>Selector</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="-387891611%2FClasslikes%2F-1793262594"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
                    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api" data-active="" data-toggle=":docs/java_api">API</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/java_api"><div class="symbol monospace"><span class="token keyword">interface </span><a href="-module-version-selector/index.html">ModuleVersionSelector</a></div><div class="brief ">Selects a module version.</div></div>                  </div>
                </div>
              </div>
            </div>
          </div>
<a data-name="2097058025%2FClasslikes%2F-1793262594" anchor-label="MutableVariantFilesMetadata" id="2097058025%2FClasslikes%2F-1793262594" data-filterable-set=":docs/java_api"></a>
          <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="-mutable-variant-files-metadata/index.html"><span>Mutable</span><wbr></wbr><span>Variant</span><wbr></wbr><span>Files</span><wbr></wbr><span><span>Metadata</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="2097058025%2FClasslikes%2F-1793262594"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
                    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api" data-active="" data-toggle=":docs/java_api">API</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/java_api"><div class="symbol monospace"><span class="token keyword">interface </span><a href="-mutable-variant-files-metadata/index.html">MutableVariantFilesMetadata</a></div><div class="brief ">Mutable information about the files that belong to a variant.</div></div>                  </div>
                </div>
              </div>
            </div>
          </div>
<a data-name="1677894595%2FClasslikes%2F-1793262594" anchor-label="MutableVersionConstraint" id="1677894595%2FClasslikes%2F-1793262594" data-filterable-set=":docs/java_api"></a>
          <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="-mutable-version-constraint/index.html"><span>Mutable</span><wbr></wbr><span>Version</span><wbr></wbr><span><span>Constraint</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="1677894595%2FClasslikes%2F-1793262594"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
                    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api" data-active="" data-toggle=":docs/java_api">API</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/java_api"><div class="symbol monospace"><span class="token keyword">interface </span><a href="-mutable-version-constraint/index.html">MutableVersionConstraint</a> : <a href="-version-constraint/index.html">VersionConstraint</a></div><div class="brief ">A configurable version constraint.</div></div>                  </div>
                </div>
              </div>
            </div>
          </div>
<a data-name="1840564620%2FClasslikes%2F-1793262594" anchor-label="ProjectDependency" id="1840564620%2FClasslikes%2F-1793262594" data-filterable-set=":docs/java_api"></a>
          <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="-project-dependency/index.html"><span>Project</span><wbr></wbr><span><span>Dependency</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="1840564620%2FClasslikes%2F-1793262594"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
                    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api" data-active="" data-toggle=":docs/java_api">API</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/java_api"><div class="symbol monospace"><span class="token keyword">interface </span><a href="-project-dependency/index.html">ProjectDependency</a> : <a href="-module-dependency/index.html">ModuleDependency</a>, <a href="-self-resolving-dependency/index.html">SelfResolvingDependency</a></div><div class="brief ">A <code class="lang-kotlin">ProjectDependency</code> is a <a href="-dependency/index.html">Dependency</a> on another project in the current project hierarchy.</div></div>                  </div>
                </div>
              </div>
            </div>
          </div>
<a data-name="1315392303%2FClasslikes%2F-1793262594" anchor-label="PublishArtifact" id="1315392303%2FClasslikes%2F-1793262594" data-filterable-set=":docs/java_api"></a>
          <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="-publish-artifact/index.html"><span>Publish</span><wbr></wbr><span><span>Artifact</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="1315392303%2FClasslikes%2F-1793262594"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
                    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api" data-active="" data-toggle=":docs/java_api">API</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/java_api"><div class="symbol monospace"><span class="token keyword">interface </span><a href="-publish-artifact/index.html">PublishArtifact</a> : <a href="../org.gradle.api/-buildable/index.html">Buildable</a></div><div class="brief ">A <code class="lang-kotlin">PublishArtifact</code> is an artifact produced by a project.</div></div>                  </div>
                </div>
              </div>
            </div>
          </div>
<a data-name="-360250767%2FClasslikes%2F-1793262594" anchor-label="PublishArtifactSet" id="-360250767%2FClasslikes%2F-1793262594" data-filterable-set=":docs/java_api"></a>
          <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="-publish-artifact-set/index.html"><span>Publish</span><wbr></wbr><span>Artifact</span><wbr></wbr><span><span>Set</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="-360250767%2FClasslikes%2F-1793262594"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
                    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api" data-active="" data-toggle=":docs/java_api">API</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/java_api"><div class="symbol monospace"><span class="token keyword">interface </span><a href="-publish-artifact-set/index.html">PublishArtifactSet</a> : <a href="../org.gradle.api/-domain-object-set/index.html">DomainObjectSet</a><span class="token operator">&lt;</span><a href="../org.gradle.api/-domain-object-set/index.html">T</a><span class="token operator">&gt; </span>, <a href="../org.gradle.api/-buildable/index.html">Buildable</a></div><div class="brief ">A set of artifacts to be published.</div></div>                  </div>
                </div>
              </div>
            </div>
          </div>
<a data-name="-1759423438%2FClasslikes%2F-1793262594" anchor-label="PublishException" id="-1759423438%2FClasslikes%2F-1793262594" data-filterable-set=":docs/java_api"></a>
          <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="-publish-exception/index.html"><span>Publish</span><wbr></wbr><span><span>Exception</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="-1759423438%2FClasslikes%2F-1793262594"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
                    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api" data-active="" data-toggle=":docs/java_api">API</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/java_api"><div class="symbol monospace"><span class="token keyword">open </span><span class="token keyword">class </span><a href="-publish-exception/index.html">PublishException</a> : <a href="../org.gradle.api/-gradle-exception/index.html">GradleException</a></div><div class="brief ">A <code class="lang-kotlin">PublishException</code> is thrown when a dependency configuration cannot be published for some reason.</div></div>                  </div>
                </div>
              </div>
            </div>
          </div>
<a data-name="-1717973869%2FClasslikes%2F-1793262594" anchor-label="ResolutionStrategy" id="-1717973869%2FClasslikes%2F-1793262594" data-filterable-set=":docs/java_api"></a>
          <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="-resolution-strategy/index.html"><span>Resolution</span><wbr></wbr><span><span>Strategy</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="-1717973869%2FClasslikes%2F-1793262594"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
                    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api" data-active="" data-toggle=":docs/java_api">API</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/java_api"><div class="symbol monospace"><span class="token keyword">interface </span><a href="-resolution-strategy/index.html">ResolutionStrategy</a></div><div class="brief ">Defines the strategies around dependency resolution.</div></div>                  </div>
                </div>
              </div>
            </div>
          </div>
<a data-name="116714797%2FClasslikes%2F-1793262594" anchor-label="ResolvableConfiguration" id="116714797%2FClasslikes%2F-1793262594" data-filterable-set=":docs/java_api"></a>
          <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="-resolvable-configuration/index.html"><span>Resolvable</span><wbr></wbr><span><span>Configuration</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="116714797%2FClasslikes%2F-1793262594"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
                    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api" data-active="" data-toggle=":docs/java_api">API</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/java_api"><div class="symbol monospace"><div class="block"><div class="block"><span class="token annotation builtin">@</span><a href="../org.gradle.api/-incubating/index.html"><span class="token annotation builtin">Incubating</span></a></div></div><span class="token keyword">interface </span><a href="-resolvable-configuration/index.html">ResolvableConfiguration</a> : <a href="-configuration/index.html">Configuration</a></div><div class="brief ">A <a href="-configuration/index.html">Configuration</a> which performs dependency resolution to build dependency graphs and resolve artifacts.</div></div>                  </div>
                </div>
              </div>
            </div>
          </div>
<a data-name="-769691722%2FClasslikes%2F-1793262594" anchor-label="ResolvableDependencies" id="-769691722%2FClasslikes%2F-1793262594" data-filterable-set=":docs/java_api"></a>
          <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="-resolvable-dependencies/index.html"><span>Resolvable</span><wbr></wbr><span><span>Dependencies</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="-769691722%2FClasslikes%2F-1793262594"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
                    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api" data-active="" data-toggle=":docs/java_api">API</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/java_api"><div class="symbol monospace"><span class="token keyword">interface </span><a href="-resolvable-dependencies/index.html">ResolvableDependencies</a> : <a href="-artifact-view/index.html">ArtifactView</a></div><div class="brief ">A set of <a href="-dependency/index.html">Dependency</a> objects which can be resolved to a set of files.</div></div>                  </div>
                </div>
              </div>
            </div>
          </div>
<a data-name="1045976456%2FClasslikes%2F-1793262594" anchor-label="ResolvedArtifact" id="1045976456%2FClasslikes%2F-1793262594" data-filterable-set=":docs/java_api"></a>
          <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="-resolved-artifact/index.html"><span>Resolved</span><wbr></wbr><span><span>Artifact</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="1045976456%2FClasslikes%2F-1793262594"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
                    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api" data-active="" data-toggle=":docs/java_api">API</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/java_api"><div class="symbol monospace"><span class="token keyword">interface </span><a href="-resolved-artifact/index.html">ResolvedArtifact</a></div><div class="brief ">Information about a resolved artifact.</div></div>                  </div>
                </div>
              </div>
            </div>
          </div>
<a data-name="-1402554702%2FClasslikes%2F-1793262594" anchor-label="ResolvedConfiguration" id="-1402554702%2FClasslikes%2F-1793262594" data-filterable-set=":docs/java_api"></a>
          <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="-resolved-configuration/index.html"><span>Resolved</span><wbr></wbr><span><span>Configuration</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="-1402554702%2FClasslikes%2F-1793262594"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
                    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api" data-active="" data-toggle=":docs/java_api">API</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/java_api"><div class="symbol monospace"><span class="token keyword">interface </span><a href="-resolved-configuration/index.html">ResolvedConfiguration</a></div><div class="brief ">A <code class="lang-kotlin">ResolvedConfiguration</code> represents the result of resolving a <a href="-configuration/index.html">Configuration</a>, and provides access to both the artifacts and the meta-data of the result.</div></div>                  </div>
                </div>
              </div>
            </div>
          </div>
<a data-name="1035656175%2FClasslikes%2F-1793262594" anchor-label="ResolvedDependency" id="1035656175%2FClasslikes%2F-1793262594" data-filterable-set=":docs/java_api"></a>
          <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="-resolved-dependency/index.html"><span>Resolved</span><wbr></wbr><span><span>Dependency</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="1035656175%2FClasslikes%2F-1793262594"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
                    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api" data-active="" data-toggle=":docs/java_api">API</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/java_api"><div class="symbol monospace"><span class="token keyword">interface </span><a href="-resolved-dependency/index.html">ResolvedDependency</a></div><div class="brief ">Information about a resolved dependency.</div></div>                  </div>
                </div>
              </div>
            </div>
          </div>
<a data-name="101272476%2FClasslikes%2F-1793262594" anchor-label="ResolvedModuleVersion" id="101272476%2FClasslikes%2F-1793262594" data-filterable-set=":docs/java_api"></a>
          <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="-resolved-module-version/index.html"><span>Resolved</span><wbr></wbr><span>Module</span><wbr></wbr><span><span>Version</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="101272476%2FClasslikes%2F-1793262594"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
                    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api" data-active="" data-toggle=":docs/java_api">API</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/java_api"><div class="symbol monospace"><span class="token keyword">interface </span><a href="-resolved-module-version/index.html">ResolvedModuleVersion</a></div><div class="brief ">Represents meta-data about a resolved module version.</div></div>                  </div>
                </div>
              </div>
            </div>
          </div>
<a data-name="515228111%2FClasslikes%2F-1793262594" anchor-label="ResolveException" id="515228111%2FClasslikes%2F-1793262594" data-filterable-set=":docs/java_api"></a>
          <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="-resolve-exception/index.html"><span>Resolve</span><wbr></wbr><span><span>Exception</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="515228111%2FClasslikes%2F-1793262594"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
                    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api" data-active="" data-toggle=":docs/java_api">API</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/java_api"><div class="symbol monospace"><span class="token keyword">open </span><span class="token keyword">class </span><a href="-resolve-exception/index.html">ResolveException</a> : <span data-unresolved-link="org.gradle.internal.exceptions/DefaultMultiCauseException///PointingToDeclaration/">DefaultMultiCauseException</span></div><div class="brief ">A <code class="lang-kotlin">ResolveException</code> is thrown when dependency resolution fails for some reason.</div></div>                  </div>
                </div>
              </div>
            </div>
          </div>
<a data-name="-1807317720%2FClasslikes%2F-1793262594" anchor-label="SelfResolvingDependency" id="-1807317720%2FClasslikes%2F-1793262594" data-filterable-set=":docs/java_api"></a>
          <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="-self-resolving-dependency/index.html"><span>Self</span><wbr></wbr><span>Resolving</span><wbr></wbr><span><span>Dependency</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="-1807317720%2FClasslikes%2F-1793262594"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
                    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api" data-active="" data-toggle=":docs/java_api">API</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/java_api"><div class="symbol monospace"><span class="token keyword">interface </span><a href="-self-resolving-dependency/index.html">SelfResolvingDependency</a> : <a href="-dependency/index.html">Dependency</a>, <a href="../org.gradle.api/-buildable/index.html">Buildable</a></div><div class="brief ">A <code class="lang-kotlin">SelfResolvingDependency</code> is a <a href="-dependency/index.html">Dependency</a> which is able to resolve itself, independent of a repository.</div></div>                  </div>
                </div>
              </div>
            </div>
          </div>
<a data-name="2104569421%2FClasslikes%2F-1793262594" anchor-label="UnknownConfigurationException" id="2104569421%2FClasslikes%2F-1793262594" data-filterable-set=":docs/java_api"></a>
          <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="-unknown-configuration-exception/index.html"><span>Unknown</span><wbr></wbr><span>Configuration</span><wbr></wbr><span><span>Exception</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="2104569421%2FClasslikes%2F-1793262594"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
                    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api" data-active="" data-toggle=":docs/java_api">API</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/java_api"><div class="symbol monospace"><span class="token keyword">open </span><span class="token keyword">class </span><a href="-unknown-configuration-exception/index.html">UnknownConfigurationException</a> : <a href="../org.gradle.api/-unknown-domain-object-exception/index.html">UnknownDomainObjectException</a></div><div class="brief ">An <code class="lang-kotlin">UnknownConfigurationException</code> is thrown when a configuration referenced by name cannot be found.</div></div>                  </div>
                </div>
              </div>
            </div>
          </div>
<a data-name="-2065092649%2FClasslikes%2F-1793262594" anchor-label="UnknownRepositoryException" id="-2065092649%2FClasslikes%2F-1793262594" data-filterable-set=":docs/java_api"></a>
          <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="-unknown-repository-exception/index.html"><span>Unknown</span><wbr></wbr><span>Repository</span><wbr></wbr><span><span>Exception</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="-2065092649%2FClasslikes%2F-1793262594"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
                    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api" data-active="" data-toggle=":docs/java_api">API</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/java_api"><div class="symbol monospace"><span class="token keyword">open </span><span class="token keyword">class </span><a href="-unknown-repository-exception/index.html">UnknownRepositoryException</a> : <a href="../org.gradle.api/-unknown-domain-object-exception/index.html">UnknownDomainObjectException</a></div><div class="brief ">An <code class="lang-kotlin">UnknownRepositoryException</code> is thrown when a repository referenced by name cannot be found.</div></div>                  </div>
                </div>
              </div>
            </div>
          </div>
<a data-name="2017106806%2FClasslikes%2F-1793262594" anchor-label="UnresolvedDependency" id="2017106806%2FClasslikes%2F-1793262594" data-filterable-set=":docs/java_api"></a>
          <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="-unresolved-dependency/index.html"><span>Unresolved</span><wbr></wbr><span><span>Dependency</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="2017106806%2FClasslikes%2F-1793262594"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
                    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api" data-active="" data-toggle=":docs/java_api">API</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/java_api"><div class="symbol monospace"><span class="token keyword">interface </span><a href="-unresolved-dependency/index.html">UnresolvedDependency</a></div><div class="brief ">Unsuccessfully resolved dependency.</div></div>                  </div>
                </div>
              </div>
            </div>
          </div>
<a data-name="-332486496%2FClasslikes%2F-1793262594" anchor-label="VariantFileMetadata" id="-332486496%2FClasslikes%2F-1793262594" data-filterable-set=":docs/java_api"></a>
          <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="-variant-file-metadata/index.html"><span>Variant</span><wbr></wbr><span>File</span><wbr></wbr><span><span>Metadata</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="-332486496%2FClasslikes%2F-1793262594"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
                    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api" data-active="" data-toggle=":docs/java_api">API</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/java_api"><div class="symbol monospace"><span class="token keyword">interface </span><a href="-variant-file-metadata/index.html">VariantFileMetadata</a></div><div class="brief ">Part of a component variant's metadata representing a file and its location.</div></div>                  </div>
                </div>
              </div>
            </div>
          </div>
<a data-name="124189628%2FClasslikes%2F-1793262594" anchor-label="VariantMetadata" id="124189628%2FClasslikes%2F-1793262594" data-filterable-set=":docs/java_api"></a>
          <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="-variant-metadata/index.html"><span>Variant</span><wbr></wbr><span><span>Metadata</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="124189628%2FClasslikes%2F-1793262594"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
                    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api" data-active="" data-toggle=":docs/java_api">API</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/java_api"><div class="symbol monospace"><span class="token keyword">interface </span><a href="-variant-metadata/index.html">VariantMetadata</a> : <a href="../org.gradle.api.attributes/-has-configurable-attributes/index.html">HasConfigurableAttributes</a><span class="token operator">&lt;</span><a href="../org.gradle.api.attributes/-has-configurable-attributes/index.html">SELF</a><span class="token operator">&gt; </span></div><div class="brief ">Represents the metadata of one variant of a component, see <a href="-component-metadata-details/with-variant.html">withVariant</a>.</div></div>                  </div>
                </div>
              </div>
            </div>
          </div>
<a data-name="-1476305995%2FClasslikes%2F-1793262594" anchor-label="VariantSelectionDetails" id="-1476305995%2FClasslikes%2F-1793262594" data-filterable-set=":docs/java_api"></a>
          <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="-variant-selection-details/index.html"><span>Variant</span><wbr></wbr><span>Selection</span><wbr></wbr><span><span>Details</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="-1476305995%2FClasslikes%2F-1793262594"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
                    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api" data-active="" data-toggle=":docs/java_api">API</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/java_api"><div class="symbol monospace"><span class="token keyword">interface </span><a href="-variant-selection-details/index.html">VariantSelectionDetails</a></div><div class="brief ">Allows configuring the variant-aware selection aspects of a specific dependency.</div></div>                  </div>
                </div>
              </div>
            </div>
          </div>
<a data-name="-1635617935%2FClasslikes%2F-1793262594" anchor-label="VersionCatalog" id="-1635617935%2FClasslikes%2F-1793262594" data-filterable-set=":docs/java_api"></a>
          <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="-version-catalog/index.html"><span>Version</span><wbr></wbr><span><span>Catalog</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="-1635617935%2FClasslikes%2F-1793262594"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
                    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api" data-active="" data-toggle=":docs/java_api">API</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/java_api"><div class="symbol monospace"><div class="block"><div class="block"><span class="token annotation builtin">@</span><a href="../org.gradle.api/-non-null-api/index.html"><span class="token annotation builtin">NonNullApi</span></a></div></div><span class="token keyword">interface </span><a href="-version-catalog/index.html">VersionCatalog</a> : <a href="../org.gradle.api/-named/index.html">Named</a></div><div class="brief ">Provides access to a version catalog.</div></div>                  </div>
                </div>
              </div>
            </div>
          </div>
<a data-name="-972399643%2FClasslikes%2F-1793262594" anchor-label="VersionCatalogsExtension" id="-972399643%2FClasslikes%2F-1793262594" data-filterable-set=":docs/java_api"></a>
          <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="-version-catalogs-extension/index.html"><span>Version</span><wbr></wbr><span>Catalogs</span><wbr></wbr><span><span>Extension</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="-972399643%2FClasslikes%2F-1793262594"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
                    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api" data-active="" data-toggle=":docs/java_api">API</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/java_api"><div class="symbol monospace"><span class="token keyword">interface </span><a href="-version-catalogs-extension/index.html">VersionCatalogsExtension</a> : <a href="https://docs.oracle.com/en/java/javase/11/docs/api/java.base/java/lang/Iterable.html">Iterable</a><span class="token operator">&lt;</span><a href="https://docs.oracle.com/en/java/javase/11/docs/api/java.base/java/lang/Iterable.html">T</a><span class="token operator">&gt; </span></div><div class="brief ">Gives access to all version catalogs available.</div></div>                  </div>
                </div>
              </div>
            </div>
          </div>
<a data-name="1378405659%2FClasslikes%2F-1793262594" anchor-label="VersionConstraint" id="1378405659%2FClasslikes%2F-1793262594" data-filterable-set=":docs/java_api"></a>
          <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="-version-constraint/index.html"><span>Version</span><wbr></wbr><span><span>Constraint</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="1378405659%2FClasslikes%2F-1793262594"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
                    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api" data-active="" data-toggle=":docs/java_api">API</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/java_api"><div class="symbol monospace"><span class="token keyword">interface </span><a href="-version-constraint/index.html">VersionConstraint</a> : <a href="../org.gradle.api/-describable/index.html">Describable</a></div><div class="brief ">Represents a constraint that is used to match module versions to a dependency.</div></div>                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
      <div class="footer">
        <span class="go-to-top-icon"><a href="#content" id="go-to-top-link"></a></span><span>Gradle Kotlin DSL Reference</span><span
                class="pull-right"><span>Generated by </span><a
                href="https://github.com/Kotlin/dokka"><span>dokka</span><span class="padded-icon"></span></a></span>
      </div>
    </div>
</div>
</body>
</html>
