<!DOCTYPE html>
<html>
<head>
    <meta name="viewport" content="width=device-width, initial-scale=1" charset="UTF-8">
    <title>TaskContainerScope</title>
    <link href="../../../images/logo-icon.svg" rel="icon" type="image/svg">
    <script>var pathToRoot = "../../../";</script>
    <script>const storage = localStorage.getItem("dokka-dark-mode")
    if (storage == null) {
        const osDarkSchemePreferred = window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches
        if (osDarkSchemePreferred === true) {
            document.getElementsByTagName("html")[0].classList.add("theme-dark")
        }
    } else {
        const savedDarkMode = JSON.parse(storage)
        if(savedDarkMode === true) {
            document.getElementsByTagName("html")[0].classList.add("theme-dark")
        }
    }
    </script>
<script type="text/javascript" src="../../../scripts/sourceset_dependencies.js" async="async"></script>
<link href="../../../styles/style.css" rel="Stylesheet">
<link href="../../../styles/jetbrains-mono.css" rel="Stylesheet">
<link href="../../../styles/main.css" rel="Stylesheet">
<link href="../../../styles/prism.css" rel="Stylesheet">
<link href="../../../styles/logo-styles.css" rel="Stylesheet">
<script type="text/javascript" src="../../../scripts/clipboard.js" async="async"></script>
<script type="text/javascript" src="../../../scripts/navigation-loader.js" async="async"></script>
<script type="text/javascript" src="../../../scripts/platform-content-handler.js" async="async"></script>
<script type="text/javascript" src="../../../scripts/main.js" defer="defer"></script>
<script type="text/javascript" src="../../../scripts/prism.js" async="async"></script>
<script type="text/javascript" src="../../../scripts/symbol-parameters-wrapper_deferred.js" defer="defer"></script>
<link href="../../../images/gradle-logo.svg">
<link href="../../../styles/gradle.css" rel="Stylesheet">
</head>
<body>
<div class="navigation-wrapper" id="navigation-wrapper">
    <div id="leftToggler"><span class="icon-toggler"></span></div>
    <div class="library-name">
            <a href="../../../index.html">
                    <span>gradle</span>
            </a>
    </div>
    <div>
8.5    </div>
    <div class="pull-right d-flex">
        <div class="filter-section" id="filter-section">
                <button class="platform-tag platform-selector jvm-like" data-active="" data-filter=":docs/kotlin_dsl">DSL</button>
        </div>
        <button id="theme-toggle-button"><span id="theme-toggle"></span></button>
        <div id="searchBar"></div>
    </div>
</div>
<div id="container">
    <div id="leftColumn">
        <div id="sideMenu"></div>
    </div>
    <div id="main">
<div class="main-content" data-page-type="classlike" id="content" pageIds="gradle::org.gradle.kotlin.dsl/TaskContainerScope///PointingToDeclaration//-**********">
  <div class="breadcrumbs"><a href="../../../index.html">gradle</a><span class="delimiter">/</span><a href="../index.html">org.gradle.kotlin.dsl</a><span class="delimiter">/</span><span class="current">TaskContainerScope</span></div>
  <div class="cover ">
    <h1 class="cover"><span>Task</span><wbr></wbr><span>Container</span><wbr></wbr><span><span>Scope</span></span></h1>
    <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
      <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/kotlin_dsl" data-filterable-set=":docs/kotlin_dsl" data-active="" data-toggle=":docs/kotlin_dsl">DSL</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/kotlin_dsl"><div class="symbol monospace"><span class="token keyword">class </span><a href="index.html">TaskContainerScope</a> : <span data-unresolved-link="org.gradle.kotlin.dsl.support.delegates/TaskContainerDelegate///PointingToDeclaration/">TaskContainerDelegate</span><span class="clearfix"><span class="floating-right">(<a href="https://github.com/gradle/gradle/blob/28aca86a7180baa17117e0e5ba01d8ea9feca598/platforms/core-configuration/kotlin-dsl/src/main/kotlin/org/gradle/kotlin/dsl/TaskContainerExtensions.kt#L147">source</a>)</span></span></div><p class="paragraph">Receiver for the <code class="lang-kotlin">tasks</code> block providing an extended set of operators for the configuration of tasks.</p></div>    </div>
  </div>
  <div class="tabbedcontent">
    <div class="tabs-section" tabs-section="tabs-section"><button class="section-tab" data-active="" data-togglable="CONSTRUCTOR,TYPE,PROPERTY,FUNCTION">Members</button><button class="section-tab" data-togglable="CONSTRUCTOR,TYPE,PROPERTY,FUNCTION,EXTENSION_PROPERTY,EXTENSION_FUNCTION">Members &amp; Extensions</button></div>
    <div class="tabs-section-body">
      <div data-togglable="TYPE">
        <h2 class="">Types</h2>
        <div class="table"><a data-name="-1075146407%2FClasslikes%2F-**********" anchor-label="Companion" id="-1075146407%2FClasslikes%2F-**********" data-filterable-set=":docs/kotlin_dsl"></a>
          <div class="table-row" data-filterable-current=":docs/kotlin_dsl" data-filterable-set=":docs/kotlin_dsl">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="-companion/index.html"><span><span>Companion</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="-1075146407%2FClasslikes%2F-**********"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
                    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/kotlin_dsl" data-filterable-set=":docs/kotlin_dsl" data-active="" data-toggle=":docs/kotlin_dsl">DSL</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/kotlin_dsl"><div class="symbol monospace"><span class="token keyword">object </span><a href="-companion/index.html">Companion</a></div></div>                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div data-togglable="PROPERTY">
        <h2 class="">Properties</h2>
        <div class="table"><a data-name="-2042833231%2FProperties%2F-**********" anchor-label="container" id="-2042833231%2FProperties%2F-**********" data-filterable-set=":docs/kotlin_dsl"></a>
          <div class="table-row" data-filterable-current=":docs/kotlin_dsl" data-filterable-set=":docs/kotlin_dsl">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="container.html"><span><span>container</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="-2042833231%2FProperties%2F-**********"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
                    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/kotlin_dsl" data-filterable-set=":docs/kotlin_dsl" data-active="" data-toggle=":docs/kotlin_dsl">DSL</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/kotlin_dsl"><div class="symbol monospace"><span class="token keyword"></span><span class="token keyword">val </span><a href="container.html">container</a><span class="token operator">: </span><a href="../../org.gradle.api.tasks/-task-container/index.html#-**********%2FMain%2F-**********">TaskContainer</a></div></div>                  </div>
                </div>
              </div>
            </div>
          </div>
<a data-name="-127433782%2FProperties%2F-**********" anchor-label="creating" id="-127433782%2FProperties%2F-**********" data-filterable-set=":docs/kotlin_dsl"></a>
          <div class="table-row" data-togglable="EXTENSION_PROPERTY" data-filterable-current=":docs/kotlin_dsl" data-filterable-set=":docs/kotlin_dsl">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="../creating.html"><span><span>creating</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="-127433782%2FProperties%2F-**********"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
                    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/kotlin_dsl" data-filterable-set=":docs/kotlin_dsl" data-active="" data-toggle=":docs/kotlin_dsl">DSL</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/kotlin_dsl"><div class="symbol monospace"><span class="token keyword"></span><span class="token keyword">val </span><span class="token operator">&lt;</span><span class="token keyword"></span><a href="../creating.html">T</a><span class="token operator"> : </span><a href="https://kotlinlang.org/api/latest/jvm/stdlib/kotlin/-any/index.html">Any</a><span class="token operator">&gt; </span><a href="../../org.gradle.api/-named-domain-object-container/index.html#**********%2FMain%2F-**********">NamedDomainObjectContainer</a><span class="token operator">&lt;</span><span class="token keyword"></span><a href="../creating.html">T</a><span class="token operator">&gt;</span><span class="token punctuation">.</span><a href="../creating.html">creating</a><span class="token operator">: </span><a href="../-named-domain-object-container-creating-delegate-provider/index.html">NamedDomainObjectContainerCreatingDelegateProvider</a><span class="token operator">&lt;</span><span class="token keyword"></span><a href="../creating.html">T</a><span class="token operator">&gt;</span></div><div class="brief "><p class="paragraph">Provides a property delegate that creates elements of the default collection type.</p></div></div>                  </div>
                </div>
              </div>
            </div>
          </div>
<a data-name="**********%2FProperties%2F-**********" anchor-label="delegate" id="**********%2FProperties%2F-**********" data-filterable-set=":docs/kotlin_dsl"></a>
          <div class="table-row" data-filterable-current=":docs/kotlin_dsl" data-filterable-set=":docs/kotlin_dsl">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="delegate.html"><span><span>delegate</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="**********%2FProperties%2F-**********"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
                    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/kotlin_dsl" data-filterable-set=":docs/kotlin_dsl" data-active="" data-toggle=":docs/kotlin_dsl">DSL</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/kotlin_dsl"><div class="symbol monospace"><span class="token keyword">open </span><span class="token keyword"></span><span class="token keyword">val </span><a href="delegate.html">delegate</a><span class="token operator">: </span><a href="../../org.gradle.api.tasks/-task-container/index.html#-**********%2FMain%2F-**********">TaskContainer</a></div></div>                  </div>
                </div>
              </div>
            </div>
          </div>
<a data-name="-498739443%2FProperties%2F-**********" anchor-label="size" id="-498739443%2FProperties%2F-**********" data-filterable-set=":docs/kotlin_dsl"></a>
          <div class="table-row" data-filterable-current=":docs/kotlin_dsl" data-filterable-set=":docs/kotlin_dsl">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="index.html#-498739443%2FProperties%2F-**********"><span><span>size</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="-498739443%2FProperties%2F-**********"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
                    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/kotlin_dsl" data-filterable-set=":docs/kotlin_dsl" data-active="" data-toggle=":docs/kotlin_dsl">DSL</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/kotlin_dsl"><div class="symbol monospace"><span class="token keyword">open </span><span class="token keyword">override </span><span class="token keyword">val </span><a href="index.html#-498739443%2FProperties%2F-**********">size</a><span class="token operator">: </span><a href="https://kotlinlang.org/api/latest/jvm/stdlib/kotlin/-int/index.html">Int</a></div></div>                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div data-togglable="FUNCTION">
        <h2 class="">Functions</h2>
        <div class="table"><a data-name="525559169%2FFunctions%2F-**********" anchor-label="add" id="525559169%2FFunctions%2F-**********" data-filterable-set=":docs/kotlin_dsl"></a>
          <div class="table-row" data-filterable-current=":docs/kotlin_dsl" data-filterable-set=":docs/kotlin_dsl">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="index.html#525559169%2FFunctions%2F-**********"><span><span>add</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="525559169%2FFunctions%2F-**********"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
                    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/kotlin_dsl" data-filterable-set=":docs/kotlin_dsl" data-active="" data-toggle=":docs/kotlin_dsl">DSL</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/kotlin_dsl"><div class="symbol monospace"><span class="token keyword">open </span><span class="token keyword">override </span><span class="token keyword">fun </span><a href="index.html#525559169%2FFunctions%2F-**********"><span class="token function">add</span></a><span class="token punctuation">(</span><span class="parameters "><span class="parameter ">element<span class="token operator">: </span><a href="../../org.gradle.api/-task/index.html#217904858%2FMain%2F-**********">Task</a></span></span><span class="token punctuation">)</span><span class="token operator">: </span><a href="https://kotlinlang.org/api/latest/jvm/stdlib/kotlin/-boolean/index.html">Boolean</a></div></div>                  </div>
                </div>
              </div>
            </div>
          </div>
<a data-name="1693803760%2FFunctions%2F-**********" anchor-label="addAll" id="1693803760%2FFunctions%2F-**********" data-filterable-set=":docs/kotlin_dsl"></a>
          <div class="table-row" data-filterable-current=":docs/kotlin_dsl" data-filterable-set=":docs/kotlin_dsl">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="index.html#1693803760%2FFunctions%2F-**********"><span>add</span><wbr></wbr><span><span>All</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="1693803760%2FFunctions%2F-**********"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
                    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/kotlin_dsl" data-filterable-set=":docs/kotlin_dsl" data-active="" data-toggle=":docs/kotlin_dsl">DSL</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/kotlin_dsl"><div class="symbol monospace"><span class="token keyword">open </span><span class="token keyword">override </span><span class="token keyword">fun </span><a href="index.html#1693803760%2FFunctions%2F-**********"><span class="token function">addAll</span></a><span class="token punctuation">(</span><span class="parameters "><span class="parameter ">elements<span class="token operator">: </span><a href="https://kotlinlang.org/api/latest/jvm/stdlib/kotlin.collections/-collection/index.html">Collection</a><span class="token operator">&lt;</span><span class="token keyword"></span><a href="../../org.gradle.api/-task/index.html#217904858%2FMain%2F-**********">Task</a><span class="token operator">&gt;</span></span></span><span class="token punctuation">)</span><span class="token operator">: </span><a href="https://kotlinlang.org/api/latest/jvm/stdlib/kotlin/-boolean/index.html">Boolean</a></div></div>                  </div>
                </div>
              </div>
            </div>
          </div>
<a data-name="**********%2FFunctions%2F-**********" anchor-label="addAllLater" id="**********%2FFunctions%2F-**********" data-filterable-set=":docs/kotlin_dsl"></a>
          <div class="table-row" data-filterable-current=":docs/kotlin_dsl" data-filterable-set=":docs/kotlin_dsl">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="index.html#**********%2FFunctions%2F-**********"><span>add</span><wbr></wbr><span>All</span><wbr></wbr><span><span>Later</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="**********%2FFunctions%2F-**********"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
                    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/kotlin_dsl" data-filterable-set=":docs/kotlin_dsl" data-active="" data-toggle=":docs/kotlin_dsl">DSL</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/kotlin_dsl"><div class="symbol monospace"><span class="token keyword">open </span><span class="token keyword">override </span><span class="token keyword">fun </span><a href="index.html#**********%2FFunctions%2F-**********"><span class="token function">addAllLater</span></a><span class="token punctuation">(</span><span class="parameters "><span class="parameter ">provider<span class="token operator">: </span><span data-unresolved-link="org.gradle.api.provider/Provider///PointingToDeclaration/">Provider</span><span class="token operator">&lt;</span><span class="token keyword">out </span><a href="https://kotlinlang.org/api/latest/jvm/stdlib/kotlin.collections/-iterable/index.html">Iterable</a><span class="token operator">&lt;</span><span class="token keyword"></span><a href="../../org.gradle.api/-task/index.html#217904858%2FMain%2F-**********">Task</a><span class="token operator">&gt;</span><span class="token operator">&gt;</span></span></span><span class="token punctuation">)</span></div></div>                  </div>
                </div>
              </div>
            </div>
          </div>
<a data-name="882419962%2FFunctions%2F-**********" anchor-label="addLater" id="882419962%2FFunctions%2F-**********" data-filterable-set=":docs/kotlin_dsl"></a>
          <div class="table-row" data-filterable-current=":docs/kotlin_dsl" data-filterable-set=":docs/kotlin_dsl">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="index.html#882419962%2FFunctions%2F-**********"><span>add</span><wbr></wbr><span><span>Later</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="882419962%2FFunctions%2F-**********"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
                    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/kotlin_dsl" data-filterable-set=":docs/kotlin_dsl" data-active="" data-toggle=":docs/kotlin_dsl">DSL</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/kotlin_dsl"><div class="symbol monospace"><span class="token keyword">open </span><span class="token keyword">override </span><span class="token keyword">fun </span><a href="index.html#882419962%2FFunctions%2F-**********"><span class="token function">addLater</span></a><span class="token punctuation">(</span><span class="parameters "><span class="parameter ">provider<span class="token operator">: </span><span data-unresolved-link="org.gradle.api.provider/Provider///PointingToDeclaration/">Provider</span><span class="token operator">&lt;</span><span class="token keyword">out </span><a href="../../org.gradle.api/-task/index.html#217904858%2FMain%2F-**********">Task</a><span class="token operator">&gt;</span></span></span><span class="token punctuation">)</span></div></div>                  </div>
                </div>
              </div>
            </div>
          </div>
<a data-name="-32229267%2FFunctions%2F-**********" anchor-label="addRule" id="-32229267%2FFunctions%2F-**********" data-filterable-set=":docs/kotlin_dsl"></a>
          <div class="table-row" data-filterable-current=":docs/kotlin_dsl" data-filterable-set=":docs/kotlin_dsl">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="index.html#**********%2FFunctions%2F-**********"><span>add</span><wbr></wbr><span><span>Rule</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="-32229267%2FFunctions%2F-**********"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
                    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/kotlin_dsl" data-filterable-set=":docs/kotlin_dsl" data-active="" data-toggle=":docs/kotlin_dsl">DSL</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/kotlin_dsl"><div class="symbol monospace"><span class="token keyword">open </span><span class="token keyword">override </span><span class="token keyword">fun </span><a href="index.html#**********%2FFunctions%2F-**********"><span class="token function">addRule</span></a><span class="token punctuation">(</span><span class="parameters "><span class="parameter ">rule<span class="token operator">: </span><span data-unresolved-link="org.gradle.api/Rule///PointingToDeclaration/">Rule</span></span></span><span class="token punctuation">)</span><span class="token operator">: </span><span data-unresolved-link="org.gradle.api/Rule///PointingToDeclaration/">Rule</span></div><div class="symbol monospace"><span class="token keyword">open </span><span class="token keyword">override </span><span class="token keyword">fun </span><a href="index.html#-1782074086%2FFunctions%2F-**********"><span class="token function">addRule</span></a><span class="token punctuation">(</span><span class="parameters "><span class="parameter ">description<span class="token operator">: </span><a href="https://kotlinlang.org/api/latest/jvm/stdlib/kotlin/-string/index.html">String</a><span class="token punctuation">, </span></span><span class="parameter ">ruleAction<span class="token operator">: </span><span data-unresolved-link="groovy.lang/Closure///PointingToDeclaration/">Closure</span><span class="token operator">&lt;</span><span class="token keyword"></span><a href="https://kotlinlang.org/api/latest/jvm/stdlib/kotlin/-any/index.html">Any</a><span class="token operator">&gt;</span></span></span><span class="token punctuation">)</span><span class="token operator">: </span><span data-unresolved-link="org.gradle.api/Rule///PointingToDeclaration/">Rule</span></div><div class="symbol monospace"><span class="token keyword">open </span><span class="token keyword">override </span><span class="token keyword">fun </span><a href="index.html#-2137283913%2FFunctions%2F-**********"><span class="token function">addRule</span></a><span class="token punctuation">(</span><span class="parameters "><span class="parameter ">description<span class="token operator">: </span><a href="https://kotlinlang.org/api/latest/jvm/stdlib/kotlin/-string/index.html">String</a><span class="token punctuation">, </span></span><span class="parameter ">ruleAction<span class="token operator">: </span><a href="../../org.gradle.api/-action/index.html#-199246903%2FMain%2F-**********">Action</a><span class="token operator">&lt;</span><span class="token keyword"></span><a href="https://kotlinlang.org/api/latest/jvm/stdlib/kotlin/-string/index.html">String</a><span class="token operator">&gt;</span></span></span><span class="token punctuation">)</span><span class="token operator">: </span><span data-unresolved-link="org.gradle.api/Rule///PointingToDeclaration/">Rule</span></div></div>                  </div>
                </div>
              </div>
            </div>
          </div>
<a data-name="577981789%2FFunctions%2F-**********" anchor-label="all" id="577981789%2FFunctions%2F-**********" data-filterable-set=":docs/kotlin_dsl"></a>
          <div class="table-row" data-filterable-current=":docs/kotlin_dsl" data-filterable-set=":docs/kotlin_dsl">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="index.html#1075105561%2FFunctions%2F-**********"><span><span>all</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="577981789%2FFunctions%2F-**********"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
                    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/kotlin_dsl" data-filterable-set=":docs/kotlin_dsl" data-active="" data-toggle=":docs/kotlin_dsl">DSL</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/kotlin_dsl"><div class="symbol monospace"><span class="token keyword">open </span><span class="token keyword">override </span><span class="token keyword">fun </span><a href="index.html#1075105561%2FFunctions%2F-**********"><span class="token function">all</span></a><span class="token punctuation">(</span><span class="parameters "><span class="parameter ">action<span class="token operator">: </span><span data-unresolved-link="groovy.lang/Closure///PointingToDeclaration/">Closure</span><span class="token operator">&lt;</span><span class="token keyword"></span><a href="https://kotlinlang.org/api/latest/jvm/stdlib/kotlin/-any/index.html">Any</a><span class="token operator">&gt;</span></span></span><span class="token punctuation">)</span></div><div class="symbol monospace"><span class="token keyword">open </span><span class="token keyword">override </span><span class="token keyword">fun </span><a href="index.html#-1687447152%2FFunctions%2F-**********"><span class="token function">all</span></a><span class="token punctuation">(</span><span class="parameters "><span class="parameter ">action<span class="token operator">: </span><a href="../../org.gradle.api/-action/index.html#-199246903%2FMain%2F-**********">Action</a><span class="token operator">&lt;</span><span class="token keyword">in </span><a href="../../org.gradle.api/-task/index.html#217904858%2FMain%2F-**********">Task</a><span class="token operator">&gt;</span></span></span><span class="token punctuation">)</span></div></div>                  </div>
                </div>
              </div>
            </div>
          </div>
<a data-name="-1934494721%2FFunctions%2F-**********" anchor-label="clear" id="-1934494721%2FFunctions%2F-**********" data-filterable-set=":docs/kotlin_dsl"></a>
          <div class="table-row" data-filterable-current=":docs/kotlin_dsl" data-filterable-set=":docs/kotlin_dsl">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="index.html#-1934494721%2FFunctions%2F-**********"><span><span>clear</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="-1934494721%2FFunctions%2F-**********"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
                    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/kotlin_dsl" data-filterable-set=":docs/kotlin_dsl" data-active="" data-toggle=":docs/kotlin_dsl">DSL</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/kotlin_dsl"><div class="symbol monospace"><span class="token keyword">open </span><span class="token keyword">override </span><span class="token keyword">fun </span><a href="index.html#-1934494721%2FFunctions%2F-**********"><span class="token function">clear</span></a><span class="token punctuation">(</span><span class="token punctuation">)</span></div></div>                  </div>
                </div>
              </div>
            </div>
          </div>
<a data-name="817450142%2FFunctions%2F-**********" anchor-label="configure" id="817450142%2FFunctions%2F-**********" data-filterable-set=":docs/kotlin_dsl"></a>
          <div class="table-row" data-filterable-current=":docs/kotlin_dsl" data-filterable-set=":docs/kotlin_dsl">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="index.html#817450142%2FFunctions%2F-**********"><span><span>configure</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="817450142%2FFunctions%2F-**********"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
                    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/kotlin_dsl" data-filterable-set=":docs/kotlin_dsl" data-active="" data-toggle=":docs/kotlin_dsl">DSL</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/kotlin_dsl"><div class="symbol monospace"><span class="token keyword">open </span><span class="token keyword">override </span><span class="token keyword">fun </span><a href="index.html#817450142%2FFunctions%2F-**********"><span class="token function">configure</span></a><span class="token punctuation">(</span><span class="parameters "><span class="parameter ">configureClosure<span class="token operator">: </span><span data-unresolved-link="groovy.lang/Closure///PointingToDeclaration/">Closure</span><span class="token operator">&lt;</span><span class="token keyword"></span><a href="https://kotlinlang.org/api/latest/jvm/stdlib/kotlin/-any/index.html">Any</a><span class="token operator">&gt;</span></span></span><span class="token punctuation">)</span><span class="token operator">: </span><a href="../../org.gradle.api/-named-domain-object-container/index.html#**********%2FMain%2F-**********">NamedDomainObjectContainer</a><span class="token operator">&lt;</span><span class="token keyword"></span><a href="../../org.gradle.api/-task/index.html#217904858%2FMain%2F-**********">Task</a><span class="token operator">&gt;</span></div></div>                  </div>
                </div>
              </div>
            </div>
          </div>
<a data-name="1506946218%2FFunctions%2F-**********" anchor-label="configureEach" id="1506946218%2FFunctions%2F-**********" data-filterable-set=":docs/kotlin_dsl"></a>
          <div class="table-row" data-filterable-current=":docs/kotlin_dsl" data-filterable-set=":docs/kotlin_dsl">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="index.html#1506946218%2FFunctions%2F-**********"><span>configure</span><wbr></wbr><span><span>Each</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="1506946218%2FFunctions%2F-**********"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
                    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/kotlin_dsl" data-filterable-set=":docs/kotlin_dsl" data-active="" data-toggle=":docs/kotlin_dsl">DSL</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/kotlin_dsl"><div class="symbol monospace"><span class="token keyword">open </span><span class="token keyword">override </span><span class="token keyword">fun </span><a href="index.html#1506946218%2FFunctions%2F-**********"><span class="token function">configureEach</span></a><span class="token punctuation">(</span><span class="parameters "><span class="parameter ">action<span class="token operator">: </span><a href="../../org.gradle.api/-action/index.html#-199246903%2FMain%2F-**********">Action</a><span class="token operator">&lt;</span><span class="token keyword">in </span><a href="../../org.gradle.api/-task/index.html#217904858%2FMain%2F-**********">Task</a><span class="token operator">&gt;</span></span></span><span class="token punctuation">)</span></div></div>                  </div>
                </div>
              </div>
            </div>
          </div>
<a data-name="-1094054223%2FFunctions%2F-**********" anchor-label="containerWithType" id="-1094054223%2FFunctions%2F-**********" data-filterable-set=":docs/kotlin_dsl"></a>
          <div class="table-row" data-filterable-current=":docs/kotlin_dsl" data-filterable-set=":docs/kotlin_dsl">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="index.html#-1094054223%2FFunctions%2F-**********"><span>container</span><wbr></wbr><span>With</span><wbr></wbr><span><span>Type</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="-1094054223%2FFunctions%2F-**********"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
                    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/kotlin_dsl" data-filterable-set=":docs/kotlin_dsl" data-active="" data-toggle=":docs/kotlin_dsl">DSL</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/kotlin_dsl"><div class="symbol monospace"><span class="token keyword">open </span><span class="token keyword">override </span><span class="token keyword">fun </span><span class="token operator">&lt;</span><span class="token keyword"></span><a href="index.html#-1094054223%2FFunctions%2F-**********">U</a><span class="token operator"> : </span><a href="../../org.gradle.api/-task/index.html#217904858%2FMain%2F-**********">Task</a><span class="token operator">&gt; </span><a href="index.html#-1094054223%2FFunctions%2F-**********"><span class="token function">containerWithType</span></a><span class="token punctuation">(</span><span class="parameters "><span class="parameter ">type<span class="token operator">: </span><a href="https://docs.oracle.com/en/java/javase/11/docs/api/java.base/java/lang/Class.html">Class</a><span class="token operator">&lt;</span><span class="token keyword"></span><a href="index.html#-1094054223%2FFunctions%2F-**********">U</a><span class="token operator">&gt;</span></span></span><span class="token punctuation">)</span><span class="token operator">: </span><a href="../../org.gradle.api/-named-domain-object-container/index.html#**********%2FMain%2F-**********">NamedDomainObjectContainer</a><span class="token operator">&lt;</span><span class="token keyword"></span><a href="index.html#-1094054223%2FFunctions%2F-**********">U</a><span class="token operator">&gt;</span></div></div>                  </div>
                </div>
              </div>
            </div>
          </div>
<a data-name="621944769%2FFunctions%2F-**********" anchor-label="containerWithType" id="621944769%2FFunctions%2F-**********" data-filterable-set=":docs/kotlin_dsl"></a>
          <div class="table-row" data-togglable="EXTENSION_FUNCTION" data-filterable-current=":docs/kotlin_dsl" data-filterable-set=":docs/kotlin_dsl">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="../container-with-type.html"><span>container</span><wbr></wbr><span>With</span><wbr></wbr><span><span>Type</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="621944769%2FFunctions%2F-**********"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
                    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/kotlin_dsl" data-filterable-set=":docs/kotlin_dsl" data-active="" data-toggle=":docs/kotlin_dsl">DSL</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/kotlin_dsl"><div class="symbol monospace"><span class="token keyword">inline </span><span class="token keyword">fun </span><span class="token operator">&lt;</span><span class="token keyword"></span><a href="../container-with-type.html">U</a><span class="token operator"> : </span><a href="../container-with-type.html">T</a><span class="token punctuation">, </span><span class="token keyword"></span><a href="../container-with-type.html">T</a><span class="token operator"> : </span><a href="https://kotlinlang.org/api/latest/jvm/stdlib/kotlin/-any/index.html">Any</a><span class="token operator">&gt; </span><a href="../../org.gradle.api/-polymorphic-domain-object-container/index.html#427856727%2FMain%2F-**********">PolymorphicDomainObjectContainer</a><span class="token operator">&lt;</span><span class="token keyword"></span><a href="../container-with-type.html">T</a><span class="token operator">&gt;</span><span class="token punctuation">.</span><a href="../container-with-type.html"><span class="token function">containerWithType</span></a><span class="token punctuation">(</span><span class="parameters "><span class="parameter ">type<span class="token operator">: </span><a href="https://kotlinlang.org/api/latest/jvm/stdlib/kotlin.reflect/-k-class/index.html">KClass</a><span class="token operator">&lt;</span><span class="token keyword"></span><a href="../container-with-type.html">U</a><span class="token operator">&gt;</span></span></span><span class="token punctuation">)</span><span class="token operator">: </span><a href="../../org.gradle.api/-named-domain-object-container/index.html#**********%2FMain%2F-**********">NamedDomainObjectContainer</a><span class="token operator">&lt;</span><span class="token keyword"></span><a href="../container-with-type.html">U</a><span class="token operator">&gt;</span></div><div class="brief "><p class="paragraph">Kotlin extension function taking <a href="https://kotlinlang.org/api/latest/jvm/stdlib/kotlin.reflect/-k-class/index.html">kotlin.reflect.KClass</a> for <span data-unresolved-link="org.gradle.api/PolymorphicDomainObjectContainer/containerWithType/#java.lang.Class[TypeParam(bounds=[TypeParam(bounds=[kotlin.Any])])]/PointingToDeclaration/">org.gradle.api.PolymorphicDomainObjectContainer.containerWithType</span>.</p></div></div>                  </div>
                </div>
              </div>
            </div>
          </div>
<a data-name="130138301%2FFunctions%2F-**********" anchor-label="contains" id="130138301%2FFunctions%2F-**********" data-filterable-set=":docs/kotlin_dsl"></a>
          <div class="table-row" data-filterable-current=":docs/kotlin_dsl" data-filterable-set=":docs/kotlin_dsl">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="index.html#130138301%2FFunctions%2F-**********"><span><span>contains</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="130138301%2FFunctions%2F-**********"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
                    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/kotlin_dsl" data-filterable-set=":docs/kotlin_dsl" data-active="" data-toggle=":docs/kotlin_dsl">DSL</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/kotlin_dsl"><div class="symbol monospace"><span class="token keyword">open </span><span class="token keyword">operator override </span><span class="token keyword">fun </span><a href="index.html#130138301%2FFunctions%2F-**********"><span class="token function">contains</span></a><span class="token punctuation">(</span><span class="parameters "><span class="parameter ">element<span class="token operator">: </span><a href="../../org.gradle.api/-task/index.html#217904858%2FMain%2F-**********">Task</a></span></span><span class="token punctuation">)</span><span class="token operator">: </span><a href="https://kotlinlang.org/api/latest/jvm/stdlib/kotlin/-boolean/index.html">Boolean</a></div></div>                  </div>
                </div>
              </div>
            </div>
          </div>
<a data-name="-656805588%2FFunctions%2F-**********" anchor-label="containsAll" id="-656805588%2FFunctions%2F-**********" data-filterable-set=":docs/kotlin_dsl"></a>
          <div class="table-row" data-filterable-current=":docs/kotlin_dsl" data-filterable-set=":docs/kotlin_dsl">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="index.html#-656805588%2FFunctions%2F-**********"><span>contains</span><wbr></wbr><span><span>All</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="-656805588%2FFunctions%2F-**********"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
                    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/kotlin_dsl" data-filterable-set=":docs/kotlin_dsl" data-active="" data-toggle=":docs/kotlin_dsl">DSL</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/kotlin_dsl"><div class="symbol monospace"><span class="token keyword">open </span><span class="token keyword">override </span><span class="token keyword">fun </span><a href="index.html#-656805588%2FFunctions%2F-**********"><span class="token function">containsAll</span></a><span class="token punctuation">(</span><span class="parameters "><span class="parameter ">elements<span class="token operator">: </span><a href="https://kotlinlang.org/api/latest/jvm/stdlib/kotlin.collections/-collection/index.html">Collection</a><span class="token operator">&lt;</span><span class="token keyword"></span><a href="../../org.gradle.api/-task/index.html#217904858%2FMain%2F-**********">Task</a><span class="token operator">&gt;</span></span></span><span class="token punctuation">)</span><span class="token operator">: </span><a href="https://kotlinlang.org/api/latest/jvm/stdlib/kotlin/-boolean/index.html">Boolean</a></div></div>                  </div>
                </div>
              </div>
            </div>
          </div>
<a data-name="410654318%2FFunctions%2F-**********" anchor-label="create" id="410654318%2FFunctions%2F-**********" data-filterable-set=":docs/kotlin_dsl"></a>
          <div class="table-row" data-filterable-current=":docs/kotlin_dsl" data-filterable-set=":docs/kotlin_dsl">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="index.html#1594418194%2FFunctions%2F-**********"><span><span>create</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="410654318%2FFunctions%2F-**********"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
                    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/kotlin_dsl" data-filterable-set=":docs/kotlin_dsl" data-active="" data-toggle=":docs/kotlin_dsl">DSL</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/kotlin_dsl"><div class="symbol monospace"><span class="token keyword">open </span><span class="token keyword">override </span><span class="token keyword">fun </span><a href="index.html#1594418194%2FFunctions%2F-**********"><span class="token function">create</span></a><span class="token punctuation">(</span><span class="parameters "><span class="parameter ">name<span class="token operator">: </span><a href="https://kotlinlang.org/api/latest/jvm/stdlib/kotlin/-string/index.html">String</a></span></span><span class="token punctuation">)</span><span class="token operator">: </span><a href="../../org.gradle.api/-task/index.html#217904858%2FMain%2F-**********">Task</a></div><div class="symbol monospace"><span class="token keyword">open </span><span class="token keyword">override </span><span class="token keyword">fun </span><a href="index.html#-1988917942%2FFunctions%2F-**********"><span class="token function">create</span></a><span class="token punctuation">(</span><span class="parameters "><span class="parameter ">options<span class="token operator">: </span><a href="https://kotlinlang.org/api/latest/jvm/stdlib/kotlin.collections/-map/index.html">Map</a><span class="token operator">&lt;</span><span class="token keyword"></span><a href="https://kotlinlang.org/api/latest/jvm/stdlib/kotlin/-string/index.html">String</a><span class="token punctuation">, </span><span class="token operator">*</span><span class="token operator">&gt;</span></span></span><span class="token punctuation">)</span><span class="token operator">: </span><a href="../../org.gradle.api/-task/index.html#217904858%2FMain%2F-**********">Task</a></div><div class="symbol monospace"><span class="token keyword">open </span><span class="token keyword">override </span><span class="token keyword">fun </span><a href="index.html#1652558103%2FFunctions%2F-**********"><span class="token function">create</span></a><span class="token punctuation">(</span><span class="parameters "><span class="parameter ">name<span class="token operator">: </span><a href="https://kotlinlang.org/api/latest/jvm/stdlib/kotlin/-string/index.html">String</a><span class="token punctuation">, </span></span><span class="parameter ">configureClosure<span class="token operator">: </span><span data-unresolved-link="groovy.lang/Closure///PointingToDeclaration/">Closure</span><span class="token operator">&lt;</span><span class="token keyword"></span><a href="https://kotlinlang.org/api/latest/jvm/stdlib/kotlin/-any/index.html">Any</a><span class="token operator">&gt;</span></span></span><span class="token punctuation">)</span><span class="token operator">: </span><a href="../../org.gradle.api/-task/index.html#217904858%2FMain%2F-**********">Task</a></div><div class="symbol monospace"><span class="token keyword">open </span><span class="token keyword">override </span><span class="token keyword">fun </span><span class="token operator">&lt;</span><span class="token keyword"></span><a href="index.html#1277734867%2FFunctions%2F-**********">T</a><span class="token operator"> : </span><a href="../../org.gradle.api/-task/index.html#217904858%2FMain%2F-**********">Task</a><span class="token operator">&gt; </span><a href="index.html#1277734867%2FFunctions%2F-**********"><span class="token function">create</span></a><span class="token punctuation">(</span><span class="parameters "><span class="parameter ">name<span class="token operator">: </span><a href="https://kotlinlang.org/api/latest/jvm/stdlib/kotlin/-string/index.html">String</a><span class="token punctuation">, </span></span><span class="parameter ">type<span class="token operator">: </span><a href="https://docs.oracle.com/en/java/javase/11/docs/api/java.base/java/lang/Class.html">Class</a><span class="token operator">&lt;</span><span class="token keyword"></span><a href="index.html#1277734867%2FFunctions%2F-**********">T</a><span class="token operator">&gt;</span></span></span><span class="token punctuation">)</span><span class="token operator">: </span><a href="index.html#1277734867%2FFunctions%2F-**********">T</a></div><div class="symbol monospace"><span class="token keyword">open </span><span class="token keyword">override </span><span class="token keyword">fun </span><a href="index.html#-2009803566%2FFunctions%2F-**********"><span class="token function">create</span></a><span class="token punctuation">(</span><span class="parameters "><span class="parameter ">name<span class="token operator">: </span><a href="https://kotlinlang.org/api/latest/jvm/stdlib/kotlin/-string/index.html">String</a><span class="token punctuation">, </span></span><span class="parameter ">configureAction<span class="token operator">: </span><a href="../../org.gradle.api/-action/index.html#-199246903%2FMain%2F-**********">Action</a><span class="token operator">&lt;</span><span class="token keyword">in </span><a href="../../org.gradle.api/-task/index.html#217904858%2FMain%2F-**********">Task</a><span class="token operator">&gt;</span></span></span><span class="token punctuation">)</span><span class="token operator">: </span><a href="../../org.gradle.api/-task/index.html#217904858%2FMain%2F-**********">Task</a></div><div class="symbol monospace"><span class="token keyword">open </span><span class="token keyword">override </span><span class="token keyword">fun </span><a href="index.html#-1722299825%2FFunctions%2F-**********"><span class="token function">create</span></a><span class="token punctuation">(</span><span class="parameters "><span class="parameter ">options<span class="token operator">: </span><a href="https://kotlinlang.org/api/latest/jvm/stdlib/kotlin.collections/-map/index.html">Map</a><span class="token operator">&lt;</span><span class="token keyword"></span><a href="https://kotlinlang.org/api/latest/jvm/stdlib/kotlin/-string/index.html">String</a><span class="token punctuation">, </span><span class="token operator">*</span><span class="token operator">&gt;</span><span class="token punctuation">, </span></span><span class="parameter ">configureClosure<span class="token operator">: </span><span data-unresolved-link="groovy.lang/Closure///PointingToDeclaration/">Closure</span><span class="token operator">&lt;</span><span class="token keyword"></span><a href="https://kotlinlang.org/api/latest/jvm/stdlib/kotlin/-any/index.html">Any</a><span class="token operator">&gt;</span></span></span><span class="token punctuation">)</span><span class="token operator">: </span><a href="../../org.gradle.api/-task/index.html#217904858%2FMain%2F-**********">Task</a></div><div class="symbol monospace"><span class="token keyword">open </span><span class="token keyword">override </span><span class="token keyword">fun </span><span class="token operator">&lt;</span><span class="token keyword"></span><a href="index.html#472423946%2FFunctions%2F-**********">T</a><span class="token operator"> : </span><a href="../../org.gradle.api/-task/index.html#217904858%2FMain%2F-**********">Task</a><span class="token operator">&gt; </span><a href="index.html#472423946%2FFunctions%2F-**********"><span class="token function">create</span></a><span class="token punctuation">(</span><span class="parameters "><span class="parameter ">name<span class="token operator">: </span><a href="https://kotlinlang.org/api/latest/jvm/stdlib/kotlin/-string/index.html">String</a><span class="token punctuation">, </span></span><span class="parameter ">type<span class="token operator">: </span><a href="https://docs.oracle.com/en/java/javase/11/docs/api/java.base/java/lang/Class.html">Class</a><span class="token operator">&lt;</span><span class="token keyword"></span><a href="index.html#472423946%2FFunctions%2F-**********">T</a><span class="token operator">&gt;</span><span class="token punctuation">, </span></span><span class="parameter "><span class="token keyword">vararg </span>constructorArgs<span class="token operator">: </span><a href="https://kotlinlang.org/api/latest/jvm/stdlib/kotlin/-any/index.html">Any</a><span class="token operator">?</span></span></span><span class="token punctuation">)</span><span class="token operator">: </span><a href="index.html#472423946%2FFunctions%2F-**********">T</a></div><div class="symbol monospace"><span class="token keyword">open </span><span class="token keyword">override </span><span class="token keyword">fun </span><span class="token operator">&lt;</span><span class="token keyword"></span><a href="index.html#-568312203%2FFunctions%2F-**********">T</a><span class="token operator"> : </span><a href="../../org.gradle.api/-task/index.html#217904858%2FMain%2F-**********">Task</a><span class="token operator">&gt; </span><a href="index.html#-568312203%2FFunctions%2F-**********"><span class="token function">create</span></a><span class="token punctuation">(</span><span class="parameters "><span class="parameter ">name<span class="token operator">: </span><a href="https://kotlinlang.org/api/latest/jvm/stdlib/kotlin/-string/index.html">String</a><span class="token punctuation">, </span></span><span class="parameter ">type<span class="token operator">: </span><a href="https://docs.oracle.com/en/java/javase/11/docs/api/java.base/java/lang/Class.html">Class</a><span class="token operator">&lt;</span><span class="token keyword"></span><a href="index.html#-568312203%2FFunctions%2F-**********">T</a><span class="token operator">&gt;</span><span class="token punctuation">, </span></span><span class="parameter ">configuration<span class="token operator">: </span><a href="../../org.gradle.api/-action/index.html#-199246903%2FMain%2F-**********">Action</a><span class="token operator">&lt;</span><span class="token keyword">in </span><a href="index.html#-568312203%2FFunctions%2F-**********">T</a><span class="token operator">&gt;</span></span></span><span class="token punctuation">)</span><span class="token operator">: </span><a href="index.html#-568312203%2FFunctions%2F-**********">T</a></div></div>                  </div>
                </div>
              </div>
            </div>
          </div>
<a data-name="-1169386562%2FFunctions%2F-**********" anchor-label="create" id="-1169386562%2FFunctions%2F-**********" data-filterable-set=":docs/kotlin_dsl"></a>
          <div class="table-row" data-togglable="EXTENSION_FUNCTION" data-filterable-current=":docs/kotlin_dsl" data-filterable-set=":docs/kotlin_dsl">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="../create.html"><span><span>create</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="-1169386562%2FFunctions%2F-**********"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
                    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/kotlin_dsl" data-filterable-set=":docs/kotlin_dsl" data-active="" data-toggle=":docs/kotlin_dsl">DSL</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/kotlin_dsl"><div class="symbol monospace"><span class="token keyword">inline </span><span class="token keyword">fun </span><span class="token operator">&lt;</span><span class="token keyword"></span><a href="../create.html">U</a><span class="token operator"> : </span><a href="https://kotlinlang.org/api/latest/jvm/stdlib/kotlin/-any/index.html">Any</a><span class="token operator">&gt; </span><a href="../../org.gradle.api/-polymorphic-domain-object-container/index.html#427856727%2FMain%2F-**********">PolymorphicDomainObjectContainer</a><span class="token operator">&lt;</span><span class="token keyword">in </span><a href="../create.html">U</a><span class="token operator">&gt;</span><span class="token punctuation">.</span><a href="../create.html"><span class="token function">create</span></a><span class="token punctuation">(</span><span class="parameters "><span class="parameter ">name<span class="token operator">: </span><a href="https://kotlinlang.org/api/latest/jvm/stdlib/kotlin/-string/index.html">String</a></span></span><span class="token punctuation">)</span><span class="token operator">: </span><a href="../create.html">U</a></div><div class="brief "><p class="paragraph">Creates a domain object with the specified name and type, and adds it to the container.</p></div><div class="symbol monospace"><span class="token keyword">inline </span><span class="token keyword">fun </span><a href="../../org.gradle.api.tasks/-task-container/index.html#-**********%2FMain%2F-**********">TaskContainer</a><span class="token punctuation">.</span><a href="../create.html"><span class="token function">create</span></a><span class="token punctuation">(</span><span class="parameters "><span class="parameter "><span class="token keyword">vararg </span>options<span class="token operator">: </span><a href="https://kotlinlang.org/api/latest/jvm/stdlib/kotlin/-pair/index.html">Pair</a><span class="token operator">&lt;</span><span class="token keyword"></span><a href="https://kotlinlang.org/api/latest/jvm/stdlib/kotlin/-string/index.html">String</a><span class="token punctuation">, </span><span class="token keyword"></span><a href="https://kotlinlang.org/api/latest/jvm/stdlib/kotlin/-any/index.html">Any</a><span class="token operator">?</span><span class="token operator">&gt;</span></span></span><span class="token punctuation">)</span><span class="token operator">: </span><a href="../../org.gradle.api/-task/index.html#217904858%2FMain%2F-**********">Task</a></div><div class="brief "><p class="paragraph">Kotlin extension function for <span data-unresolved-link="org.gradle.api.tasks/TaskContainer/create/#kotlin.collections.MutableMap[kotlin.String,*]/PointingToDeclaration/">org.gradle.api.tasks.TaskContainer.create</span>.</p></div><div class="symbol monospace"><span class="token keyword">inline </span><span class="token keyword">fun </span><span class="token operator">&lt;</span><span class="token keyword"></span><a href="../create.html">U</a><span class="token operator"> : </span><a href="https://kotlinlang.org/api/latest/jvm/stdlib/kotlin/-any/index.html">Any</a><span class="token operator">&gt; </span><a href="../../org.gradle.api/-polymorphic-domain-object-container/index.html#427856727%2FMain%2F-**********">PolymorphicDomainObjectContainer</a><span class="token operator">&lt;</span><span class="token keyword">in </span><a href="../create.html">U</a><span class="token operator">&gt;</span><span class="token punctuation">.</span><a href="../create.html"><span class="token function">create</span></a><span class="token punctuation">(</span><span class="parameters "><span class="parameter ">name<span class="token operator">: </span><a href="https://kotlinlang.org/api/latest/jvm/stdlib/kotlin/-string/index.html">String</a><span class="token punctuation">, </span></span><span class="parameter "><span class="token keyword">noinline </span>configuration<span class="token operator">: </span><span class="token keyword"></span><a href="../create.html">U</a><span class="token punctuation">.</span><span class="token punctuation">(</span><span class="token punctuation">)</span><span class="token operator"> -&gt; </span><span class="token keyword"></span><a href="https://kotlinlang.org/api/latest/jvm/stdlib/kotlin/-unit/index.html">Unit</a></span></span><span class="token punctuation">)</span><span class="token operator">: </span><a href="../create.html">U</a></div><div class="brief "><p class="paragraph">Creates a domain object with the specified name and type, adds it to the container, and configures it with the specified action.</p></div><div class="symbol monospace"><span class="token keyword">inline </span><span class="token keyword">fun </span><span class="token operator">&lt;</span><span class="token keyword"></span><a href="../create.html">U</a><span class="token operator"> : </span><a href="../create.html">T</a><span class="token punctuation">, </span><span class="token keyword"></span><a href="../create.html">T</a><span class="token operator"> : </span><a href="https://kotlinlang.org/api/latest/jvm/stdlib/kotlin/-any/index.html">Any</a><span class="token operator">&gt; </span><a href="../../org.gradle.api/-polymorphic-domain-object-container/index.html#427856727%2FMain%2F-**********">PolymorphicDomainObjectContainer</a><span class="token operator">&lt;</span><span class="token keyword"></span><a href="../create.html">T</a><span class="token operator">&gt;</span><span class="token punctuation">.</span><a href="../create.html"><span class="token function">create</span></a><span class="token punctuation">(</span><span class="parameters "><span class="parameter ">name<span class="token operator">: </span><a href="https://kotlinlang.org/api/latest/jvm/stdlib/kotlin/-string/index.html">String</a><span class="token punctuation">, </span></span><span class="parameter ">type<span class="token operator">: </span><a href="https://kotlinlang.org/api/latest/jvm/stdlib/kotlin.reflect/-k-class/index.html">KClass</a><span class="token operator">&lt;</span><span class="token keyword"></span><a href="../create.html">U</a><span class="token operator">&gt;</span></span></span><span class="token punctuation">)</span><span class="token operator">: </span><a href="../create.html">U</a></div><div class="symbol monospace"><span class="token keyword">inline </span><span class="token keyword">fun </span><span class="token operator">&lt;</span><span class="token keyword"></span><a href="../create.html">U</a><span class="token operator"> : </span><a href="../create.html">T</a><span class="token punctuation">, </span><span class="token keyword"></span><a href="../create.html">T</a><span class="token operator"> : </span><a href="https://kotlinlang.org/api/latest/jvm/stdlib/kotlin/-any/index.html">Any</a><span class="token operator">&gt; </span><a href="../../org.gradle.api/-polymorphic-domain-object-container/index.html#427856727%2FMain%2F-**********">PolymorphicDomainObjectContainer</a><span class="token operator">&lt;</span><span class="token keyword"></span><a href="../create.html">T</a><span class="token operator">&gt;</span><span class="token punctuation">.</span><a href="../create.html"><span class="token function">create</span></a><span class="token punctuation">(</span><span class="parameters "><span class="parameter ">name<span class="token operator">: </span><a href="https://kotlinlang.org/api/latest/jvm/stdlib/kotlin/-string/index.html">String</a><span class="token punctuation">, </span></span><span class="parameter ">type<span class="token operator">: </span><a href="https://kotlinlang.org/api/latest/jvm/stdlib/kotlin.reflect/-k-class/index.html">KClass</a><span class="token operator">&lt;</span><span class="token keyword"></span><a href="../create.html">U</a><span class="token operator">&gt;</span><span class="token punctuation">, </span></span><span class="parameter ">configuration<span class="token operator">: </span><a href="../../org.gradle.api/-action/index.html#-199246903%2FMain%2F-**********">Action</a><span class="token operator">&lt;</span><span class="token keyword">in </span><a href="../create.html">U</a><span class="token operator">&gt;</span></span></span><span class="token punctuation">)</span><span class="token operator">: </span><a href="../create.html">U</a></div><div class="brief "><p class="paragraph">Kotlin extension function taking <a href="https://kotlinlang.org/api/latest/jvm/stdlib/kotlin.reflect/-k-class/index.html">kotlin.reflect.KClass</a> for <span data-unresolved-link="org.gradle.api/PolymorphicDomainObjectContainer/create/#kotlin.String#java.lang.Class[TypeParam(bounds=[TypeParam(bounds=[kotlin.Any])])]/PointingToDeclaration/">org.gradle.api.PolymorphicDomainObjectContainer.create</span>.</p></div><div class="symbol monospace"><span class="token keyword">inline </span><span class="token keyword">fun </span><span class="token operator">&lt;</span><span class="token keyword"></span><a href="../create.html">T</a><span class="token operator"> : </span><a href="../../org.gradle.api/-task/index.html#217904858%2FMain%2F-**********">Task</a><span class="token operator">&gt; </span><a href="../../org.gradle.api.tasks/-task-container/index.html#-**********%2FMain%2F-**********">TaskContainer</a><span class="token punctuation">.</span><a href="../create.html"><span class="token function">create</span></a><span class="token punctuation">(</span><span class="parameters "><span class="parameter ">name<span class="token operator">: </span><a href="https://kotlinlang.org/api/latest/jvm/stdlib/kotlin/-string/index.html">String</a><span class="token punctuation">, </span></span><span class="parameter "><span class="token keyword">vararg </span>arguments<span class="token operator">: </span><a href="https://kotlinlang.org/api/latest/jvm/stdlib/kotlin/-any/index.html">Any</a></span></span><span class="token punctuation">)</span><span class="token operator">: </span><a href="../create.html">T</a></div><div class="brief "><p class="paragraph">Creates a <a href="../../org.gradle.api/-task/index.html#217904858%2FMain%2F-**********">Task</a> with the given <a href="../create.html">name</a> and type, passing the given arguments to the <span data-unresolved-link="javax.inject/Inject///PointingToDeclaration/">javax.inject.Inject</span>-annotated constructor, and adds it to this project tasks container.</p></div><div class="symbol monospace"><span class="token keyword">inline </span><span class="token keyword">fun </span><span class="token operator">&lt;</span><span class="token keyword"></span><a href="../create.html">T</a><span class="token operator"> : </span><a href="../../org.gradle.api/-task/index.html#217904858%2FMain%2F-**********">Task</a><span class="token operator">&gt; </span><a href="../../org.gradle.api.tasks/-task-container/index.html#-**********%2FMain%2F-**********">TaskContainer</a><span class="token punctuation">.</span><a href="../create.html"><span class="token function">create</span></a><span class="token punctuation">(</span><span class="parameters "><span class="parameter ">name<span class="token operator">: </span><a href="https://kotlinlang.org/api/latest/jvm/stdlib/kotlin/-string/index.html">String</a><span class="token punctuation">, </span></span><span class="parameter ">type<span class="token operator">: </span><a href="https://kotlinlang.org/api/latest/jvm/stdlib/kotlin.reflect/-k-class/index.html">KClass</a><span class="token operator">&lt;</span><span class="token keyword"></span><a href="../create.html">T</a><span class="token operator">&gt;</span></span></span><span class="token punctuation">)</span><span class="token operator">: </span><a href="../create.html">T</a></div><div class="symbol monospace"><span class="token keyword">inline </span><span class="token keyword">fun </span><span class="token operator">&lt;</span><span class="token keyword"></span><a href="../create.html">T</a><span class="token operator"> : </span><a href="../../org.gradle.api/-task/index.html#217904858%2FMain%2F-**********">Task</a><span class="token operator">&gt; </span><a href="../../org.gradle.api.tasks/-task-container/index.html#-**********%2FMain%2F-**********">TaskContainer</a><span class="token punctuation">.</span><a href="../create.html"><span class="token function">create</span></a><span class="token punctuation">(</span><span class="parameters "><span class="parameter ">name<span class="token operator">: </span><a href="https://kotlinlang.org/api/latest/jvm/stdlib/kotlin/-string/index.html">String</a><span class="token punctuation">, </span></span><span class="parameter ">type<span class="token operator">: </span><a href="https://kotlinlang.org/api/latest/jvm/stdlib/kotlin.reflect/-k-class/index.html">KClass</a><span class="token operator">&lt;</span><span class="token keyword"></span><a href="../create.html">T</a><span class="token operator">&gt;</span><span class="token punctuation">, </span></span><span class="parameter "><span class="token keyword">vararg </span>constructorArgs<span class="token operator">: </span><a href="https://kotlinlang.org/api/latest/jvm/stdlib/kotlin/-any/index.html">Any</a></span></span><span class="token punctuation">)</span><span class="token operator">: </span><a href="../create.html">T</a></div><div class="symbol monospace"><span class="token keyword">inline </span><span class="token keyword">fun </span><span class="token operator">&lt;</span><span class="token keyword"></span><a href="../create.html">T</a><span class="token operator"> : </span><a href="../../org.gradle.api/-task/index.html#217904858%2FMain%2F-**********">Task</a><span class="token operator">&gt; </span><a href="../../org.gradle.api.tasks/-task-container/index.html#-**********%2FMain%2F-**********">TaskContainer</a><span class="token punctuation">.</span><a href="../create.html"><span class="token function">create</span></a><span class="token punctuation">(</span><span class="parameters "><span class="parameter ">name<span class="token operator">: </span><a href="https://kotlinlang.org/api/latest/jvm/stdlib/kotlin/-string/index.html">String</a><span class="token punctuation">, </span></span><span class="parameter ">type<span class="token operator">: </span><a href="https://kotlinlang.org/api/latest/jvm/stdlib/kotlin.reflect/-k-class/index.html">KClass</a><span class="token operator">&lt;</span><span class="token keyword"></span><a href="../create.html">T</a><span class="token operator">&gt;</span><span class="token punctuation">, </span></span><span class="parameter ">configuration<span class="token operator">: </span><a href="../../org.gradle.api/-action/index.html#-199246903%2FMain%2F-**********">Action</a><span class="token operator">&lt;</span><span class="token keyword">in </span><a href="../create.html">T</a><span class="token operator">&gt;</span></span></span><span class="token punctuation">)</span><span class="token operator">: </span><a href="../create.html">T</a></div><div class="brief "><p class="paragraph">Kotlin extension function taking <a href="https://kotlinlang.org/api/latest/jvm/stdlib/kotlin.reflect/-k-class/index.html">kotlin.reflect.KClass</a> for <span data-unresolved-link="org.gradle.api.tasks/TaskContainer/create/#kotlin.collections.MutableMap[kotlin.String,*]/PointingToDeclaration/">org.gradle.api.tasks.TaskContainer.create</span>.</p></div></div>                  </div>
                </div>
              </div>
            </div>
          </div>
<a data-name="961862878%2FFunctions%2F-**********" anchor-label="creating" id="961862878%2FFunctions%2F-**********" data-filterable-set=":docs/kotlin_dsl"></a>
          <div class="table-row" data-togglable="EXTENSION_FUNCTION" data-filterable-current=":docs/kotlin_dsl" data-filterable-set=":docs/kotlin_dsl">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="../creating.html"><span><span>creating</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="961862878%2FFunctions%2F-**********"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
                    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/kotlin_dsl" data-filterable-set=":docs/kotlin_dsl" data-active="" data-toggle=":docs/kotlin_dsl">DSL</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/kotlin_dsl"><div class="symbol monospace"><span class="token keyword"></span><span class="token keyword">fun </span><span class="token operator">&lt;</span><span class="token keyword"></span><a href="../creating.html">T</a><span class="token operator"> : </span><a href="https://kotlinlang.org/api/latest/jvm/stdlib/kotlin/-any/index.html">Any</a><span class="token operator">&gt; </span><a href="../../org.gradle.api/-named-domain-object-container/index.html#**********%2FMain%2F-**********">NamedDomainObjectContainer</a><span class="token operator">&lt;</span><span class="token keyword"></span><a href="../creating.html">T</a><span class="token operator">&gt;</span><span class="token punctuation">.</span><a href="../creating.html"><span class="token function">creating</span></a><span class="token punctuation">(</span><span class="parameters "><span class="parameter ">configuration<span class="token operator">: </span><span class="token keyword"></span><a href="../creating.html">T</a><span class="token punctuation">.</span><span class="token punctuation">(</span><span class="token punctuation">)</span><span class="token operator"> -&gt; </span><span class="token keyword"></span><a href="https://kotlinlang.org/api/latest/jvm/stdlib/kotlin/-unit/index.html">Unit</a></span></span><span class="token punctuation">)</span><span class="token operator">: </span><a href="../-named-domain-object-container-creating-delegate-provider/index.html">NamedDomainObjectContainerCreatingDelegateProvider</a><span class="token operator">&lt;</span><span class="token keyword"></span><a href="../creating.html">T</a><span class="token operator">&gt;</span></div><div class="brief "><p class="paragraph">Provides a property delegate that creates elements of the default collection type with the given <a href="../creating.html">configuration</a>.</p></div><div class="symbol monospace"><span class="token keyword"></span><span class="token keyword">fun </span><span class="token operator">&lt;</span><span class="token keyword"></span><a href="../creating.html">T</a><span class="token operator"> : </span><a href="https://kotlinlang.org/api/latest/jvm/stdlib/kotlin/-any/index.html">Any</a><span class="token punctuation">, </span><span class="token keyword"></span><a href="../creating.html">U</a><span class="token operator"> : </span><a href="../creating.html">T</a><span class="token operator">&gt; </span><a href="../../org.gradle.api/-polymorphic-domain-object-container/index.html#427856727%2FMain%2F-**********">PolymorphicDomainObjectContainer</a><span class="token operator">&lt;</span><span class="token keyword"></span><a href="../creating.html">T</a><span class="token operator">&gt;</span><span class="token punctuation">.</span><a href="../creating.html"><span class="token function">creating</span></a><span class="token punctuation">(</span><span class="parameters "><span class="parameter ">type<span class="token operator">: </span><a href="https://kotlinlang.org/api/latest/jvm/stdlib/kotlin.reflect/-k-class/index.html">KClass</a><span class="token operator">&lt;</span><span class="token keyword"></span><a href="../creating.html">U</a><span class="token operator">&gt;</span></span></span><span class="token punctuation">)</span><span class="token operator">: </span><a href="../-polymorphic-domain-object-container-creating-delegate-provider/index.html">PolymorphicDomainObjectContainerCreatingDelegateProvider</a><span class="token operator">&lt;</span><span class="token keyword"></span><a href="../creating.html">T</a><span class="token punctuation">, </span><span class="token keyword"></span><a href="../creating.html">U</a><span class="token operator">&gt;</span></div><div class="brief "><p class="paragraph">Provides a property delegate that creates elements of the given <a href="../creating.html">type</a>.</p></div><div class="symbol monospace"><span class="token keyword"></span><span class="token keyword">fun </span><span class="token operator">&lt;</span><span class="token keyword"></span><a href="../creating.html">T</a><span class="token operator"> : </span><a href="https://kotlinlang.org/api/latest/jvm/stdlib/kotlin/-any/index.html">Any</a><span class="token punctuation">, </span><span class="token keyword"></span><a href="../creating.html">U</a><span class="token operator"> : </span><a href="../creating.html">T</a><span class="token operator">&gt; </span><a href="../../org.gradle.api/-polymorphic-domain-object-container/index.html#427856727%2FMain%2F-**********">PolymorphicDomainObjectContainer</a><span class="token operator">&lt;</span><span class="token keyword"></span><a href="../creating.html">T</a><span class="token operator">&gt;</span><span class="token punctuation">.</span><a href="../creating.html"><span class="token function">creating</span></a><span class="token punctuation">(</span><span class="parameters "><span class="parameter ">type<span class="token operator">: </span><a href="https://docs.oracle.com/en/java/javase/11/docs/api/java.base/java/lang/Class.html">Class</a><span class="token operator">&lt;</span><span class="token keyword"></span><a href="../creating.html">U</a><span class="token operator">&gt;</span><span class="token punctuation">, </span></span><span class="parameter ">configuration<span class="token operator">: </span><span class="token keyword"></span><a href="../creating.html">U</a><span class="token punctuation">.</span><span class="token punctuation">(</span><span class="token punctuation">)</span><span class="token operator"> -&gt; </span><span class="token keyword"></span><a href="https://kotlinlang.org/api/latest/jvm/stdlib/kotlin/-unit/index.html">Unit</a></span></span><span class="token punctuation">)</span><span class="token operator">: </span><a href="../-polymorphic-domain-object-container-creating-delegate-provider/index.html">PolymorphicDomainObjectContainerCreatingDelegateProvider</a><span class="token operator">&lt;</span><span class="token keyword"></span><a href="../creating.html">T</a><span class="token punctuation">, </span><span class="token keyword"></span><a href="../creating.html">U</a><span class="token operator">&gt;</span></div><div class="brief "><p class="paragraph">Provides a property delegate that creates elements of the given <a href="../creating.html">type</a> expressed as a <a href="https://docs.oracle.com/en/java/javase/11/docs/api/java.base/java/lang/Class.html">java.lang.Class</a> with the given <a href="../creating.html">configuration</a>.</p></div><div class="symbol monospace"><span class="token keyword"></span><span class="token keyword">fun </span><span class="token operator">&lt;</span><span class="token keyword"></span><a href="../creating.html">T</a><span class="token operator"> : </span><a href="https://kotlinlang.org/api/latest/jvm/stdlib/kotlin/-any/index.html">Any</a><span class="token punctuation">, </span><span class="token keyword"></span><a href="../creating.html">U</a><span class="token operator"> : </span><a href="../creating.html">T</a><span class="token operator">&gt; </span><a href="../../org.gradle.api/-polymorphic-domain-object-container/index.html#427856727%2FMain%2F-**********">PolymorphicDomainObjectContainer</a><span class="token operator">&lt;</span><span class="token keyword"></span><a href="../creating.html">T</a><span class="token operator">&gt;</span><span class="token punctuation">.</span><a href="../creating.html"><span class="token function">creating</span></a><span class="token punctuation">(</span><span class="parameters "><span class="parameter ">type<span class="token operator">: </span><a href="https://kotlinlang.org/api/latest/jvm/stdlib/kotlin.reflect/-k-class/index.html">KClass</a><span class="token operator">&lt;</span><span class="token keyword"></span><a href="../creating.html">U</a><span class="token operator">&gt;</span><span class="token punctuation">, </span></span><span class="parameter ">configuration<span class="token operator">: </span><span class="token keyword"></span><a href="../creating.html">U</a><span class="token punctuation">.</span><span class="token punctuation">(</span><span class="token punctuation">)</span><span class="token operator"> -&gt; </span><span class="token keyword"></span><a href="https://kotlinlang.org/api/latest/jvm/stdlib/kotlin/-unit/index.html">Unit</a></span></span><span class="token punctuation">)</span><span class="token operator">: </span><a href="../-polymorphic-domain-object-container-creating-delegate-provider/index.html">PolymorphicDomainObjectContainerCreatingDelegateProvider</a><span class="token operator">&lt;</span><span class="token keyword"></span><a href="../creating.html">T</a><span class="token punctuation">, </span><span class="token keyword"></span><a href="../creating.html">U</a><span class="token operator">&gt;</span></div><div class="brief "><p class="paragraph">Provides a property delegate that creates elements of the given <a href="../creating.html">type</a> with the given <a href="../creating.html">configuration</a>.</p></div></div>                  </div>
                </div>
              </div>
            </div>
          </div>
<a data-name="**********%2FFunctions%2F-**********" anchor-label="findAll" id="**********%2FFunctions%2F-**********" data-filterable-set=":docs/kotlin_dsl"></a>
          <div class="table-row" data-filterable-current=":docs/kotlin_dsl" data-filterable-set=":docs/kotlin_dsl">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="index.html#**********%2FFunctions%2F-**********"><span>find</span><wbr></wbr><span><span>All</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="**********%2FFunctions%2F-**********"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
                    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/kotlin_dsl" data-filterable-set=":docs/kotlin_dsl" data-active="" data-toggle=":docs/kotlin_dsl">DSL</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/kotlin_dsl"><div class="symbol monospace"><span class="token keyword">open </span><span class="token keyword">override </span><span class="token keyword">fun </span><a href="index.html#**********%2FFunctions%2F-**********"><span class="token function">findAll</span></a><span class="token punctuation">(</span><span class="parameters "><span class="parameter ">spec<span class="token operator">: </span><span data-unresolved-link="groovy.lang/Closure///PointingToDeclaration/">Closure</span><span class="token operator">&lt;</span><span class="token keyword"></span><a href="https://kotlinlang.org/api/latest/jvm/stdlib/kotlin/-any/index.html">Any</a><span class="token operator">&gt;</span></span></span><span class="token punctuation">)</span><span class="token operator">: </span><a href="https://kotlinlang.org/api/latest/jvm/stdlib/kotlin.collections/-mutable-set/index.html">MutableSet</a><span class="token operator">&lt;</span><span class="token keyword"></span><a href="../../org.gradle.api/-task/index.html#217904858%2FMain%2F-**********">Task</a><span class="token operator">&gt;</span></div></div>                  </div>
                </div>
              </div>
            </div>
          </div>
<a data-name="-1344303375%2FFunctions%2F-**********" anchor-label="findByName" id="-1344303375%2FFunctions%2F-**********" data-filterable-set=":docs/kotlin_dsl"></a>
          <div class="table-row" data-filterable-current=":docs/kotlin_dsl" data-filterable-set=":docs/kotlin_dsl">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="index.html#-1344303375%2FFunctions%2F-**********"><span>find</span><wbr></wbr><span>By</span><wbr></wbr><span><span>Name</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="-1344303375%2FFunctions%2F-**********"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
                    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/kotlin_dsl" data-filterable-set=":docs/kotlin_dsl" data-active="" data-toggle=":docs/kotlin_dsl">DSL</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/kotlin_dsl"><div class="symbol monospace"><span class="token keyword">open </span><span class="token keyword">override </span><span class="token keyword">fun </span><a href="index.html#-1344303375%2FFunctions%2F-**********"><span class="token function">findByName</span></a><span class="token punctuation">(</span><span class="parameters "><span class="parameter ">name<span class="token operator">: </span><a href="https://kotlinlang.org/api/latest/jvm/stdlib/kotlin/-string/index.html">String</a></span></span><span class="token punctuation">)</span><span class="token operator">: </span><a href="../../org.gradle.api/-task/index.html#217904858%2FMain%2F-**********">Task</a><span class="token operator">?</span></div></div>                  </div>
                </div>
              </div>
            </div>
          </div>
<a data-name="2071885067%2FFunctions%2F-**********" anchor-label="findByPath" id="2071885067%2FFunctions%2F-**********" data-filterable-set=":docs/kotlin_dsl"></a>
          <div class="table-row" data-filterable-current=":docs/kotlin_dsl" data-filterable-set=":docs/kotlin_dsl">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="index.html#2071885067%2FFunctions%2F-**********"><span>find</span><wbr></wbr><span>By</span><wbr></wbr><span><span>Path</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="2071885067%2FFunctions%2F-**********"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
                    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/kotlin_dsl" data-filterable-set=":docs/kotlin_dsl" data-active="" data-toggle=":docs/kotlin_dsl">DSL</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/kotlin_dsl"><div class="symbol monospace"><span class="token keyword">open </span><span class="token keyword">override </span><span class="token keyword">fun </span><a href="index.html#2071885067%2FFunctions%2F-**********"><span class="token function">findByPath</span></a><span class="token punctuation">(</span><span class="parameters "><span class="parameter ">path<span class="token operator">: </span><a href="https://kotlinlang.org/api/latest/jvm/stdlib/kotlin/-string/index.html">String</a></span></span><span class="token punctuation">)</span><span class="token operator">: </span><a href="../../org.gradle.api/-task/index.html#217904858%2FMain%2F-**********">Task</a><span class="token operator">?</span></div></div>                  </div>
                </div>
              </div>
            </div>
          </div>
<a data-name="547531923%2FFunctions%2F-**********" anchor-label="forEach" id="547531923%2FFunctions%2F-**********" data-filterable-set=":docs/kotlin_dsl"></a>
          <div class="table-row" data-filterable-current=":docs/kotlin_dsl" data-filterable-set=":docs/kotlin_dsl">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="index.html#547531923%2FFunctions%2F-**********"><span>for</span><wbr></wbr><span><span>Each</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="547531923%2FFunctions%2F-**********"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
                    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/kotlin_dsl" data-filterable-set=":docs/kotlin_dsl" data-active="" data-toggle=":docs/kotlin_dsl">DSL</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/kotlin_dsl"><div class="symbol monospace"><span class="token keyword">open </span><span class="token keyword"></span><span class="token keyword">fun </span><a href="index.html#547531923%2FFunctions%2F-**********"><span class="token function">forEach</span></a><span class="token punctuation">(</span><span class="parameters "><span class="parameter ">p0<span class="token operator">: </span><a href="https://docs.oracle.com/en/java/javase/11/docs/api/java.base/java/util/function/Consumer.html">Consumer</a><span class="token operator">&lt;</span><span class="token keyword">in </span><a href="../../org.gradle.api/-task/index.html#217904858%2FMain%2F-**********">Task</a><span class="token operator">&gt;</span></span></span><span class="token punctuation">)</span></div></div>                  </div>
                </div>
              </div>
            </div>
          </div>
<a data-name="1886686968%2FFunctions%2F-**********" anchor-label="get" id="1886686968%2FFunctions%2F-**********" data-filterable-set=":docs/kotlin_dsl"></a>
          <div class="table-row" data-togglable="EXTENSION_FUNCTION" data-filterable-current=":docs/kotlin_dsl" data-filterable-set=":docs/kotlin_dsl">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="../get.html"><span><span>get</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="1886686968%2FFunctions%2F-**********"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
                    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/kotlin_dsl" data-filterable-set=":docs/kotlin_dsl" data-active="" data-toggle=":docs/kotlin_dsl">DSL</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/kotlin_dsl"><div class="symbol monospace"><span class="token keyword">operator </span><span class="token keyword">fun </span><span class="token operator">&lt;</span><span class="token keyword"></span><a href="../get.html">T</a><span class="token operator"> : </span><a href="https://kotlinlang.org/api/latest/jvm/stdlib/kotlin/-any/index.html">Any</a><span class="token operator">&gt; </span><a href="../../org.gradle.api/-named-domain-object-collection/index.html#-87091607%2FMain%2F-**********">NamedDomainObjectCollection</a><span class="token operator">&lt;</span><span class="token keyword"></span><a href="../get.html">T</a><span class="token operator">&gt;</span><span class="token punctuation">.</span><a href="../get.html"><span class="token function">get</span></a><span class="token punctuation">(</span><span class="parameters "><span class="parameter ">name<span class="token operator">: </span><a href="https://kotlinlang.org/api/latest/jvm/stdlib/kotlin/-string/index.html">String</a></span></span><span class="token punctuation">)</span><span class="token operator">: </span><a href="../get.html">T</a></div><div class="brief "><p class="paragraph">Locates an object by name, failing if there is no such object.</p></div></div>                  </div>
                </div>
              </div>
            </div>
          </div>
<a data-name="-1236295718%2FFunctions%2F-**********" anchor-label="getAsMap" id="-1236295718%2FFunctions%2F-**********" data-filterable-set=":docs/kotlin_dsl"></a>
          <div class="table-row" data-filterable-current=":docs/kotlin_dsl" data-filterable-set=":docs/kotlin_dsl">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="index.html#-1236295718%2FFunctions%2F-**********"><span>get</span><wbr></wbr><span>As</span><wbr></wbr><span><span>Map</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="-1236295718%2FFunctions%2F-**********"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
                    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/kotlin_dsl" data-filterable-set=":docs/kotlin_dsl" data-active="" data-toggle=":docs/kotlin_dsl">DSL</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/kotlin_dsl"><div class="symbol monospace"><span class="token keyword">open </span><span class="token keyword">override </span><span class="token keyword">fun </span><a href="index.html#-1236295718%2FFunctions%2F-**********"><span class="token function">getAsMap</span></a><span class="token punctuation">(</span><span class="token punctuation">)</span><span class="token operator">: </span><a href="https://docs.oracle.com/en/java/javase/11/docs/api/java.base/java/util/SortedMap.html">SortedMap</a><span class="token operator">&lt;</span><span class="token keyword"></span><a href="https://kotlinlang.org/api/latest/jvm/stdlib/kotlin/-string/index.html">String</a><span class="token punctuation">, </span><span class="token keyword"></span><a href="../../org.gradle.api/-task/index.html#217904858%2FMain%2F-**********">Task</a><span class="token operator">&gt;</span></div></div>                  </div>
                </div>
              </div>
            </div>
          </div>
<a data-name="-1052741055%2FFunctions%2F-**********" anchor-label="getAt" id="-1052741055%2FFunctions%2F-**********" data-filterable-set=":docs/kotlin_dsl"></a>
          <div class="table-row" data-filterable-current=":docs/kotlin_dsl" data-filterable-set=":docs/kotlin_dsl">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="index.html#-1052741055%2FFunctions%2F-**********"><span>get</span><wbr></wbr><span><span>At</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="-1052741055%2FFunctions%2F-**********"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
                    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/kotlin_dsl" data-filterable-set=":docs/kotlin_dsl" data-active="" data-toggle=":docs/kotlin_dsl">DSL</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/kotlin_dsl"><div class="symbol monospace"><span class="token keyword">open </span><span class="token keyword">override </span><span class="token keyword">fun </span><a href="index.html#-1052741055%2FFunctions%2F-**********"><span class="token function">getAt</span></a><span class="token punctuation">(</span><span class="parameters "><span class="parameter ">name<span class="token operator">: </span><a href="https://kotlinlang.org/api/latest/jvm/stdlib/kotlin/-string/index.html">String</a></span></span><span class="token punctuation">)</span><span class="token operator">: </span><a href="../../org.gradle.api/-task/index.html#217904858%2FMain%2F-**********">Task</a></div></div>                  </div>
                </div>
              </div>
            </div>
          </div>
<a data-name="-271460385%2FFunctions%2F-**********" anchor-label="getByName" id="-271460385%2FFunctions%2F-**********" data-filterable-set=":docs/kotlin_dsl"></a>
          <div class="table-row" data-filterable-current=":docs/kotlin_dsl" data-filterable-set=":docs/kotlin_dsl">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="index.html#2001673936%2FFunctions%2F-**********"><span>get</span><wbr></wbr><span>By</span><wbr></wbr><span><span>Name</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="-271460385%2FFunctions%2F-**********"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
                    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/kotlin_dsl" data-filterable-set=":docs/kotlin_dsl" data-active="" data-toggle=":docs/kotlin_dsl">DSL</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/kotlin_dsl"><div class="symbol monospace"><span class="token keyword">open </span><span class="token keyword">override </span><span class="token keyword">fun </span><a href="index.html#2001673936%2FFunctions%2F-**********"><span class="token function">getByName</span></a><span class="token punctuation">(</span><span class="parameters "><span class="parameter ">name<span class="token operator">: </span><a href="https://kotlinlang.org/api/latest/jvm/stdlib/kotlin/-string/index.html">String</a></span></span><span class="token punctuation">)</span><span class="token operator">: </span><a href="../../org.gradle.api/-task/index.html#217904858%2FMain%2F-**********">Task</a></div><div class="symbol monospace"><span class="token keyword">open </span><span class="token keyword">override </span><span class="token keyword">fun </span><a href="index.html#695978965%2FFunctions%2F-**********"><span class="token function">getByName</span></a><span class="token punctuation">(</span><span class="parameters "><span class="parameter ">name<span class="token operator">: </span><a href="https://kotlinlang.org/api/latest/jvm/stdlib/kotlin/-string/index.html">String</a><span class="token punctuation">, </span></span><span class="parameter ">configureClosure<span class="token operator">: </span><span data-unresolved-link="groovy.lang/Closure///PointingToDeclaration/">Closure</span><span class="token operator">&lt;</span><span class="token keyword"></span><a href="https://kotlinlang.org/api/latest/jvm/stdlib/kotlin/-any/index.html">Any</a><span class="token operator">&gt;</span></span></span><span class="token punctuation">)</span><span class="token operator">: </span><a href="../../org.gradle.api/-task/index.html#217904858%2FMain%2F-**********">Task</a></div><div class="symbol monospace"><span class="token keyword">open </span><span class="token keyword">override </span><span class="token keyword">fun </span><a href="index.html#2072285012%2FFunctions%2F-**********"><span class="token function">getByName</span></a><span class="token punctuation">(</span><span class="parameters "><span class="parameter ">name<span class="token operator">: </span><a href="https://kotlinlang.org/api/latest/jvm/stdlib/kotlin/-string/index.html">String</a><span class="token punctuation">, </span></span><span class="parameter ">configureAction<span class="token operator">: </span><a href="../../org.gradle.api/-action/index.html#-199246903%2FMain%2F-**********">Action</a><span class="token operator">&lt;</span><span class="token keyword">in </span><a href="../../org.gradle.api/-task/index.html#217904858%2FMain%2F-**********">Task</a><span class="token operator">&gt;</span></span></span><span class="token punctuation">)</span><span class="token operator">: </span><a href="../../org.gradle.api/-task/index.html#217904858%2FMain%2F-**********">Task</a></div></div>                  </div>
                </div>
              </div>
            </div>
          </div>
<a data-name="484209280%2FFunctions%2F-**********" anchor-label="getByName" id="484209280%2FFunctions%2F-**********" data-filterable-set=":docs/kotlin_dsl"></a>
          <div class="table-row" data-togglable="EXTENSION_FUNCTION" data-filterable-current=":docs/kotlin_dsl" data-filterable-set=":docs/kotlin_dsl">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="../get-by-name.html"><span>get</span><wbr></wbr><span>By</span><wbr></wbr><span><span>Name</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="484209280%2FFunctions%2F-**********"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
                    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/kotlin_dsl" data-filterable-set=":docs/kotlin_dsl" data-active="" data-toggle=":docs/kotlin_dsl">DSL</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/kotlin_dsl"><div class="symbol monospace"><span class="token keyword">inline </span><span class="token keyword">fun </span><span class="token operator">&lt;</span><span class="token keyword"></span><a href="../get-by-name.html">T</a><span class="token operator"> : </span><a href="https://kotlinlang.org/api/latest/jvm/stdlib/kotlin/-any/index.html">Any</a><span class="token operator">&gt; </span><a href="../../org.gradle.api/-named-domain-object-collection/index.html#-87091607%2FMain%2F-**********">NamedDomainObjectCollection</a><span class="token operator">&lt;</span><span class="token keyword">out </span><a href="https://kotlinlang.org/api/latest/jvm/stdlib/kotlin/-any/index.html">Any</a><span class="token operator">&gt;</span><span class="token punctuation">.</span><a href="../get-by-name.html"><span class="token function">getByName</span></a><span class="token punctuation">(</span><span class="parameters "><span class="parameter ">name<span class="token operator">: </span><a href="https://kotlinlang.org/api/latest/jvm/stdlib/kotlin/-string/index.html">String</a></span></span><span class="token punctuation">)</span><span class="token operator">: </span><a href="../get-by-name.html">T</a></div><div class="brief "><p class="paragraph">Locates an object by name and casts it to the expected type <a href="../get-by-name.html">T</a>.</p></div><div class="symbol monospace"><span class="token keyword">inline </span><span class="token keyword">fun </span><span class="token operator">&lt;</span><span class="token keyword"></span><a href="../get-by-name.html">T</a><span class="token operator"> : </span><a href="https://kotlinlang.org/api/latest/jvm/stdlib/kotlin/-any/index.html">Any</a><span class="token operator">&gt; </span><a href="../../org.gradle.api/-named-domain-object-collection/index.html#-87091607%2FMain%2F-**********">NamedDomainObjectCollection</a><span class="token operator">&lt;</span><span class="token keyword">out </span><a href="https://kotlinlang.org/api/latest/jvm/stdlib/kotlin/-any/index.html">Any</a><span class="token operator">&gt;</span><span class="token punctuation">.</span><a href="../get-by-name.html"><span class="token function">getByName</span></a><span class="token punctuation">(</span><span class="parameters "><span class="parameter ">name<span class="token operator">: </span><a href="https://kotlinlang.org/api/latest/jvm/stdlib/kotlin/-string/index.html">String</a><span class="token punctuation">, </span></span><span class="parameter ">configure<span class="token operator">: </span><span class="token keyword"></span><a href="../get-by-name.html">T</a><span class="token punctuation">.</span><span class="token punctuation">(</span><span class="token punctuation">)</span><span class="token operator"> -&gt; </span><span class="token keyword"></span><a href="https://kotlinlang.org/api/latest/jvm/stdlib/kotlin/-unit/index.html">Unit</a></span></span><span class="token punctuation">)</span><span class="token operator">: </span><a href="../get-by-name.html">T</a></div><div class="brief "><p class="paragraph">Locates an object by name and casts it to the expected type <a href="../get-by-name.html">T</a> then configures it.</p></div><div class="symbol monospace"><span class="token keyword"></span><span class="token keyword">fun </span><span class="token operator">&lt;</span><span class="token keyword"></span><a href="../get-by-name.html">T</a><span class="token operator"> : </span><a href="https://kotlinlang.org/api/latest/jvm/stdlib/kotlin/-any/index.html">Any</a><span class="token operator">&gt; </span><a href="../../org.gradle.api/-named-domain-object-collection/index.html#-87091607%2FMain%2F-**********">NamedDomainObjectCollection</a><span class="token operator">&lt;</span><span class="token keyword">out </span><a href="https://kotlinlang.org/api/latest/jvm/stdlib/kotlin/-any/index.html">Any</a><span class="token operator">&gt;</span><span class="token punctuation">.</span><a href="../get-by-name.html"><span class="token function">getByName</span></a><span class="token punctuation">(</span><span class="parameters "><span class="parameter ">name<span class="token operator">: </span><a href="https://kotlinlang.org/api/latest/jvm/stdlib/kotlin/-string/index.html">String</a><span class="token punctuation">, </span></span><span class="parameter ">type<span class="token operator">: </span><a href="https://kotlinlang.org/api/latest/jvm/stdlib/kotlin.reflect/-k-class/index.html">KClass</a><span class="token operator">&lt;</span><span class="token keyword"></span><a href="../get-by-name.html">T</a><span class="token operator">&gt;</span></span></span><span class="token punctuation">)</span><span class="token operator">: </span><a href="../get-by-name.html">T</a></div><div class="brief "><p class="paragraph">Locates an object by name and casts it to the expected <a href="../get-by-name.html">type</a>.</p></div><div class="symbol monospace"><span class="token keyword"></span><span class="token keyword">fun </span><span class="token operator">&lt;</span><span class="token keyword"></span><a href="../get-by-name.html">T</a><span class="token operator"> : </span><a href="https://kotlinlang.org/api/latest/jvm/stdlib/kotlin/-any/index.html">Any</a><span class="token operator">&gt; </span><a href="../../org.gradle.api/-named-domain-object-collection/index.html#-87091607%2FMain%2F-**********">NamedDomainObjectCollection</a><span class="token operator">&lt;</span><span class="token keyword">out </span><a href="https://kotlinlang.org/api/latest/jvm/stdlib/kotlin/-any/index.html">Any</a><span class="token operator">&gt;</span><span class="token punctuation">.</span><a href="../get-by-name.html"><span class="token function">getByName</span></a><span class="token punctuation">(</span><span class="parameters "><span class="parameter ">name<span class="token operator">: </span><a href="https://kotlinlang.org/api/latest/jvm/stdlib/kotlin/-string/index.html">String</a><span class="token punctuation">, </span></span><span class="parameter ">type<span class="token operator">: </span><a href="https://kotlinlang.org/api/latest/jvm/stdlib/kotlin.reflect/-k-class/index.html">KClass</a><span class="token operator">&lt;</span><span class="token keyword"></span><a href="../get-by-name.html">T</a><span class="token operator">&gt;</span><span class="token punctuation">, </span></span><span class="parameter ">configure<span class="token operator">: </span><span class="token keyword"></span><a href="../get-by-name.html">T</a><span class="token punctuation">.</span><span class="token punctuation">(</span><span class="token punctuation">)</span><span class="token operator"> -&gt; </span><span class="token keyword"></span><a href="https://kotlinlang.org/api/latest/jvm/stdlib/kotlin/-unit/index.html">Unit</a></span></span><span class="token punctuation">)</span><span class="token operator">: </span><a href="../get-by-name.html">T</a></div><div class="brief "><p class="paragraph">Locates an object by name and casts it to the expected <a href="../get-by-name.html">type</a> then configures it.</p></div></div>                  </div>
                </div>
              </div>
            </div>
          </div>
<a data-name="1122895082%2FFunctions%2F-**********" anchor-label="getByPath" id="1122895082%2FFunctions%2F-**********" data-filterable-set=":docs/kotlin_dsl"></a>
          <div class="table-row" data-filterable-current=":docs/kotlin_dsl" data-filterable-set=":docs/kotlin_dsl">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="index.html#1122895082%2FFunctions%2F-**********"><span>get</span><wbr></wbr><span>By</span><wbr></wbr><span><span>Path</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="1122895082%2FFunctions%2F-**********"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
                    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/kotlin_dsl" data-filterable-set=":docs/kotlin_dsl" data-active="" data-toggle=":docs/kotlin_dsl">DSL</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/kotlin_dsl"><div class="symbol monospace"><span class="token keyword">open </span><span class="token keyword">override </span><span class="token keyword">fun </span><a href="index.html#1122895082%2FFunctions%2F-**********"><span class="token function">getByPath</span></a><span class="token punctuation">(</span><span class="parameters "><span class="parameter ">path<span class="token operator">: </span><a href="https://kotlinlang.org/api/latest/jvm/stdlib/kotlin/-string/index.html">String</a></span></span><span class="token punctuation">)</span><span class="token operator">: </span><a href="../../org.gradle.api/-task/index.html#217904858%2FMain%2F-**********">Task</a></div></div>                  </div>
                </div>
              </div>
            </div>
          </div>
<a data-name="992398263%2FFunctions%2F-**********" anchor-label="getCollectionSchema" id="992398263%2FFunctions%2F-**********" data-filterable-set=":docs/kotlin_dsl"></a>
          <div class="table-row" data-filterable-current=":docs/kotlin_dsl" data-filterable-set=":docs/kotlin_dsl">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="index.html#992398263%2FFunctions%2F-**********"><span>get</span><wbr></wbr><span>Collection</span><wbr></wbr><span><span>Schema</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="992398263%2FFunctions%2F-**********"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
                    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/kotlin_dsl" data-filterable-set=":docs/kotlin_dsl" data-active="" data-toggle=":docs/kotlin_dsl">DSL</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/kotlin_dsl"><div class="symbol monospace"><span class="token keyword">open </span><span class="token keyword">override </span><span class="token keyword">fun </span><a href="index.html#992398263%2FFunctions%2F-**********"><span class="token function">getCollectionSchema</span></a><span class="token punctuation">(</span><span class="token punctuation">)</span><span class="token operator">: </span><span data-unresolved-link="org.gradle.api/NamedDomainObjectCollectionSchema///PointingToDeclaration/">NamedDomainObjectCollectionSchema</span></div></div>                  </div>
                </div>
              </div>
            </div>
          </div>
<a data-name="-628221187%2FFunctions%2F-**********" anchor-label="getNamer" id="-628221187%2FFunctions%2F-**********" data-filterable-set=":docs/kotlin_dsl"></a>
          <div class="table-row" data-filterable-current=":docs/kotlin_dsl" data-filterable-set=":docs/kotlin_dsl">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="index.html#-628221187%2FFunctions%2F-**********"><span>get</span><wbr></wbr><span><span>Namer</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="-628221187%2FFunctions%2F-**********"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
                    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/kotlin_dsl" data-filterable-set=":docs/kotlin_dsl" data-active="" data-toggle=":docs/kotlin_dsl">DSL</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/kotlin_dsl"><div class="symbol monospace"><span class="token keyword">open </span><span class="token keyword">override </span><span class="token keyword">fun </span><a href="index.html#-628221187%2FFunctions%2F-**********"><span class="token function">getNamer</span></a><span class="token punctuation">(</span><span class="token punctuation">)</span><span class="token operator">: </span><span data-unresolved-link="org.gradle.api/Namer///PointingToDeclaration/">Namer</span><span class="token operator">&lt;</span><span class="token keyword"></span><a href="../../org.gradle.api/-task/index.html#217904858%2FMain%2F-**********">Task</a><span class="token operator">&gt;</span></div></div>                  </div>
                </div>
              </div>
            </div>
          </div>
<a data-name="-597201380%2FFunctions%2F-**********" anchor-label="getNames" id="-597201380%2FFunctions%2F-**********" data-filterable-set=":docs/kotlin_dsl"></a>
          <div class="table-row" data-filterable-current=":docs/kotlin_dsl" data-filterable-set=":docs/kotlin_dsl">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="index.html#-597201380%2FFunctions%2F-**********"><span>get</span><wbr></wbr><span><span>Names</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="-597201380%2FFunctions%2F-**********"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
                    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/kotlin_dsl" data-filterable-set=":docs/kotlin_dsl" data-active="" data-toggle=":docs/kotlin_dsl">DSL</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/kotlin_dsl"><div class="symbol monospace"><span class="token keyword">open </span><span class="token keyword">override </span><span class="token keyword">fun </span><a href="index.html#-597201380%2FFunctions%2F-**********"><span class="token function">getNames</span></a><span class="token punctuation">(</span><span class="token punctuation">)</span><span class="token operator">: </span><a href="https://docs.oracle.com/en/java/javase/11/docs/api/java.base/java/util/SortedSet.html">SortedSet</a><span class="token operator">&lt;</span><span class="token keyword"></span><a href="https://kotlinlang.org/api/latest/jvm/stdlib/kotlin/-string/index.html">String</a><span class="token operator">&gt;</span></div></div>                  </div>
                </div>
              </div>
            </div>
          </div>
<a data-name="679931725%2FFunctions%2F-**********" anchor-label="getRules" id="679931725%2FFunctions%2F-**********" data-filterable-set=":docs/kotlin_dsl"></a>
          <div class="table-row" data-filterable-current=":docs/kotlin_dsl" data-filterable-set=":docs/kotlin_dsl">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="index.html#679931725%2FFunctions%2F-**********"><span>get</span><wbr></wbr><span><span>Rules</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="679931725%2FFunctions%2F-**********"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
                    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/kotlin_dsl" data-filterable-set=":docs/kotlin_dsl" data-active="" data-toggle=":docs/kotlin_dsl">DSL</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/kotlin_dsl"><div class="symbol monospace"><span class="token keyword">open </span><span class="token keyword">override </span><span class="token keyword">fun </span><a href="index.html#679931725%2FFunctions%2F-**********"><span class="token function">getRules</span></a><span class="token punctuation">(</span><span class="token punctuation">)</span><span class="token operator">: </span><a href="https://kotlinlang.org/api/latest/jvm/stdlib/kotlin.collections/-mutable-list/index.html">MutableList</a><span class="token operator">&lt;</span><span class="token keyword"></span><span data-unresolved-link="org.gradle.api/Rule///PointingToDeclaration/">Rule</span><span class="token operator">&gt;</span></div></div>                  </div>
                </div>
              </div>
            </div>
          </div>
<a data-name="1218004881%2FFunctions%2F-**********" anchor-label="getting" id="1218004881%2FFunctions%2F-**********" data-filterable-set=":docs/kotlin_dsl"></a>
          <div class="table-row" data-togglable="EXTENSION_FUNCTION" data-filterable-current=":docs/kotlin_dsl" data-filterable-set=":docs/kotlin_dsl">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="../getting.html"><span><span>getting</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="1218004881%2FFunctions%2F-**********"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
                    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/kotlin_dsl" data-filterable-set=":docs/kotlin_dsl" data-active="" data-toggle=":docs/kotlin_dsl">DSL</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/kotlin_dsl"><div class="symbol monospace"><span class="token keyword"></span><span class="token keyword">fun </span><span class="token operator">&lt;</span><span class="token keyword"></span><a href="../getting.html">T</a><span class="token operator"> : </span><a href="https://kotlinlang.org/api/latest/jvm/stdlib/kotlin/-any/index.html">Any</a><span class="token punctuation">, </span><span class="token keyword"></span><a href="../getting.html">U</a><span class="token operator"> : </span><a href="../getting.html">T</a><span class="token operator">&gt; </span><a href="../../org.gradle.api/-named-domain-object-container/index.html#**********%2FMain%2F-**********">NamedDomainObjectContainer</a><span class="token operator">&lt;</span><span class="token keyword"></span><a href="../getting.html">T</a><span class="token operator">&gt;</span><span class="token punctuation">.</span><a href="../getting.html"><span class="token function">getting</span></a><span class="token punctuation">(</span><span class="parameters "><span class="parameter ">type<span class="token operator">: </span><a href="https://kotlinlang.org/api/latest/jvm/stdlib/kotlin.reflect/-k-class/index.html">KClass</a><span class="token operator">&lt;</span><span class="token keyword"></span><a href="../getting.html">U</a><span class="token operator">&gt;</span></span></span><span class="token punctuation">)</span><span class="token operator">: </span><a href="../-polymorphic-domain-object-container-getting-delegate-provider/index.html">PolymorphicDomainObjectContainerGettingDelegateProvider</a><span class="token operator">&lt;</span><span class="token keyword"></span><a href="../getting.html">T</a><span class="token punctuation">, </span><span class="token keyword"></span><a href="../getting.html">U</a><span class="token operator">&gt;</span></div><div class="brief "><p class="paragraph">Provides a property delegate that gets elements of the given <a href="../getting.html">type</a>.</p></div><div class="symbol monospace"><span class="token keyword"></span><span class="token keyword">fun </span><span class="token operator">&lt;</span><span class="token keyword"></span><a href="../getting.html">T</a><span class="token operator"> : </span><a href="https://kotlinlang.org/api/latest/jvm/stdlib/kotlin/-any/index.html">Any</a><span class="token punctuation">, </span><span class="token keyword"></span><a href="../getting.html">U</a><span class="token operator"> : </span><a href="../getting.html">T</a><span class="token operator">&gt; </span><a href="../../org.gradle.api/-named-domain-object-container/index.html#**********%2FMain%2F-**********">NamedDomainObjectContainer</a><span class="token operator">&lt;</span><span class="token keyword"></span><a href="../getting.html">T</a><span class="token operator">&gt;</span><span class="token punctuation">.</span><a href="../getting.html"><span class="token function">getting</span></a><span class="token punctuation">(</span><span class="parameters "><span class="parameter ">type<span class="token operator">: </span><a href="https://kotlinlang.org/api/latest/jvm/stdlib/kotlin.reflect/-k-class/index.html">KClass</a><span class="token operator">&lt;</span><span class="token keyword"></span><a href="../getting.html">U</a><span class="token operator">&gt;</span><span class="token punctuation">, </span></span><span class="parameter ">configuration<span class="token operator">: </span><span class="token keyword"></span><a href="../getting.html">U</a><span class="token punctuation">.</span><span class="token punctuation">(</span><span class="token punctuation">)</span><span class="token operator"> -&gt; </span><span class="token keyword"></span><a href="https://kotlinlang.org/api/latest/jvm/stdlib/kotlin/-unit/index.html">Unit</a></span></span><span class="token punctuation">)</span><span class="token operator">: </span><a href="../-polymorphic-domain-object-container-getting-delegate-provider/index.html">PolymorphicDomainObjectContainerGettingDelegateProvider</a><span class="token operator">&lt;</span><span class="token keyword"></span><a href="../getting.html">T</a><span class="token punctuation">, </span><span class="token keyword"></span><a href="../getting.html">U</a><span class="token operator">&gt;</span></div><div class="brief "><p class="paragraph">Provides a property delegate that gets elements of the given <a href="../getting.html">type</a> and applies the given <a href="../getting.html">configuration</a>.</p></div></div>                  </div>
                </div>
              </div>
            </div>
          </div>
<a data-name="**********%2FFunctions%2F-**********" anchor-label="invoke" id="**********%2FFunctions%2F-**********" data-filterable-set=":docs/kotlin_dsl"></a>
          <div class="table-row" data-togglable="EXTENSION_FUNCTION" data-filterable-current=":docs/kotlin_dsl" data-filterable-set=":docs/kotlin_dsl">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="../invoke.html"><span><span>invoke</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="**********%2FFunctions%2F-**********"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
                    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/kotlin_dsl" data-filterable-set=":docs/kotlin_dsl" data-active="" data-toggle=":docs/kotlin_dsl">DSL</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/kotlin_dsl"><div class="symbol monospace"><span class="token keyword">inline operator </span><span class="token keyword">fun </span><a href="../../org.gradle.api.tasks/-task-container/index.html#-**********%2FMain%2F-**********">TaskContainer</a><span class="token punctuation">.</span><a href="../invoke.html"><span class="token function">invoke</span></a><span class="token punctuation">(</span><span class="parameters "><span class="parameter ">configuration<span class="token operator">: </span><span class="token keyword"></span><a href="index.html">TaskContainerScope</a><span class="token punctuation">.</span><span class="token punctuation">(</span><span class="token punctuation">)</span><span class="token operator"> -&gt; </span><span class="token keyword"></span><a href="https://kotlinlang.org/api/latest/jvm/stdlib/kotlin/-unit/index.html">Unit</a></span></span><span class="token punctuation">)</span><span class="token operator">: </span><a href="../../org.gradle.api.tasks/-task-container/index.html#-**********%2FMain%2F-**********">TaskContainer</a></div><div class="brief "><p class="paragraph">Allows a <a href="../../org.gradle.api.tasks/-task-container/index.html#-**********%2FMain%2F-**********">TaskContainer</a> to be configured via an augmented DSL that includes a shorthand string notation for configuring existing tasks.</p></div><div class="symbol monospace"><span class="token keyword">operator </span><span class="token keyword">fun </span><a href="https://kotlinlang.org/api/latest/jvm/stdlib/kotlin/-string/index.html">String</a><span class="token punctuation">.</span><a href="invoke.html"><span class="token function">invoke</span></a><span class="token punctuation">(</span><span class="token punctuation">)</span><span class="token operator">: </span><a href="../../org.gradle.api.tasks/-task-provider/index.html#-740321463%2FMain%2F-**********">TaskProvider</a><span class="token operator">&lt;</span><span class="token keyword"></span><a href="../../org.gradle.api/-task/index.html#217904858%2FMain%2F-**********">Task</a><span class="token operator">&gt;</span></div><div class="brief "><p class="paragraph">Locates a task by name, without triggering its creation or configuration, failing if there is no such task.</p></div><div class="symbol monospace"><span class="token keyword">operator </span><span class="token keyword">fun </span><a href="https://kotlinlang.org/api/latest/jvm/stdlib/kotlin/-string/index.html">String</a><span class="token punctuation">.</span><a href="invoke.html"><span class="token function">invoke</span></a><span class="token punctuation">(</span><span class="parameters "><span class="parameter ">configuration<span class="token operator">: </span><span class="token keyword"></span><a href="../../org.gradle.api/-task/index.html#217904858%2FMain%2F-**********">Task</a><span class="token punctuation">.</span><span class="token punctuation">(</span><span class="token punctuation">)</span><span class="token operator"> -&gt; </span><span class="token keyword"></span><a href="https://kotlinlang.org/api/latest/jvm/stdlib/kotlin/-unit/index.html">Unit</a></span></span><span class="token punctuation">)</span><span class="token operator">: </span><a href="../../org.gradle.api.tasks/-task-provider/index.html#-740321463%2FMain%2F-**********">TaskProvider</a><span class="token operator">&lt;</span><span class="token keyword"></span><a href="../../org.gradle.api/-task/index.html#217904858%2FMain%2F-**********">Task</a><span class="token operator">&gt;</span></div><div class="symbol monospace"><span class="token keyword">operator </span><span class="token keyword">fun </span><span class="token operator">&lt;</span><span class="token keyword"></span><a href="invoke.html">U</a><span class="token operator"> : </span><a href="../../org.gradle.api/-task/index.html#217904858%2FMain%2F-**********">Task</a><span class="token operator">&gt; </span><a href="https://kotlinlang.org/api/latest/jvm/stdlib/kotlin/-string/index.html">String</a><span class="token punctuation">.</span><a href="invoke.html"><span class="token function">invoke</span></a><span class="token punctuation">(</span><span class="parameters "><span class="parameter ">type<span class="token operator">: </span><a href="https://kotlinlang.org/api/latest/jvm/stdlib/kotlin.reflect/-k-class/index.html">KClass</a><span class="token operator">&lt;</span><span class="token keyword"></span><a href="invoke.html">U</a><span class="token operator">&gt;</span><span class="token punctuation">, </span></span><span class="parameter ">configuration<span class="token operator">: </span><span class="token keyword"></span><a href="invoke.html">U</a><span class="token punctuation">.</span><span class="token punctuation">(</span><span class="token punctuation">)</span><span class="token operator"> -&gt; </span><span class="token keyword"></span><a href="https://kotlinlang.org/api/latest/jvm/stdlib/kotlin/-unit/index.html">Unit</a></span></span><span class="token punctuation">)</span><span class="token operator">: </span><a href="../../org.gradle.api.tasks/-task-provider/index.html#-740321463%2FMain%2F-**********">TaskProvider</a><span class="token operator">&lt;</span><span class="token keyword"></span><a href="invoke.html">U</a><span class="token operator">&gt;</span></div><div class="brief "><p class="paragraph">Configures a task by name, without triggering its creation or configuration, failing if there is no such task.</p></div><div class="symbol monospace"><span class="token keyword">operator </span><span class="token keyword">fun </span><span class="token operator">&lt;</span><span class="token keyword"></span><a href="invoke.html">U</a><span class="token operator"> : </span><a href="../../org.gradle.api/-task/index.html#217904858%2FMain%2F-**********">Task</a><span class="token operator">&gt; </span><a href="https://kotlinlang.org/api/latest/jvm/stdlib/kotlin/-string/index.html">String</a><span class="token punctuation">.</span><a href="invoke.html"><span class="token function">invoke</span></a><span class="token punctuation">(</span><span class="parameters "><span class="parameter ">type<span class="token operator">: </span><a href="https://kotlinlang.org/api/latest/jvm/stdlib/kotlin.reflect/-k-class/index.html">KClass</a><span class="token operator">&lt;</span><span class="token keyword"></span><a href="invoke.html">U</a><span class="token operator">&gt;</span></span></span><span class="token punctuation">)</span><span class="token operator">: </span><a href="../../org.gradle.api.tasks/-task-provider/index.html#-740321463%2FMain%2F-**********">TaskProvider</a><span class="token operator">&lt;</span><span class="token keyword"></span><a href="invoke.html">U</a><span class="token operator">&gt;</span></div><div class="brief "><p class="paragraph">Locates a task by name and type, without triggering its creation or configuration, failing if there is no such task.</p></div></div>                  </div>
                </div>
              </div>
            </div>
          </div>
<a data-name="-740755191%2FFunctions%2F-**********" anchor-label="isEmpty" id="-740755191%2FFunctions%2F-**********" data-filterable-set=":docs/kotlin_dsl"></a>
          <div class="table-row" data-filterable-current=":docs/kotlin_dsl" data-filterable-set=":docs/kotlin_dsl">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="index.html#-740755191%2FFunctions%2F-**********"><span>is</span><wbr></wbr><span><span>Empty</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="-740755191%2FFunctions%2F-**********"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
                    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/kotlin_dsl" data-filterable-set=":docs/kotlin_dsl" data-active="" data-toggle=":docs/kotlin_dsl">DSL</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/kotlin_dsl"><div class="symbol monospace"><span class="token keyword">open </span><span class="token keyword">override </span><span class="token keyword">fun </span><a href="index.html#-740755191%2FFunctions%2F-**********"><span class="token function">isEmpty</span></a><span class="token punctuation">(</span><span class="token punctuation">)</span><span class="token operator">: </span><a href="https://kotlinlang.org/api/latest/jvm/stdlib/kotlin/-boolean/index.html">Boolean</a></div></div>                  </div>
                </div>
              </div>
            </div>
          </div>
<a data-name="-2103995712%2FFunctions%2F-**********" anchor-label="iterator" id="-2103995712%2FFunctions%2F-**********" data-filterable-set=":docs/kotlin_dsl"></a>
          <div class="table-row" data-filterable-current=":docs/kotlin_dsl" data-filterable-set=":docs/kotlin_dsl">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="index.html#-2103995712%2FFunctions%2F-**********"><span><span>iterator</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="-2103995712%2FFunctions%2F-**********"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
                    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/kotlin_dsl" data-filterable-set=":docs/kotlin_dsl" data-active="" data-toggle=":docs/kotlin_dsl">DSL</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/kotlin_dsl"><div class="symbol monospace"><span class="token keyword">open </span><span class="token keyword">operator override </span><span class="token keyword">fun </span><a href="index.html#-2103995712%2FFunctions%2F-**********"><span class="token function">iterator</span></a><span class="token punctuation">(</span><span class="token punctuation">)</span><span class="token operator">: </span><a href="https://kotlinlang.org/api/latest/jvm/stdlib/kotlin.collections/-mutable-iterator/index.html">MutableIterator</a><span class="token operator">&lt;</span><span class="token keyword"></span><a href="../../org.gradle.api/-task/index.html#217904858%2FMain%2F-**********">Task</a><span class="token operator">&gt;</span></div></div>                  </div>
                </div>
              </div>
            </div>
          </div>
<a data-name="1978090826%2FFunctions%2F-**********" anchor-label="matching" id="1978090826%2FFunctions%2F-**********" data-filterable-set=":docs/kotlin_dsl"></a>
          <div class="table-row" data-filterable-current=":docs/kotlin_dsl" data-filterable-set=":docs/kotlin_dsl">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="index.html#-163499213%2FFunctions%2F-**********"><span><span>matching</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="1978090826%2FFunctions%2F-**********"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
                    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/kotlin_dsl" data-filterable-set=":docs/kotlin_dsl" data-active="" data-toggle=":docs/kotlin_dsl">DSL</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/kotlin_dsl"><div class="symbol monospace"><span class="token keyword">open </span><span class="token keyword">override </span><span class="token keyword">fun </span><a href="index.html#-163499213%2FFunctions%2F-**********"><span class="token function">matching</span></a><span class="token punctuation">(</span><span class="parameters "><span class="parameter ">closure<span class="token operator">: </span><span data-unresolved-link="groovy.lang/Closure///PointingToDeclaration/">Closure</span><span class="token operator">&lt;</span><span class="token keyword"></span><a href="https://kotlinlang.org/api/latest/jvm/stdlib/kotlin/-any/index.html">Any</a><span class="token operator">&gt;</span></span></span><span class="token punctuation">)</span><span class="token operator">: </span><a href="../../org.gradle.api.tasks/-task-collection/index.html#-**********%2FMain%2F-**********">TaskCollection</a><span class="token operator">&lt;</span><span class="token keyword"></span><a href="../../org.gradle.api/-task/index.html#217904858%2FMain%2F-**********">Task</a><span class="token operator">&gt;</span></div><div class="symbol monospace"><span class="token keyword">open </span><span class="token keyword">override </span><span class="token keyword">fun </span><a href="index.html#1824964049%2FFunctions%2F-**********"><span class="token function">matching</span></a><span class="token punctuation">(</span><span class="parameters "><span class="parameter ">spec<span class="token operator">: </span><a href="../../org.gradle.api.specs/-spec/index.html#-759770418%2FMain%2F-**********">Spec</a><span class="token operator">&lt;</span><span class="token keyword">in </span><a href="../../org.gradle.api/-task/index.html#217904858%2FMain%2F-**********">Task</a><span class="token operator">&gt;</span></span></span><span class="token punctuation">)</span><span class="token operator">: </span><a href="../../org.gradle.api.tasks/-task-collection/index.html#-**********%2FMain%2F-**********">TaskCollection</a><span class="token operator">&lt;</span><span class="token keyword"></span><a href="../../org.gradle.api/-task/index.html#217904858%2FMain%2F-**********">Task</a><span class="token operator">&gt;</span></div></div>                  </div>
                </div>
              </div>
            </div>
          </div>
<a data-name="1355388223%2FFunctions%2F-**********" anchor-label="maybeCreate" id="1355388223%2FFunctions%2F-**********" data-filterable-set=":docs/kotlin_dsl"></a>
          <div class="table-row" data-filterable-current=":docs/kotlin_dsl" data-filterable-set=":docs/kotlin_dsl">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="index.html#846704284%2FFunctions%2F-**********"><span>maybe</span><wbr></wbr><span><span>Create</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="1355388223%2FFunctions%2F-**********"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
                    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/kotlin_dsl" data-filterable-set=":docs/kotlin_dsl" data-active="" data-toggle=":docs/kotlin_dsl">DSL</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/kotlin_dsl"><div class="symbol monospace"><span class="token keyword">open </span><span class="token keyword">override </span><span class="token keyword">fun </span><a href="index.html#846704284%2FFunctions%2F-**********"><span class="token function">maybeCreate</span></a><span class="token punctuation">(</span><span class="parameters "><span class="parameter ">name<span class="token operator">: </span><a href="https://kotlinlang.org/api/latest/jvm/stdlib/kotlin/-string/index.html">String</a></span></span><span class="token punctuation">)</span><span class="token operator">: </span><a href="../../org.gradle.api/-task/index.html#217904858%2FMain%2F-**********">Task</a></div><div class="symbol monospace"><span class="token keyword">open </span><span class="token keyword">override </span><span class="token keyword">fun </span><span class="token operator">&lt;</span><span class="token keyword"></span><a href="index.html#-729310071%2FFunctions%2F-**********">U</a><span class="token operator"> : </span><a href="../../org.gradle.api/-task/index.html#217904858%2FMain%2F-**********">Task</a><span class="token operator">&gt; </span><a href="index.html#-729310071%2FFunctions%2F-**********"><span class="token function">maybeCreate</span></a><span class="token punctuation">(</span><span class="parameters "><span class="parameter ">name<span class="token operator">: </span><a href="https://kotlinlang.org/api/latest/jvm/stdlib/kotlin/-string/index.html">String</a><span class="token punctuation">, </span></span><span class="parameter ">type<span class="token operator">: </span><a href="https://docs.oracle.com/en/java/javase/11/docs/api/java.base/java/lang/Class.html">Class</a><span class="token operator">&lt;</span><span class="token keyword"></span><a href="index.html#-729310071%2FFunctions%2F-**********">U</a><span class="token operator">&gt;</span></span></span><span class="token punctuation">)</span><span class="token operator">: </span><a href="index.html#-729310071%2FFunctions%2F-**********">U</a></div></div>                  </div>
                </div>
              </div>
            </div>
          </div>
<a data-name="1357933583%2FFunctions%2F-**********" anchor-label="maybeCreate" id="1357933583%2FFunctions%2F-**********" data-filterable-set=":docs/kotlin_dsl"></a>
          <div class="table-row" data-togglable="EXTENSION_FUNCTION" data-filterable-current=":docs/kotlin_dsl" data-filterable-set=":docs/kotlin_dsl">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="../maybe-create.html"><span>maybe</span><wbr></wbr><span><span>Create</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="1357933583%2FFunctions%2F-**********"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
                    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/kotlin_dsl" data-filterable-set=":docs/kotlin_dsl" data-active="" data-toggle=":docs/kotlin_dsl">DSL</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/kotlin_dsl"><div class="symbol monospace"><span class="token keyword">inline </span><span class="token keyword">fun </span><span class="token operator">&lt;</span><span class="token keyword"></span><a href="../maybe-create.html">U</a><span class="token operator"> : </span><a href="https://kotlinlang.org/api/latest/jvm/stdlib/kotlin/-any/index.html">Any</a><span class="token operator">&gt; </span><a href="../../org.gradle.api/-polymorphic-domain-object-container/index.html#427856727%2FMain%2F-**********">PolymorphicDomainObjectContainer</a><span class="token operator">&lt;</span><span class="token keyword">in </span><a href="../maybe-create.html">U</a><span class="token operator">&gt;</span><span class="token punctuation">.</span><a href="../maybe-create.html"><span class="token function">maybeCreate</span></a><span class="token punctuation">(</span><span class="parameters "><span class="parameter ">name<span class="token operator">: </span><a href="https://kotlinlang.org/api/latest/jvm/stdlib/kotlin/-string/index.html">String</a></span></span><span class="token punctuation">)</span><span class="token operator">: </span><a href="../maybe-create.html">U</a></div><div class="brief "><p class="paragraph">Creates a domain object with the specified name and type if it does not exists, and adds it to the container.</p></div><div class="symbol monospace"><span class="token keyword">inline </span><span class="token keyword">fun </span><span class="token operator">&lt;</span><span class="token keyword"></span><a href="../maybe-create.html">U</a><span class="token operator"> : </span><a href="../maybe-create.html">T</a><span class="token punctuation">, </span><span class="token keyword"></span><a href="../maybe-create.html">T</a><span class="token operator"> : </span><a href="https://kotlinlang.org/api/latest/jvm/stdlib/kotlin/-any/index.html">Any</a><span class="token operator">&gt; </span><a href="../../org.gradle.api/-polymorphic-domain-object-container/index.html#427856727%2FMain%2F-**********">PolymorphicDomainObjectContainer</a><span class="token operator">&lt;</span><span class="token keyword"></span><a href="../maybe-create.html">T</a><span class="token operator">&gt;</span><span class="token punctuation">.</span><a href="../maybe-create.html"><span class="token function">maybeCreate</span></a><span class="token punctuation">(</span><span class="parameters "><span class="parameter ">name<span class="token operator">: </span><a href="https://kotlinlang.org/api/latest/jvm/stdlib/kotlin/-string/index.html">String</a><span class="token punctuation">, </span></span><span class="parameter ">type<span class="token operator">: </span><a href="https://kotlinlang.org/api/latest/jvm/stdlib/kotlin.reflect/-k-class/index.html">KClass</a><span class="token operator">&lt;</span><span class="token keyword"></span><a href="../maybe-create.html">U</a><span class="token operator">&gt;</span></span></span><span class="token punctuation">)</span><span class="token operator">: </span><a href="../maybe-create.html">U</a></div><div class="brief "><p class="paragraph">Kotlin extension function taking <a href="https://kotlinlang.org/api/latest/jvm/stdlib/kotlin.reflect/-k-class/index.html">kotlin.reflect.KClass</a> for <span data-unresolved-link="org.gradle.api/PolymorphicDomainObjectContainer/maybeCreate/#kotlin.String#java.lang.Class[TypeParam(bounds=[TypeParam(bounds=[kotlin.Any])])]/PointingToDeclaration/">org.gradle.api.PolymorphicDomainObjectContainer.maybeCreate</span>.</p></div></div>                  </div>
                </div>
              </div>
            </div>
          </div>
<a data-name="1414550842%2FFunctions%2F-**********" anchor-label="named" id="1414550842%2FFunctions%2F-**********" data-filterable-set=":docs/kotlin_dsl"></a>
          <div class="table-row" data-filterable-current=":docs/kotlin_dsl" data-filterable-set=":docs/kotlin_dsl">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="index.html#-**********%2FFunctions%2F-**********"><span><span>named</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="1414550842%2FFunctions%2F-**********"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
                    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/kotlin_dsl" data-filterable-set=":docs/kotlin_dsl" data-active="" data-toggle=":docs/kotlin_dsl">DSL</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/kotlin_dsl"><div class="symbol monospace"><span class="token keyword">open </span><span class="token keyword">override </span><span class="token keyword">fun </span><a href="index.html#-**********%2FFunctions%2F-**********"><span class="token function">named</span></a><span class="token punctuation">(</span><span class="parameters "><span class="parameter ">name<span class="token operator">: </span><a href="https://kotlinlang.org/api/latest/jvm/stdlib/kotlin/-string/index.html">String</a></span></span><span class="token punctuation">)</span><span class="token operator">: </span><a href="../../org.gradle.api.tasks/-task-provider/index.html#-740321463%2FMain%2F-**********">TaskProvider</a><span class="token operator">&lt;</span><span class="token keyword"></span><a href="../../org.gradle.api/-task/index.html#217904858%2FMain%2F-**********">Task</a><span class="token operator">&gt;</span></div><div class="symbol monospace"><span class="token keyword">open </span><span class="token keyword">override </span><span class="token keyword">fun </span><span class="token operator">&lt;</span><span class="token keyword"></span><a href="index.html#**********%2FFunctions%2F-**********">S</a><span class="token operator"> : </span><a href="../../org.gradle.api/-task/index.html#217904858%2FMain%2F-**********">Task</a><span class="token operator">&gt; </span><a href="index.html#**********%2FFunctions%2F-**********"><span class="token function">named</span></a><span class="token punctuation">(</span><span class="parameters "><span class="parameter ">name<span class="token operator">: </span><a href="https://kotlinlang.org/api/latest/jvm/stdlib/kotlin/-string/index.html">String</a><span class="token punctuation">, </span></span><span class="parameter ">type<span class="token operator">: </span><a href="https://docs.oracle.com/en/java/javase/11/docs/api/java.base/java/lang/Class.html">Class</a><span class="token operator">&lt;</span><span class="token keyword"></span><a href="index.html#**********%2FFunctions%2F-**********">S</a><span class="token operator">&gt;</span></span></span><span class="token punctuation">)</span><span class="token operator">: </span><a href="../../org.gradle.api.tasks/-task-provider/index.html#-740321463%2FMain%2F-**********">TaskProvider</a><span class="token operator">&lt;</span><span class="token keyword"></span><a href="index.html#**********%2FFunctions%2F-**********">S</a><span class="token operator">&gt;</span></div><div class="symbol monospace"><span class="token keyword">open </span><span class="token keyword">override </span><span class="token keyword">fun </span><a href="index.html#663184435%2FFunctions%2F-**********"><span class="token function">named</span></a><span class="token punctuation">(</span><span class="parameters "><span class="parameter ">name<span class="token operator">: </span><a href="https://kotlinlang.org/api/latest/jvm/stdlib/kotlin/-string/index.html">String</a><span class="token punctuation">, </span></span><span class="parameter ">configurationAction<span class="token operator">: </span><a href="../../org.gradle.api/-action/index.html#-199246903%2FMain%2F-**********">Action</a><span class="token operator">&lt;</span><span class="token keyword">in </span><a href="../../org.gradle.api/-task/index.html#217904858%2FMain%2F-**********">Task</a><span class="token operator">&gt;</span></span></span><span class="token punctuation">)</span><span class="token operator">: </span><a href="../../org.gradle.api.tasks/-task-provider/index.html#-740321463%2FMain%2F-**********">TaskProvider</a><span class="token operator">&lt;</span><span class="token keyword"></span><a href="../../org.gradle.api/-task/index.html#217904858%2FMain%2F-**********">Task</a><span class="token operator">&gt;</span></div><div class="symbol monospace"><span class="token keyword">open </span><span class="token keyword">override </span><span class="token keyword">fun </span><span class="token operator">&lt;</span><span class="token keyword"></span><a href="index.html#-98173772%2FFunctions%2F-**********">S</a><span class="token operator"> : </span><a href="../../org.gradle.api/-task/index.html#217904858%2FMain%2F-**********">Task</a><span class="token operator">&gt; </span><a href="index.html#-98173772%2FFunctions%2F-**********"><span class="token function">named</span></a><span class="token punctuation">(</span><span class="parameters "><span class="parameter ">name<span class="token operator">: </span><a href="https://kotlinlang.org/api/latest/jvm/stdlib/kotlin/-string/index.html">String</a><span class="token punctuation">, </span></span><span class="parameter ">type<span class="token operator">: </span><a href="https://docs.oracle.com/en/java/javase/11/docs/api/java.base/java/lang/Class.html">Class</a><span class="token operator">&lt;</span><span class="token keyword"></span><a href="index.html#-98173772%2FFunctions%2F-**********">S</a><span class="token operator">&gt;</span><span class="token punctuation">, </span></span><span class="parameter ">configurationAction<span class="token operator">: </span><a href="../../org.gradle.api/-action/index.html#-199246903%2FMain%2F-**********">Action</a><span class="token operator">&lt;</span><span class="token keyword">in </span><a href="index.html#-98173772%2FFunctions%2F-**********">S</a><span class="token operator">&gt;</span></span></span><span class="token punctuation">)</span><span class="token operator">: </span><a href="../../org.gradle.api.tasks/-task-provider/index.html#-740321463%2FMain%2F-**********">TaskProvider</a><span class="token operator">&lt;</span><span class="token keyword"></span><a href="index.html#-98173772%2FFunctions%2F-**********">S</a><span class="token operator">&gt;</span></div></div>                  </div>
                </div>
              </div>
            </div>
          </div>
<a data-name="-**********%2FFunctions%2F-**********" anchor-label="named" id="-**********%2FFunctions%2F-**********" data-filterable-set=":docs/kotlin_dsl"></a>
          <div class="table-row" data-togglable="EXTENSION_FUNCTION" data-filterable-current=":docs/kotlin_dsl" data-filterable-set=":docs/kotlin_dsl">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="../named.html"><span><span>named</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="-**********%2FFunctions%2F-**********"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
                    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/kotlin_dsl" data-filterable-set=":docs/kotlin_dsl" data-active="" data-toggle=":docs/kotlin_dsl">DSL</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/kotlin_dsl"><div class="symbol monospace"><span class="token keyword">inline </span><span class="token keyword">fun </span><span class="token operator">&lt;</span><span class="token keyword"></span><a href="../named.html">T</a><span class="token operator"> : </span><a href="https://kotlinlang.org/api/latest/jvm/stdlib/kotlin/-any/index.html">Any</a><span class="token operator">&gt; </span><a href="../../org.gradle.api/-named-domain-object-collection/index.html#-87091607%2FMain%2F-**********">NamedDomainObjectCollection</a><span class="token operator">&lt;</span><span class="token keyword">out </span><a href="https://kotlinlang.org/api/latest/jvm/stdlib/kotlin/-any/index.html">Any</a><span class="token operator">&gt;</span><span class="token punctuation">.</span><a href="../named.html"><span class="token function">named</span></a><span class="token punctuation">(</span><span class="parameters "><span class="parameter ">name<span class="token operator">: </span><a href="https://kotlinlang.org/api/latest/jvm/stdlib/kotlin/-string/index.html">String</a></span></span><span class="token punctuation">)</span><span class="token operator">: </span><a href="../../org.gradle.api/-named-domain-object-provider/index.html#306700278%2FMain%2F-**********">NamedDomainObjectProvider</a><span class="token operator">&lt;</span><span class="token keyword"></span><a href="../named.html">T</a><span class="token operator">&gt;</span></div><div class="symbol monospace"><span class="token keyword"></span><span class="token keyword">fun </span><span class="token operator">&lt;</span><span class="token keyword"></span><a href="../named.html">T</a><span class="token operator"> : </span><a href="https://kotlinlang.org/api/latest/jvm/stdlib/kotlin/-any/index.html">Any</a><span class="token operator">&gt; </span><a href="../../org.gradle.api/-named-domain-object-collection/index.html#-87091607%2FMain%2F-**********">NamedDomainObjectCollection</a><span class="token operator">&lt;</span><span class="token keyword">out </span><a href="https://kotlinlang.org/api/latest/jvm/stdlib/kotlin/-any/index.html">Any</a><span class="token operator">&gt;</span><span class="token punctuation">.</span><a href="../named.html"><span class="token function">named</span></a><span class="token punctuation">(</span><span class="parameters "><span class="parameter ">name<span class="token operator">: </span><a href="https://kotlinlang.org/api/latest/jvm/stdlib/kotlin/-string/index.html">String</a><span class="token punctuation">, </span></span><span class="parameter ">type<span class="token operator">: </span><a href="https://kotlinlang.org/api/latest/jvm/stdlib/kotlin.reflect/-k-class/index.html">KClass</a><span class="token operator">&lt;</span><span class="token keyword"></span><a href="../named.html">T</a><span class="token operator">&gt;</span></span></span><span class="token punctuation">)</span><span class="token operator">: </span><a href="../../org.gradle.api/-named-domain-object-provider/index.html#306700278%2FMain%2F-**********">NamedDomainObjectProvider</a><span class="token operator">&lt;</span><span class="token keyword"></span><a href="../named.html">T</a><span class="token operator">&gt;</span></div><div class="brief "><p class="paragraph">Locates an object by name and type, without triggering its creation or configuration, failing if there is no such object.</p></div><div class="symbol monospace"><span class="token keyword">inline </span><span class="token keyword">fun </span><span class="token operator">&lt;</span><span class="token keyword"></span><a href="../named.html">T</a><span class="token operator"> : </span><a href="../../org.gradle.api/-task/index.html#217904858%2FMain%2F-**********">Task</a><span class="token operator">&gt; </span><a href="../../org.gradle.api.tasks/-task-collection/index.html#-**********%2FMain%2F-**********">TaskCollection</a><span class="token operator">&lt;</span><span class="token keyword">out </span><a href="../../org.gradle.api/-task/index.html#217904858%2FMain%2F-**********">Task</a><span class="token operator">&gt;</span><span class="token punctuation">.</span><a href="../named.html"><span class="token function">named</span></a><span class="token punctuation">(</span><span class="parameters "><span class="parameter ">name<span class="token operator">: </span><a href="https://kotlinlang.org/api/latest/jvm/stdlib/kotlin/-string/index.html">String</a></span></span><span class="token punctuation">)</span><span class="token operator">: </span><a href="../../org.gradle.api.tasks/-task-provider/index.html#-740321463%2FMain%2F-**********">TaskProvider</a><span class="token operator">&lt;</span><span class="token keyword"></span><a href="../named.html">T</a><span class="token operator">&gt;</span></div><div class="symbol monospace"><span class="token keyword"></span><span class="token keyword">fun </span><span class="token operator">&lt;</span><span class="token keyword"></span><a href="../named.html">T</a><span class="token operator"> : </span><a href="../../org.gradle.api/-task/index.html#217904858%2FMain%2F-**********">Task</a><span class="token operator">&gt; </span><a href="../../org.gradle.api.tasks/-task-collection/index.html#-**********%2FMain%2F-**********">TaskCollection</a><span class="token operator">&lt;</span><span class="token keyword">out </span><a href="../../org.gradle.api/-task/index.html#217904858%2FMain%2F-**********">Task</a><span class="token operator">&gt;</span><span class="token punctuation">.</span><a href="../named.html"><span class="token function">named</span></a><span class="token punctuation">(</span><span class="parameters "><span class="parameter ">name<span class="token operator">: </span><a href="https://kotlinlang.org/api/latest/jvm/stdlib/kotlin/-string/index.html">String</a><span class="token punctuation">, </span></span><span class="parameter ">type<span class="token operator">: </span><a href="https://kotlinlang.org/api/latest/jvm/stdlib/kotlin.reflect/-k-class/index.html">KClass</a><span class="token operator">&lt;</span><span class="token keyword"></span><a href="../named.html">T</a><span class="token operator">&gt;</span></span></span><span class="token punctuation">)</span><span class="token operator">: </span><a href="../../org.gradle.api.tasks/-task-provider/index.html#-740321463%2FMain%2F-**********">TaskProvider</a><span class="token operator">&lt;</span><span class="token keyword"></span><a href="../named.html">T</a><span class="token operator">&gt;</span></div><div class="brief "><p class="paragraph">Locates a task by name and type, without triggering its creation or configuration, failing if there is no such task.</p></div><div class="symbol monospace"><span class="token keyword">inline </span><span class="token keyword">fun </span><span class="token operator">&lt;</span><span class="token keyword"></span><a href="../named.html">S</a><span class="token operator"> : </span><a href="../named.html">T</a><span class="token punctuation">, </span><span class="token keyword"></span><a href="../named.html">T</a><span class="token operator"> : </span><a href="https://kotlinlang.org/api/latest/jvm/stdlib/kotlin/-any/index.html">Any</a><span class="token operator">&gt; </span><a href="../../org.gradle.api/-named-domain-object-collection/index.html#-87091607%2FMain%2F-**********">NamedDomainObjectCollection</a><span class="token operator">&lt;</span><span class="token keyword"></span><a href="../named.html">T</a><span class="token operator">&gt;</span><span class="token punctuation">.</span><a href="../named.html"><span class="token function">named</span></a><span class="token punctuation">(</span><span class="parameters "><span class="parameter ">name<span class="token operator">: </span><a href="https://kotlinlang.org/api/latest/jvm/stdlib/kotlin/-string/index.html">String</a><span class="token punctuation">, </span></span><span class="parameter ">type<span class="token operator">: </span><a href="https://kotlinlang.org/api/latest/jvm/stdlib/kotlin.reflect/-k-class/index.html">KClass</a><span class="token operator">&lt;</span><span class="token keyword"></span><a href="../named.html">S</a><span class="token operator">&gt;</span></span></span><span class="token punctuation">)</span><span class="token operator">: </span><a href="../../org.gradle.api/-named-domain-object-provider/index.html#306700278%2FMain%2F-**********">NamedDomainObjectProvider</a><span class="token operator">&lt;</span><span class="token keyword"></span><a href="../named.html">S</a><span class="token operator">&gt;</span></div><div class="symbol monospace"><span class="token keyword">inline </span><span class="token keyword">fun </span><span class="token operator">&lt;</span><span class="token keyword"></span><a href="../named.html">S</a><span class="token operator"> : </span><a href="../named.html">T</a><span class="token punctuation">, </span><span class="token keyword"></span><a href="../named.html">T</a><span class="token operator"> : </span><a href="https://kotlinlang.org/api/latest/jvm/stdlib/kotlin/-any/index.html">Any</a><span class="token operator">&gt; </span><a href="../../org.gradle.api/-named-domain-object-collection/index.html#-87091607%2FMain%2F-**********">NamedDomainObjectCollection</a><span class="token operator">&lt;</span><span class="token keyword"></span><a href="../named.html">T</a><span class="token operator">&gt;</span><span class="token punctuation">.</span><a href="../named.html"><span class="token function">named</span></a><span class="token punctuation">(</span><span class="parameters "><span class="parameter ">name<span class="token operator">: </span><a href="https://kotlinlang.org/api/latest/jvm/stdlib/kotlin/-string/index.html">String</a><span class="token punctuation">, </span></span><span class="parameter ">type<span class="token operator">: </span><a href="https://kotlinlang.org/api/latest/jvm/stdlib/kotlin.reflect/-k-class/index.html">KClass</a><span class="token operator">&lt;</span><span class="token keyword"></span><a href="../named.html">S</a><span class="token operator">&gt;</span><span class="token punctuation">, </span></span><span class="parameter ">configurationAction<span class="token operator">: </span><a href="../../org.gradle.api/-action/index.html#-199246903%2FMain%2F-**********">Action</a><span class="token operator">&lt;</span><span class="token keyword">in </span><a href="../named.html">S</a><span class="token operator">&gt;</span></span></span><span class="token punctuation">)</span><span class="token operator">: </span><a href="../../org.gradle.api/-named-domain-object-provider/index.html#306700278%2FMain%2F-**********">NamedDomainObjectProvider</a><span class="token operator">&lt;</span><span class="token keyword"></span><a href="../named.html">S</a><span class="token operator">&gt;</span></div><div class="brief "><p class="paragraph">Kotlin extension function taking <a href="https://kotlinlang.org/api/latest/jvm/stdlib/kotlin.reflect/-k-class/index.html">kotlin.reflect.KClass</a> for <span data-unresolved-link="org.gradle.api/NamedDomainObjectCollection/named/#kotlin.String/PointingToDeclaration/">org.gradle.api.NamedDomainObjectCollection.named</span>.</p></div><div class="symbol monospace"><span class="token keyword">inline </span><span class="token keyword">fun </span><span class="token operator">&lt;</span><span class="token keyword"></span><a href="../named.html">T</a><span class="token operator"> : </span><a href="https://kotlinlang.org/api/latest/jvm/stdlib/kotlin/-any/index.html">Any</a><span class="token operator">&gt; </span><a href="../../org.gradle.api/-named-domain-object-collection/index.html#-87091607%2FMain%2F-**********">NamedDomainObjectCollection</a><span class="token operator">&lt;</span><span class="token keyword">out </span><a href="https://kotlinlang.org/api/latest/jvm/stdlib/kotlin/-any/index.html">Any</a><span class="token operator">&gt;</span><span class="token punctuation">.</span><a href="../named.html"><span class="token function">named</span></a><span class="token punctuation">(</span><span class="parameters "><span class="parameter ">name<span class="token operator">: </span><a href="https://kotlinlang.org/api/latest/jvm/stdlib/kotlin/-string/index.html">String</a><span class="token punctuation">, </span></span><span class="parameter "><span class="token keyword">noinline </span>configuration<span class="token operator">: </span><span class="token keyword"></span><a href="../named.html">T</a><span class="token punctuation">.</span><span class="token punctuation">(</span><span class="token punctuation">)</span><span class="token operator"> -&gt; </span><span class="token keyword"></span><a href="https://kotlinlang.org/api/latest/jvm/stdlib/kotlin/-unit/index.html">Unit</a></span></span><span class="token punctuation">)</span><span class="token operator">: </span><a href="../../org.gradle.api/-named-domain-object-provider/index.html#306700278%2FMain%2F-**********">NamedDomainObjectProvider</a><span class="token operator">&lt;</span><span class="token keyword"></span><a href="../named.html">T</a><span class="token operator">&gt;</span></div><div class="symbol monospace"><span class="token keyword"></span><span class="token keyword">fun </span><span class="token operator">&lt;</span><span class="token keyword"></span><a href="../named.html">T</a><span class="token operator"> : </span><a href="https://kotlinlang.org/api/latest/jvm/stdlib/kotlin/-any/index.html">Any</a><span class="token operator">&gt; </span><a href="../../org.gradle.api/-named-domain-object-collection/index.html#-87091607%2FMain%2F-**********">NamedDomainObjectCollection</a><span class="token operator">&lt;</span><span class="token keyword">out </span><a href="https://kotlinlang.org/api/latest/jvm/stdlib/kotlin/-any/index.html">Any</a><span class="token operator">&gt;</span><span class="token punctuation">.</span><a href="../named.html"><span class="token function">named</span></a><span class="token punctuation">(</span><span class="parameters "><span class="parameter ">name<span class="token operator">: </span><a href="https://kotlinlang.org/api/latest/jvm/stdlib/kotlin/-string/index.html">String</a><span class="token punctuation">, </span></span><span class="parameter ">type<span class="token operator">: </span><a href="https://kotlinlang.org/api/latest/jvm/stdlib/kotlin.reflect/-k-class/index.html">KClass</a><span class="token operator">&lt;</span><span class="token keyword"></span><a href="../named.html">T</a><span class="token operator">&gt;</span><span class="token punctuation">, </span></span><span class="parameter ">configuration<span class="token operator">: </span><span class="token keyword"></span><a href="../named.html">T</a><span class="token punctuation">.</span><span class="token punctuation">(</span><span class="token punctuation">)</span><span class="token operator"> -&gt; </span><span class="token keyword"></span><a href="https://kotlinlang.org/api/latest/jvm/stdlib/kotlin/-unit/index.html">Unit</a></span></span><span class="token punctuation">)</span><span class="token operator">: </span><a href="../../org.gradle.api/-named-domain-object-provider/index.html#306700278%2FMain%2F-**********">NamedDomainObjectProvider</a><span class="token operator">&lt;</span><span class="token keyword"></span><a href="../named.html">T</a><span class="token operator">&gt;</span></div><div class="brief "><p class="paragraph">Configures an object by name and type, without triggering its creation or configuration, failing if there is no such object.</p></div><div class="symbol monospace"><span class="token keyword">inline </span><span class="token keyword">fun </span><span class="token operator">&lt;</span><span class="token keyword"></span><a href="../named.html">S</a><span class="token operator"> : </span><a href="../named.html">T</a><span class="token punctuation">, </span><span class="token keyword"></span><a href="../named.html">T</a><span class="token operator"> : </span><a href="../../org.gradle.api/-task/index.html#217904858%2FMain%2F-**********">Task</a><span class="token operator">&gt; </span><a href="../../org.gradle.api.tasks/-task-collection/index.html#-**********%2FMain%2F-**********">TaskCollection</a><span class="token operator">&lt;</span><span class="token keyword"></span><a href="../named.html">T</a><span class="token operator">&gt;</span><span class="token punctuation">.</span><a href="../named.html"><span class="token function">named</span></a><span class="token punctuation">(</span><span class="parameters "><span class="parameter ">name<span class="token operator">: </span><a href="https://kotlinlang.org/api/latest/jvm/stdlib/kotlin/-string/index.html">String</a><span class="token punctuation">, </span></span><span class="parameter ">type<span class="token operator">: </span><a href="https://kotlinlang.org/api/latest/jvm/stdlib/kotlin.reflect/-k-class/index.html">KClass</a><span class="token operator">&lt;</span><span class="token keyword"></span><a href="../named.html">S</a><span class="token operator">&gt;</span></span></span><span class="token punctuation">)</span><span class="token operator">: </span><a href="../../org.gradle.api.tasks/-task-provider/index.html#-740321463%2FMain%2F-**********">TaskProvider</a><span class="token operator">&lt;</span><span class="token keyword"></span><a href="../named.html">S</a><span class="token operator">&gt;</span></div><div class="symbol monospace"><span class="token keyword">inline </span><span class="token keyword">fun </span><span class="token operator">&lt;</span><span class="token keyword"></span><a href="../named.html">S</a><span class="token operator"> : </span><a href="../named.html">T</a><span class="token punctuation">, </span><span class="token keyword"></span><a href="../named.html">T</a><span class="token operator"> : </span><a href="../../org.gradle.api/-task/index.html#217904858%2FMain%2F-**********">Task</a><span class="token operator">&gt; </span><a href="../../org.gradle.api.tasks/-task-collection/index.html#-**********%2FMain%2F-**********">TaskCollection</a><span class="token operator">&lt;</span><span class="token keyword"></span><a href="../named.html">T</a><span class="token operator">&gt;</span><span class="token punctuation">.</span><a href="../named.html"><span class="token function">named</span></a><span class="token punctuation">(</span><span class="parameters "><span class="parameter ">name<span class="token operator">: </span><a href="https://kotlinlang.org/api/latest/jvm/stdlib/kotlin/-string/index.html">String</a><span class="token punctuation">, </span></span><span class="parameter ">type<span class="token operator">: </span><a href="https://kotlinlang.org/api/latest/jvm/stdlib/kotlin.reflect/-k-class/index.html">KClass</a><span class="token operator">&lt;</span><span class="token keyword"></span><a href="../named.html">S</a><span class="token operator">&gt;</span><span class="token punctuation">, </span></span><span class="parameter ">configurationAction<span class="token operator">: </span><a href="../../org.gradle.api/-action/index.html#-199246903%2FMain%2F-**********">Action</a><span class="token operator">&lt;</span><span class="token keyword">in </span><a href="../named.html">S</a><span class="token operator">&gt;</span></span></span><span class="token punctuation">)</span><span class="token operator">: </span><a href="../../org.gradle.api.tasks/-task-provider/index.html#-740321463%2FMain%2F-**********">TaskProvider</a><span class="token operator">&lt;</span><span class="token keyword"></span><a href="../named.html">S</a><span class="token operator">&gt;</span></div><div class="brief "><p class="paragraph">Kotlin extension function taking <a href="https://kotlinlang.org/api/latest/jvm/stdlib/kotlin.reflect/-k-class/index.html">kotlin.reflect.KClass</a> for <span data-unresolved-link="org.gradle.api.tasks/TaskCollection/named/#kotlin.String/PointingToDeclaration/">org.gradle.api.tasks.TaskCollection.named</span>.</p></div><div class="symbol monospace"><span class="token keyword">inline </span><span class="token keyword">fun </span><span class="token operator">&lt;</span><span class="token keyword"></span><a href="../named.html">T</a><span class="token operator"> : </span><a href="../../org.gradle.api/-task/index.html#217904858%2FMain%2F-**********">Task</a><span class="token operator">&gt; </span><a href="../../org.gradle.api.tasks/-task-collection/index.html#-**********%2FMain%2F-**********">TaskCollection</a><span class="token operator">&lt;</span><span class="token keyword">out </span><a href="../../org.gradle.api/-task/index.html#217904858%2FMain%2F-**********">Task</a><span class="token operator">&gt;</span><span class="token punctuation">.</span><a href="../named.html"><span class="token function">named</span></a><span class="token punctuation">(</span><span class="parameters "><span class="parameter ">name<span class="token operator">: </span><a href="https://kotlinlang.org/api/latest/jvm/stdlib/kotlin/-string/index.html">String</a><span class="token punctuation">, </span></span><span class="parameter "><span class="token keyword">noinline </span>configuration<span class="token operator">: </span><span class="token keyword"></span><a href="../named.html">T</a><span class="token punctuation">.</span><span class="token punctuation">(</span><span class="token punctuation">)</span><span class="token operator"> -&gt; </span><span class="token keyword"></span><a href="https://kotlinlang.org/api/latest/jvm/stdlib/kotlin/-unit/index.html">Unit</a></span></span><span class="token punctuation">)</span><span class="token operator">: </span><a href="../../org.gradle.api.tasks/-task-provider/index.html#-740321463%2FMain%2F-**********">TaskProvider</a><span class="token operator">&lt;</span><span class="token keyword"></span><a href="../named.html">T</a><span class="token operator">&gt;</span></div><div class="symbol monospace"><span class="token keyword"></span><span class="token keyword">fun </span><span class="token operator">&lt;</span><span class="token keyword"></span><a href="../named.html">T</a><span class="token operator"> : </span><a href="../../org.gradle.api/-task/index.html#217904858%2FMain%2F-**********">Task</a><span class="token operator">&gt; </span><a href="../../org.gradle.api.tasks/-task-collection/index.html#-**********%2FMain%2F-**********">TaskCollection</a><span class="token operator">&lt;</span><span class="token keyword">out </span><a href="../../org.gradle.api/-task/index.html#217904858%2FMain%2F-**********">Task</a><span class="token operator">&gt;</span><span class="token punctuation">.</span><a href="../named.html"><span class="token function">named</span></a><span class="token punctuation">(</span><span class="parameters "><span class="parameter ">name<span class="token operator">: </span><a href="https://kotlinlang.org/api/latest/jvm/stdlib/kotlin/-string/index.html">String</a><span class="token punctuation">, </span></span><span class="parameter ">type<span class="token operator">: </span><a href="https://kotlinlang.org/api/latest/jvm/stdlib/kotlin.reflect/-k-class/index.html">KClass</a><span class="token operator">&lt;</span><span class="token keyword"></span><a href="../named.html">T</a><span class="token operator">&gt;</span><span class="token punctuation">, </span></span><span class="parameter ">configuration<span class="token operator">: </span><span class="token keyword"></span><a href="../named.html">T</a><span class="token punctuation">.</span><span class="token punctuation">(</span><span class="token punctuation">)</span><span class="token operator"> -&gt; </span><span class="token keyword"></span><a href="https://kotlinlang.org/api/latest/jvm/stdlib/kotlin/-unit/index.html">Unit</a></span></span><span class="token punctuation">)</span><span class="token operator">: </span><a href="../../org.gradle.api.tasks/-task-provider/index.html#-740321463%2FMain%2F-**********">TaskProvider</a><span class="token operator">&lt;</span><span class="token keyword"></span><a href="../named.html">T</a><span class="token operator">&gt;</span></div><div class="brief "><p class="paragraph">Configures a task by name and type, without triggering its creation or configuration, failing if there is no such task.</p></div></div>                  </div>
                </div>
              </div>
            </div>
          </div>
<a data-name="-**********%2FFunctions%2F-**********" anchor-label="parallelStream" id="-**********%2FFunctions%2F-**********" data-filterable-set=":docs/kotlin_dsl"></a>
          <div class="table-row" data-filterable-current=":docs/kotlin_dsl" data-filterable-set=":docs/kotlin_dsl">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="index.html#-**********%2FFunctions%2F-**********"><span>parallel</span><wbr></wbr><span><span>Stream</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="-**********%2FFunctions%2F-**********"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
                    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/kotlin_dsl" data-filterable-set=":docs/kotlin_dsl" data-active="" data-toggle=":docs/kotlin_dsl">DSL</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/kotlin_dsl"><div class="symbol monospace"><span class="token keyword">open </span><span class="token keyword"></span><span class="token keyword">fun </span><a href="index.html#-**********%2FFunctions%2F-**********"><span class="token function">parallelStream</span></a><span class="token punctuation">(</span><span class="token punctuation">)</span><span class="token operator">: </span><a href="https://docs.oracle.com/en/java/javase/11/docs/api/java.base/java/util/stream/Stream.html">Stream</a><span class="token operator">&lt;</span><span class="token keyword"></span><a href="../../org.gradle.api/-task/index.html#217904858%2FMain%2F-**********">Task</a><span class="token operator">&gt;</span></div></div>                  </div>
                </div>
              </div>
            </div>
          </div>
<a data-name="-2099703139%2FFunctions%2F-**********" anchor-label="provideDelegate" id="-2099703139%2FFunctions%2F-**********" data-filterable-set=":docs/kotlin_dsl"></a>
          <div class="table-row" data-togglable="EXTENSION_FUNCTION" data-filterable-current=":docs/kotlin_dsl" data-filterable-set=":docs/kotlin_dsl">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="../provide-delegate.html"><span>provide</span><wbr></wbr><span><span>Delegate</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="-2099703139%2FFunctions%2F-**********"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
                    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/kotlin_dsl" data-filterable-set=":docs/kotlin_dsl" data-active="" data-toggle=":docs/kotlin_dsl">DSL</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/kotlin_dsl"><div class="symbol monospace"><span class="token keyword">operator </span><span class="token keyword">fun </span><span class="token operator">&lt;</span><span class="token keyword"></span><a href="../provide-delegate.html">T</a><span class="token operator"> : </span><a href="https://kotlinlang.org/api/latest/jvm/stdlib/kotlin/-any/index.html">Any</a><span class="token operator">&gt; </span><a href="../../org.gradle.api/-named-domain-object-collection/index.html#-87091607%2FMain%2F-**********">NamedDomainObjectCollection</a><span class="token operator">&lt;</span><span class="token keyword"></span><a href="../provide-delegate.html">T</a><span class="token operator">&gt;</span><span class="token punctuation">.</span><a href="../provide-delegate.html"><span class="token function">provideDelegate</span></a><span class="token punctuation">(</span><span class="parameters "><span class="parameter ">thisRef<span class="token operator">: </span><a href="https://kotlinlang.org/api/latest/jvm/stdlib/kotlin/-any/index.html">Any</a><span class="token operator">?</span><span class="token punctuation">, </span></span><span class="parameter ">property<span class="token operator">: </span><a href="https://kotlinlang.org/api/latest/jvm/stdlib/kotlin.reflect/-k-property/index.html">KProperty</a><span class="token operator">&lt;</span><span class="token operator">*</span><span class="token operator">&gt;</span></span></span><span class="token punctuation">)</span><span class="token operator">: </span><a href="../../org.gradle.api/-named-domain-object-provider/index.html#306700278%2FMain%2F-**********">NamedDomainObjectProvider</a><span class="token operator">&lt;</span><span class="token keyword"></span><a href="../provide-delegate.html">T</a><span class="token operator">&gt;</span></div><div class="brief "><p class="paragraph">Allows a <a href="../../org.gradle.api/-named-domain-object-collection/index.html#-87091607%2FMain%2F-**********">NamedDomainObjectCollection</a> to be used as a property delegate.</p></div></div>                  </div>
                </div>
              </div>
            </div>
          </div>
<a data-name="-**********%2FFunctions%2F-**********" anchor-label="register" id="-**********%2FFunctions%2F-**********" data-filterable-set=":docs/kotlin_dsl"></a>
          <div class="table-row" data-filterable-current=":docs/kotlin_dsl" data-filterable-set=":docs/kotlin_dsl">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="index.html#796904633%2FFunctions%2F-**********"><span><span>register</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="-**********%2FFunctions%2F-**********"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
                    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/kotlin_dsl" data-filterable-set=":docs/kotlin_dsl" data-active="" data-toggle=":docs/kotlin_dsl">DSL</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/kotlin_dsl"><div class="symbol monospace"><span class="token keyword">open </span><span class="token keyword">override </span><span class="token keyword">fun </span><a href="index.html#796904633%2FFunctions%2F-**********"><span class="token function">register</span></a><span class="token punctuation">(</span><span class="parameters "><span class="parameter ">name<span class="token operator">: </span><a href="https://kotlinlang.org/api/latest/jvm/stdlib/kotlin/-string/index.html">String</a></span></span><span class="token punctuation">)</span><span class="token operator">: </span><a href="../../org.gradle.api.tasks/-task-provider/index.html#-740321463%2FMain%2F-**********">TaskProvider</a><span class="token operator">&lt;</span><span class="token keyword"></span><a href="../../org.gradle.api/-task/index.html#217904858%2FMain%2F-**********">Task</a><span class="token operator">&gt;</span></div><div class="symbol monospace"><span class="token keyword">open </span><span class="token keyword">override </span><span class="token keyword">fun </span><span class="token operator">&lt;</span><span class="token keyword"></span><a href="index.html#-267330292%2FFunctions%2F-**********">T</a><span class="token operator"> : </span><a href="../../org.gradle.api/-task/index.html#217904858%2FMain%2F-**********">Task</a><span class="token operator">&gt; </span><a href="index.html#-267330292%2FFunctions%2F-**********"><span class="token function">register</span></a><span class="token punctuation">(</span><span class="parameters "><span class="parameter ">name<span class="token operator">: </span><a href="https://kotlinlang.org/api/latest/jvm/stdlib/kotlin/-string/index.html">String</a><span class="token punctuation">, </span></span><span class="parameter ">type<span class="token operator">: </span><a href="https://docs.oracle.com/en/java/javase/11/docs/api/java.base/java/lang/Class.html">Class</a><span class="token operator">&lt;</span><span class="token keyword"></span><a href="index.html#-267330292%2FFunctions%2F-**********">T</a><span class="token operator">&gt;</span></span></span><span class="token punctuation">)</span><span class="token operator">: </span><a href="../../org.gradle.api.tasks/-task-provider/index.html#-740321463%2FMain%2F-**********">TaskProvider</a><span class="token operator">&lt;</span><span class="token keyword"></span><a href="index.html#-267330292%2FFunctions%2F-**********">T</a><span class="token operator">&gt;</span></div><div class="symbol monospace"><span class="token keyword">open </span><span class="token keyword">override </span><span class="token keyword">fun </span><a href="index.html#-**********%2FFunctions%2F-**********"><span class="token function">register</span></a><span class="token punctuation">(</span><span class="parameters "><span class="parameter ">name<span class="token operator">: </span><a href="https://kotlinlang.org/api/latest/jvm/stdlib/kotlin/-string/index.html">String</a><span class="token punctuation">, </span></span><span class="parameter ">configurationAction<span class="token operator">: </span><a href="../../org.gradle.api/-action/index.html#-199246903%2FMain%2F-**********">Action</a><span class="token operator">&lt;</span><span class="token keyword">in </span><a href="../../org.gradle.api/-task/index.html#217904858%2FMain%2F-**********">Task</a><span class="token operator">&gt;</span></span></span><span class="token punctuation">)</span><span class="token operator">: </span><a href="../../org.gradle.api.tasks/-task-provider/index.html#-740321463%2FMain%2F-**********">TaskProvider</a><span class="token operator">&lt;</span><span class="token keyword"></span><a href="../../org.gradle.api/-task/index.html#217904858%2FMain%2F-**********">Task</a><span class="token operator">&gt;</span></div><div class="symbol monospace"><span class="token keyword">open </span><span class="token keyword">override </span><span class="token keyword">fun </span><span class="token operator">&lt;</span><span class="token keyword"></span><a href="index.html#823084547%2FFunctions%2F-**********">T</a><span class="token operator"> : </span><a href="../../org.gradle.api/-task/index.html#217904858%2FMain%2F-**********">Task</a><span class="token operator">&gt; </span><a href="index.html#823084547%2FFunctions%2F-**********"><span class="token function">register</span></a><span class="token punctuation">(</span><span class="parameters "><span class="parameter ">name<span class="token operator">: </span><a href="https://kotlinlang.org/api/latest/jvm/stdlib/kotlin/-string/index.html">String</a><span class="token punctuation">, </span></span><span class="parameter ">type<span class="token operator">: </span><a href="https://docs.oracle.com/en/java/javase/11/docs/api/java.base/java/lang/Class.html">Class</a><span class="token operator">&lt;</span><span class="token keyword"></span><a href="index.html#823084547%2FFunctions%2F-**********">T</a><span class="token operator">&gt;</span><span class="token punctuation">, </span></span><span class="parameter "><span class="token keyword">vararg </span>constructorArgs<span class="token operator">: </span><a href="https://kotlinlang.org/api/latest/jvm/stdlib/kotlin/-any/index.html">Any</a><span class="token operator">?</span></span></span><span class="token punctuation">)</span><span class="token operator">: </span><a href="../../org.gradle.api.tasks/-task-provider/index.html#-740321463%2FMain%2F-**********">TaskProvider</a><span class="token operator">&lt;</span><span class="token keyword"></span><a href="index.html#823084547%2FFunctions%2F-**********">T</a><span class="token operator">&gt;</span></div><div class="symbol monospace"><span class="token keyword">open </span><span class="token keyword">override </span><span class="token keyword">fun </span><span class="token operator">&lt;</span><span class="token keyword"></span><a href="index.html#-**********%2FFunctions%2F-**********">T</a><span class="token operator"> : </span><a href="../../org.gradle.api/-task/index.html#217904858%2FMain%2F-**********">Task</a><span class="token operator">&gt; </span><a href="index.html#-**********%2FFunctions%2F-**********"><span class="token function">register</span></a><span class="token punctuation">(</span><span class="parameters "><span class="parameter ">name<span class="token operator">: </span><a href="https://kotlinlang.org/api/latest/jvm/stdlib/kotlin/-string/index.html">String</a><span class="token punctuation">, </span></span><span class="parameter ">type<span class="token operator">: </span><a href="https://docs.oracle.com/en/java/javase/11/docs/api/java.base/java/lang/Class.html">Class</a><span class="token operator">&lt;</span><span class="token keyword"></span><a href="index.html#-**********%2FFunctions%2F-**********">T</a><span class="token operator">&gt;</span><span class="token punctuation">, </span></span><span class="parameter ">configurationAction<span class="token operator">: </span><a href="../../org.gradle.api/-action/index.html#-199246903%2FMain%2F-**********">Action</a><span class="token operator">&lt;</span><span class="token keyword">in </span><a href="index.html#-**********%2FFunctions%2F-**********">T</a><span class="token operator">&gt;</span></span></span><span class="token punctuation">)</span><span class="token operator">: </span><a href="../../org.gradle.api.tasks/-task-provider/index.html#-740321463%2FMain%2F-**********">TaskProvider</a><span class="token operator">&lt;</span><span class="token keyword"></span><a href="index.html#-**********%2FFunctions%2F-**********">T</a><span class="token operator">&gt;</span></div></div>                  </div>
                </div>
              </div>
            </div>
          </div>
<a data-name="-**********%2FFunctions%2F-**********" anchor-label="register" id="-**********%2FFunctions%2F-**********" data-filterable-set=":docs/kotlin_dsl"></a>
          <div class="table-row" data-togglable="EXTENSION_FUNCTION" data-filterable-current=":docs/kotlin_dsl" data-filterable-set=":docs/kotlin_dsl">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="../register.html"><span><span>register</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="-**********%2FFunctions%2F-**********"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
                    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/kotlin_dsl" data-filterable-set=":docs/kotlin_dsl" data-active="" data-toggle=":docs/kotlin_dsl">DSL</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/kotlin_dsl"><div class="symbol monospace"><span class="token keyword">inline </span><span class="token keyword">fun </span><span class="token operator">&lt;</span><span class="token keyword"></span><a href="../register.html">T</a><span class="token operator"> : </span><a href="https://kotlinlang.org/api/latest/jvm/stdlib/kotlin/-any/index.html">Any</a><span class="token operator">&gt; </span><a href="../../org.gradle.api/-polymorphic-domain-object-container/index.html#427856727%2FMain%2F-**********">PolymorphicDomainObjectContainer</a><span class="token operator">&lt;</span><span class="token keyword">in </span><a href="../register.html">T</a><span class="token operator">&gt;</span><span class="token punctuation">.</span><a href="../register.html"><span class="token function">register</span></a><span class="token punctuation">(</span><span class="parameters "><span class="parameter ">name<span class="token operator">: </span><a href="https://kotlinlang.org/api/latest/jvm/stdlib/kotlin/-string/index.html">String</a></span></span><span class="token punctuation">)</span><span class="token operator">: </span><a href="../../org.gradle.api/-named-domain-object-provider/index.html#306700278%2FMain%2F-**********">NamedDomainObjectProvider</a><span class="token operator">&lt;</span><span class="token keyword"></span><a href="../register.html">T</a><span class="token operator">&gt;</span></div><div class="brief "><p class="paragraph">Defines a new object, which will be created when it is required.</p></div><div class="symbol monospace"><span class="token keyword">inline </span><span class="token keyword">fun </span><span class="token operator">&lt;</span><span class="token keyword"></span><a href="../register.html">T</a><span class="token operator"> : </span><a href="../../org.gradle.api/-task/index.html#217904858%2FMain%2F-**********">Task</a><span class="token operator">&gt; </span><a href="../../org.gradle.api.tasks/-task-container/index.html#-**********%2FMain%2F-**********">TaskContainer</a><span class="token punctuation">.</span><a href="../register.html"><span class="token function">register</span></a><span class="token punctuation">(</span><span class="parameters "><span class="parameter ">name<span class="token operator">: </span><a href="https://kotlinlang.org/api/latest/jvm/stdlib/kotlin/-string/index.html">String</a></span></span><span class="token punctuation">)</span><span class="token operator">: </span><a href="../../org.gradle.api.tasks/-task-provider/index.html#-740321463%2FMain%2F-**********">TaskProvider</a><span class="token operator">&lt;</span><span class="token keyword"></span><a href="../register.html">T</a><span class="token operator">&gt;</span></div><div class="brief "><p class="paragraph">Defines a new task, which will be created when it is required.</p></div><div class="symbol monospace"><span class="token keyword">inline </span><span class="token keyword">fun </span><span class="token operator">&lt;</span><span class="token keyword"></span><a href="../register.html">T</a><span class="token operator"> : </span><a href="https://kotlinlang.org/api/latest/jvm/stdlib/kotlin/-any/index.html">Any</a><span class="token operator">&gt; </span><a href="../../org.gradle.api/-polymorphic-domain-object-container/index.html#427856727%2FMain%2F-**********">PolymorphicDomainObjectContainer</a><span class="token operator">&lt;</span><span class="token keyword">in </span><a href="../register.html">T</a><span class="token operator">&gt;</span><span class="token punctuation">.</span><a href="../register.html"><span class="token function">register</span></a><span class="token punctuation">(</span><span class="parameters "><span class="parameter ">name<span class="token operator">: </span><a href="https://kotlinlang.org/api/latest/jvm/stdlib/kotlin/-string/index.html">String</a><span class="token punctuation">, </span></span><span class="parameter "><span class="token keyword">noinline </span>configuration<span class="token operator">: </span><span class="token keyword"></span><a href="../register.html">T</a><span class="token punctuation">.</span><span class="token punctuation">(</span><span class="token punctuation">)</span><span class="token operator"> -&gt; </span><span class="token keyword"></span><a href="https://kotlinlang.org/api/latest/jvm/stdlib/kotlin/-unit/index.html">Unit</a></span></span><span class="token punctuation">)</span><span class="token operator">: </span><a href="../../org.gradle.api/-named-domain-object-provider/index.html#306700278%2FMain%2F-**********">NamedDomainObjectProvider</a><span class="token operator">&lt;</span><span class="token keyword"></span><a href="../register.html">T</a><span class="token operator">&gt;</span></div><div class="brief "><p class="paragraph">Defines and configure a new object, which will be created when it is required.</p></div><div class="symbol monospace"><span class="token keyword">inline </span><span class="token keyword">fun </span><span class="token operator">&lt;</span><span class="token keyword"></span><a href="../register.html">U</a><span class="token operator"> : </span><a href="../register.html">T</a><span class="token punctuation">, </span><span class="token keyword"></span><a href="../register.html">T</a><span class="token operator"> : </span><a href="https://kotlinlang.org/api/latest/jvm/stdlib/kotlin/-any/index.html">Any</a><span class="token operator">&gt; </span><a href="../../org.gradle.api/-polymorphic-domain-object-container/index.html#427856727%2FMain%2F-**********">PolymorphicDomainObjectContainer</a><span class="token operator">&lt;</span><span class="token keyword"></span><a href="../register.html">T</a><span class="token operator">&gt;</span><span class="token punctuation">.</span><a href="../register.html"><span class="token function">register</span></a><span class="token punctuation">(</span><span class="parameters "><span class="parameter ">name<span class="token operator">: </span><a href="https://kotlinlang.org/api/latest/jvm/stdlib/kotlin/-string/index.html">String</a><span class="token punctuation">, </span></span><span class="parameter ">type<span class="token operator">: </span><a href="https://kotlinlang.org/api/latest/jvm/stdlib/kotlin.reflect/-k-class/index.html">KClass</a><span class="token operator">&lt;</span><span class="token keyword"></span><a href="../register.html">U</a><span class="token operator">&gt;</span></span></span><span class="token punctuation">)</span><span class="token operator">: </span><a href="../../org.gradle.api/-named-domain-object-provider/index.html#306700278%2FMain%2F-**********">NamedDomainObjectProvider</a><span class="token operator">&lt;</span><span class="token keyword"></span><a href="../register.html">U</a><span class="token operator">&gt;</span></div><div class="symbol monospace"><span class="token keyword">inline </span><span class="token keyword">fun </span><span class="token operator">&lt;</span><span class="token keyword"></span><a href="../register.html">U</a><span class="token operator"> : </span><a href="../register.html">T</a><span class="token punctuation">, </span><span class="token keyword"></span><a href="../register.html">T</a><span class="token operator"> : </span><a href="https://kotlinlang.org/api/latest/jvm/stdlib/kotlin/-any/index.html">Any</a><span class="token operator">&gt; </span><a href="../../org.gradle.api/-polymorphic-domain-object-container/index.html#427856727%2FMain%2F-**********">PolymorphicDomainObjectContainer</a><span class="token operator">&lt;</span><span class="token keyword"></span><a href="../register.html">T</a><span class="token operator">&gt;</span><span class="token punctuation">.</span><a href="../register.html"><span class="token function">register</span></a><span class="token punctuation">(</span><span class="parameters "><span class="parameter ">name<span class="token operator">: </span><a href="https://kotlinlang.org/api/latest/jvm/stdlib/kotlin/-string/index.html">String</a><span class="token punctuation">, </span></span><span class="parameter ">type<span class="token operator">: </span><a href="https://kotlinlang.org/api/latest/jvm/stdlib/kotlin.reflect/-k-class/index.html">KClass</a><span class="token operator">&lt;</span><span class="token keyword"></span><a href="../register.html">U</a><span class="token operator">&gt;</span><span class="token punctuation">, </span></span><span class="parameter ">configurationAction<span class="token operator">: </span><a href="../../org.gradle.api/-action/index.html#-199246903%2FMain%2F-**********">Action</a><span class="token operator">&lt;</span><span class="token keyword">in </span><a href="../register.html">U</a><span class="token operator">&gt;</span></span></span><span class="token punctuation">)</span><span class="token operator">: </span><a href="../../org.gradle.api/-named-domain-object-provider/index.html#306700278%2FMain%2F-**********">NamedDomainObjectProvider</a><span class="token operator">&lt;</span><span class="token keyword"></span><a href="../register.html">U</a><span class="token operator">&gt;</span></div><div class="brief "><p class="paragraph">Kotlin extension function taking <a href="https://kotlinlang.org/api/latest/jvm/stdlib/kotlin.reflect/-k-class/index.html">kotlin.reflect.KClass</a> for <span data-unresolved-link="org.gradle.api/PolymorphicDomainObjectContainer/register/#kotlin.String#java.lang.Class[TypeParam(bounds=[TypeParam(bounds=[kotlin.Any])])]#org.gradle.api.Action[TypeParam(bounds=[TypeParam(bounds=[kotlin.Any])])]/PointingToDeclaration/">org.gradle.api.PolymorphicDomainObjectContainer.register</span>.</p></div><div class="symbol monospace"><span class="token keyword">inline </span><span class="token keyword">fun </span><span class="token operator">&lt;</span><span class="token keyword"></span><a href="../register.html">T</a><span class="token operator"> : </span><a href="../../org.gradle.api/-task/index.html#217904858%2FMain%2F-**********">Task</a><span class="token operator">&gt; </span><a href="../../org.gradle.api.tasks/-task-container/index.html#-**********%2FMain%2F-**********">TaskContainer</a><span class="token punctuation">.</span><a href="../register.html"><span class="token function">register</span></a><span class="token punctuation">(</span><span class="parameters "><span class="parameter ">name<span class="token operator">: </span><a href="https://kotlinlang.org/api/latest/jvm/stdlib/kotlin/-string/index.html">String</a><span class="token punctuation">, </span></span><span class="parameter "><span class="token keyword">vararg </span>arguments<span class="token operator">: </span><a href="https://kotlinlang.org/api/latest/jvm/stdlib/kotlin/-any/index.html">Any</a></span></span><span class="token punctuation">)</span><span class="token operator">: </span><a href="../../org.gradle.api.tasks/-task-provider/index.html#-740321463%2FMain%2F-**********">TaskProvider</a><span class="token operator">&lt;</span><span class="token keyword"></span><a href="../register.html">T</a><span class="token operator">&gt;</span></div><div class="brief "><p class="paragraph">Defines a new task, which will be created when it is required passing the given arguments to the <span data-unresolved-link="javax.inject/Inject///PointingToDeclaration/">javax.inject.Inject</span>-annotated constructor.</p></div><div class="symbol monospace"><span class="token keyword">inline </span><span class="token keyword">fun </span><span class="token operator">&lt;</span><span class="token keyword"></span><a href="../register.html">T</a><span class="token operator"> : </span><a href="../../org.gradle.api/-task/index.html#217904858%2FMain%2F-**********">Task</a><span class="token operator">&gt; </span><a href="../../org.gradle.api.tasks/-task-container/index.html#-**********%2FMain%2F-**********">TaskContainer</a><span class="token punctuation">.</span><a href="../register.html"><span class="token function">register</span></a><span class="token punctuation">(</span><span class="parameters "><span class="parameter ">name<span class="token operator">: </span><a href="https://kotlinlang.org/api/latest/jvm/stdlib/kotlin/-string/index.html">String</a><span class="token punctuation">, </span></span><span class="parameter "><span class="token keyword">noinline </span>configuration<span class="token operator">: </span><span class="token keyword"></span><a href="../register.html">T</a><span class="token punctuation">.</span><span class="token punctuation">(</span><span class="token punctuation">)</span><span class="token operator"> -&gt; </span><span class="token keyword"></span><a href="https://kotlinlang.org/api/latest/jvm/stdlib/kotlin/-unit/index.html">Unit</a></span></span><span class="token punctuation">)</span><span class="token operator">: </span><a href="../../org.gradle.api.tasks/-task-provider/index.html#-740321463%2FMain%2F-**********">TaskProvider</a><span class="token operator">&lt;</span><span class="token keyword"></span><a href="../register.html">T</a><span class="token operator">&gt;</span></div><div class="brief "><p class="paragraph">Defines and configure a new task, which will be created when it is required.</p></div><div class="symbol monospace"><span class="token keyword">inline </span><span class="token keyword">fun </span><span class="token operator">&lt;</span><span class="token keyword"></span><a href="../register.html">T</a><span class="token operator"> : </span><a href="../../org.gradle.api/-task/index.html#217904858%2FMain%2F-**********">Task</a><span class="token operator">&gt; </span><a href="../../org.gradle.api.tasks/-task-container/index.html#-**********%2FMain%2F-**********">TaskContainer</a><span class="token punctuation">.</span><a href="../register.html"><span class="token function">register</span></a><span class="token punctuation">(</span><span class="parameters "><span class="parameter ">name<span class="token operator">: </span><a href="https://kotlinlang.org/api/latest/jvm/stdlib/kotlin/-string/index.html">String</a><span class="token punctuation">, </span></span><span class="parameter ">type<span class="token operator">: </span><a href="https://kotlinlang.org/api/latest/jvm/stdlib/kotlin.reflect/-k-class/index.html">KClass</a><span class="token operator">&lt;</span><span class="token keyword"></span><a href="../register.html">T</a><span class="token operator">&gt;</span></span></span><span class="token punctuation">)</span><span class="token operator">: </span><a href="../../org.gradle.api.tasks/-task-provider/index.html#-740321463%2FMain%2F-**********">TaskProvider</a><span class="token operator">&lt;</span><span class="token keyword"></span><a href="../register.html">T</a><span class="token operator">&gt;</span></div><div class="symbol monospace"><span class="token keyword">inline </span><span class="token keyword">fun </span><span class="token operator">&lt;</span><span class="token keyword"></span><a href="../register.html">T</a><span class="token operator"> : </span><a href="../../org.gradle.api/-task/index.html#217904858%2FMain%2F-**********">Task</a><span class="token operator">&gt; </span><a href="../../org.gradle.api.tasks/-task-container/index.html#-**********%2FMain%2F-**********">TaskContainer</a><span class="token punctuation">.</span><a href="../register.html"><span class="token function">register</span></a><span class="token punctuation">(</span><span class="parameters "><span class="parameter ">name<span class="token operator">: </span><a href="https://kotlinlang.org/api/latest/jvm/stdlib/kotlin/-string/index.html">String</a><span class="token punctuation">, </span></span><span class="parameter ">type<span class="token operator">: </span><a href="https://kotlinlang.org/api/latest/jvm/stdlib/kotlin.reflect/-k-class/index.html">KClass</a><span class="token operator">&lt;</span><span class="token keyword"></span><a href="../register.html">T</a><span class="token operator">&gt;</span><span class="token punctuation">, </span></span><span class="parameter "><span class="token keyword">vararg </span>constructorArgs<span class="token operator">: </span><a href="https://kotlinlang.org/api/latest/jvm/stdlib/kotlin/-any/index.html">Any</a></span></span><span class="token punctuation">)</span><span class="token operator">: </span><a href="../../org.gradle.api.tasks/-task-provider/index.html#-740321463%2FMain%2F-**********">TaskProvider</a><span class="token operator">&lt;</span><span class="token keyword"></span><a href="../register.html">T</a><span class="token operator">&gt;</span></div><div class="symbol monospace"><span class="token keyword">inline </span><span class="token keyword">fun </span><span class="token operator">&lt;</span><span class="token keyword"></span><a href="../register.html">T</a><span class="token operator"> : </span><a href="../../org.gradle.api/-task/index.html#217904858%2FMain%2F-**********">Task</a><span class="token operator">&gt; </span><a href="../../org.gradle.api.tasks/-task-container/index.html#-**********%2FMain%2F-**********">TaskContainer</a><span class="token punctuation">.</span><a href="../register.html"><span class="token function">register</span></a><span class="token punctuation">(</span><span class="parameters "><span class="parameter ">name<span class="token operator">: </span><a href="https://kotlinlang.org/api/latest/jvm/stdlib/kotlin/-string/index.html">String</a><span class="token punctuation">, </span></span><span class="parameter ">type<span class="token operator">: </span><a href="https://kotlinlang.org/api/latest/jvm/stdlib/kotlin.reflect/-k-class/index.html">KClass</a><span class="token operator">&lt;</span><span class="token keyword"></span><a href="../register.html">T</a><span class="token operator">&gt;</span><span class="token punctuation">, </span></span><span class="parameter ">configurationAction<span class="token operator">: </span><a href="../../org.gradle.api/-action/index.html#-199246903%2FMain%2F-**********">Action</a><span class="token operator">&lt;</span><span class="token keyword">in </span><a href="../register.html">T</a><span class="token operator">&gt;</span></span></span><span class="token punctuation">)</span><span class="token operator">: </span><a href="../../org.gradle.api.tasks/-task-provider/index.html#-740321463%2FMain%2F-**********">TaskProvider</a><span class="token operator">&lt;</span><span class="token keyword"></span><a href="../register.html">T</a><span class="token operator">&gt;</span></div><div class="brief "><p class="paragraph">Kotlin extension function taking <a href="https://kotlinlang.org/api/latest/jvm/stdlib/kotlin.reflect/-k-class/index.html">kotlin.reflect.KClass</a> for <span data-unresolved-link="org.gradle.api.tasks/TaskContainer/register/#kotlin.String#org.gradle.api.Action[org.gradle.api.Task]/PointingToDeclaration/">org.gradle.api.tasks.TaskContainer.register</span>.</p></div></div>                  </div>
                </div>
              </div>
            </div>
          </div>
<a data-name="254631778%2FFunctions%2F-**********" anchor-label="remove" id="254631778%2FFunctions%2F-**********" data-filterable-set=":docs/kotlin_dsl"></a>
          <div class="table-row" data-filterable-current=":docs/kotlin_dsl" data-filterable-set=":docs/kotlin_dsl">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="index.html#254631778%2FFunctions%2F-**********"><span><span>remove</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="254631778%2FFunctions%2F-**********"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
                    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/kotlin_dsl" data-filterable-set=":docs/kotlin_dsl" data-active="" data-toggle=":docs/kotlin_dsl">DSL</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/kotlin_dsl"><div class="symbol monospace"><span class="token keyword">open </span><span class="token keyword">override </span><span class="token keyword">fun </span><a href="index.html#254631778%2FFunctions%2F-**********"><span class="token function">remove</span></a><span class="token punctuation">(</span><span class="parameters "><span class="parameter ">element<span class="token operator">: </span><a href="../../org.gradle.api/-task/index.html#217904858%2FMain%2F-**********">Task</a></span></span><span class="token punctuation">)</span><span class="token operator">: </span><a href="https://kotlinlang.org/api/latest/jvm/stdlib/kotlin/-boolean/index.html">Boolean</a></div></div>                  </div>
                </div>
              </div>
            </div>
          </div>
<a data-name="1435608721%2FFunctions%2F-**********" anchor-label="removeAll" id="1435608721%2FFunctions%2F-**********" data-filterable-set=":docs/kotlin_dsl"></a>
          <div class="table-row" data-filterable-current=":docs/kotlin_dsl" data-filterable-set=":docs/kotlin_dsl">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="index.html#1435608721%2FFunctions%2F-**********"><span>remove</span><wbr></wbr><span><span>All</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="1435608721%2FFunctions%2F-**********"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
                    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/kotlin_dsl" data-filterable-set=":docs/kotlin_dsl" data-active="" data-toggle=":docs/kotlin_dsl">DSL</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/kotlin_dsl"><div class="symbol monospace"><span class="token keyword">open </span><span class="token keyword">override </span><span class="token keyword">fun </span><a href="index.html#1435608721%2FFunctions%2F-**********"><span class="token function">removeAll</span></a><span class="token punctuation">(</span><span class="parameters "><span class="parameter ">elements<span class="token operator">: </span><a href="https://kotlinlang.org/api/latest/jvm/stdlib/kotlin.collections/-collection/index.html">Collection</a><span class="token operator">&lt;</span><span class="token keyword"></span><a href="../../org.gradle.api/-task/index.html#217904858%2FMain%2F-**********">Task</a><span class="token operator">&gt;</span></span></span><span class="token punctuation">)</span><span class="token operator">: </span><a href="https://kotlinlang.org/api/latest/jvm/stdlib/kotlin/-boolean/index.html">Boolean</a></div></div>                  </div>
                </div>
              </div>
            </div>
          </div>
<a data-name="-1630912201%2FFunctions%2F-**********" anchor-label="removeIf" id="-1630912201%2FFunctions%2F-**********" data-filterable-set=":docs/kotlin_dsl"></a>
          <div class="table-row" data-filterable-current=":docs/kotlin_dsl" data-filterable-set=":docs/kotlin_dsl">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="index.html#-1630912201%2FFunctions%2F-**********"><span>remove</span><wbr></wbr><span><span>If</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="-1630912201%2FFunctions%2F-**********"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
                    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/kotlin_dsl" data-filterable-set=":docs/kotlin_dsl" data-active="" data-toggle=":docs/kotlin_dsl">DSL</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/kotlin_dsl"><div class="symbol monospace"><span class="token keyword">open </span><span class="token keyword"></span><span class="token keyword">fun </span><a href="index.html#-1630912201%2FFunctions%2F-**********"><span class="token function">removeIf</span></a><span class="token punctuation">(</span><span class="parameters "><span class="parameter ">p0<span class="token operator">: </span><a href="https://docs.oracle.com/en/java/javase/11/docs/api/java.base/java/util/function/Predicate.html">Predicate</a><span class="token operator">&lt;</span><span class="token keyword">in </span><a href="../../org.gradle.api/-task/index.html#217904858%2FMain%2F-**********">Task</a><span class="token operator">&gt;</span></span></span><span class="token punctuation">)</span><span class="token operator">: </span><a href="https://kotlinlang.org/api/latest/jvm/stdlib/kotlin/-boolean/index.html">Boolean</a></div></div>                  </div>
                </div>
              </div>
            </div>
          </div>
<a data-name="-1179737569%2FFunctions%2F-**********" anchor-label="replace" id="-1179737569%2FFunctions%2F-**********" data-filterable-set=":docs/kotlin_dsl"></a>
          <div class="table-row" data-filterable-current=":docs/kotlin_dsl" data-filterable-set=":docs/kotlin_dsl">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="index.html#-217419188%2FFunctions%2F-**********"><span><span>replace</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="-1179737569%2FFunctions%2F-**********"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
                    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/kotlin_dsl" data-filterable-set=":docs/kotlin_dsl" data-active="" data-toggle=":docs/kotlin_dsl">DSL</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/kotlin_dsl"><div class="symbol monospace"><span class="token keyword">open </span><span class="token keyword">override </span><span class="token keyword">fun </span><a href="index.html#-217419188%2FFunctions%2F-**********"><span class="token function">replace</span></a><span class="token punctuation">(</span><span class="parameters "><span class="parameter ">name<span class="token operator">: </span><a href="https://kotlinlang.org/api/latest/jvm/stdlib/kotlin/-string/index.html">String</a></span></span><span class="token punctuation">)</span><span class="token operator">: </span><a href="../../org.gradle.api/-task/index.html#217904858%2FMain%2F-**********">Task</a></div><div class="symbol monospace"><span class="token keyword">open </span><span class="token keyword">override </span><span class="token keyword">fun </span><span class="token operator">&lt;</span><span class="token keyword"></span><a href="index.html#-1076748583%2FFunctions%2F-**********">T</a><span class="token operator"> : </span><a href="../../org.gradle.api/-task/index.html#217904858%2FMain%2F-**********">Task</a><span class="token operator">&gt; </span><a href="index.html#-1076748583%2FFunctions%2F-**********"><span class="token function">replace</span></a><span class="token punctuation">(</span><span class="parameters "><span class="parameter ">name<span class="token operator">: </span><a href="https://kotlinlang.org/api/latest/jvm/stdlib/kotlin/-string/index.html">String</a><span class="token punctuation">, </span></span><span class="parameter ">type<span class="token operator">: </span><a href="https://docs.oracle.com/en/java/javase/11/docs/api/java.base/java/lang/Class.html">Class</a><span class="token operator">&lt;</span><span class="token keyword"></span><a href="index.html#-1076748583%2FFunctions%2F-**********">T</a><span class="token operator">&gt;</span></span></span><span class="token punctuation">)</span><span class="token operator">: </span><a href="index.html#-1076748583%2FFunctions%2F-**********">T</a></div></div>                  </div>
                </div>
              </div>
            </div>
          </div>
<a data-name="-1474064375%2FFunctions%2F-**********" anchor-label="replace" id="-1474064375%2FFunctions%2F-**********" data-filterable-set=":docs/kotlin_dsl"></a>
          <div class="table-row" data-togglable="EXTENSION_FUNCTION" data-filterable-current=":docs/kotlin_dsl" data-filterable-set=":docs/kotlin_dsl">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="../replace.html"><span><span>replace</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="-1474064375%2FFunctions%2F-**********"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
                    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/kotlin_dsl" data-filterable-set=":docs/kotlin_dsl" data-active="" data-toggle=":docs/kotlin_dsl">DSL</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/kotlin_dsl"><div class="symbol monospace"><span class="token keyword">inline </span><span class="token keyword">fun </span><span class="token operator">&lt;</span><span class="token keyword"></span><a href="../replace.html">T</a><span class="token operator"> : </span><a href="../../org.gradle.api/-task/index.html#217904858%2FMain%2F-**********">Task</a><span class="token operator">&gt; </span><a href="../../org.gradle.api.tasks/-task-container/index.html#-**********%2FMain%2F-**********">TaskContainer</a><span class="token punctuation">.</span><a href="../replace.html"><span class="token function">replace</span></a><span class="token punctuation">(</span><span class="parameters "><span class="parameter ">name<span class="token operator">: </span><a href="https://kotlinlang.org/api/latest/jvm/stdlib/kotlin/-string/index.html">String</a><span class="token punctuation">, </span></span><span class="parameter ">type<span class="token operator">: </span><a href="https://kotlinlang.org/api/latest/jvm/stdlib/kotlin.reflect/-k-class/index.html">KClass</a><span class="token operator">&lt;</span><span class="token keyword"></span><a href="../replace.html">T</a><span class="token operator">&gt;</span></span></span><span class="token punctuation">)</span><span class="token operator">: </span><a href="../replace.html">T</a></div><div class="brief "><p class="paragraph">Kotlin extension function taking <a href="https://kotlinlang.org/api/latest/jvm/stdlib/kotlin.reflect/-k-class/index.html">kotlin.reflect.KClass</a> for <span data-unresolved-link="org.gradle.api.tasks/TaskContainer/replace/#kotlin.String/PointingToDeclaration/">org.gradle.api.tasks.TaskContainer.replace</span>.</p></div></div>                  </div>
                </div>
              </div>
            </div>
          </div>
<a data-name="1356636498%2FFunctions%2F-**********" anchor-label="retainAll" id="1356636498%2FFunctions%2F-**********" data-filterable-set=":docs/kotlin_dsl"></a>
          <div class="table-row" data-filterable-current=":docs/kotlin_dsl" data-filterable-set=":docs/kotlin_dsl">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="index.html#1356636498%2FFunctions%2F-**********"><span>retain</span><wbr></wbr><span><span>All</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="1356636498%2FFunctions%2F-**********"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
                    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/kotlin_dsl" data-filterable-set=":docs/kotlin_dsl" data-active="" data-toggle=":docs/kotlin_dsl">DSL</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/kotlin_dsl"><div class="symbol monospace"><span class="token keyword">open </span><span class="token keyword">override </span><span class="token keyword">fun </span><a href="index.html#1356636498%2FFunctions%2F-**********"><span class="token function">retainAll</span></a><span class="token punctuation">(</span><span class="parameters "><span class="parameter ">elements<span class="token operator">: </span><a href="https://kotlinlang.org/api/latest/jvm/stdlib/kotlin.collections/-collection/index.html">Collection</a><span class="token operator">&lt;</span><span class="token keyword"></span><a href="../../org.gradle.api/-task/index.html#217904858%2FMain%2F-**********">Task</a><span class="token operator">&gt;</span></span></span><span class="token punctuation">)</span><span class="token operator">: </span><a href="https://kotlinlang.org/api/latest/jvm/stdlib/kotlin/-boolean/index.html">Boolean</a></div></div>                  </div>
                </div>
              </div>
            </div>
          </div>
<a data-name="1956926474%2FFunctions%2F-**********" anchor-label="spliterator" id="1956926474%2FFunctions%2F-**********" data-filterable-set=":docs/kotlin_dsl"></a>
          <div class="table-row" data-filterable-current=":docs/kotlin_dsl" data-filterable-set=":docs/kotlin_dsl">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="index.html#1956926474%2FFunctions%2F-**********"><span><span>spliterator</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="1956926474%2FFunctions%2F-**********"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
                    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/kotlin_dsl" data-filterable-set=":docs/kotlin_dsl" data-active="" data-toggle=":docs/kotlin_dsl">DSL</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/kotlin_dsl"><div class="symbol monospace"><span class="token keyword">open </span><span class="token keyword">override </span><span class="token keyword">fun </span><a href="index.html#1956926474%2FFunctions%2F-**********"><span class="token function">spliterator</span></a><span class="token punctuation">(</span><span class="token punctuation">)</span><span class="token operator">: </span><a href="https://docs.oracle.com/en/java/javase/11/docs/api/java.base/java/util/Spliterator.html">Spliterator</a><span class="token operator">&lt;</span><span class="token keyword"></span><a href="../../org.gradle.api/-task/index.html#217904858%2FMain%2F-**********">Task</a><span class="token operator">&gt;</span></div></div>                  </div>
                </div>
              </div>
            </div>
          </div>
<a data-name="135225651%2FFunctions%2F-**********" anchor-label="stream" id="135225651%2FFunctions%2F-**********" data-filterable-set=":docs/kotlin_dsl"></a>
          <div class="table-row" data-filterable-current=":docs/kotlin_dsl" data-filterable-set=":docs/kotlin_dsl">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="index.html#135225651%2FFunctions%2F-**********"><span><span>stream</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="135225651%2FFunctions%2F-**********"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
                    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/kotlin_dsl" data-filterable-set=":docs/kotlin_dsl" data-active="" data-toggle=":docs/kotlin_dsl">DSL</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/kotlin_dsl"><div class="symbol monospace"><span class="token keyword">open </span><span class="token keyword"></span><span class="token keyword">fun </span><a href="index.html#135225651%2FFunctions%2F-**********"><span class="token function">stream</span></a><span class="token punctuation">(</span><span class="token punctuation">)</span><span class="token operator">: </span><a href="https://docs.oracle.com/en/java/javase/11/docs/api/java.base/java/util/stream/Stream.html">Stream</a><span class="token operator">&lt;</span><span class="token keyword"></span><a href="../../org.gradle.api/-task/index.html#217904858%2FMain%2F-**********">Task</a><span class="token operator">&gt;</span></div></div>                  </div>
                </div>
              </div>
            </div>
          </div>
<a data-name="-1215154575%2FFunctions%2F-**********" anchor-label="toArray" id="-1215154575%2FFunctions%2F-**********" data-filterable-set=":docs/kotlin_dsl"></a>
          <div class="table-row" data-filterable-current=":docs/kotlin_dsl" data-filterable-set=":docs/kotlin_dsl">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="index.html#-1215154575%2FFunctions%2F-**********"><span>to</span><wbr></wbr><span><span>Array</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="-1215154575%2FFunctions%2F-**********"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
                    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/kotlin_dsl" data-filterable-set=":docs/kotlin_dsl" data-active="" data-toggle=":docs/kotlin_dsl">DSL</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/kotlin_dsl"><div class="symbol monospace"><span class="token keyword">open </span><span class="token keyword"></span><span class="token keyword">fun </span><span class="token operator">&lt;</span><span class="token keyword"></span><a href="index.html#-1215154575%2FFunctions%2F-**********">T</a><span class="token operator"> : </span><a href="https://kotlinlang.org/api/latest/jvm/stdlib/kotlin/-any/index.html">Any</a><span class="token operator">&gt; </span><a href="index.html#-1215154575%2FFunctions%2F-**********"><span class="token function"><strike>toArray</strike></span></a><span class="token punctuation">(</span><span class="parameters "><span class="parameter ">p0<span class="token operator">: </span><a href="https://docs.oracle.com/en/java/javase/11/docs/api/java.base/java/util/function/IntFunction.html">IntFunction</a><span class="token operator">&lt;</span><span class="token keyword"></span><a href="https://kotlinlang.org/api/latest/jvm/stdlib/kotlin/-array/index.html">Array</a><span class="token operator">&lt;</span><span class="token keyword"></span><a href="index.html#-1215154575%2FFunctions%2F-**********">T</a><span class="token operator">&gt;</span><span class="token operator">&gt;</span></span></span><span class="token punctuation">)</span><span class="token operator">: </span><a href="https://kotlinlang.org/api/latest/jvm/stdlib/kotlin/-array/index.html">Array</a><span class="token operator">&lt;</span><span class="token keyword"></span><a href="index.html#-1215154575%2FFunctions%2F-**********">T</a><span class="token operator">&gt;</span></div></div>                  </div>
                </div>
              </div>
            </div>
          </div>
<a data-name="1906578589%2FFunctions%2F-**********" anchor-label="whenObjectAdded" id="1906578589%2FFunctions%2F-**********" data-filterable-set=":docs/kotlin_dsl"></a>
          <div class="table-row" data-filterable-current=":docs/kotlin_dsl" data-filterable-set=":docs/kotlin_dsl">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="index.html#124992351%2FFunctions%2F-**********"><span>when</span><wbr></wbr><span>Object</span><wbr></wbr><span><span>Added</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="1906578589%2FFunctions%2F-**********"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
                    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/kotlin_dsl" data-filterable-set=":docs/kotlin_dsl" data-active="" data-toggle=":docs/kotlin_dsl">DSL</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/kotlin_dsl"><div class="symbol monospace"><span class="token keyword">open </span><span class="token keyword">override </span><span class="token keyword">fun </span><a href="index.html#124992351%2FFunctions%2F-**********"><span class="token function">whenObjectAdded</span></a><span class="token punctuation">(</span><span class="parameters "><span class="parameter ">action<span class="token operator">: </span><span data-unresolved-link="groovy.lang/Closure///PointingToDeclaration/">Closure</span><span class="token operator">&lt;</span><span class="token keyword"></span><a href="https://kotlinlang.org/api/latest/jvm/stdlib/kotlin/-any/index.html">Any</a><span class="token operator">&gt;</span></span></span><span class="token punctuation">)</span></div><div class="symbol monospace"><span class="token keyword">open </span><span class="token keyword">override </span><span class="token keyword">fun </span><a href="index.html#967107978%2FFunctions%2F-**********"><span class="token function">whenObjectAdded</span></a><span class="token punctuation">(</span><span class="parameters "><span class="parameter ">action<span class="token operator">: </span><a href="../../org.gradle.api/-action/index.html#-199246903%2FMain%2F-**********">Action</a><span class="token operator">&lt;</span><span class="token keyword">in </span><a href="../../org.gradle.api/-task/index.html#217904858%2FMain%2F-**********">Task</a><span class="token operator">&gt;</span></span></span><span class="token punctuation">)</span><span class="token operator">: </span><a href="../../org.gradle.api/-action/index.html#-199246903%2FMain%2F-**********">Action</a><span class="token operator">&lt;</span><span class="token keyword">in </span><a href="../../org.gradle.api/-task/index.html#217904858%2FMain%2F-**********">Task</a><span class="token operator">&gt;</span></div></div>                  </div>
                </div>
              </div>
            </div>
          </div>
<a data-name="-1096314915%2FFunctions%2F-**********" anchor-label="whenObjectRemoved" id="-1096314915%2FFunctions%2F-**********" data-filterable-set=":docs/kotlin_dsl"></a>
          <div class="table-row" data-filterable-current=":docs/kotlin_dsl" data-filterable-set=":docs/kotlin_dsl">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="index.html#1263312063%2FFunctions%2F-**********"><span>when</span><wbr></wbr><span>Object</span><wbr></wbr><span><span>Removed</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="-1096314915%2FFunctions%2F-**********"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
                    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/kotlin_dsl" data-filterable-set=":docs/kotlin_dsl" data-active="" data-toggle=":docs/kotlin_dsl">DSL</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/kotlin_dsl"><div class="symbol monospace"><span class="token keyword">open </span><span class="token keyword">override </span><span class="token keyword">fun </span><a href="index.html#1263312063%2FFunctions%2F-**********"><span class="token function">whenObjectRemoved</span></a><span class="token punctuation">(</span><span class="parameters "><span class="parameter ">action<span class="token operator">: </span><span data-unresolved-link="groovy.lang/Closure///PointingToDeclaration/">Closure</span><span class="token operator">&lt;</span><span class="token keyword"></span><a href="https://kotlinlang.org/api/latest/jvm/stdlib/kotlin/-any/index.html">Any</a><span class="token operator">&gt;</span></span></span><span class="token punctuation">)</span></div><div class="symbol monospace"><span class="token keyword">open </span><span class="token keyword">override </span><span class="token keyword">fun </span><a href="index.html#1987242026%2FFunctions%2F-**********"><span class="token function">whenObjectRemoved</span></a><span class="token punctuation">(</span><span class="parameters "><span class="parameter ">action<span class="token operator">: </span><a href="../../org.gradle.api/-action/index.html#-199246903%2FMain%2F-**********">Action</a><span class="token operator">&lt;</span><span class="token keyword">in </span><a href="../../org.gradle.api/-task/index.html#217904858%2FMain%2F-**********">Task</a><span class="token operator">&gt;</span></span></span><span class="token punctuation">)</span><span class="token operator">: </span><a href="../../org.gradle.api/-action/index.html#-199246903%2FMain%2F-**********">Action</a><span class="token operator">&lt;</span><span class="token keyword">in </span><a href="../../org.gradle.api/-task/index.html#217904858%2FMain%2F-**********">Task</a><span class="token operator">&gt;</span></div></div>                  </div>
                </div>
              </div>
            </div>
          </div>
<a data-name="651870493%2FFunctions%2F-**********" anchor-label="whenTaskAdded" id="651870493%2FFunctions%2F-**********" data-filterable-set=":docs/kotlin_dsl"></a>
          <div class="table-row" data-filterable-current=":docs/kotlin_dsl" data-filterable-set=":docs/kotlin_dsl">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="index.html#-778119015%2FFunctions%2F-**********"><span>when</span><wbr></wbr><span>Task</span><wbr></wbr><span><span>Added</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="651870493%2FFunctions%2F-**********"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
                    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/kotlin_dsl" data-filterable-set=":docs/kotlin_dsl" data-active="" data-toggle=":docs/kotlin_dsl">DSL</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/kotlin_dsl"><div class="symbol monospace"><span class="token keyword">open </span><span class="token keyword">override </span><span class="token keyword">fun </span><a href="index.html#-778119015%2FFunctions%2F-**********"><span class="token function">whenTaskAdded</span></a><span class="token punctuation">(</span><span class="parameters "><span class="parameter ">closure<span class="token operator">: </span><span data-unresolved-link="groovy.lang/Closure///PointingToDeclaration/">Closure</span><span class="token operator">&lt;</span><span class="token keyword"></span><a href="https://kotlinlang.org/api/latest/jvm/stdlib/kotlin/-any/index.html">Any</a><span class="token operator">&gt;</span></span></span><span class="token punctuation">)</span></div><div class="symbol monospace"><span class="token keyword">open </span><span class="token keyword">override </span><span class="token keyword">fun </span><a href="index.html#-1488674800%2FFunctions%2F-**********"><span class="token function">whenTaskAdded</span></a><span class="token punctuation">(</span><span class="parameters "><span class="parameter ">action<span class="token operator">: </span><a href="../../org.gradle.api/-action/index.html#-199246903%2FMain%2F-**********">Action</a><span class="token operator">&lt;</span><span class="token keyword">in </span><a href="../../org.gradle.api/-task/index.html#217904858%2FMain%2F-**********">Task</a><span class="token operator">&gt;</span></span></span><span class="token punctuation">)</span><span class="token operator">: </span><a href="../../org.gradle.api/-action/index.html#-199246903%2FMain%2F-**********">Action</a><span class="token operator">&lt;</span><span class="token keyword">in </span><a href="../../org.gradle.api/-task/index.html#217904858%2FMain%2F-**********">Task</a><span class="token operator">&gt;</span></div></div>                  </div>
                </div>
              </div>
            </div>
          </div>
<a data-name="1540463359%2FFunctions%2F-**********" anchor-label="withType" id="1540463359%2FFunctions%2F-**********" data-filterable-set=":docs/kotlin_dsl"></a>
          <div class="table-row" data-filterable-current=":docs/kotlin_dsl" data-filterable-set=":docs/kotlin_dsl">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="index.html#995019380%2FFunctions%2F-**********"><span>with</span><wbr></wbr><span><span>Type</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="1540463359%2FFunctions%2F-**********"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
                    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/kotlin_dsl" data-filterable-set=":docs/kotlin_dsl" data-active="" data-toggle=":docs/kotlin_dsl">DSL</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/kotlin_dsl"><div class="symbol monospace"><span class="token keyword">open </span><span class="token keyword">override </span><span class="token keyword">fun </span><span class="token operator">&lt;</span><span class="token keyword"></span><a href="index.html#995019380%2FFunctions%2F-**********">S</a><span class="token operator"> : </span><a href="../../org.gradle.api/-task/index.html#217904858%2FMain%2F-**********">Task</a><span class="token operator">&gt; </span><a href="index.html#995019380%2FFunctions%2F-**********"><span class="token function">withType</span></a><span class="token punctuation">(</span><span class="parameters "><span class="parameter ">type<span class="token operator">: </span><a href="https://docs.oracle.com/en/java/javase/11/docs/api/java.base/java/lang/Class.html">Class</a><span class="token operator">&lt;</span><span class="token keyword"></span><a href="index.html#995019380%2FFunctions%2F-**********">S</a><span class="token operator">&gt;</span></span></span><span class="token punctuation">)</span><span class="token operator">: </span><a href="../../org.gradle.api.tasks/-task-collection/index.html#-**********%2FMain%2F-**********">TaskCollection</a><span class="token operator">&lt;</span><span class="token keyword"></span><a href="index.html#995019380%2FFunctions%2F-**********">S</a><span class="token operator">&gt;</span></div><div class="symbol monospace"><span class="token keyword">open </span><span class="token keyword">override </span><span class="token keyword">fun </span><span class="token operator">&lt;</span><span class="token keyword"></span><a href="index.html#-364648583%2FFunctions%2F-**********">S</a><span class="token operator"> : </span><a href="../../org.gradle.api/-task/index.html#217904858%2FMain%2F-**********">Task</a><span class="token operator">&gt; </span><a href="index.html#-364648583%2FFunctions%2F-**********"><span class="token function">withType</span></a><span class="token punctuation">(</span><span class="parameters "><span class="parameter ">type<span class="token operator">: </span><a href="https://docs.oracle.com/en/java/javase/11/docs/api/java.base/java/lang/Class.html">Class</a><span class="token operator">&lt;</span><span class="token keyword"></span><a href="index.html#-364648583%2FFunctions%2F-**********">S</a><span class="token operator">&gt;</span><span class="token punctuation">, </span></span><span class="parameter ">configureClosure<span class="token operator">: </span><span data-unresolved-link="groovy.lang/Closure///PointingToDeclaration/">Closure</span><span class="token operator">&lt;</span><span class="token keyword"></span><a href="https://kotlinlang.org/api/latest/jvm/stdlib/kotlin/-any/index.html">Any</a><span class="token operator">&gt;</span></span></span><span class="token punctuation">)</span><span class="token operator">: </span><a href="../../org.gradle.api/-domain-object-collection/index.html#1990905918%2FMain%2F-**********">DomainObjectCollection</a><span class="token operator">&lt;</span><span class="token keyword"></span><a href="index.html#-364648583%2FFunctions%2F-**********">S</a><span class="token operator">&gt;</span></div><div class="symbol monospace"><span class="token keyword">open </span><span class="token keyword">override </span><span class="token keyword">fun </span><span class="token operator">&lt;</span><span class="token keyword"></span><a href="index.html#-140356684%2FFunctions%2F-**********">S</a><span class="token operator"> : </span><a href="../../org.gradle.api/-task/index.html#217904858%2FMain%2F-**********">Task</a><span class="token operator">&gt; </span><a href="index.html#-140356684%2FFunctions%2F-**********"><span class="token function">withType</span></a><span class="token punctuation">(</span><span class="parameters "><span class="parameter ">type<span class="token operator">: </span><a href="https://docs.oracle.com/en/java/javase/11/docs/api/java.base/java/lang/Class.html">Class</a><span class="token operator">&lt;</span><span class="token keyword"></span><a href="index.html#-140356684%2FFunctions%2F-**********">S</a><span class="token operator">&gt;</span><span class="token punctuation">, </span></span><span class="parameter ">configureAction<span class="token operator">: </span><a href="../../org.gradle.api/-action/index.html#-199246903%2FMain%2F-**********">Action</a><span class="token operator">&lt;</span><span class="token keyword">in </span><a href="index.html#-140356684%2FFunctions%2F-**********">S</a><span class="token operator">&gt;</span></span></span><span class="token punctuation">)</span><span class="token operator">: </span><a href="../../org.gradle.api/-domain-object-collection/index.html#1990905918%2FMain%2F-**********">DomainObjectCollection</a><span class="token operator">&lt;</span><span class="token keyword"></span><a href="index.html#-140356684%2FFunctions%2F-**********">S</a><span class="token operator">&gt;</span></div></div>                  </div>
                </div>
              </div>
            </div>
          </div>
<a data-name="-153190767%2FFunctions%2F-**********" anchor-label="withType" id="-153190767%2FFunctions%2F-**********" data-filterable-set=":docs/kotlin_dsl"></a>
          <div class="table-row" data-togglable="EXTENSION_FUNCTION" data-filterable-current=":docs/kotlin_dsl" data-filterable-set=":docs/kotlin_dsl">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="../with-type.html"><span>with</span><wbr></wbr><span><span>Type</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="-153190767%2FFunctions%2F-**********"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
                    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/kotlin_dsl" data-filterable-set=":docs/kotlin_dsl" data-active="" data-toggle=":docs/kotlin_dsl">DSL</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/kotlin_dsl"><div class="symbol monospace"><span class="token keyword">inline </span><span class="token keyword">fun </span><span class="token operator">&lt;</span><span class="token keyword"></span><a href="../with-type.html">S</a><span class="token operator"> : </span><a href="https://kotlinlang.org/api/latest/jvm/stdlib/kotlin/-any/index.html">Any</a><span class="token operator">&gt; </span><a href="../../org.gradle.api/-domain-object-collection/index.html#1990905918%2FMain%2F-**********">DomainObjectCollection</a><span class="token operator">&lt;</span><span class="token keyword">in </span><a href="../with-type.html">S</a><span class="token operator">&gt;</span><span class="token punctuation">.</span><a href="../with-type.html"><span class="token function">withType</span></a><span class="token punctuation">(</span><span class="token punctuation">)</span><span class="token operator">: </span><a href="../../org.gradle.api/-domain-object-collection/index.html#1990905918%2FMain%2F-**********">DomainObjectCollection</a><span class="token operator">&lt;</span><span class="token keyword"></span><a href="../with-type.html">S</a><span class="token operator">&gt;</span></div><div class="symbol monospace"><span class="token keyword">inline </span><span class="token keyword">fun </span><span class="token operator">&lt;</span><span class="token keyword"></span><a href="../with-type.html">S</a><span class="token operator"> : </span><a href="https://kotlinlang.org/api/latest/jvm/stdlib/kotlin/-any/index.html">Any</a><span class="token operator">&gt; </span><a href="../../org.gradle.api/-named-domain-object-collection/index.html#-87091607%2FMain%2F-**********">NamedDomainObjectCollection</a><span class="token operator">&lt;</span><span class="token keyword">in </span><a href="../with-type.html">S</a><span class="token operator">&gt;</span><span class="token punctuation">.</span><a href="../with-type.html"><span class="token function">withType</span></a><span class="token punctuation">(</span><span class="token punctuation">)</span><span class="token operator">: </span><a href="../../org.gradle.api/-named-domain-object-collection/index.html#-87091607%2FMain%2F-**********">NamedDomainObjectCollection</a><span class="token operator">&lt;</span><span class="token keyword"></span><a href="../with-type.html">S</a><span class="token operator">&gt;</span></div><div class="symbol monospace"><span class="token keyword">inline </span><span class="token keyword">fun </span><span class="token operator">&lt;</span><span class="token keyword"></span><a href="../with-type.html">S</a><span class="token operator"> : </span><a href="../../org.gradle.api/-task/index.html#217904858%2FMain%2F-**********">Task</a><span class="token operator">&gt; </span><a href="../../org.gradle.api.tasks/-task-collection/index.html#-**********%2FMain%2F-**********">TaskCollection</a><span class="token operator">&lt;</span><span class="token keyword">in </span><a href="../with-type.html">S</a><span class="token operator">&gt;</span><span class="token punctuation">.</span><a href="../with-type.html"><span class="token function">withType</span></a><span class="token punctuation">(</span><span class="token punctuation">)</span><span class="token operator">: </span><a href="../../org.gradle.api.tasks/-task-collection/index.html#-**********%2FMain%2F-**********">TaskCollection</a><span class="token operator">&lt;</span><span class="token keyword"></span><a href="../with-type.html">S</a><span class="token operator">&gt;</span></div><div class="brief "><p class="paragraph">Returns a collection containing the objects in this collection of the given type. The returned collection is live, so that when matching objects are later added to this collection, they are also visible in the filtered collection.</p></div><div class="symbol monospace"><span class="token keyword">inline </span><span class="token keyword">fun </span><span class="token operator">&lt;</span><span class="token keyword"></span><a href="../with-type.html">S</a><span class="token operator"> : </span><a href="https://kotlinlang.org/api/latest/jvm/stdlib/kotlin/-any/index.html">Any</a><span class="token operator">&gt; </span><a href="../../org.gradle.api/-domain-object-collection/index.html#1990905918%2FMain%2F-**********">DomainObjectCollection</a><span class="token operator">&lt;</span><span class="token keyword">in </span><a href="../with-type.html">S</a><span class="token operator">&gt;</span><span class="token punctuation">.</span><a href="../with-type.html"><span class="token function">withType</span></a><span class="token punctuation">(</span><span class="parameters "><span class="parameter "><span class="token keyword">noinline </span>configuration<span class="token operator">: </span><span class="token keyword"></span><a href="../with-type.html">S</a><span class="token punctuation">.</span><span class="token punctuation">(</span><span class="token punctuation">)</span><span class="token operator"> -&gt; </span><span class="token keyword"></span><a href="https://kotlinlang.org/api/latest/jvm/stdlib/kotlin/-unit/index.html">Unit</a></span></span><span class="token punctuation">)</span><span class="token operator">: </span><a href="../../org.gradle.api/-domain-object-collection/index.html#1990905918%2FMain%2F-**********">DomainObjectCollection</a><span class="token operator">&lt;</span><span class="token keyword"></span><a href="../with-type.html">S</a><span class="token operator">&gt;</span></div><div class="brief "><p class="paragraph">Returns a collection containing the objects in this collection of the given type. Equivalent to calling <code class="lang-kotlin">withType(type).all(configureAction)</code>.</p></div><div class="symbol monospace"><span class="token keyword">inline </span><span class="token keyword">fun </span><span class="token operator">&lt;</span><span class="token keyword"></span><a href="../with-type.html">S</a><span class="token operator"> : </span><a href="../with-type.html">T</a><span class="token punctuation">, </span><span class="token keyword"></span><a href="../with-type.html">T</a><span class="token operator"> : </span><a href="https://kotlinlang.org/api/latest/jvm/stdlib/kotlin/-any/index.html">Any</a><span class="token operator">&gt; </span><a href="../../org.gradle.api/-domain-object-collection/index.html#1990905918%2FMain%2F-**********">DomainObjectCollection</a><span class="token operator">&lt;</span><span class="token keyword"></span><a href="../with-type.html">T</a><span class="token operator">&gt;</span><span class="token punctuation">.</span><a href="../with-type.html"><span class="token function">withType</span></a><span class="token punctuation">(</span><span class="parameters "><span class="parameter ">type<span class="token operator">: </span><a href="https://kotlinlang.org/api/latest/jvm/stdlib/kotlin.reflect/-k-class/index.html">KClass</a><span class="token operator">&lt;</span><span class="token keyword"></span><a href="../with-type.html">S</a><span class="token operator">&gt;</span></span></span><span class="token punctuation">)</span><span class="token operator">: </span><a href="../../org.gradle.api/-domain-object-collection/index.html#1990905918%2FMain%2F-**********">DomainObjectCollection</a><span class="token operator">&lt;</span><span class="token keyword"></span><a href="../with-type.html">S</a><span class="token operator">&gt;</span></div><div class="symbol monospace"><span class="token keyword">inline </span><span class="token keyword">fun </span><span class="token operator">&lt;</span><span class="token keyword"></span><a href="../with-type.html">S</a><span class="token operator"> : </span><a href="../with-type.html">T</a><span class="token punctuation">, </span><span class="token keyword"></span><a href="../with-type.html">T</a><span class="token operator"> : </span><a href="https://kotlinlang.org/api/latest/jvm/stdlib/kotlin/-any/index.html">Any</a><span class="token operator">&gt; </span><a href="../../org.gradle.api/-domain-object-collection/index.html#1990905918%2FMain%2F-**********">DomainObjectCollection</a><span class="token operator">&lt;</span><span class="token keyword"></span><a href="../with-type.html">T</a><span class="token operator">&gt;</span><span class="token punctuation">.</span><a href="../with-type.html"><span class="token function">withType</span></a><span class="token punctuation">(</span><span class="parameters "><span class="parameter ">type<span class="token operator">: </span><a href="https://kotlinlang.org/api/latest/jvm/stdlib/kotlin.reflect/-k-class/index.html">KClass</a><span class="token operator">&lt;</span><span class="token keyword"></span><a href="../with-type.html">S</a><span class="token operator">&gt;</span><span class="token punctuation">, </span></span><span class="parameter ">configureAction<span class="token operator">: </span><a href="../../org.gradle.api/-action/index.html#-199246903%2FMain%2F-**********">Action</a><span class="token operator">&lt;</span><span class="token keyword">in </span><a href="../with-type.html">S</a><span class="token operator">&gt;</span></span></span><span class="token punctuation">)</span><span class="token operator">: </span><a href="../../org.gradle.api/-domain-object-collection/index.html#1990905918%2FMain%2F-**********">DomainObjectCollection</a><span class="token operator">&lt;</span><span class="token keyword"></span><a href="../with-type.html">S</a><span class="token operator">&gt;</span></div><div class="brief "><p class="paragraph">Kotlin extension function taking <a href="https://kotlinlang.org/api/latest/jvm/stdlib/kotlin.reflect/-k-class/index.html">kotlin.reflect.KClass</a> for <span data-unresolved-link="org.gradle.api/DomainObjectCollection/withType/#java.lang.Class[TypeParam(bounds=[TypeParam(bounds=[kotlin.Any])])]/PointingToDeclaration/">org.gradle.api.DomainObjectCollection.withType</span>.</p></div><div class="symbol monospace"><span class="token keyword">inline </span><span class="token keyword">fun </span><span class="token operator">&lt;</span><span class="token keyword"></span><a href="../with-type.html">S</a><span class="token operator"> : </span><a href="../with-type.html">T</a><span class="token punctuation">, </span><span class="token keyword"></span><a href="../with-type.html">T</a><span class="token operator"> : </span><a href="https://kotlinlang.org/api/latest/jvm/stdlib/kotlin/-any/index.html">Any</a><span class="token operator">&gt; </span><a href="../../org.gradle.api/-domain-object-set/index.html#-1050381436%2FMain%2F-**********">DomainObjectSet</a><span class="token operator">&lt;</span><span class="token keyword"></span><a href="../with-type.html">T</a><span class="token operator">&gt;</span><span class="token punctuation">.</span><a href="../with-type.html"><span class="token function">withType</span></a><span class="token punctuation">(</span><span class="parameters "><span class="parameter ">type<span class="token operator">: </span><a href="https://kotlinlang.org/api/latest/jvm/stdlib/kotlin.reflect/-k-class/index.html">KClass</a><span class="token operator">&lt;</span><span class="token keyword"></span><a href="../with-type.html">S</a><span class="token operator">&gt;</span></span></span><span class="token punctuation">)</span><span class="token operator">: </span><a href="../../org.gradle.api/-domain-object-set/index.html#-1050381436%2FMain%2F-**********">DomainObjectSet</a><span class="token operator">&lt;</span><span class="token keyword"></span><a href="../with-type.html">S</a><span class="token operator">&gt;</span></div><div class="brief "><p class="paragraph">Kotlin extension function taking <a href="https://kotlinlang.org/api/latest/jvm/stdlib/kotlin.reflect/-k-class/index.html">kotlin.reflect.KClass</a> for <span data-unresolved-link="org.gradle.api/DomainObjectSet/withType/#java.lang.Class[TypeParam(bounds=[TypeParam(bounds=[kotlin.Any])])]/PointingToDeclaration/">org.gradle.api.DomainObjectSet.withType</span>.</p></div><div class="symbol monospace"><span class="token keyword">inline </span><span class="token keyword">fun </span><span class="token operator">&lt;</span><span class="token keyword"></span><a href="../with-type.html">S</a><span class="token operator"> : </span><a href="../with-type.html">T</a><span class="token punctuation">, </span><span class="token keyword"></span><a href="../with-type.html">T</a><span class="token operator"> : </span><a href="https://kotlinlang.org/api/latest/jvm/stdlib/kotlin/-any/index.html">Any</a><span class="token operator">&gt; </span><a href="../../org.gradle.api/-named-domain-object-collection/index.html#-87091607%2FMain%2F-**********">NamedDomainObjectCollection</a><span class="token operator">&lt;</span><span class="token keyword"></span><a href="../with-type.html">T</a><span class="token operator">&gt;</span><span class="token punctuation">.</span><a href="../with-type.html"><span class="token function">withType</span></a><span class="token punctuation">(</span><span class="parameters "><span class="parameter ">type<span class="token operator">: </span><a href="https://kotlinlang.org/api/latest/jvm/stdlib/kotlin.reflect/-k-class/index.html">KClass</a><span class="token operator">&lt;</span><span class="token keyword"></span><a href="../with-type.html">S</a><span class="token operator">&gt;</span></span></span><span class="token punctuation">)</span><span class="token operator">: </span><a href="../../org.gradle.api/-named-domain-object-collection/index.html#-87091607%2FMain%2F-**********">NamedDomainObjectCollection</a><span class="token operator">&lt;</span><span class="token keyword"></span><a href="../with-type.html">S</a><span class="token operator">&gt;</span></div><div class="brief "><p class="paragraph">Kotlin extension function taking <a href="https://kotlinlang.org/api/latest/jvm/stdlib/kotlin.reflect/-k-class/index.html">kotlin.reflect.KClass</a> for <span data-unresolved-link="org.gradle.api/NamedDomainObjectCollection/withType/#java.lang.Class[TypeParam(bounds=[TypeParam(bounds=[kotlin.Any])])]/PointingToDeclaration/">org.gradle.api.NamedDomainObjectCollection.withType</span>.</p></div><div class="symbol monospace"><span class="token keyword">inline </span><span class="token keyword">fun </span><span class="token operator">&lt;</span><span class="token keyword"></span><a href="../with-type.html">S</a><span class="token operator"> : </span><a href="../with-type.html">T</a><span class="token punctuation">, </span><span class="token keyword"></span><a href="../with-type.html">T</a><span class="token operator"> : </span><a href="https://kotlinlang.org/api/latest/jvm/stdlib/kotlin/-any/index.html">Any</a><span class="token operator">&gt; </span><a href="../../org.gradle.api/-named-domain-object-set/index.html#325187513%2FMain%2F-**********">NamedDomainObjectSet</a><span class="token operator">&lt;</span><span class="token keyword"></span><a href="../with-type.html">T</a><span class="token operator">&gt;</span><span class="token punctuation">.</span><a href="../with-type.html"><span class="token function">withType</span></a><span class="token punctuation">(</span><span class="parameters "><span class="parameter ">type<span class="token operator">: </span><a href="https://kotlinlang.org/api/latest/jvm/stdlib/kotlin.reflect/-k-class/index.html">KClass</a><span class="token operator">&lt;</span><span class="token keyword"></span><a href="../with-type.html">S</a><span class="token operator">&gt;</span></span></span><span class="token punctuation">)</span><span class="token operator">: </span><a href="../../org.gradle.api/-named-domain-object-set/index.html#325187513%2FMain%2F-**********">NamedDomainObjectSet</a><span class="token operator">&lt;</span><span class="token keyword"></span><a href="../with-type.html">S</a><span class="token operator">&gt;</span></div><div class="brief "><p class="paragraph">Kotlin extension function taking <a href="https://kotlinlang.org/api/latest/jvm/stdlib/kotlin.reflect/-k-class/index.html">kotlin.reflect.KClass</a> for <span data-unresolved-link="org.gradle.api/NamedDomainObjectSet/withType/#java.lang.Class[TypeParam(bounds=[TypeParam(bounds=[kotlin.Any])])]/PointingToDeclaration/">org.gradle.api.NamedDomainObjectSet.withType</span>.</p></div><div class="symbol monospace"><span class="token keyword">inline </span><span class="token keyword">fun </span><span class="token operator">&lt;</span><span class="token keyword"></span><a href="../with-type.html">S</a><span class="token operator"> : </span><a href="../with-type.html">T</a><span class="token punctuation">, </span><span class="token keyword"></span><a href="../with-type.html">T</a><span class="token operator"> : </span><a href="../../org.gradle.api/-task/index.html#217904858%2FMain%2F-**********">Task</a><span class="token operator">&gt; </span><a href="../../org.gradle.api.tasks/-task-collection/index.html#-**********%2FMain%2F-**********">TaskCollection</a><span class="token operator">&lt;</span><span class="token keyword"></span><a href="../with-type.html">T</a><span class="token operator">&gt;</span><span class="token punctuation">.</span><a href="../with-type.html"><span class="token function">withType</span></a><span class="token punctuation">(</span><span class="parameters "><span class="parameter ">type<span class="token operator">: </span><a href="https://kotlinlang.org/api/latest/jvm/stdlib/kotlin.reflect/-k-class/index.html">KClass</a><span class="token operator">&lt;</span><span class="token keyword"></span><a href="../with-type.html">S</a><span class="token operator">&gt;</span></span></span><span class="token punctuation">)</span><span class="token operator">: </span><a href="../../org.gradle.api.tasks/-task-collection/index.html#-**********%2FMain%2F-**********">TaskCollection</a><span class="token operator">&lt;</span><span class="token keyword"></span><a href="../with-type.html">S</a><span class="token operator">&gt;</span></div><div class="brief "><p class="paragraph">Kotlin extension function taking <a href="https://kotlinlang.org/api/latest/jvm/stdlib/kotlin.reflect/-k-class/index.html">kotlin.reflect.KClass</a> for <span data-unresolved-link="org.gradle.api.tasks/TaskCollection/withType/#java.lang.Class[TypeParam(bounds=[TypeParam(bounds=[org.gradle.api.Task])])]/PointingToDeclaration/">org.gradle.api.tasks.TaskCollection.withType</span>.</p></div></div>                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
      <div class="footer">
        <span class="go-to-top-icon"><a href="#content" id="go-to-top-link"></a></span><span>Gradle Kotlin DSL Reference</span><span
                class="pull-right"><span>Generated by </span><a
                href="https://github.com/Kotlin/dokka"><span>dokka</span><span class="padded-icon"></span></a></span>
      </div>
    </div>
</div>
</body>
</html>
