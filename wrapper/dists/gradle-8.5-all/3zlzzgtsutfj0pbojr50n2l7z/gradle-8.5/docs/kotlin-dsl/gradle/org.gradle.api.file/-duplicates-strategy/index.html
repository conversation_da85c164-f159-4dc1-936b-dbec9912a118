<!DOCTYPE html>
<html>
<head>
    <meta name="viewport" content="width=device-width, initial-scale=1" charset="UTF-8">
    <title>DuplicatesStrategy</title>
    <link href="../../../images/logo-icon.svg" rel="icon" type="image/svg">
    <script>var pathToRoot = "../../../";</script>
    <script>const storage = localStorage.getItem("dokka-dark-mode")
    if (storage == null) {
        const osDarkSchemePreferred = window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches
        if (osDarkSchemePreferred === true) {
            document.getElementsByTagName("html")[0].classList.add("theme-dark")
        }
    } else {
        const savedDarkMode = JSON.parse(storage)
        if(savedDarkMode === true) {
            document.getElementsByTagName("html")[0].classList.add("theme-dark")
        }
    }
    </script>
<script type="text/javascript" src="../../../scripts/sourceset_dependencies.js" async="async"></script>
<link href="../../../styles/style.css" rel="Stylesheet">
<link href="../../../styles/jetbrains-mono.css" rel="Stylesheet">
<link href="../../../styles/main.css" rel="Stylesheet">
<link href="../../../styles/prism.css" rel="Stylesheet">
<link href="../../../styles/logo-styles.css" rel="Stylesheet">
<script type="text/javascript" src="../../../scripts/clipboard.js" async="async"></script>
<script type="text/javascript" src="../../../scripts/navigation-loader.js" async="async"></script>
<script type="text/javascript" src="../../../scripts/platform-content-handler.js" async="async"></script>
<script type="text/javascript" src="../../../scripts/main.js" defer="defer"></script>
<script type="text/javascript" src="../../../scripts/prism.js" async="async"></script>
<script type="text/javascript" src="../../../scripts/symbol-parameters-wrapper_deferred.js" defer="defer"></script>
<link href="../../../images/gradle-logo.svg">
<link href="../../../styles/gradle.css" rel="Stylesheet">
</head>
<body>
<div class="navigation-wrapper" id="navigation-wrapper">
    <div id="leftToggler"><span class="icon-toggler"></span></div>
    <div class="library-name">
            <a href="../../../index.html">
                    <span>gradle</span>
            </a>
    </div>
    <div>
8.5    </div>
    <div class="pull-right d-flex">
        <div class="filter-section" id="filter-section">
                <button class="platform-tag platform-selector jvm-like" data-active="" data-filter=":docs/java_api">API</button>
        </div>
        <button id="theme-toggle-button"><span id="theme-toggle"></span></button>
        <div id="searchBar"></div>
    </div>
</div>
<div id="container">
    <div id="leftColumn">
        <div id="sideMenu"></div>
    </div>
    <div id="main">
<div class="main-content" data-page-type="classlike" id="content" pageIds="gradle::org.gradle.api.file/DuplicatesStrategy///PointingToDeclaration//-1793262594">
  <div class="breadcrumbs"><a href="../../../index.html">gradle</a><span class="delimiter">/</span><a href="../index.html">org.gradle.api.file</a><span class="delimiter">/</span><span class="current">DuplicatesStrategy</span></div>
  <div class="cover ">
    <h1 class="cover"><span>Duplicates</span><wbr></wbr><span><span>Strategy</span></span></h1>
    <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
      <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api" data-active="" data-toggle=":docs/java_api">API</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/java_api"><div class="symbol monospace"><span class="token keyword">enum </span><a href="index.html">DuplicatesStrategy</a><span class="clearfix"><span class="floating-right">(<a href="https://github.com/gradle/gradle/blob/28aca86a7180baa17117e0e5ba01d8ea9feca598/subprojects/core-api/src/main/java/org/gradle/api/file/DuplicatesStrategy.java#L22">source</a>)</span></span></div><p class="paragraph">Strategies for dealing with the potential creation of duplicate files or archive entries.</p></div>    </div>
  </div>
  <div class="tabbedcontent">
    <div class="tabs-section" tabs-section="tabs-section"><button class="section-tab" data-active="" data-togglable="CONSTRUCTOR,TYPE,PROPERTY,FUNCTION">Members</button><button class="section-tab" data-togglable="ENTRY">Entries</button></div>
    <div class="tabs-section-body">
      <div data-togglable="ENTRY">
        <h2 class="">Entries</h2>
        <div class="table"><a data-name="1379230507%2FClasslikes%2F-1793262594" anchor-label="INCLUDE" id="1379230507%2FClasslikes%2F-1793262594" data-filterable-set=":docs/java_api"></a>
          <div class="table-row" data-togglable="ENTRY" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="-i-n-c-l-u-d-e/index.html">INCLUDE</a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="1379230507%2FClasslikes%2F-1793262594"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
                    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api" data-active="" data-toggle=":docs/java_api">API</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/java_api"><div class="symbol monospace"><div class="block"><a href="-i-n-c-l-u-d-e/index.html">INCLUDE</a></div></div><div class="brief "><p class="paragraph">Do not attempt to prevent duplicates. </p></div></div>                  </div>
                </div>
              </div>
            </div>
          </div>
<a data-name="2096394461%2FClasslikes%2F-1793262594" anchor-label="EXCLUDE" id="2096394461%2FClasslikes%2F-1793262594" data-filterable-set=":docs/java_api"></a>
          <div class="table-row" data-togglable="ENTRY" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="-e-x-c-l-u-d-e/index.html">EXCLUDE</a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="2096394461%2FClasslikes%2F-1793262594"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
                    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api" data-active="" data-toggle=":docs/java_api">API</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/java_api"><div class="symbol monospace"><div class="block"><a href="-e-x-c-l-u-d-e/index.html">EXCLUDE</a></div></div><div class="brief "><p class="paragraph">Do not allow duplicates by ignoring subsequent items to be created at the same path. </p></div></div>                  </div>
                </div>
              </div>
            </div>
          </div>
<a data-name="-1883749141%2FClasslikes%2F-1793262594" anchor-label="WARN" id="-1883749141%2FClasslikes%2F-1793262594" data-filterable-set=":docs/java_api"></a>
          <div class="table-row" data-togglable="ENTRY" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="-w-a-r-n/index.html">WARN</a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="-1883749141%2FClasslikes%2F-1793262594"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
                    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api" data-active="" data-toggle=":docs/java_api">API</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/java_api"><div class="symbol monospace"><div class="block"><a href="-w-a-r-n/index.html">WARN</a></div></div><div class="brief "><p class="paragraph">Do not attempt to prevent duplicates, but log a warning message when multiple items are to be created at the same path. </p></div></div>                  </div>
                </div>
              </div>
            </div>
          </div>
<a data-name="1885818755%2FClasslikes%2F-1793262594" anchor-label="FAIL" id="1885818755%2FClasslikes%2F-1793262594" data-filterable-set=":docs/java_api"></a>
          <div class="table-row" data-togglable="ENTRY" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="-f-a-i-l/index.html">FAIL</a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="1885818755%2FClasslikes%2F-1793262594"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
                    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api" data-active="" data-toggle=":docs/java_api">API</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/java_api"><div class="symbol monospace"><div class="block"><a href="-f-a-i-l/index.html">FAIL</a></div></div><div class="brief "><p class="paragraph">Throw a <a href="../-duplicate-file-copying-exception/index.html">DuplicateFileCopyingException</a> when subsequent items are to be created at the same path. </p></div></div>                  </div>
                </div>
              </div>
            </div>
          </div>
<a data-name="-1349687234%2FClasslikes%2F-1793262594" anchor-label="INHERIT" id="-1349687234%2FClasslikes%2F-1793262594" data-filterable-set=":docs/java_api"></a>
          <div class="table-row" data-togglable="ENTRY" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="-i-n-h-e-r-i-t/index.html">INHERIT</a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="-1349687234%2FClasslikes%2F-1793262594"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
                    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api" data-active="" data-toggle=":docs/java_api">API</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/java_api"><div class="symbol monospace"><div class="block"><a href="-i-n-h-e-r-i-t/index.html">INHERIT</a></div></div><div class="brief "><p class="paragraph">The default strategy, which is to inherit the strategy from the parent copy spec, if any, or <a href="-i-n-c-l-u-d-e/index.html">INCLUDE</a> if the copy spec has no parent.</p></div></div>                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div data-togglable="FUNCTION">
        <h2 class="">Functions</h2>
        <div class="table"><a data-name="-2061511902%2FFunctions%2F-1793262594" anchor-label="valueOf" id="-2061511902%2FFunctions%2F-1793262594" data-filterable-set=":docs/java_api"></a>
          <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="value-of.html"><span>value</span><wbr></wbr><span><span>Of</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="-2061511902%2FFunctions%2F-1793262594"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
                    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api" data-active="" data-toggle=":docs/java_api">API</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/java_api"><div class="symbol monospace"><span class="token keyword">open </span><span class="token keyword"></span><span class="token keyword">fun </span><a href="value-of.html"><span class="token function">valueOf</span></a><span class="token punctuation">(</span><span class="parameters "><span class="parameter ">name<span class="token operator">: </span><a href="https://docs.oracle.com/en/java/javase/11/docs/api/java.base/java/lang/String.html">String</a></span></span><span class="token punctuation">)</span><span class="token operator">: </span><a href="index.html">DuplicatesStrategy</a></div><div class="brief "><p class="paragraph">Returns the enum constant of this type with the specified name. The string must match exactly an identifier used to declare an enum constant in this type. (Extraneous whitespace characters are not permitted.)</p></div></div>                  </div>
                </div>
              </div>
            </div>
          </div>
<a data-name="589618441%2FFunctions%2F-1793262594" anchor-label="values" id="589618441%2FFunctions%2F-1793262594" data-filterable-set=":docs/java_api"></a>
          <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="values.html"><span><span>values</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="589618441%2FFunctions%2F-1793262594"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
                    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api" data-active="" data-toggle=":docs/java_api">API</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/java_api"><div class="symbol monospace"><span class="token keyword">open </span><span class="token keyword"></span><span class="token keyword">fun </span><a href="values.html"><span class="token function">values</span></a><span class="token punctuation">(</span><span class="token punctuation">)</span><span class="token operator">: </span><a href="https://kotlinlang.org/api/latest/jvm/stdlib/kotlin/-array/index.html">Array</a><span class="token operator">&lt;</span><a href="index.html">DuplicatesStrategy</a><span class="token operator">&gt;</span></div><div class="brief "><p class="paragraph">Returns an array containing the constants of this enum type, in the order they're declared. This method may be used to iterate over the constants.</p></div></div>                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
      <div class="footer">
        <span class="go-to-top-icon"><a href="#content" id="go-to-top-link"></a></span><span>Gradle Kotlin DSL Reference</span><span
                class="pull-right"><span>Generated by </span><a
                href="https://github.com/Kotlin/dokka"><span>dokka</span><span class="padded-icon"></span></a></span>
      </div>
    </div>
</div>
</body>
</html>
