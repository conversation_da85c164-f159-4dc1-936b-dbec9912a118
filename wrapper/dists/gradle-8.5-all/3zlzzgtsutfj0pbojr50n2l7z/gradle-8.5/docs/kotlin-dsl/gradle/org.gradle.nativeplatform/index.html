<!DOCTYPE html>
<html>
<head>
    <meta name="viewport" content="width=device-width, initial-scale=1" charset="UTF-8">
    <title>org.gradle.nativeplatform</title>
    <link href="../../images/logo-icon.svg" rel="icon" type="image/svg">
    <script>var pathToRoot = "../../";</script>
    <script>const storage = localStorage.getItem("dokka-dark-mode")
    if (storage == null) {
        const osDarkSchemePreferred = window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches
        if (osDarkSchemePreferred === true) {
            document.getElementsByTagName("html")[0].classList.add("theme-dark")
        }
    } else {
        const savedDarkMode = JSON.parse(storage)
        if(savedDarkMode === true) {
            document.getElementsByTagName("html")[0].classList.add("theme-dark")
        }
    }
    </script>
<script type="text/javascript" src="../../scripts/sourceset_dependencies.js" async="async"></script>
<link href="../../styles/style.css" rel="Stylesheet">
<link href="../../styles/jetbrains-mono.css" rel="Stylesheet">
<link href="../../styles/main.css" rel="Stylesheet">
<link href="../../styles/prism.css" rel="Stylesheet">
<link href="../../styles/logo-styles.css" rel="Stylesheet">
<script type="text/javascript" src="../../scripts/clipboard.js" async="async"></script>
<script type="text/javascript" src="../../scripts/navigation-loader.js" async="async"></script>
<script type="text/javascript" src="../../scripts/platform-content-handler.js" async="async"></script>
<script type="text/javascript" src="../../scripts/main.js" defer="defer"></script>
<script type="text/javascript" src="../../scripts/prism.js" async="async"></script>
<script type="text/javascript" src="../../scripts/symbol-parameters-wrapper_deferred.js" defer="defer"></script>
<link href="../../images/gradle-logo.svg">
<link href="../../styles/gradle.css" rel="Stylesheet">
</head>
<body>
<div class="navigation-wrapper" id="navigation-wrapper">
    <div id="leftToggler"><span class="icon-toggler"></span></div>
    <div class="library-name">
            <a href="../../index.html">
                    <span>gradle</span>
            </a>
    </div>
    <div>
8.5    </div>
    <div class="pull-right d-flex">
        <div class="filter-section" id="filter-section">
                <button class="platform-tag platform-selector jvm-like" data-active="" data-filter=":docs/java_api">API</button>
        </div>
        <button id="theme-toggle-button"><span id="theme-toggle"></span></button>
        <div id="searchBar"></div>
    </div>
</div>
<div id="container">
    <div id="leftColumn">
        <div id="sideMenu"></div>
    </div>
    <div id="main">
<div class="main-content" data-page-type="package" id="content" pageIds="gradle::org.gradle.nativeplatform////PointingToDeclaration//-1793262594">
  <div class="breadcrumbs"><a href="../../index.html">gradle</a><span class="delimiter">/</span><span class="current">org.gradle.nativeplatform</span></div>
  <div class="cover ">
    <h1 class="cover"><span><span>Package-level</span></span> <span><span>declarations</span></span></h1>
    <div class="platform-hinted UnderCoverText with-platform-tabs" data-platform-hinted="data-platform-hinted">
      <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api" data-active="" data-toggle=":docs/java_api">API</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/java_api"><p class="paragraph">Classes that model aspects of native component projects.</p></div>    </div>
  </div>
  <div class="tabbedcontent">
    <div class="tabs-section" tabs-section="tabs-section"><button class="section-tab" data-active="" data-togglable="TYPE">Types</button></div>
    <div class="tabs-section-body">
      <div data-togglable="TYPE">
        <h2 class="">Types</h2>
        <div class="table"><a data-name="1012663569%2FClasslikes%2F-1793262594" anchor-label="BuildType" id="1012663569%2FClasslikes%2F-1793262594" data-filterable-set=":docs/java_api"></a>
          <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="-build-type/index.html"><span>Build</span><wbr></wbr><span><span>Type</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="1012663569%2FClasslikes%2F-1793262594"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
                    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api" data-active="" data-toggle=":docs/java_api">API</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/java_api"><div class="symbol monospace"><div class="block"><div class="block"><span class="token annotation builtin">@</span><a href="../org.gradle.api/-incubating/index.html"><span class="token annotation builtin">Incubating</span></a></div></div><span class="token keyword">interface </span><a href="-build-type/index.html">BuildType</a> : <a href="../org.gradle.api/-named/index.html">Named</a></div><div class="brief ">Specifies a build-type for a native binary.</div></div>                  </div>
                </div>
              </div>
            </div>
          </div>
<a data-name="-1399458288%2FClasslikes%2F-1793262594" anchor-label="BuildTypeContainer" id="-1399458288%2FClasslikes%2F-1793262594" data-filterable-set=":docs/java_api"></a>
          <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="-build-type-container/index.html"><span>Build</span><wbr></wbr><span>Type</span><wbr></wbr><span><span>Container</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="-1399458288%2FClasslikes%2F-1793262594"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
                    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api" data-active="" data-toggle=":docs/java_api">API</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/java_api"><div class="symbol monospace"><div class="block"><div class="block"><span class="token annotation builtin">@</span><a href="../org.gradle.api/-incubating/index.html"><span class="token annotation builtin">Incubating</span></a></div></div><span class="token keyword">interface </span><a href="-build-type-container/index.html">BuildTypeContainer</a> : <a href="../org.gradle.api/-named-domain-object-container/index.html">NamedDomainObjectContainer</a><span class="token operator">&lt;</span><a href="../org.gradle.api/-named-domain-object-container/index.html">T</a><span class="token operator">&gt; </span></div><div class="brief ">A container of <a href="-build-type/index.html">BuildType</a>s.</div></div>                  </div>
                </div>
              </div>
            </div>
          </div>
<a data-name="-1876072021%2FClasslikes%2F-1793262594" anchor-label="Flavor" id="-1876072021%2FClasslikes%2F-1793262594" data-filterable-set=":docs/java_api"></a>
          <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="-flavor/index.html"><span><span>Flavor</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="-1876072021%2FClasslikes%2F-1793262594"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
                    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api" data-active="" data-toggle=":docs/java_api">API</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/java_api"><div class="symbol monospace"><div class="block"><div class="block"><span class="token annotation builtin">@</span><a href="../org.gradle.api/-incubating/index.html"><span class="token annotation builtin">Incubating</span></a></div></div><span class="token keyword">interface </span><a href="-flavor/index.html">Flavor</a> : <a href="../org.gradle.api/-named/index.html">Named</a></div><div class="brief ">Defines a custom variant that differentiate a <a href="-native-binary/index.html">NativeBinary</a>.</div></div>                  </div>
                </div>
              </div>
            </div>
          </div>
<a data-name="-1203560778%2FClasslikes%2F-1793262594" anchor-label="FlavorContainer" id="-1203560778%2FClasslikes%2F-1793262594" data-filterable-set=":docs/java_api"></a>
          <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="-flavor-container/index.html"><span>Flavor</span><wbr></wbr><span><span>Container</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="-1203560778%2FClasslikes%2F-1793262594"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
                    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api" data-active="" data-toggle=":docs/java_api">API</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/java_api"><div class="symbol monospace"><div class="block"><div class="block"><span class="token annotation builtin">@</span><a href="../org.gradle.api/-incubating/index.html"><span class="token annotation builtin">Incubating</span></a></div></div><span class="token keyword">interface </span><a href="-flavor-container/index.html">FlavorContainer</a> : <a href="../org.gradle.api/-named-domain-object-container/index.html">NamedDomainObjectContainer</a><span class="token operator">&lt;</span><a href="../org.gradle.api/-named-domain-object-container/index.html">T</a><span class="token operator">&gt; </span></div><div class="brief ">A container of <a href="-flavor/index.html">Flavor</a>s.</div></div>                  </div>
                </div>
              </div>
            </div>
          </div>
<a data-name="-375756364%2FClasslikes%2F-1793262594" anchor-label="Linkage" id="-375756364%2FClasslikes%2F-1793262594" data-filterable-set=":docs/java_api"></a>
          <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="-linkage/index.html"><span><span>Linkage</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="-375756364%2FClasslikes%2F-1793262594"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
                    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api" data-active="" data-toggle=":docs/java_api">API</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/java_api"><div class="symbol monospace"><span class="token keyword">enum </span><a href="-linkage/index.html">Linkage</a> : <a href="../org.gradle.api/-named/index.html">Named</a></div><div class="brief ">Specify how a native library should be linked into another binary.</div></div>                  </div>
                </div>
              </div>
            </div>
          </div>
<a data-name="-1127814433%2FClasslikes%2F-1793262594" anchor-label="MachineArchitecture" id="-1127814433%2FClasslikes%2F-1793262594" data-filterable-set=":docs/java_api"></a>
          <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="-machine-architecture/index.html"><span>Machine</span><wbr></wbr><span><span>Architecture</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="-1127814433%2FClasslikes%2F-1793262594"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
                    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api" data-active="" data-toggle=":docs/java_api">API</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/java_api"><div class="symbol monospace"><span class="token keyword">abstract </span><span class="token keyword">class </span><a href="-machine-architecture/index.html">MachineArchitecture</a> : <a href="../org.gradle.api/-named/index.html">Named</a></div><div class="brief ">Represents a target architecture of a component.</div></div>                  </div>
                </div>
              </div>
            </div>
          </div>
<a data-name="-1699839855%2FClasslikes%2F-1793262594" anchor-label="NativeBinary" id="-1699839855%2FClasslikes%2F-1793262594" data-filterable-set=":docs/java_api"></a>
          <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="-native-binary/index.html"><span>Native</span><wbr></wbr><span><span>Binary</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="-1699839855%2FClasslikes%2F-1793262594"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
                    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api" data-active="" data-toggle=":docs/java_api">API</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/java_api"><div class="symbol monospace"><div class="block"><div class="block"><span class="token annotation builtin">@</span><a href="../org.gradle.api/-incubating/index.html"><span class="token annotation builtin">Incubating</span></a></div></div><span class="token keyword">interface </span><a href="-native-binary/index.html">NativeBinary</a> : <a href="../org.gradle.platform.base/-binary/index.html">Binary</a></div><div class="brief ">Represents a particular binary artifact.</div></div>                  </div>
                </div>
              </div>
            </div>
          </div>
<a data-name="36921622%2FClasslikes%2F-1793262594" anchor-label="NativeBinarySpec" id="36921622%2FClasslikes%2F-1793262594" data-filterable-set=":docs/java_api"></a>
          <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="-native-binary-spec/index.html"><span>Native</span><wbr></wbr><span>Binary</span><wbr></wbr><span><span>Spec</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="36921622%2FClasslikes%2F-1793262594"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
                    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api" data-active="" data-toggle=":docs/java_api">API</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/java_api"><div class="symbol monospace"><div class="block"><div class="block"><span class="token annotation builtin">@</span><a href="../org.gradle.api/-incubating/index.html"><span class="token annotation builtin">Incubating</span></a></div></div><span class="token keyword">interface </span><a href="-native-binary-spec/index.html">NativeBinarySpec</a> : <a href="../org.gradle.platform.base/-binary-spec/index.html">BinarySpec</a></div><div class="brief ">Represents a binary artifact that is the result of building a native component.</div></div>                  </div>
                </div>
              </div>
            </div>
          </div>
<a data-name="-1704406416%2FClasslikes%2F-1793262594" anchor-label="NativeComponentExtension" id="-1704406416%2FClasslikes%2F-1793262594" data-filterable-set=":docs/java_api"></a>
          <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="-native-component-extension/index.html"><span>Native</span><wbr></wbr><span>Component</span><wbr></wbr><span><span>Extension</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="-1704406416%2FClasslikes%2F-1793262594"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
                    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api" data-active="" data-toggle=":docs/java_api">API</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/java_api"><div class="symbol monospace"><div class="block"><div class="block"><span class="token annotation builtin">@</span><a href="../org.gradle.api/-incubating/index.html"><span class="token annotation builtin">Incubating</span></a></div></div><span class="token keyword">interface </span><a href="-native-component-extension/index.html">NativeComponentExtension</a></div><div class="brief ">The configuration for native components generated by this build.</div></div>                  </div>
                </div>
              </div>
            </div>
          </div>
<a data-name="1848838264%2FClasslikes%2F-1793262594" anchor-label="NativeComponentSpec" id="1848838264%2FClasslikes%2F-1793262594" data-filterable-set=":docs/java_api"></a>
          <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="-native-component-spec/index.html"><span>Native</span><wbr></wbr><span>Component</span><wbr></wbr><span><span>Spec</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="1848838264%2FClasslikes%2F-1793262594"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
                    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api" data-active="" data-toggle=":docs/java_api">API</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/java_api"><div class="symbol monospace"><div class="block"><div class="block"><span class="token annotation builtin">@</span><a href="../org.gradle.api/-incubating/index.html"><span class="token annotation builtin">Incubating</span></a></div></div><span class="token keyword">interface </span><a href="-native-component-spec/index.html">NativeComponentSpec</a> : <a href="../org.gradle.platform.base/-component-spec/index.html">ComponentSpec</a></div><div class="brief ">Definition of a software component that is to be built by Gradle to run a on JVM platform.</div></div>                  </div>
                </div>
              </div>
            </div>
          </div>
<a data-name="-1023613287%2FClasslikes%2F-1793262594" anchor-label="NativeDependencySet" id="-1023613287%2FClasslikes%2F-1793262594" data-filterable-set=":docs/java_api"></a>
          <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="-native-dependency-set/index.html"><span>Native</span><wbr></wbr><span>Dependency</span><wbr></wbr><span><span>Set</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="-1023613287%2FClasslikes%2F-1793262594"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
                    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api" data-active="" data-toggle=":docs/java_api">API</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/java_api"><div class="symbol monospace"><div class="block"><div class="block"><span class="token annotation builtin">@</span><a href="../org.gradle.api/-incubating/index.html"><span class="token annotation builtin">Incubating</span></a></div></div><span class="token keyword">interface </span><a href="-native-dependency-set/index.html">NativeDependencySet</a></div><div class="brief ">Models a collection of native type dependencies.</div></div>                  </div>
                </div>
              </div>
            </div>
          </div>
<a data-name="1452946408%2FClasslikes%2F-1793262594" anchor-label="NativeExecutable" id="1452946408%2FClasslikes%2F-1793262594" data-filterable-set=":docs/java_api"></a>
          <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="-native-executable/index.html"><span>Native</span><wbr></wbr><span><span>Executable</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="1452946408%2FClasslikes%2F-1793262594"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
                    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api" data-active="" data-toggle=":docs/java_api">API</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/java_api"><div class="symbol monospace"><div class="block"><div class="block"><span class="token annotation builtin">@</span><a href="../org.gradle.api/-incubating/index.html"><span class="token annotation builtin">Incubating</span></a></div></div><span class="token keyword">interface </span><a href="-native-executable/index.html">NativeExecutable</a> : <a href="../org.gradle.platform.base/-application/index.html">Application</a></div><div class="brief ">An executable native component that is built by Gradle.</div></div>                  </div>
                </div>
              </div>
            </div>
          </div>
<a data-name="2034535847%2FClasslikes%2F-1793262594" anchor-label="NativeExecutableBinary" id="2034535847%2FClasslikes%2F-1793262594" data-filterable-set=":docs/java_api"></a>
          <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="-native-executable-binary/index.html"><span>Native</span><wbr></wbr><span>Executable</span><wbr></wbr><span><span>Binary</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="2034535847%2FClasslikes%2F-1793262594"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
                    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api" data-active="" data-toggle=":docs/java_api">API</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/java_api"><div class="symbol monospace"><div class="block"><div class="block"><span class="token annotation builtin">@</span><a href="../org.gradle.api/-incubating/index.html"><span class="token annotation builtin">Incubating</span></a></div></div><span class="token keyword">interface </span><a href="-native-executable-binary/index.html">NativeExecutableBinary</a> : <a href="-native-binary/index.html">NativeBinary</a></div><div class="brief ">A binary artifact for a <a href="-native-executable/index.html">NativeExecutable</a>, targeted at a particular platform with specific configuration.</div></div>                  </div>
                </div>
              </div>
            </div>
          </div>
<a data-name="1580266284%2FClasslikes%2F-1793262594" anchor-label="NativeExecutableBinarySpec" id="1580266284%2FClasslikes%2F-1793262594" data-filterable-set=":docs/java_api"></a>
          <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="-native-executable-binary-spec/index.html"><span>Native</span><wbr></wbr><span>Executable</span><wbr></wbr><span>Binary</span><wbr></wbr><span><span>Spec</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="1580266284%2FClasslikes%2F-1793262594"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
                    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api" data-active="" data-toggle=":docs/java_api">API</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/java_api"><div class="symbol monospace"><div class="block"><div class="block"><span class="token annotation builtin">@</span><a href="../org.gradle.api/-incubating/index.html"><span class="token annotation builtin">Incubating</span></a></div></div><span class="token keyword">interface </span><a href="-native-executable-binary-spec/index.html">NativeExecutableBinarySpec</a> : <a href="-native-binary-spec/index.html">NativeBinarySpec</a>, <a href="../org.gradle.platform.base/-application-binary-spec/index.html">ApplicationBinarySpec</a></div><div class="brief ">An binary built by Gradle for a native application.</div></div>                  </div>
                </div>
              </div>
            </div>
          </div>
<a data-name="-1210750639%2FClasslikes%2F-1793262594" anchor-label="NativeExecutableFileSpec" id="-1210750639%2FClasslikes%2F-1793262594" data-filterable-set=":docs/java_api"></a>
          <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="-native-executable-file-spec/index.html"><span>Native</span><wbr></wbr><span>Executable</span><wbr></wbr><span>File</span><wbr></wbr><span><span>Spec</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="-1210750639%2FClasslikes%2F-1793262594"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
                    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api" data-active="" data-toggle=":docs/java_api">API</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/java_api"><div class="symbol monospace"><div class="block"><div class="block"><span class="token annotation builtin">@</span><a href="../org.gradle.api/-incubating/index.html"><span class="token annotation builtin">Incubating</span></a></div></div><span class="token keyword">open </span><span class="token keyword">class </span><a href="-native-executable-file-spec/index.html">NativeExecutableFileSpec</a></div><div class="brief ">Specifies how to build and where to place a native executable file.</div></div>                  </div>
                </div>
              </div>
            </div>
          </div>
<a data-name="-1344827155%2FClasslikes%2F-1793262594" anchor-label="NativeExecutableSpec" id="-1344827155%2FClasslikes%2F-1793262594" data-filterable-set=":docs/java_api"></a>
          <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="-native-executable-spec/index.html"><span>Native</span><wbr></wbr><span>Executable</span><wbr></wbr><span><span>Spec</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="-1344827155%2FClasslikes%2F-1793262594"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
                    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api" data-active="" data-toggle=":docs/java_api">API</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/java_api"><div class="symbol monospace"><div class="block"><div class="block"><span class="token annotation builtin">@</span><a href="../org.gradle.api/-incubating/index.html"><span class="token annotation builtin">Incubating</span></a></div></div><span class="token keyword">interface </span><a href="-native-executable-spec/index.html">NativeExecutableSpec</a> : <a href="../org.gradle.platform.base/-application-spec/index.html">ApplicationSpec</a>, <a href="-native-component-spec/index.html">NativeComponentSpec</a>, <a href="-targeted-native-component/index.html">TargetedNativeComponent</a></div><div class="brief ">Definition of a native executable component that is to be built by Gradle.</div></div>                  </div>
                </div>
              </div>
            </div>
          </div>
<a data-name="722383037%2FClasslikes%2F-1793262594" anchor-label="NativeInstallationSpec" id="722383037%2FClasslikes%2F-1793262594" data-filterable-set=":docs/java_api"></a>
          <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="-native-installation-spec/index.html"><span>Native</span><wbr></wbr><span>Installation</span><wbr></wbr><span><span>Spec</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="722383037%2FClasslikes%2F-1793262594"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
                    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api" data-active="" data-toggle=":docs/java_api">API</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/java_api"><div class="symbol monospace"><div class="block"><div class="block"><span class="token annotation builtin">@</span><a href="../org.gradle.api/-incubating/index.html"><span class="token annotation builtin">Incubating</span></a></div></div><span class="token keyword">open </span><span class="token keyword">class </span><a href="-native-installation-spec/index.html">NativeInstallationSpec</a></div><div class="brief ">Specifies the installation location for a native executable.</div></div>                  </div>
                </div>
              </div>
            </div>
          </div>
<a data-name="979594933%2FClasslikes%2F-1793262594" anchor-label="NativeLibrary" id="979594933%2FClasslikes%2F-1793262594" data-filterable-set=":docs/java_api"></a>
          <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="-native-library/index.html"><span>Native</span><wbr></wbr><span><span>Library</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="979594933%2FClasslikes%2F-1793262594"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
                    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api" data-active="" data-toggle=":docs/java_api">API</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/java_api"><div class="symbol monospace"><div class="block"><div class="block"><span class="token annotation builtin">@</span><a href="../org.gradle.api/-incubating/index.html"><span class="token annotation builtin">Incubating</span></a></div></div><span class="token keyword">interface </span><a href="-native-library/index.html">NativeLibrary</a> : <a href="../org.gradle.platform.base/-library/index.html">Library</a></div><div class="brief ">A library component that is built by a gradle project.</div></div>                  </div>
                </div>
              </div>
            </div>
          </div>
<a data-name="672643764%2FClasslikes%2F-1793262594" anchor-label="NativeLibraryBinary" id="672643764%2FClasslikes%2F-1793262594" data-filterable-set=":docs/java_api"></a>
          <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="-native-library-binary/index.html"><span>Native</span><wbr></wbr><span>Library</span><wbr></wbr><span><span>Binary</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="672643764%2FClasslikes%2F-1793262594"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
                    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api" data-active="" data-toggle=":docs/java_api">API</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/java_api"><div class="symbol monospace"><div class="block"><div class="block"><span class="token annotation builtin">@</span><a href="../org.gradle.api/-incubating/index.html"><span class="token annotation builtin">Incubating</span></a></div></div><span class="token keyword">interface </span><a href="-native-library-binary/index.html">NativeLibraryBinary</a> : <a href="-native-binary/index.html">NativeBinary</a></div><div class="brief ">A physical representation of a <a href="-native-library/index.html">NativeLibrary</a> component.</div></div>                  </div>
                </div>
              </div>
            </div>
          </div>
<a data-name="-430124615%2FClasslikes%2F-1793262594" anchor-label="NativeLibraryBinarySpec" id="-430124615%2FClasslikes%2F-1793262594" data-filterable-set=":docs/java_api"></a>
          <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="-native-library-binary-spec/index.html"><span>Native</span><wbr></wbr><span>Library</span><wbr></wbr><span>Binary</span><wbr></wbr><span><span>Spec</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="-430124615%2FClasslikes%2F-1793262594"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
                    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api" data-active="" data-toggle=":docs/java_api">API</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/java_api"><div class="symbol monospace"><div class="block"><div class="block"><span class="token annotation builtin">@</span><a href="../org.gradle.api/-incubating/index.html"><span class="token annotation builtin">Incubating</span></a></div></div><span class="token keyword">interface </span><a href="-native-library-binary-spec/index.html">NativeLibraryBinarySpec</a> : <a href="-native-binary-spec/index.html">NativeBinarySpec</a>, <a href="../org.gradle.platform.base/-library-binary-spec/index.html">LibraryBinarySpec</a></div><div class="brief ">Represents a binary artifact that is the result of building a native library component.</div></div>                  </div>
                </div>
              </div>
            </div>
          </div>
<a data-name="-1045701526%2FClasslikes%2F-1793262594" anchor-label="NativeLibraryRequirement" id="-1045701526%2FClasslikes%2F-1793262594" data-filterable-set=":docs/java_api"></a>
          <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="-native-library-requirement/index.html"><span>Native</span><wbr></wbr><span>Library</span><wbr></wbr><span><span>Requirement</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="-1045701526%2FClasslikes%2F-1793262594"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
                    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api" data-active="" data-toggle=":docs/java_api">API</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/java_api"><div class="symbol monospace"><div class="block"><div class="block"><span class="token annotation builtin">@</span><a href="../org.gradle.api/-incubating/index.html"><span class="token annotation builtin">Incubating</span></a></div></div><span class="token keyword">interface </span><a href="-native-library-requirement/index.html">NativeLibraryRequirement</a></div><div class="brief ">A dependency on a native library within the build.</div></div>                  </div>
                </div>
              </div>
            </div>
          </div>
<a data-name="-1011049158%2FClasslikes%2F-1793262594" anchor-label="NativeLibrarySpec" id="-1011049158%2FClasslikes%2F-1793262594" data-filterable-set=":docs/java_api"></a>
          <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="-native-library-spec/index.html"><span>Native</span><wbr></wbr><span>Library</span><wbr></wbr><span><span>Spec</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="-1011049158%2FClasslikes%2F-1793262594"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
                    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api" data-active="" data-toggle=":docs/java_api">API</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/java_api"><div class="symbol monospace"><div class="block"><div class="block"><span class="token annotation builtin">@</span><a href="../org.gradle.api/-incubating/index.html"><span class="token annotation builtin">Incubating</span></a></div></div><span class="token keyword">interface </span><a href="-native-library-spec/index.html">NativeLibrarySpec</a> : <a href="../org.gradle.platform.base/-library-spec/index.html">LibrarySpec</a>, <a href="-native-component-spec/index.html">NativeComponentSpec</a>, <a href="-targeted-native-component/index.html">TargetedNativeComponent</a></div><div class="brief ">Definition of a native library component that is to be built by Gradle.</div></div>                  </div>
                </div>
              </div>
            </div>
          </div>
<a data-name="1708843406%2FClasslikes%2F-1793262594" anchor-label="ObjectFile" id="1708843406%2FClasslikes%2F-1793262594" data-filterable-set=":docs/java_api"></a>
          <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="-object-file/index.html"><span>Object</span><wbr></wbr><span><span>File</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="1708843406%2FClasslikes%2F-1793262594"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
                    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api" data-active="" data-toggle=":docs/java_api">API</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/java_api"><div class="symbol monospace"><div class="block"><div class="block"><span class="token annotation builtin">@</span><a href="../org.gradle.api/-incubating/index.html"><span class="token annotation builtin">Incubating</span></a></div></div><span class="token keyword">open </span><span class="token keyword">class </span><a href="-object-file/index.html">ObjectFile</a> : <a href="../org.gradle.platform.base/-transformation-file-type/index.html">TransformationFileType</a></div><div class="brief ">LanguageOutputType marking object file output type.</div></div>                  </div>
                </div>
              </div>
            </div>
          </div>
<a data-name="264378117%2FClasslikes%2F-1793262594" anchor-label="OperatingSystemFamily" id="264378117%2FClasslikes%2F-1793262594" data-filterable-set=":docs/java_api"></a>
          <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="-operating-system-family/index.html"><span>Operating</span><wbr></wbr><span>System</span><wbr></wbr><span><span>Family</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="264378117%2FClasslikes%2F-1793262594"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
                    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api" data-active="" data-toggle=":docs/java_api">API</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/java_api"><div class="symbol monospace"><span class="token keyword">abstract </span><span class="token keyword">class </span><a href="-operating-system-family/index.html">OperatingSystemFamily</a> : <a href="../org.gradle.api/-named/index.html">Named</a></div><div class="brief ">Represents the operating system of a configuration.</div></div>                  </div>
                </div>
              </div>
            </div>
          </div>
<a data-name="-1021290117%2FClasslikes%2F-1793262594" anchor-label="PrebuiltLibraries" id="-1021290117%2FClasslikes%2F-1793262594" data-filterable-set=":docs/java_api"></a>
          <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="-prebuilt-libraries/index.html"><span>Prebuilt</span><wbr></wbr><span><span>Libraries</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="-1021290117%2FClasslikes%2F-1793262594"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
                    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api" data-active="" data-toggle=":docs/java_api">API</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/java_api"><div class="symbol monospace"><div class="block"><div class="block"><span class="token annotation builtin">@</span><a href="../org.gradle.api/-incubating/index.html"><span class="token annotation builtin">Incubating</span></a></div></div><span class="token keyword">interface </span><a href="-prebuilt-libraries/index.html">PrebuiltLibraries</a> : <a href="../org.gradle.api.artifacts.repositories/-artifact-repository/index.html">ArtifactRepository</a>, <a href="../org.gradle.api/-named-domain-object-set/index.html">NamedDomainObjectSet</a><span class="token operator">&lt;</span><a href="../org.gradle.api/-named-domain-object-set/index.html">T</a><span class="token operator">&gt; </span></div><div class="brief ">A container of <a href="-prebuilt-library/index.html">PrebuiltLibrary</a> instances.</div></div>                  </div>
                </div>
              </div>
            </div>
          </div>
<a data-name="1615873625%2FClasslikes%2F-1793262594" anchor-label="PrebuiltLibrary" id="1615873625%2FClasslikes%2F-1793262594" data-filterable-set=":docs/java_api"></a>
          <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="-prebuilt-library/index.html"><span>Prebuilt</span><wbr></wbr><span><span>Library</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="1615873625%2FClasslikes%2F-1793262594"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
                    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api" data-active="" data-toggle=":docs/java_api">API</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/java_api"><div class="symbol monospace"><div class="block"><div class="block"><span class="token annotation builtin">@</span><a href="../org.gradle.api/-incubating/index.html"><span class="token annotation builtin">Incubating</span></a></div></div><span class="token keyword">interface </span><a href="-prebuilt-library/index.html">PrebuiltLibrary</a> : <a href="../org.gradle.api/-named/index.html">Named</a>, <a href="-native-library/index.html">NativeLibrary</a></div><div class="brief ">A library component that is not built by gradle.</div></div>                  </div>
                </div>
              </div>
            </div>
          </div>
<a data-name="-1262621219%2FClasslikes%2F-1793262594" anchor-label="PrebuiltSharedLibraryBinary" id="-1262621219%2FClasslikes%2F-1793262594" data-filterable-set=":docs/java_api"></a>
          <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="-prebuilt-shared-library-binary/index.html"><span>Prebuilt</span><wbr></wbr><span>Shared</span><wbr></wbr><span>Library</span><wbr></wbr><span><span>Binary</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="-1262621219%2FClasslikes%2F-1793262594"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
                    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api" data-active="" data-toggle=":docs/java_api">API</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/java_api"><div class="symbol monospace"><div class="block"><div class="block"><span class="token annotation builtin">@</span><a href="../org.gradle.api/-incubating/index.html"><span class="token annotation builtin">Incubating</span></a></div></div><span class="token keyword">interface </span><a href="-prebuilt-shared-library-binary/index.html">PrebuiltSharedLibraryBinary</a> : <a href="-shared-library-binary/index.html">SharedLibraryBinary</a></div><div class="brief ">A shared library that exists at a known location on the filesystem.</div></div>                  </div>
                </div>
              </div>
            </div>
          </div>
<a data-name="545669414%2FClasslikes%2F-1793262594" anchor-label="PrebuiltStaticLibraryBinary" id="545669414%2FClasslikes%2F-1793262594" data-filterable-set=":docs/java_api"></a>
          <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="-prebuilt-static-library-binary/index.html"><span>Prebuilt</span><wbr></wbr><span>Static</span><wbr></wbr><span>Library</span><wbr></wbr><span><span>Binary</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="545669414%2FClasslikes%2F-1793262594"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
                    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api" data-active="" data-toggle=":docs/java_api">API</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/java_api"><div class="symbol monospace"><div class="block"><div class="block"><span class="token annotation builtin">@</span><a href="../org.gradle.api/-incubating/index.html"><span class="token annotation builtin">Incubating</span></a></div></div><span class="token keyword">interface </span><a href="-prebuilt-static-library-binary/index.html">PrebuiltStaticLibraryBinary</a> : <a href="-static-library-binary/index.html">StaticLibraryBinary</a></div><div class="brief ">A static library that exists at a known location on the filesystem.</div></div>                  </div>
                </div>
              </div>
            </div>
          </div>
<a data-name="-1745196565%2FClasslikes%2F-1793262594" anchor-label="PreprocessingTool" id="-1745196565%2FClasslikes%2F-1793262594" data-filterable-set=":docs/java_api"></a>
          <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="-preprocessing-tool/index.html"><span>Preprocessing</span><wbr></wbr><span><span>Tool</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="-1745196565%2FClasslikes%2F-1793262594"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
                    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api" data-active="" data-toggle=":docs/java_api">API</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/java_api"><div class="symbol monospace"><div class="block"><div class="block"><span class="token annotation builtin">@</span><a href="../org.gradle.api/-incubating/index.html"><span class="token annotation builtin">Incubating</span></a></div></div><span class="token keyword">interface </span><a href="-preprocessing-tool/index.html">PreprocessingTool</a> : <a href="-tool/index.html">Tool</a></div><div class="brief ">A tool that permits configuration of the C preprocessor.</div></div>                  </div>
                </div>
              </div>
            </div>
          </div>
<a data-name="-2047526879%2FClasslikes%2F-1793262594" anchor-label="Repositories" id="-2047526879%2FClasslikes%2F-1793262594" data-filterable-set=":docs/java_api"></a>
          <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="-repositories/index.html"><span><span>Repositories</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="-2047526879%2FClasslikes%2F-1793262594"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
                    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api" data-active="" data-toggle=":docs/java_api">API</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/java_api"><div class="symbol monospace"><div class="block"><div class="block"><span class="token annotation builtin">@</span><a href="../org.gradle.api/-incubating/index.html"><span class="token annotation builtin">Incubating</span></a></div></div><span class="token keyword">interface </span><a href="-repositories/index.html">Repositories</a> : <a href="../org.gradle.api/-polymorphic-domain-object-container/index.html">PolymorphicDomainObjectContainer</a><span class="token operator">&lt;</span><a href="../org.gradle.api/-polymorphic-domain-object-container/index.html">T</a><span class="token operator">&gt; </span></div><div class="brief ">The repositories that Gradle will search for prebuilt libraries.</div></div>                  </div>
                </div>
              </div>
            </div>
          </div>
<a data-name="245394562%2FClasslikes%2F-1793262594" anchor-label="SharedLibraryBinary" id="245394562%2FClasslikes%2F-1793262594" data-filterable-set=":docs/java_api"></a>
          <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="-shared-library-binary/index.html"><span>Shared</span><wbr></wbr><span>Library</span><wbr></wbr><span><span>Binary</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="245394562%2FClasslikes%2F-1793262594"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
                    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api" data-active="" data-toggle=":docs/java_api">API</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/java_api"><div class="symbol monospace"><div class="block"><div class="block"><span class="token annotation builtin">@</span><a href="../org.gradle.api/-incubating/index.html"><span class="token annotation builtin">Incubating</span></a></div></div><span class="token keyword">interface </span><a href="-shared-library-binary/index.html">SharedLibraryBinary</a> : <a href="-native-library-binary/index.html">NativeLibraryBinary</a></div><div class="brief ">A <a href="-native-library/index.html">NativeLibrary</a> that has been compiled and linked as a shared library.</div></div>                  </div>
                </div>
              </div>
            </div>
          </div>
<a data-name="310111367%2FClasslikes%2F-1793262594" anchor-label="SharedLibraryBinarySpec" id="310111367%2FClasslikes%2F-1793262594" data-filterable-set=":docs/java_api"></a>
          <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="-shared-library-binary-spec/index.html"><span>Shared</span><wbr></wbr><span>Library</span><wbr></wbr><span>Binary</span><wbr></wbr><span><span>Spec</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="310111367%2FClasslikes%2F-1793262594"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
                    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api" data-active="" data-toggle=":docs/java_api">API</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/java_api"><div class="symbol monospace"><div class="block"><div class="block"><span class="token annotation builtin">@</span><a href="../org.gradle.api/-incubating/index.html"><span class="token annotation builtin">Incubating</span></a></div></div><span class="token keyword">interface </span><a href="-shared-library-binary-spec/index.html">SharedLibraryBinarySpec</a> : <a href="-native-library-binary-spec/index.html">NativeLibraryBinarySpec</a></div><div class="brief ">A shared library binary built by Gradle for a native library.</div></div>                  </div>
                </div>
              </div>
            </div>
          </div>
<a data-name="2053685195%2FClasslikes%2F-1793262594" anchor-label="StaticLibraryBinary" id="2053685195%2FClasslikes%2F-1793262594" data-filterable-set=":docs/java_api"></a>
          <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="-static-library-binary/index.html"><span>Static</span><wbr></wbr><span>Library</span><wbr></wbr><span><span>Binary</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="2053685195%2FClasslikes%2F-1793262594"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
                    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api" data-active="" data-toggle=":docs/java_api">API</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/java_api"><div class="symbol monospace"><div class="block"><div class="block"><span class="token annotation builtin">@</span><a href="../org.gradle.api/-incubating/index.html"><span class="token annotation builtin">Incubating</span></a></div></div><span class="token keyword">interface </span><a href="-static-library-binary/index.html">StaticLibraryBinary</a> : <a href="-native-library-binary/index.html">NativeLibraryBinary</a></div><div class="brief ">A <a href="-native-library/index.html">NativeLibrary</a> that has been compiled and archived into a static library.</div></div>                  </div>
                </div>
              </div>
            </div>
          </div>
<a data-name="-270044336%2FClasslikes%2F-1793262594" anchor-label="StaticLibraryBinarySpec" id="-270044336%2FClasslikes%2F-1793262594" data-filterable-set=":docs/java_api"></a>
          <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="-static-library-binary-spec/index.html"><span>Static</span><wbr></wbr><span>Library</span><wbr></wbr><span>Binary</span><wbr></wbr><span><span>Spec</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="-270044336%2FClasslikes%2F-1793262594"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
                    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api" data-active="" data-toggle=":docs/java_api">API</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/java_api"><div class="symbol monospace"><div class="block"><div class="block"><span class="token annotation builtin">@</span><a href="../org.gradle.api/-incubating/index.html"><span class="token annotation builtin">Incubating</span></a></div></div><span class="token keyword">interface </span><a href="-static-library-binary-spec/index.html">StaticLibraryBinarySpec</a> : <a href="-native-library-binary-spec/index.html">NativeLibraryBinarySpec</a></div><div class="brief ">A static library binary built by Gradle for a native library.</div></div>                  </div>
                </div>
              </div>
            </div>
          </div>
<a data-name="-434793725%2FClasslikes%2F-1793262594" anchor-label="TargetedNativeComponent" id="-434793725%2FClasslikes%2F-1793262594" data-filterable-set=":docs/java_api"></a>
          <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="-targeted-native-component/index.html"><span>Targeted</span><wbr></wbr><span>Native</span><wbr></wbr><span><span>Component</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="-434793725%2FClasslikes%2F-1793262594"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
                    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api" data-active="" data-toggle=":docs/java_api">API</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/java_api"><div class="symbol monospace"><div class="block"><div class="block"><span class="token annotation builtin">@</span><a href="../org.gradle.api/-incubating/index.html"><span class="token annotation builtin">Incubating</span></a></div></div><span class="token keyword">interface </span><a href="-targeted-native-component/index.html">TargetedNativeComponent</a> : <a href="../org.gradle.platform.base/-platform-aware-component-spec/index.html">PlatformAwareComponentSpec</a>, <a href="-native-component-spec/index.html">NativeComponentSpec</a></div><div class="brief ">A native component that can be configured to target certain variant dimensions.</div></div>                  </div>
                </div>
              </div>
            </div>
          </div>
<a data-name="-580100573%2FClasslikes%2F-1793262594" anchor-label="TargetMachine" id="-580100573%2FClasslikes%2F-1793262594" data-filterable-set=":docs/java_api"></a>
          <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="-target-machine/index.html"><span>Target</span><wbr></wbr><span><span>Machine</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="-580100573%2FClasslikes%2F-1793262594"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
                    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api" data-active="" data-toggle=":docs/java_api">API</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/java_api"><div class="symbol monospace"><span class="token keyword">interface </span><a href="-target-machine/index.html">TargetMachine</a></div><div class="brief ">Represents a combination of operating system and cpu architecture that a variant might be built for.</div></div>                  </div>
                </div>
              </div>
            </div>
          </div>
<a data-name="738403044%2FClasslikes%2F-1793262594" anchor-label="TargetMachineBuilder" id="738403044%2FClasslikes%2F-1793262594" data-filterable-set=":docs/java_api"></a>
          <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="-target-machine-builder/index.html"><span>Target</span><wbr></wbr><span>Machine</span><wbr></wbr><span><span>Builder</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="738403044%2FClasslikes%2F-1793262594"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
                    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api" data-active="" data-toggle=":docs/java_api">API</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/java_api"><div class="symbol monospace"><span class="token keyword">interface </span><a href="-target-machine-builder/index.html">TargetMachineBuilder</a> : <a href="-target-machine/index.html">TargetMachine</a></div><div class="brief ">A builder for configuring the architecture of a <a href="-target-machine/index.html">TargetMachine</a> objects.</div></div>                  </div>
                </div>
              </div>
            </div>
          </div>
<a data-name="1074924629%2FClasslikes%2F-1793262594" anchor-label="TargetMachineFactory" id="1074924629%2FClasslikes%2F-1793262594" data-filterable-set=":docs/java_api"></a>
          <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="-target-machine-factory/index.html"><span>Target</span><wbr></wbr><span>Machine</span><wbr></wbr><span><span>Factory</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="1074924629%2FClasslikes%2F-1793262594"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
                    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api" data-active="" data-toggle=":docs/java_api">API</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/java_api"><div class="symbol monospace"><span class="token keyword">interface </span><a href="-target-machine-factory/index.html">TargetMachineFactory</a></div><div class="brief ">A factory for creating <a href="-target-machine/index.html">TargetMachine</a> objects.</div></div>                  </div>
                </div>
              </div>
            </div>
          </div>
<a data-name="1443905009%2FClasslikes%2F-1793262594" anchor-label="Tool" id="1443905009%2FClasslikes%2F-1793262594" data-filterable-set=":docs/java_api"></a>
          <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="-tool/index.html"><span><span>Tool</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="1443905009%2FClasslikes%2F-1793262594"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
                    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api" data-active="" data-toggle=":docs/java_api">API</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/java_api"><div class="symbol monospace"><div class="block"><div class="block"><span class="token annotation builtin">@</span><a href="../org.gradle.api/-incubating/index.html"><span class="token annotation builtin">Incubating</span></a></div></div><span class="token keyword">interface </span><a href="-tool/index.html">Tool</a></div><div class="brief ">Configuration of the arguments of a ToolChain executable.</div></div>                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
      <div class="footer">
        <span class="go-to-top-icon"><a href="#content" id="go-to-top-link"></a></span><span>Gradle Kotlin DSL Reference</span><span
                class="pull-right"><span>Generated by </span><a
                href="https://github.com/Kotlin/dokka"><span>dokka</span><span class="padded-icon"></span></a></span>
      </div>
    </div>
</div>
</body>
</html>
