<!DOCTYPE html>
<html>
<head>
    <meta name="viewport" content="width=device-width, initial-scale=1" charset="UTF-8">
    <title>ResolutionStrategy</title>
    <link href="../../../images/logo-icon.svg" rel="icon" type="image/svg">
    <script>var pathToRoot = "../../../";</script>
    <script>const storage = localStorage.getItem("dokka-dark-mode")
    if (storage == null) {
        const osDarkSchemePreferred = window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches
        if (osDarkSchemePreferred === true) {
            document.getElementsByTagName("html")[0].classList.add("theme-dark")
        }
    } else {
        const savedDarkMode = JSON.parse(storage)
        if(savedDarkMode === true) {
            document.getElementsByTagName("html")[0].classList.add("theme-dark")
        }
    }
    </script>
<script type="text/javascript" src="../../../scripts/sourceset_dependencies.js" async="async"></script>
<link href="../../../styles/style.css" rel="Stylesheet">
<link href="../../../styles/jetbrains-mono.css" rel="Stylesheet">
<link href="../../../styles/main.css" rel="Stylesheet">
<link href="../../../styles/prism.css" rel="Stylesheet">
<link href="../../../styles/logo-styles.css" rel="Stylesheet">
<script type="text/javascript" src="../../../scripts/clipboard.js" async="async"></script>
<script type="text/javascript" src="../../../scripts/navigation-loader.js" async="async"></script>
<script type="text/javascript" src="../../../scripts/platform-content-handler.js" async="async"></script>
<script type="text/javascript" src="../../../scripts/main.js" defer="defer"></script>
<script type="text/javascript" src="../../../scripts/prism.js" async="async"></script>
<script type="text/javascript" src="../../../scripts/symbol-parameters-wrapper_deferred.js" defer="defer"></script>
<link href="../../../images/gradle-logo.svg">
<link href="../../../styles/gradle.css" rel="Stylesheet">
</head>
<body>
<div class="navigation-wrapper" id="navigation-wrapper">
    <div id="leftToggler"><span class="icon-toggler"></span></div>
    <div class="library-name">
            <a href="../../../index.html">
                    <span>gradle</span>
            </a>
    </div>
    <div>
8.5    </div>
    <div class="pull-right d-flex">
        <div class="filter-section" id="filter-section">
                <button class="platform-tag platform-selector jvm-like" data-active="" data-filter=":docs/java_api">API</button>
        </div>
        <button id="theme-toggle-button"><span id="theme-toggle"></span></button>
        <div id="searchBar"></div>
    </div>
</div>
<div id="container">
    <div id="leftColumn">
        <div id="sideMenu"></div>
    </div>
    <div id="main">
<div class="main-content" data-page-type="classlike" id="content" pageIds="gradle::org.gradle.api.artifacts/ResolutionStrategy///PointingToDeclaration//-**********">
  <div class="breadcrumbs"><a href="../../../index.html">gradle</a><span class="delimiter">/</span><a href="../index.html">org.gradle.api.artifacts</a><span class="delimiter">/</span><span class="current">ResolutionStrategy</span></div>
  <div class="cover ">
    <h1 class="cover"><span>Resolution</span><wbr></wbr><span><span>Strategy</span></span></h1>
    <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
      <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api" data-active="" data-toggle=":docs/java_api">API</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/java_api"><div class="symbol monospace"><span class="token keyword">interface </span><a href="index.html">ResolutionStrategy</a><span class="clearfix"><span class="floating-right">(<a href="https://github.com/gradle/gradle/blob/28aca86a7180baa17117e0e5ba01d8ea9feca598/subprojects/core-api/src/main/java/org/gradle/api/artifacts/ResolutionStrategy.java#L66">source</a>)</span></span></div><p class="paragraph">Defines the strategies around dependency resolution. For example, forcing certain dependency versions, substitutions, conflict resolutions or snapshot timeouts. </p><p class="paragraph"> Examples: </p><div class="sample-container"><pre><code class="block lang-kotlin" theme="idea">plugins {
    id 'java' // so that there are some configurations
}

configurations.all {
  resolutionStrategy {
    // fail eagerly on version conflict (includes transitive dependencies)
    // e.g. multiple different versions of the same dependency (group and name are equal)
    failOnVersionConflict()

    // prefer modules that are part of this build (multi-project or composite build) over external modules
    preferProjectModules()

    // force certain versions of dependencies (including transitive)
    //  *append new forced modules:
    force 'asm:asm-all:3.3.1', 'commons-io:commons-io:1.4'
    //  *replace existing forced modules with new ones:
    forcedModules = ['asm:asm-all:3.3.1']

    // add dependency substitution rules
    dependencySubstitution {
      substitute module('org.gradle:api') using project(':api')
      substitute project(':util') using module('org.gradle:util:3.0')
    }

    // cache dynamic versions for 10 minutes
    cacheDynamicVersionsFor 10*60, 'seconds'
    // don't cache changing modules at all
    cacheChangingModulesFor 0, 'seconds'
  }
}
</code></pre><span class="top-right-position"><span class="copy-icon"></span><div class="copy-popup-wrapper popup-to-left"><span class="copy-popup-icon"></span><span>Content copied to clipboard</span></div></span></div></div>    </div>
  </div>
  <div class="tabbedcontent">
    <div class="tabs-section" tabs-section="tabs-section"><button class="section-tab" data-active="" data-togglable="CONSTRUCTOR,TYPE,PROPERTY,FUNCTION">Members</button></div>
    <div class="tabs-section-body">
      <div data-togglable="TYPE">
        <h2 class="">Types</h2>
        <div class="table"><a data-name="-1932414831%2FClasslikes%2F-**********" anchor-label="SortOrder" id="-1932414831%2FClasslikes%2F-**********" data-filterable-set=":docs/java_api"></a>
          <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="-sort-order/index.html"><span>Sort</span><wbr></wbr><span><span>Order</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="-1932414831%2FClasslikes%2F-**********"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
                    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api" data-active="" data-toggle=":docs/java_api">API</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/java_api"><div class="symbol monospace"><span class="token keyword">enum </span><a href="-sort-order/index.html">SortOrder</a></div><div class="brief ">Defines the sort order for components and artifacts produced by the configuration.</div></div>                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div data-togglable="FUNCTION">
        <h2 class="">Functions</h2>
        <div class="table"><a data-name="-2117072939%2FFunctions%2F-**********" anchor-label="activateDependencyLocking" id="-2117072939%2FFunctions%2F-**********" data-filterable-set=":docs/java_api"></a>
          <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="activate-dependency-locking.html"><span>activate</span><wbr></wbr><span>Dependency</span><wbr></wbr><span><span>Locking</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="-2117072939%2FFunctions%2F-**********"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
                    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api" data-active="" data-toggle=":docs/java_api">API</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/java_api"><div class="symbol monospace"><span class="token keyword">abstract </span><span class="token keyword"></span><span class="token keyword">fun </span><a href="activate-dependency-locking.html"><span class="token function">activateDependencyLocking</span></a><span class="token punctuation">(</span><span class="token punctuation">)</span><span class="token operator">: </span><a href="index.html">ResolutionStrategy</a></div><div class="brief ">Activates dependency locking support in Gradle.</div></div>                  </div>
                </div>
              </div>
            </div>
          </div>
<a data-name="618970777%2FFunctions%2F-**********" anchor-label="cacheChangingModulesFor" id="618970777%2FFunctions%2F-**********" data-filterable-set=":docs/java_api"></a>
          <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="cache-changing-modules-for.html"><span>cache</span><wbr></wbr><span>Changing</span><wbr></wbr><span>Modules</span><wbr></wbr><span><span>For</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="618970777%2FFunctions%2F-**********"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
                    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api" data-active="" data-toggle=":docs/java_api">API</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/java_api"><div class="symbol monospace"><span class="token keyword">abstract </span><span class="token keyword"></span><span class="token keyword">fun </span><a href="cache-changing-modules-for.html"><span class="token function">cacheChangingModulesFor</span></a><span class="token punctuation">(</span><span class="parameters "><span class="parameter ">value<span class="token operator">: </span><a href="https://kotlinlang.org/api/latest/jvm/stdlib/kotlin/-int/index.html">Int</a><span class="token punctuation">, </span></span><span class="parameter ">units<span class="token operator">: </span><a href="https://docs.oracle.com/en/java/javase/11/docs/api/java.base/java/lang/String.html">String</a></span></span><span class="token punctuation">)</span></div><div class="brief ">Sets the length of time that changing modules will be cached, with units expressed as a String.</div><div class="symbol monospace"><span class="token keyword">abstract </span><span class="token keyword"></span><span class="token keyword">fun </span><a href="cache-changing-modules-for.html"><span class="token function">cacheChangingModulesFor</span></a><span class="token punctuation">(</span><span class="parameters "><span class="parameter ">value<span class="token operator">: </span><a href="https://kotlinlang.org/api/latest/jvm/stdlib/kotlin/-int/index.html">Int</a><span class="token punctuation">, </span></span><span class="parameter ">units<span class="token operator">: </span><a href="https://docs.oracle.com/en/java/javase/11/docs/api/java.base/java/util/concurrent/TimeUnit.html">TimeUnit</a></span></span><span class="token punctuation">)</span></div><div class="brief ">Sets the length of time that changing modules will be cached.</div></div>                  </div>
                </div>
              </div>
            </div>
          </div>
<a data-name="361935705%2FFunctions%2F-**********" anchor-label="cacheDynamicVersionsFor" id="361935705%2FFunctions%2F-**********" data-filterable-set=":docs/java_api"></a>
          <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="cache-dynamic-versions-for.html"><span>cache</span><wbr></wbr><span>Dynamic</span><wbr></wbr><span>Versions</span><wbr></wbr><span><span>For</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="361935705%2FFunctions%2F-**********"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
                    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api" data-active="" data-toggle=":docs/java_api">API</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/java_api"><div class="symbol monospace"><span class="token keyword">abstract </span><span class="token keyword"></span><span class="token keyword">fun </span><a href="cache-dynamic-versions-for.html"><span class="token function">cacheDynamicVersionsFor</span></a><span class="token punctuation">(</span><span class="parameters "><span class="parameter ">value<span class="token operator">: </span><a href="https://kotlinlang.org/api/latest/jvm/stdlib/kotlin/-int/index.html">Int</a><span class="token punctuation">, </span></span><span class="parameter ">units<span class="token operator">: </span><a href="https://docs.oracle.com/en/java/javase/11/docs/api/java.base/java/lang/String.html">String</a></span></span><span class="token punctuation">)</span></div><div class="brief ">Sets the length of time that dynamic versions will be cached, with units expressed as a String.</div><div class="symbol monospace"><span class="token keyword">abstract </span><span class="token keyword"></span><span class="token keyword">fun </span><a href="cache-dynamic-versions-for.html"><span class="token function">cacheDynamicVersionsFor</span></a><span class="token punctuation">(</span><span class="parameters "><span class="parameter ">value<span class="token operator">: </span><a href="https://kotlinlang.org/api/latest/jvm/stdlib/kotlin/-int/index.html">Int</a><span class="token punctuation">, </span></span><span class="parameter ">units<span class="token operator">: </span><a href="https://docs.oracle.com/en/java/javase/11/docs/api/java.base/java/util/concurrent/TimeUnit.html">TimeUnit</a></span></span><span class="token punctuation">)</span></div><div class="brief ">Sets the length of time that dynamic versions will be cached.</div></div>                  </div>
                </div>
              </div>
            </div>
          </div>
<a data-name="-81921391%2FFunctions%2F-**********" anchor-label="capabilitiesResolution" id="-81921391%2FFunctions%2F-**********" data-filterable-set=":docs/java_api"></a>
          <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="capabilities-resolution.html"><span>capabilities</span><wbr></wbr><span><span>Resolution</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="-81921391%2FFunctions%2F-**********"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
                    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api" data-active="" data-toggle=":docs/java_api">API</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/java_api"><div class="symbol monospace"><span class="token keyword">abstract </span><span class="token keyword"></span><span class="token keyword">fun </span><a href="capabilities-resolution.html"><span class="token function">capabilitiesResolution</span></a><span class="token punctuation">(</span><span class="parameters "><span class="parameter ">action<span class="token operator">: </span><a href="../../org.gradle.api/-action/index.html">Action</a><span class="token operator">&lt;</span><span class="token keyword">out </span><a href="https://kotlinlang.org/api/latest/jvm/stdlib/kotlin/-any/index.html">Any</a><span class="token operator">&gt;</span></span></span><span class="token punctuation">)</span><span class="token operator">: </span><a href="index.html">ResolutionStrategy</a></div><div class="brief ">Configures the capabilities resolution strategy.</div></div>                  </div>
                </div>
              </div>
            </div>
          </div>
<a data-name="-1642431284%2FFunctions%2F-**********" anchor-label="componentSelection" id="-1642431284%2FFunctions%2F-**********" data-filterable-set=":docs/java_api"></a>
          <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="component-selection.html"><span>component</span><wbr></wbr><span><span>Selection</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="-1642431284%2FFunctions%2F-**********"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
                    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api" data-active="" data-toggle=":docs/java_api">API</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/java_api"><div class="symbol monospace"><span class="token keyword">abstract </span><span class="token keyword"></span><span class="token keyword">fun </span><a href="component-selection.html"><span class="token function">componentSelection</span></a><span class="token punctuation">(</span><span class="parameters "><span class="parameter ">action<span class="token operator">: </span><a href="../../org.gradle.api/-action/index.html">Action</a><span class="token operator">&lt;</span><span class="token keyword">out </span><a href="https://kotlinlang.org/api/latest/jvm/stdlib/kotlin/-any/index.html">Any</a><span class="token operator">&gt;</span></span></span><span class="token punctuation">)</span><span class="token operator">: </span><a href="index.html">ResolutionStrategy</a></div><div class="brief ">The componentSelection block provides rules to filter or prevent certain components from appearing in the resolution result.</div></div>                  </div>
                </div>
              </div>
            </div>
          </div>
<a data-name="-319634986%2FFunctions%2F-**********" anchor-label="deactivateDependencyLocking" id="-319634986%2FFunctions%2F-**********" data-filterable-set=":docs/java_api"></a>
          <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="deactivate-dependency-locking.html"><span>deactivate</span><wbr></wbr><span>Dependency</span><wbr></wbr><span><span>Locking</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="-319634986%2FFunctions%2F-**********"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
                    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api" data-active="" data-toggle=":docs/java_api">API</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/java_api"><div class="symbol monospace"><span class="token keyword">abstract </span><span class="token keyword"></span><span class="token keyword">fun </span><a href="deactivate-dependency-locking.html"><span class="token function">deactivateDependencyLocking</span></a><span class="token punctuation">(</span><span class="token punctuation">)</span><span class="token operator">: </span><a href="index.html">ResolutionStrategy</a></div><div class="brief ">Deactivates dependency locking support in Gradle.</div></div>                  </div>
                </div>
              </div>
            </div>
          </div>
<a data-name="-1097797240%2FFunctions%2F-**********" anchor-label="dependencySubstitution" id="-1097797240%2FFunctions%2F-**********" data-filterable-set=":docs/java_api"></a>
          <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="dependency-substitution.html"><span>dependency</span><wbr></wbr><span><span>Substitution</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="-1097797240%2FFunctions%2F-**********"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
                    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api" data-active="" data-toggle=":docs/java_api">API</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/java_api"><div class="symbol monospace"><span class="token keyword">abstract </span><span class="token keyword"></span><span class="token keyword">fun </span><a href="dependency-substitution.html"><span class="token function">dependencySubstitution</span></a><span class="token punctuation">(</span><span class="parameters "><span class="parameter ">action<span class="token operator">: </span><a href="../../org.gradle.api/-action/index.html">Action</a><span class="token operator">&lt;</span><span class="token keyword">out </span><a href="https://kotlinlang.org/api/latest/jvm/stdlib/kotlin/-any/index.html">Any</a><span class="token operator">&gt;</span></span></span><span class="token punctuation">)</span><span class="token operator">: </span><a href="index.html">ResolutionStrategy</a></div><div class="brief ">Configures the set of dependency substitution rules for this configuration.</div></div>                  </div>
                </div>
              </div>
            </div>
          </div>
<a data-name="-1999528128%2FFunctions%2F-**********" anchor-label="disableDependencyVerification" id="-1999528128%2FFunctions%2F-**********" data-filterable-set=":docs/java_api"></a>
          <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="disable-dependency-verification.html"><span>disable</span><wbr></wbr><span>Dependency</span><wbr></wbr><span><span>Verification</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="-1999528128%2FFunctions%2F-**********"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
                    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api" data-active="" data-toggle=":docs/java_api">API</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/java_api"><div class="symbol monospace"><span class="token keyword">abstract </span><span class="token keyword"></span><span class="token keyword">fun </span><a href="disable-dependency-verification.html"><span class="token function">disableDependencyVerification</span></a><span class="token punctuation">(</span><span class="token punctuation">)</span><span class="token operator">: </span><a href="index.html">ResolutionStrategy</a></div><div class="brief ">Deactivates dependency verification for this configuration.</div></div>                  </div>
                </div>
              </div>
            </div>
          </div>
<a data-name="-1424406650%2FFunctions%2F-**********" anchor-label="eachDependency" id="-1424406650%2FFunctions%2F-**********" data-filterable-set=":docs/java_api"></a>
          <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="each-dependency.html"><span>each</span><wbr></wbr><span><span>Dependency</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="-1424406650%2FFunctions%2F-**********"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
                    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api" data-active="" data-toggle=":docs/java_api">API</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/java_api"><div class="symbol monospace"><span class="token keyword">abstract </span><span class="token keyword"></span><span class="token keyword">fun </span><a href="each-dependency.html"><span class="token function">eachDependency</span></a><span class="token punctuation">(</span><span class="parameters "><span class="parameter ">rule<span class="token operator">: </span><a href="../../org.gradle.api/-action/index.html">Action</a><span class="token operator">&lt;</span><span class="token keyword">out </span><a href="https://kotlinlang.org/api/latest/jvm/stdlib/kotlin/-any/index.html">Any</a><span class="token operator">&gt;</span></span></span><span class="token punctuation">)</span><span class="token operator">: </span><a href="index.html">ResolutionStrategy</a></div><div class="brief ">Adds a dependency substitution rule that is triggered for every dependency (including transitive) when the configuration is being resolved.</div></div>                  </div>
                </div>
              </div>
            </div>
          </div>
<a data-name="998987139%2FFunctions%2F-**********" anchor-label="enableDependencyVerification" id="998987139%2FFunctions%2F-**********" data-filterable-set=":docs/java_api"></a>
          <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="enable-dependency-verification.html"><span>enable</span><wbr></wbr><span>Dependency</span><wbr></wbr><span><span>Verification</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="998987139%2FFunctions%2F-**********"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
                    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api" data-active="" data-toggle=":docs/java_api">API</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/java_api"><div class="symbol monospace"><span class="token keyword">abstract </span><span class="token keyword"></span><span class="token keyword">fun </span><a href="enable-dependency-verification.html"><span class="token function">enableDependencyVerification</span></a><span class="token punctuation">(</span><span class="token punctuation">)</span><span class="token operator">: </span><a href="index.html">ResolutionStrategy</a></div><div class="brief ">Enabled dependency verification for this configuration.</div></div>                  </div>
                </div>
              </div>
            </div>
          </div>
<a data-name="942386375%2FFunctions%2F-**********" anchor-label="failOnChangingVersions" id="942386375%2FFunctions%2F-**********" data-filterable-set=":docs/java_api"></a>
          <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="fail-on-changing-versions.html"><span>fail</span><wbr></wbr><span>On</span><wbr></wbr><span>Changing</span><wbr></wbr><span><span>Versions</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="942386375%2FFunctions%2F-**********"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
                    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api" data-active="" data-toggle=":docs/java_api">API</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/java_api"><div class="symbol monospace"><span class="token keyword">abstract </span><span class="token keyword"></span><span class="token keyword">fun </span><a href="fail-on-changing-versions.html"><span class="token function">failOnChangingVersions</span></a><span class="token punctuation">(</span><span class="token punctuation">)</span><span class="token operator">: </span><a href="index.html">ResolutionStrategy</a></div><div class="brief ">If this method is called, Gradle will make sure that no changing version participates in resolution.</div></div>                  </div>
                </div>
              </div>
            </div>
          </div>
<a data-name="682825873%2FFunctions%2F-**********" anchor-label="failOnDynamicVersions" id="682825873%2FFunctions%2F-**********" data-filterable-set=":docs/java_api"></a>
          <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="fail-on-dynamic-versions.html"><span>fail</span><wbr></wbr><span>On</span><wbr></wbr><span>Dynamic</span><wbr></wbr><span><span>Versions</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="682825873%2FFunctions%2F-**********"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
                    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api" data-active="" data-toggle=":docs/java_api">API</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/java_api"><div class="symbol monospace"><span class="token keyword">abstract </span><span class="token keyword"></span><span class="token keyword">fun </span><a href="fail-on-dynamic-versions.html"><span class="token function">failOnDynamicVersions</span></a><span class="token punctuation">(</span><span class="token punctuation">)</span><span class="token operator">: </span><a href="index.html">ResolutionStrategy</a></div><div class="brief ">If this method is called, Gradle will make sure that no dynamic version was used in the resulting dependency graph.</div></div>                  </div>
                </div>
              </div>
            </div>
          </div>
<a data-name="-212061368%2FFunctions%2F-**********" anchor-label="failOnNonReproducibleResolution" id="-212061368%2FFunctions%2F-**********" data-filterable-set=":docs/java_api"></a>
          <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="fail-on-non-reproducible-resolution.html"><span>fail</span><wbr></wbr><span>On</span><wbr></wbr><span>Non</span><wbr></wbr><span>Reproducible</span><wbr></wbr><span><span>Resolution</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="-212061368%2FFunctions%2F-**********"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
                    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api" data-active="" data-toggle=":docs/java_api">API</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/java_api"><div class="symbol monospace"><span class="token keyword">abstract </span><span class="token keyword"></span><span class="token keyword">fun </span><a href="fail-on-non-reproducible-resolution.html"><span class="token function">failOnNonReproducibleResolution</span></a><span class="token punctuation">(</span><span class="token punctuation">)</span><span class="token operator">: </span><a href="index.html">ResolutionStrategy</a></div><div class="brief ">Configures Gradle to fail the build is the resolution result is expected to be unstable, that is to say that it includes dynamic versions or changing versions and therefore the result may change depending on when the build is executed.</div></div>                  </div>
                </div>
              </div>
            </div>
          </div>
<a data-name="-1255808575%2FFunctions%2F-**********" anchor-label="failOnVersionConflict" id="-1255808575%2FFunctions%2F-**********" data-filterable-set=":docs/java_api"></a>
          <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="fail-on-version-conflict.html"><span>fail</span><wbr></wbr><span>On</span><wbr></wbr><span>Version</span><wbr></wbr><span><span>Conflict</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="-1255808575%2FFunctions%2F-**********"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
                    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api" data-active="" data-toggle=":docs/java_api">API</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/java_api"><div class="symbol monospace"><span class="token keyword">abstract </span><span class="token keyword"></span><span class="token keyword">fun </span><a href="fail-on-version-conflict.html"><span class="token function">failOnVersionConflict</span></a><span class="token punctuation">(</span><span class="token punctuation">)</span><span class="token operator">: </span><a href="index.html">ResolutionStrategy</a></div><div class="brief ">In case of conflict, Gradle by default uses the newest of conflicting versions.</div></div>                  </div>
                </div>
              </div>
            </div>
          </div>
<a data-name="911569028%2FFunctions%2F-**********" anchor-label="force" id="911569028%2FFunctions%2F-**********" data-filterable-set=":docs/java_api"></a>
          <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="force.html"><span><span>force</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="911569028%2FFunctions%2F-**********"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
                    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api" data-active="" data-toggle=":docs/java_api">API</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/java_api"><div class="symbol monospace"><span class="token keyword">abstract </span><span class="token keyword"></span><span class="token keyword">fun </span><a href="force.html"><span class="token function">force</span></a><span class="token punctuation">(</span><span class="parameters "><span class="parameter ">moduleVersionSelectorNotations<span class="token operator">: </span><a href="https://kotlinlang.org/api/latest/jvm/stdlib/kotlin/-array/index.html">Array</a><span class="token operator">&lt;</span><a href="https://kotlinlang.org/api/latest/jvm/stdlib/kotlin/-any/index.html">Any</a><span class="token operator">&gt;</span></span></span><span class="token punctuation">)</span><span class="token operator">: </span><a href="index.html">ResolutionStrategy</a></div><div class="brief ">Allows forcing certain versions of dependencies, including transitive dependencies.</div></div>                  </div>
                </div>
              </div>
            </div>
          </div>
<a data-name="-1319718634%2FFunctions%2F-**********" anchor-label="getCapabilitiesResolution" id="-1319718634%2FFunctions%2F-**********" data-filterable-set=":docs/java_api"></a>
          <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="get-capabilities-resolution.html"><span>get</span><wbr></wbr><span>Capabilities</span><wbr></wbr><span><span>Resolution</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="-1319718634%2FFunctions%2F-**********"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
                    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api" data-active="" data-toggle=":docs/java_api">API</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/java_api"><div class="symbol monospace"><span class="token keyword">abstract </span><span class="token keyword"></span><span class="token keyword">fun </span><a href="get-capabilities-resolution.html"><span class="token function">getCapabilitiesResolution</span></a><span class="token punctuation">(</span><span class="token punctuation">)</span><span class="token operator">: </span><a href="../-capabilities-resolution/index.html">CapabilitiesResolution</a></div><div class="brief ">Returns the capabilities resolution strategy.</div></div>                  </div>
                </div>
              </div>
            </div>
          </div>
<a data-name="547628841%2FFunctions%2F-**********" anchor-label="getComponentSelection" id="547628841%2FFunctions%2F-**********" data-filterable-set=":docs/java_api"></a>
          <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="get-component-selection.html"><span>get</span><wbr></wbr><span>Component</span><wbr></wbr><span><span>Selection</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="547628841%2FFunctions%2F-**********"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
                    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api" data-active="" data-toggle=":docs/java_api">API</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/java_api"><div class="symbol monospace"><span class="token keyword">abstract </span><span class="token keyword"></span><span class="token keyword">fun </span><a href="get-component-selection.html"><span class="token function">getComponentSelection</span></a><span class="token punctuation">(</span><span class="token punctuation">)</span><span class="token operator">: </span><a href="../-component-selection-rules/index.html">ComponentSelectionRules</a></div><div class="brief ">Returns the currently configured version selection rules object.</div></div>                  </div>
                </div>
              </div>
            </div>
          </div>
<a data-name="-1984133392%2FFunctions%2F-**********" anchor-label="getDependencySubstitution" id="-1984133392%2FFunctions%2F-**********" data-filterable-set=":docs/java_api"></a>
          <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="get-dependency-substitution.html"><span>get</span><wbr></wbr><span>Dependency</span><wbr></wbr><span><span>Substitution</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="-1984133392%2FFunctions%2F-**********"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
                    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api" data-active="" data-toggle=":docs/java_api">API</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/java_api"><div class="symbol monospace"><span class="token keyword">abstract </span><span class="token keyword"></span><span class="token keyword">fun </span><a href="get-dependency-substitution.html"><span class="token function">getDependencySubstitution</span></a><span class="token punctuation">(</span><span class="token punctuation">)</span><span class="token operator">: </span><a href="../-dependency-substitutions/index.html">DependencySubstitutions</a></div><div class="brief ">Returns the set of dependency substitution rules that are set for this configuration.</div></div>                  </div>
                </div>
              </div>
            </div>
          </div>
<a data-name="2008533652%2FFunctions%2F-**********" anchor-label="getForcedModules" id="2008533652%2FFunctions%2F-**********" data-filterable-set=":docs/java_api"></a>
          <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="get-forced-modules.html"><span>get</span><wbr></wbr><span>Forced</span><wbr></wbr><span><span>Modules</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="2008533652%2FFunctions%2F-**********"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
                    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api" data-active="" data-toggle=":docs/java_api">API</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/java_api"><div class="symbol monospace"><span class="token keyword">abstract </span><span class="token keyword"></span><span class="token keyword">fun </span><a href="get-forced-modules.html"><span class="token function">getForcedModules</span></a><span class="token punctuation">(</span><span class="token punctuation">)</span><span class="token operator">: </span><a href="https://docs.oracle.com/en/java/javase/11/docs/api/java.base/java/util/Set.html">Set</a><span class="token operator">&lt;</span><a href="../-module-version-selector/index.html">ModuleVersionSelector</a><span class="token operator">&gt;</span></div><div class="brief ">Returns currently configured forced modules.</div></div>                  </div>
                </div>
              </div>
            </div>
          </div>
<a data-name="1176203955%2FFunctions%2F-**********" anchor-label="getUseGlobalDependencySubstitutionRules" id="1176203955%2FFunctions%2F-**********" data-filterable-set=":docs/java_api"></a>
          <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="get-use-global-dependency-substitution-rules.html"><span>get</span><wbr></wbr><span>Use</span><wbr></wbr><span>Global</span><wbr></wbr><span>Dependency</span><wbr></wbr><span>Substitution</span><wbr></wbr><span><span>Rules</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="1176203955%2FFunctions%2F-**********"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
                    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api" data-active="" data-toggle=":docs/java_api">API</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/java_api"><div class="symbol monospace"><span class="token keyword">abstract </span><span class="token keyword"></span><span class="token keyword">fun </span><a href="get-use-global-dependency-substitution-rules.html"><span class="token function">getUseGlobalDependencySubstitutionRules</span></a><span class="token punctuation">(</span><span class="token punctuation">)</span><span class="token operator">: </span><a href="../../org.gradle.api.provider/-property/index.html">Property</a><span class="token operator">&lt;</span><a href="https://docs.oracle.com/en/java/javase/11/docs/api/java.base/java/lang/Boolean.html">Boolean</a><span class="token operator">&gt;</span></div><div class="brief ">Gradle implicitly registers dependency substitution rules for all configurations in the whole build tree to find projects in other included builds.</div></div>                  </div>
                </div>
              </div>
            </div>
          </div>
<a data-name="**********%2FFunctions%2F-**********" anchor-label="preferProjectModules" id="**********%2FFunctions%2F-**********" data-filterable-set=":docs/java_api"></a>
          <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="prefer-project-modules.html"><span>prefer</span><wbr></wbr><span>Project</span><wbr></wbr><span><span>Modules</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="**********%2FFunctions%2F-**********"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
                    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api" data-active="" data-toggle=":docs/java_api">API</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/java_api"><div class="symbol monospace"><span class="token keyword">abstract </span><span class="token keyword"></span><span class="token keyword">fun </span><a href="prefer-project-modules.html"><span class="token function">preferProjectModules</span></a><span class="token punctuation">(</span><span class="token punctuation">)</span></div><div class="brief ">Gradle can resolve conflicts purely by version number or prioritize project dependencies over binary.</div></div>                  </div>
                </div>
              </div>
            </div>
          </div>
<a data-name="-171283769%2FFunctions%2F-**********" anchor-label="setForcedModules" id="-171283769%2FFunctions%2F-**********" data-filterable-set=":docs/java_api"></a>
          <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="set-forced-modules.html"><span>set</span><wbr></wbr><span>Forced</span><wbr></wbr><span><span>Modules</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="-171283769%2FFunctions%2F-**********"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
                    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api" data-active="" data-toggle=":docs/java_api">API</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/java_api"><div class="symbol monospace"><span class="token keyword">abstract </span><span class="token keyword"></span><span class="token keyword">fun </span><a href="set-forced-modules.html"><span class="token function">setForcedModules</span></a><span class="token punctuation">(</span><span class="parameters "><span class="parameter ">moduleVersionSelectorNotations<span class="token operator">: </span><a href="https://kotlinlang.org/api/latest/jvm/stdlib/kotlin/-array/index.html">Array</a><span class="token operator">&lt;</span><a href="https://kotlinlang.org/api/latest/jvm/stdlib/kotlin/-any/index.html">Any</a><span class="token operator">&gt;</span></span></span><span class="token punctuation">)</span><span class="token operator">: </span><a href="index.html">ResolutionStrategy</a></div><div class="brief ">Allows forcing certain versions of dependencies, including transitive dependencies.</div></div>                  </div>
                </div>
              </div>
            </div>
          </div>
<a data-name="1274593368%2FFunctions%2F-**********" anchor-label="sortArtifacts" id="1274593368%2FFunctions%2F-**********" data-filterable-set=":docs/java_api"></a>
          <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="sort-artifacts.html"><span>sort</span><wbr></wbr><span><span>Artifacts</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="1274593368%2FFunctions%2F-**********"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
                    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api" data-active="" data-toggle=":docs/java_api">API</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/java_api"><div class="symbol monospace"><span class="token keyword">abstract </span><span class="token keyword"></span><span class="token keyword">fun </span><a href="sort-artifacts.html"><span class="token function">sortArtifacts</span></a><span class="token punctuation">(</span><span class="parameters "><span class="parameter ">sortOrder<span class="token operator">: </span><a href="-sort-order/index.html">ResolutionStrategy.SortOrder</a></span></span><span class="token punctuation">)</span></div><div class="brief ">Specifies the ordering for resolved artifacts.</div></div>                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
      <div class="footer">
        <span class="go-to-top-icon"><a href="#content" id="go-to-top-link"></a></span><span>Gradle Kotlin DSL Reference</span><span
                class="pull-right"><span>Generated by </span><a
                href="https://github.com/Kotlin/dokka"><span>dokka</span><span class="padded-icon"></span></a></span>
      </div>
    </div>
</div>
</body>
</html>
