<!DOCTYPE html>
<html>
<head>
    <meta name="viewport" content="width=device-width, initial-scale=1" charset="UTF-8">
    <title>org.gradle.api.tasks.compile</title>
    <link href="../../images/logo-icon.svg" rel="icon" type="image/svg">
    <script>var pathToRoot = "../../";</script>
    <script>const storage = localStorage.getItem("dokka-dark-mode")
    if (storage == null) {
        const osDarkSchemePreferred = window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches
        if (osDarkSchemePreferred === true) {
            document.getElementsByTagName("html")[0].classList.add("theme-dark")
        }
    } else {
        const savedDarkMode = JSON.parse(storage)
        if(savedDarkMode === true) {
            document.getElementsByTagName("html")[0].classList.add("theme-dark")
        }
    }
    </script>
<script type="text/javascript" src="../../scripts/sourceset_dependencies.js" async="async"></script>
<link href="../../styles/style.css" rel="Stylesheet">
<link href="../../styles/jetbrains-mono.css" rel="Stylesheet">
<link href="../../styles/main.css" rel="Stylesheet">
<link href="../../styles/prism.css" rel="Stylesheet">
<link href="../../styles/logo-styles.css" rel="Stylesheet">
<script type="text/javascript" src="../../scripts/clipboard.js" async="async"></script>
<script type="text/javascript" src="../../scripts/navigation-loader.js" async="async"></script>
<script type="text/javascript" src="../../scripts/platform-content-handler.js" async="async"></script>
<script type="text/javascript" src="../../scripts/main.js" defer="defer"></script>
<script type="text/javascript" src="../../scripts/prism.js" async="async"></script>
<script type="text/javascript" src="../../scripts/symbol-parameters-wrapper_deferred.js" defer="defer"></script>
<link href="../../images/gradle-logo.svg">
<link href="../../styles/gradle.css" rel="Stylesheet">
</head>
<body>
<div class="navigation-wrapper" id="navigation-wrapper">
    <div id="leftToggler"><span class="icon-toggler"></span></div>
    <div class="library-name">
            <a href="../../index.html">
                    <span>gradle</span>
            </a>
    </div>
    <div>
8.5    </div>
    <div class="pull-right d-flex">
        <div class="filter-section" id="filter-section">
                <button class="platform-tag platform-selector jvm-like" data-active="" data-filter=":docs/java_api">API</button>
        </div>
        <button id="theme-toggle-button"><span id="theme-toggle"></span></button>
        <div id="searchBar"></div>
    </div>
</div>
<div id="container">
    <div id="leftColumn">
        <div id="sideMenu"></div>
    </div>
    <div id="main">
<div class="main-content" data-page-type="package" id="content" pageIds="gradle::org.gradle.api.tasks.compile////PointingToDeclaration//-**********">
  <div class="breadcrumbs"><a href="../../index.html">gradle</a><span class="delimiter">/</span><span class="current">org.gradle.api.tasks.compile</span></div>
  <div class="cover ">
    <h1 class="cover"><span><span>Package-level</span></span> <span><span>declarations</span></span></h1>
    <div class="platform-hinted UnderCoverText with-platform-tabs" data-platform-hinted="data-platform-hinted">
      <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api" data-active="" data-toggle=":docs/java_api">API</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/java_api"><p class="paragraph">The compilation <a href="../org.gradle.api/-task/index.html">org.gradle.api.Task</a> implementations.</p></div>    </div>
  </div>
  <div class="tabbedcontent">
    <div class="tabs-section" tabs-section="tabs-section"><button class="section-tab" data-active="" data-togglable="TYPE">Types</button></div>
    <div class="tabs-section-body">
      <div data-togglable="TYPE">
        <h2 class="">Types</h2>
        <div class="table"><a data-name="1429079629%2FClasslikes%2F-**********" anchor-label="AbstractCompile" id="1429079629%2FClasslikes%2F-**********" data-filterable-set=":docs/java_api"></a>
          <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="-abstract-compile/index.html"><span>Abstract</span><wbr></wbr><span><span>Compile</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="1429079629%2FClasslikes%2F-**********"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
                    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api" data-active="" data-toggle=":docs/java_api">API</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/java_api"><div class="symbol monospace"><div class="block"><div class="block"><span class="token annotation builtin">@</span><a href="../org.gradle.work/-disable-caching-by-default/index.html"><span class="token annotation builtin">DisableCachingByDefault</span></a><span class="token punctuation">(</span><span>because<span class="token operator"> = </span><span class="breakable-word"><span class="token string">&quot;Abstract super-class, not to be instantiated directly&quot;</span></span></span><wbr></wbr><span class="token punctuation">)</span></div></div><span class="token keyword">abstract </span><span class="token keyword">class </span><a href="-abstract-compile/index.html">AbstractCompile</a> : <a href="../org.gradle.api.tasks/-source-task/index.html">SourceTask</a></div><div class="brief ">The base class for all JVM-based language compilation tasks.</div></div>                  </div>
                </div>
              </div>
            </div>
          </div>
<a data-name="-514975678%2FClasslikes%2F-**********" anchor-label="AbstractOptions" id="-514975678%2FClasslikes%2F-**********" data-filterable-set=":docs/java_api"></a>
          <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="-abstract-options/index.html"><span>Abstract</span><wbr></wbr><span><span>Options</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="-514975678%2FClasslikes%2F-**********"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
                    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api" data-active="" data-toggle=":docs/java_api">API</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/java_api"><div class="symbol monospace"><span class="token keyword">abstract </span><span class="token keyword">class </span><a href="-abstract-options/index.html">AbstractOptions</a> : <a href="https://docs.oracle.com/en/java/javase/11/docs/api/java.base/java/io/Serializable.html">Serializable</a></div><div class="brief ">Base class for compilation-related options.</div></div>                  </div>
                </div>
              </div>
            </div>
          </div>
<a data-name="366566227%2FClasslikes%2F-**********" anchor-label="BaseForkOptions" id="366566227%2FClasslikes%2F-**********" data-filterable-set=":docs/java_api"></a>
          <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="-base-fork-options/index.html"><span>Base</span><wbr></wbr><span>Fork</span><wbr></wbr><span><span>Options</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="366566227%2FClasslikes%2F-**********"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
                    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api" data-active="" data-toggle=":docs/java_api">API</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/java_api"><div class="symbol monospace"><span class="token keyword">open </span><span class="token keyword">class </span><a href="-base-fork-options/index.html">BaseForkOptions</a> : <a href="-abstract-options/index.html">AbstractOptions</a></div><div class="brief ">Fork options for compilation.</div></div>                  </div>
                </div>
              </div>
            </div>
          </div>
<a data-name="806442841%2FClasslikes%2F-**********" anchor-label="CompileOptions" id="806442841%2FClasslikes%2F-**********" data-filterable-set=":docs/java_api"></a>
          <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="-compile-options/index.html"><span>Compile</span><wbr></wbr><span><span>Options</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="806442841%2FClasslikes%2F-**********"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
                    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api" data-active="" data-toggle=":docs/java_api">API</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/java_api"><div class="symbol monospace"><span class="token keyword">abstract </span><span class="token keyword">class </span><a href="-compile-options/index.html">CompileOptions</a> : <a href="-abstract-options/index.html">AbstractOptions</a></div><div class="brief ">Main options for Java compilation.</div></div>                  </div>
                </div>
              </div>
            </div>
          </div>
<a data-name="20866105%2FClasslikes%2F-**********" anchor-label="DebugOptions" id="20866105%2FClasslikes%2F-**********" data-filterable-set=":docs/java_api"></a>
          <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="-debug-options/index.html"><span>Debug</span><wbr></wbr><span><span>Options</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="20866105%2FClasslikes%2F-**********"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
                    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api" data-active="" data-toggle=":docs/java_api">API</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/java_api"><div class="symbol monospace"><span class="token keyword">open </span><span class="token keyword">class </span><a href="-debug-options/index.html">DebugOptions</a> : <a href="-abstract-options/index.html">AbstractOptions</a></div><div class="brief ">Debug options for Java compilation.</div></div>                  </div>
                </div>
              </div>
            </div>
          </div>
<a data-name="1102975842%2FClasslikes%2F-**********" anchor-label="ForkOptions" id="1102975842%2FClasslikes%2F-**********" data-filterable-set=":docs/java_api"></a>
          <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="-fork-options/index.html"><span>Fork</span><wbr></wbr><span><span>Options</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="1102975842%2FClasslikes%2F-**********"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
                    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api" data-active="" data-toggle=":docs/java_api">API</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/java_api"><div class="symbol monospace"><span class="token keyword">abstract </span><span class="token keyword">class </span><a href="-fork-options/index.html">ForkOptions</a> : <a href="-provider-aware-compiler-daemon-fork-options/index.html">ProviderAwareCompilerDaemonForkOptions</a></div><div class="brief ">Fork options for Java compilation.</div></div>                  </div>
                </div>
              </div>
            </div>
          </div>
<a data-name="**********%2FClasslikes%2F-**********" anchor-label="GroovyCompile" id="**********%2FClasslikes%2F-**********" data-filterable-set=":docs/java_api"></a>
          <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="-groovy-compile/index.html"><span>Groovy</span><wbr></wbr><span><span>Compile</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="**********%2FClasslikes%2F-**********"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
                    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api" data-active="" data-toggle=":docs/java_api">API</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/java_api"><div class="symbol monospace"><div class="block"><div class="block"><span class="token annotation builtin">@</span><a href="../org.gradle.api.tasks/-cacheable-task/index.html"><span class="token annotation builtin">CacheableTask</span></a></div></div><span class="token keyword">abstract </span><span class="token keyword">class </span><a href="-groovy-compile/index.html">GroovyCompile</a> : <a href="-abstract-compile/index.html">AbstractCompile</a>, <span data-unresolved-link="org.gradle.api.internal.tasks.compile/HasCompileOptions///PointingToDeclaration/">HasCompileOptions</span></div><div class="brief ">Compiles Groovy source files, and optionally, Java source files.</div></div>                  </div>
                </div>
              </div>
            </div>
          </div>
<a data-name="1334150443%2FClasslikes%2F-**********" anchor-label="GroovyCompileOptions" id="1334150443%2FClasslikes%2F-**********" data-filterable-set=":docs/java_api"></a>
          <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="-groovy-compile-options/index.html"><span>Groovy</span><wbr></wbr><span>Compile</span><wbr></wbr><span><span>Options</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="1334150443%2FClasslikes%2F-**********"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
                    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api" data-active="" data-toggle=":docs/java_api">API</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/java_api"><div class="symbol monospace"><span class="token keyword">abstract </span><span class="token keyword">class </span><a href="-groovy-compile-options/index.html">GroovyCompileOptions</a> : <a href="-abstract-options/index.html">AbstractOptions</a></div><div class="brief ">Compilation options to be passed to the Groovy compiler.</div></div>                  </div>
                </div>
              </div>
            </div>
          </div>
<a data-name="611518160%2FClasslikes%2F-**********" anchor-label="GroovyForkOptions" id="611518160%2FClasslikes%2F-**********" data-filterable-set=":docs/java_api"></a>
          <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="-groovy-fork-options/index.html"><span>Groovy</span><wbr></wbr><span>Fork</span><wbr></wbr><span><span>Options</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="611518160%2FClasslikes%2F-**********"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
                    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api" data-active="" data-toggle=":docs/java_api">API</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/java_api"><div class="symbol monospace"><span class="token keyword">abstract </span><span class="token keyword">class </span><a href="-groovy-fork-options/index.html">GroovyForkOptions</a> : <a href="-provider-aware-compiler-daemon-fork-options/index.html">ProviderAwareCompilerDaemonForkOptions</a></div><div class="brief ">Fork options for Groovy compilation.</div></div>                  </div>
                </div>
              </div>
            </div>
          </div>
<a data-name="-254665011%2FClasslikes%2F-**********" anchor-label="JavaCompile" id="-254665011%2FClasslikes%2F-**********" data-filterable-set=":docs/java_api"></a>
          <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="-java-compile/index.html"><span>Java</span><wbr></wbr><span><span>Compile</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="-254665011%2FClasslikes%2F-**********"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
                    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api" data-active="" data-toggle=":docs/java_api">API</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/java_api"><div class="symbol monospace"><div class="block"><div class="block"><span class="token annotation builtin">@</span><a href="../org.gradle.api.tasks/-cacheable-task/index.html"><span class="token annotation builtin">CacheableTask</span></a></div></div><span class="token keyword">abstract </span><span class="token keyword">class </span><a href="-java-compile/index.html">JavaCompile</a> : <a href="-abstract-compile/index.html">AbstractCompile</a>, <span data-unresolved-link="org.gradle.api.internal.tasks.compile/HasCompileOptions///PointingToDeclaration/">HasCompileOptions</span></div><div class="brief ">Compiles Java source files.</div></div>                  </div>
                </div>
              </div>
            </div>
          </div>
<a data-name="-744953512%2FClasslikes%2F-**********" anchor-label="ProviderAwareCompilerDaemonForkOptions" id="-744953512%2FClasslikes%2F-**********" data-filterable-set=":docs/java_api"></a>
          <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="-provider-aware-compiler-daemon-fork-options/index.html"><span>Provider</span><wbr></wbr><span>Aware</span><wbr></wbr><span>Compiler</span><wbr></wbr><span>Daemon</span><wbr></wbr><span>Fork</span><wbr></wbr><span><span>Options</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="-744953512%2FClasslikes%2F-**********"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
                    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api" data-active="" data-toggle=":docs/java_api">API</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/java_api"><div class="symbol monospace"><div class="block"><div class="block"><span class="token annotation builtin">@</span><a href="../org.gradle.api/-incubating/index.html"><span class="token annotation builtin">Incubating</span></a></div></div><span class="token keyword">open </span><span class="token keyword">class </span><a href="-provider-aware-compiler-daemon-fork-options/index.html">ProviderAwareCompilerDaemonForkOptions</a> : <a href="-base-fork-options/index.html">BaseForkOptions</a></div><div class="brief ">Fork options for compilation that can accept user-defined <a href="../org.gradle.process/-command-line-argument-provider/index.html">CommandLineArgumentProvider</a> objects.</div></div>                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
      <div class="footer">
        <span class="go-to-top-icon"><a href="#content" id="go-to-top-link"></a></span><span>Gradle Kotlin DSL Reference</span><span
                class="pull-right"><span>Generated by </span><a
                href="https://github.com/Kotlin/dokka"><span>dokka</span><span class="padded-icon"></span></a></span>
      </div>
    </div>
</div>
</body>
</html>
