<!DOCTYPE html>
<html>
<head>
    <meta name="viewport" content="width=device-width, initial-scale=1" charset="UTF-8">
    <title>gradle</title>
    <link href="images/logo-icon.svg" rel="icon" type="image/svg">
    <script>var pathToRoot = "";</script>
    <script>const storage = localStorage.getItem("dokka-dark-mode")
    if (storage == null) {
        const osDarkSchemePreferred = window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches
        if (osDarkSchemePreferred === true) {
            document.getElementsByTagName("html")[0].classList.add("theme-dark")
        }
    } else {
        const savedDarkMode = JSON.parse(storage)
        if(savedDarkMode === true) {
            document.getElementsByTagName("html")[0].classList.add("theme-dark")
        }
    }
    </script>
<script type="text/javascript" src="scripts/sourceset_dependencies.js" async="async"></script>
<link href="styles/style.css" rel="Stylesheet">
<link href="styles/jetbrains-mono.css" rel="Stylesheet">
<link href="styles/main.css" rel="Stylesheet">
<link href="styles/prism.css" rel="Stylesheet">
<link href="styles/logo-styles.css" rel="Stylesheet">
<script type="text/javascript" src="scripts/clipboard.js" async="async"></script>
<script type="text/javascript" src="scripts/navigation-loader.js" async="async"></script>
<script type="text/javascript" src="scripts/platform-content-handler.js" async="async"></script>
<script type="text/javascript" src="scripts/main.js" defer="defer"></script>
<script type="text/javascript" src="scripts/prism.js" async="async"></script>
<script type="text/javascript" src="scripts/symbol-parameters-wrapper_deferred.js" defer="defer"></script>
<link href="images/gradle-logo.svg">
<link href="styles/gradle.css" rel="Stylesheet">
</head>
<body>
<div class="navigation-wrapper" id="navigation-wrapper">
    <div id="leftToggler"><span class="icon-toggler"></span></div>
    <div class="library-name">
            <a href="index.html">
                    <span>gradle</span>
            </a>
    </div>
    <div>
8.5    </div>
    <div class="pull-right d-flex">
        <div class="filter-section" id="filter-section">
                <button class="platform-tag platform-selector jvm-like" data-active="" data-filter=":docs/java_api">API</button>
                <button class="platform-tag platform-selector jvm-like" data-active="" data-filter=":docs/kotlin_dsl">DSL</button>
        </div>
        <button id="theme-toggle-button"><span id="theme-toggle"></span></button>
        <div id="searchBar"></div>
    </div>
</div>
<div id="container">
    <div id="leftColumn">
        <div id="sideMenu"></div>
    </div>
    <div id="main">
<div class="main-content" id="content" pageIds="gradle::////PointingToDeclaration//490660971">
  <div class="breadcrumbs"></div>
  <div class="cover ">
    <h1 class="cover"><span><span>gradle</span></span></h1>
    <div class="platform-hinted UnderCoverText with-platform-tabs" data-platform-hinted="data-platform-hinted">
      <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api" data-active="" data-toggle=":docs/java_api">API</button><button class="platform-bookmark" data-filterable-current=":docs/kotlin_dsl" data-filterable-set=":docs/kotlin_dsl" data-toggle=":docs/kotlin_dsl">DSL</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/java_api"><h1 class="">Kotlin DSL Reference for Gradle</h1><p class="paragraph">Gradle’s Kotlin DSL provides an enhanced editing experience in supported IDEs, with superior content assist, refactoring, documentation, and more. For an introduction see the <a href="../userguide/kotlin_dsl.html">Kotlin DSL Primer</a>.</p><p class="paragraph">The Kotlin DSL is implemented on top of Gradle’s Java API. Many of the objects, functions and properties you use in your build scripts come from the Gradle API and the APIs of the applied plugins. This reference covers both the Kotlin DSL and the Java API, but it doesn't include functionality provided by external plugins.</p><p class="paragraph">The main package of the Kotlin DSL is <a href="./gradle/org.gradle.kotlin.dsl/index.html">org.gradle.kotlin.dsl</a>. All members of this package are implicitly imported and readily available in <code class="lang-kotlin">.gradle.kts</code> scripts in addition to the Java API <a href="../userguide/writing_build_scripts.html#script-default-imports">default imports</a>.</p></div><div class="content sourceset-dependent-content" data-togglable=":docs/kotlin_dsl"><h1 class="">Kotlin DSL Reference for Gradle</h1><p class="paragraph">Gradle’s Kotlin DSL provides an enhanced editing experience in supported IDEs, with superior content assist, refactoring, documentation, and more. For an introduction see the <a href="../userguide/kotlin_dsl.html">Kotlin DSL Primer</a>.</p><p class="paragraph">The Kotlin DSL is implemented on top of Gradle’s Java API. Many of the objects, functions and properties you use in your build scripts come from the Gradle API and the APIs of the applied plugins. This reference covers both the Kotlin DSL and the Java API, but it doesn't include functionality provided by external plugins.</p><p class="paragraph">The main package of the Kotlin DSL is <a href="./gradle/org.gradle.kotlin.dsl/index.html">org.gradle.kotlin.dsl</a>. All members of this package are implicitly imported and readily available in <code class="lang-kotlin">.gradle.kts</code> scripts in addition to the Java API <a href="../userguide/writing_build_scripts.html#script-default-imports">default imports</a>.</p></div>    </div>
  </div>
  <h2 class="">Packages</h2>
  <div class="table"><a data-name="-1507131341%2FPackages%2F-**********" anchor-label="org.gradle" id="-1507131341%2FPackages%2F-**********" data-filterable-set=":docs/java_api"></a>
    <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
      <div>
        <div class="main-subrow ">
          <div class=""><span class="inline-flex">
              <div><a href="gradle/org.gradle/index.html">org.gradle</a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="-1507131341%2FPackages%2F-**********"></span>
                <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
              </span></span></div>
          <div class="pull-right">
            <div class="platform-tags no-gutters">
              <div class="platform-tag jvm-like">API</div>
            </div>
          </div>
        </div>
        <div><span class="brief-comment"><a data-name="-1507131341%2FPackages%2F-**********" anchor-label="org.gradle" id="-1507131341%2FPackages%2F-**********" data-filterable-set=":docs/java_api"></a>
            <p class="paragraph">Classes for embedding Gradle.</p>
          </span></div>
      </div>
    </div>
<a data-name="-1122733089%2FPackages%2F-**********" anchor-label="org.gradle.api" id="-1122733089%2FPackages%2F-**********" data-filterable-set=":docs/java_api"></a>
    <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
      <div>
        <div class="main-subrow ">
          <div class=""><span class="inline-flex">
              <div><a href="gradle/org.gradle.api/index.html">org.gradle.api</a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="-1122733089%2FPackages%2F-**********"></span>
                <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
              </span></span></div>
          <div class="pull-right">
            <div class="platform-tags no-gutters">
              <div class="platform-tag jvm-like">API</div>
            </div>
          </div>
        </div>
        <div><span class="brief-comment"><a data-name="-1122733089%2FPackages%2F-**********" anchor-label="org.gradle.api" id="-1122733089%2FPackages%2F-**********" data-filterable-set=":docs/java_api"></a>
            <p class="paragraph">Gradle's build language API, which is available from your build files. Location of the main interfaces involved in build scripts.</p>
          </span></div>
      </div>
    </div>
<a data-name="-822990254%2FPackages%2F-**********" anchor-label="org.gradle.api.artifacts" id="-822990254%2FPackages%2F-**********" data-filterable-set=":docs/java_api"></a>
    <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
      <div>
        <div class="main-subrow ">
          <div class=""><span class="inline-flex">
              <div><a href="gradle/org.gradle.api.artifacts/index.html">org.gradle.api.artifacts</a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="-822990254%2FPackages%2F-**********"></span>
                <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
              </span></span></div>
          <div class="pull-right">
            <div class="platform-tags no-gutters">
              <div class="platform-tag jvm-like">API</div>
            </div>
          </div>
        </div>
        <div><span class="brief-comment"><a data-name="-822990254%2FPackages%2F-**********" anchor-label="org.gradle.api.artifacts" id="-822990254%2FPackages%2F-**********" data-filterable-set=":docs/java_api"></a>
            <p class="paragraph">Classes for declaring and using artifacts and artifact dependencies.</p>
          </span></div>
      </div>
    </div>
<a data-name="1964829217%2FPackages%2F-**********" anchor-label="org.gradle.api.artifacts.component" id="1964829217%2FPackages%2F-**********" data-filterable-set=":docs/java_api"></a>
    <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
      <div>
        <div class="main-subrow ">
          <div class=""><span class="inline-flex">
              <div><a href="gradle/org.gradle.api.artifacts.component/index.html">org.gradle.api.artifacts.component</a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="1964829217%2FPackages%2F-**********"></span>
                <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
              </span></span></div>
          <div class="pull-right">
            <div class="platform-tags no-gutters">
              <div class="platform-tag jvm-like">API</div>
            </div>
          </div>
        </div>
        <div><span class="brief-comment"><a data-name="1964829217%2FPackages%2F-**********" anchor-label="org.gradle.api.artifacts.component" id="1964829217%2FPackages%2F-**********" data-filterable-set=":docs/java_api"></a>
            <p class="paragraph">Classes that provide meta-data about software components.</p>
          </span></div>
      </div>
    </div>
<a data-name="1983999073%2FPackages%2F-**********" anchor-label="org.gradle.api.artifacts.dsl" id="1983999073%2FPackages%2F-**********" data-filterable-set=":docs/java_api"></a>
    <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
      <div>
        <div class="main-subrow ">
          <div class=""><span class="inline-flex">
              <div><a href="gradle/org.gradle.api.artifacts.dsl/index.html">org.gradle.api.artifacts.dsl</a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="1983999073%2FPackages%2F-**********"></span>
                <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
              </span></span></div>
          <div class="pull-right">
            <div class="platform-tags no-gutters">
              <div class="platform-tag jvm-like">API</div>
            </div>
          </div>
        </div>
        <div><span class="brief-comment"><a data-name="1983999073%2FPackages%2F-**********" anchor-label="org.gradle.api.artifacts.dsl" id="1983999073%2FPackages%2F-**********" data-filterable-set=":docs/java_api"></a>
            <p class="paragraph">Classes used in the artifact DSL.</p>
          </span></div>
      </div>
    </div>
<a data-name="6410960%2FPackages%2F-**********" anchor-label="org.gradle.api.artifacts.ivy" id="6410960%2FPackages%2F-**********" data-filterable-set=":docs/java_api"></a>
    <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
      <div>
        <div class="main-subrow ">
          <div class=""><span class="inline-flex">
              <div><a href="gradle/org.gradle.api.artifacts.ivy/index.html">org.gradle.api.artifacts.ivy</a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="6410960%2FPackages%2F-**********"></span>
                <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
              </span></span></div>
          <div class="pull-right">
            <div class="platform-tags no-gutters">
              <div class="platform-tag jvm-like">API</div>
            </div>
          </div>
        </div>
        <div><span class="brief-comment"><a data-name="6410960%2FPackages%2F-**********" anchor-label="org.gradle.api.artifacts.ivy" id="6410960%2FPackages%2F-**********" data-filterable-set=":docs/java_api"></a>
            <p class="paragraph">Classes for declaring and using Ivy modules.</p>
          </span></div>
      </div>
    </div>
<a data-name="-841905617%2FPackages%2F-**********" anchor-label="org.gradle.api.artifacts.maven" id="-841905617%2FPackages%2F-**********" data-filterable-set=":docs/java_api"></a>
    <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
      <div>
        <div class="main-subrow ">
          <div class=""><span class="inline-flex">
              <div><a href="gradle/org.gradle.api.artifacts.maven/index.html">org.gradle.api.artifacts.maven</a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="-841905617%2FPackages%2F-**********"></span>
                <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
              </span></span></div>
          <div class="pull-right">
            <div class="platform-tags no-gutters">
              <div class="platform-tag jvm-like">API</div>
            </div>
          </div>
        </div>
        <div><span class="brief-comment"><a data-name="-841905617%2FPackages%2F-**********" anchor-label="org.gradle.api.artifacts.maven" id="-841905617%2FPackages%2F-**********" data-filterable-set=":docs/java_api"></a>
            <p class="paragraph">Classes for declaring and using Maven modules.</p>
          </span></div>
      </div>
    </div>
<a data-name="566034220%2FPackages%2F-**********" anchor-label="org.gradle.api.artifacts.query" id="566034220%2FPackages%2F-**********" data-filterable-set=":docs/java_api"></a>
    <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
      <div>
        <div class="main-subrow ">
          <div class=""><span class="inline-flex">
              <div><a href="gradle/org.gradle.api.artifacts.query/index.html">org.gradle.api.artifacts.query</a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="566034220%2FPackages%2F-**********"></span>
                <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
              </span></span></div>
          <div class="pull-right">
            <div class="platform-tags no-gutters">
              <div class="platform-tag jvm-like">API</div>
            </div>
          </div>
        </div>
        <div><span class="brief-comment"><a data-name="566034220%2FPackages%2F-**********" anchor-label="org.gradle.api.artifacts.query" id="566034220%2FPackages%2F-**********" data-filterable-set=":docs/java_api"></a>
            <p class="paragraph">Classes used for querying the artifacts.</p>
          </span></div>
      </div>
    </div>
<a data-name="-193734008%2FPackages%2F-**********" anchor-label="org.gradle.api.artifacts.repositories" id="-193734008%2FPackages%2F-**********" data-filterable-set=":docs/java_api"></a>
    <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
      <div>
        <div class="main-subrow ">
          <div class=""><span class="inline-flex">
              <div><a href="gradle/org.gradle.api.artifacts.repositories/index.html">org.gradle.api.artifacts.repositories</a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="-193734008%2FPackages%2F-**********"></span>
                <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
              </span></span></div>
          <div class="pull-right">
            <div class="platform-tags no-gutters">
              <div class="platform-tag jvm-like">API</div>
            </div>
          </div>
        </div>
        <div><span class="brief-comment"><a data-name="-193734008%2FPackages%2F-**********" anchor-label="org.gradle.api.artifacts.repositories" id="-193734008%2FPackages%2F-**********" data-filterable-set=":docs/java_api"></a>
            <p class="paragraph">Classes for declaring and using artifact repositories.</p>
          </span></div>
      </div>
    </div>
<a data-name="-1970012291%2FPackages%2F-**********" anchor-label="org.gradle.api.artifacts.result" id="-1970012291%2FPackages%2F-**********" data-filterable-set=":docs/java_api"></a>
    <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
      <div>
        <div class="main-subrow ">
          <div class=""><span class="inline-flex">
              <div><a href="gradle/org.gradle.api.artifacts.result/index.html">org.gradle.api.artifacts.result</a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="-1970012291%2FPackages%2F-**********"></span>
                <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
              </span></span></div>
          <div class="pull-right">
            <div class="platform-tags no-gutters">
              <div class="platform-tag jvm-like">API</div>
            </div>
          </div>
        </div>
        <div><span class="brief-comment"><a data-name="-1970012291%2FPackages%2F-**********" anchor-label="org.gradle.api.artifacts.result" id="-1970012291%2FPackages%2F-**********" data-filterable-set=":docs/java_api"></a>
            <p class="paragraph">Classes that compose the resolution result</p>
          </span></div>
      </div>
    </div>
<a data-name="667745232%2FPackages%2F-**********" anchor-label="org.gradle.api.artifacts.transform" id="667745232%2FPackages%2F-**********" data-filterable-set=":docs/java_api"></a>
    <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
      <div>
        <div class="main-subrow ">
          <div class=""><span class="inline-flex">
              <div><a href="gradle/org.gradle.api.artifacts.transform/index.html">org.gradle.api.artifacts.transform</a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="667745232%2FPackages%2F-**********"></span>
                <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
              </span></span></div>
          <div class="pull-right">
            <div class="platform-tags no-gutters">
              <div class="platform-tag jvm-like">API</div>
            </div>
          </div>
        </div>
        <div><span class="brief-comment"><a data-name="667745232%2FPackages%2F-**********" anchor-label="org.gradle.api.artifacts.transform" id="667745232%2FPackages%2F-**********" data-filterable-set=":docs/java_api"></a>
            <p class="paragraph">Provides classes, interfaces and annotations for registering and implementing artifact transforms. </p>
          </span></div>
      </div>
    </div>
<a data-name="-1402731558%2FPackages%2F-**********" anchor-label="org.gradle.api.artifacts.type" id="-1402731558%2FPackages%2F-**********" data-filterable-set=":docs/java_api"></a>
    <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
      <div>
        <div class="main-subrow ">
          <div class=""><span class="inline-flex">
              <div><a href="gradle/org.gradle.api.artifacts.type/index.html">org.gradle.api.artifacts.type</a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="-1402731558%2FPackages%2F-**********"></span>
                <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
              </span></span></div>
          <div class="pull-right">
            <div class="platform-tags no-gutters">
              <div class="platform-tag jvm-like">API</div>
            </div>
          </div>
        </div>
        <div><span class="brief-comment"><a data-name="-1402731558%2FPackages%2F-**********" anchor-label="org.gradle.api.artifacts.type" id="-1402731558%2FPackages%2F-**********" data-filterable-set=":docs/java_api"></a>
            <p class="paragraph">Types related to artifact type definitions.</p>
          </span></div>
      </div>
    </div>
<a data-name="-108370565%2FPackages%2F-**********" anchor-label="org.gradle.api.artifacts.verification" id="-108370565%2FPackages%2F-**********" data-filterable-set=":docs/java_api"></a>
    <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
      <div>
        <div class="main-subrow ">
          <div class=""><span class="inline-flex">
              <div><a href="gradle/org.gradle.api.artifacts.verification/index.html">org.gradle.api.artifacts.verification</a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="-108370565%2FPackages%2F-**********"></span>
                <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
              </span></span></div>
          <div class="pull-right">
            <div class="platform-tags no-gutters">
              <div class="platform-tag jvm-like">API</div>
            </div>
          </div>
        </div>
        <div><span class="brief-comment"><a data-name="-108370565%2FPackages%2F-**********" anchor-label="org.gradle.api.artifacts.verification" id="-108370565%2FPackages%2F-**********" data-filterable-set=":docs/java_api"></a>
            <p class="paragraph">Dependency verification related configuration classes</p>
          </span></div>
      </div>
    </div>
<a data-name="-1899886198%2FPackages%2F-**********" anchor-label="org.gradle.api.attributes" id="-1899886198%2FPackages%2F-**********" data-filterable-set=":docs/java_api"></a>
    <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
      <div>
        <div class="main-subrow ">
          <div class=""><span class="inline-flex">
              <div><a href="gradle/org.gradle.api.attributes/index.html">org.gradle.api.attributes</a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="-1899886198%2FPackages%2F-**********"></span>
                <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
              </span></span></div>
          <div class="pull-right">
            <div class="platform-tags no-gutters">
              <div class="platform-tag jvm-like">API</div>
            </div>
          </div>
        </div>
        <div><span class="brief-comment"><a data-name="-1899886198%2FPackages%2F-**********" anchor-label="org.gradle.api.attributes" id="-1899886198%2FPackages%2F-**********" data-filterable-set=":docs/java_api"></a>
            <p class="paragraph">Classes for dealing with configuration and artifact attributes.</p>
          </span></div>
      </div>
    </div>
<a data-name="933015690%2FPackages%2F-**********" anchor-label="org.gradle.api.attributes.java" id="933015690%2FPackages%2F-**********" data-filterable-set=":docs/java_api"></a>
    <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
      <div>
        <div class="main-subrow ">
          <div class=""><span class="inline-flex">
              <div><a href="gradle/org.gradle.api.attributes.java/index.html">org.gradle.api.attributes.java</a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="933015690%2FPackages%2F-**********"></span>
                <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
              </span></span></div>
          <div class="pull-right">
            <div class="platform-tags no-gutters">
              <div class="platform-tag jvm-like">API</div>
            </div>
          </div>
        </div>
        <div><span class="brief-comment"><a data-name="933015690%2FPackages%2F-**********" anchor-label="org.gradle.api.attributes.java" id="933015690%2FPackages%2F-**********" data-filterable-set=":docs/java_api"></a>
            <p class="paragraph">Attributes specific to the Java ecosystem.</p>
          </span></div>
      </div>
    </div>
<a data-name="702245083%2FPackages%2F-**********" anchor-label="org.gradle.api.attributes.plugin" id="702245083%2FPackages%2F-**********" data-filterable-set=":docs/java_api"></a>
    <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
      <div>
        <div class="main-subrow ">
          <div class=""><span class="inline-flex">
              <div><a href="gradle/org.gradle.api.attributes.plugin/index.html">org.gradle.api.attributes.plugin</a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="702245083%2FPackages%2F-**********"></span>
                <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
              </span></span></div>
          <div class="pull-right">
            <div class="platform-tags no-gutters">
              <div class="platform-tag jvm-like">API</div>
            </div>
          </div>
        </div>
        <div><span class="brief-comment"><a data-name="702245083%2FPackages%2F-**********" anchor-label="org.gradle.api.attributes.plugin" id="702245083%2FPackages%2F-**********" data-filterable-set=":docs/java_api"></a>
            <p class="paragraph">Attributes specific to Gradle's plugin system.</p>
          </span></div>
      </div>
    </div>
<a data-name="-793023181%2FPackages%2F-**********" anchor-label="org.gradle.api.cache" id="-793023181%2FPackages%2F-**********" data-filterable-set=":docs/java_api"></a>
    <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
      <div>
        <div class="main-subrow ">
          <div class=""><span class="inline-flex">
              <div><a href="gradle/org.gradle.api.cache/index.html">org.gradle.api.cache</a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="-793023181%2FPackages%2F-**********"></span>
                <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
              </span></span></div>
          <div class="pull-right">
            <div class="platform-tags no-gutters">
              <div class="platform-tag jvm-like">API</div>
            </div>
          </div>
        </div>
        <div><span class="brief-comment"><a data-name="-793023181%2FPackages%2F-**********" anchor-label="org.gradle.api.cache" id="-793023181%2FPackages%2F-**********" data-filterable-set=":docs/java_api"></a>
            <p class="paragraph">Classes for configuring cache-related components.</p>
          </span></div>
      </div>
    </div>
<a data-name="-1003624727%2FPackages%2F-**********" anchor-label="org.gradle.api.capabilities" id="-1003624727%2FPackages%2F-**********" data-filterable-set=":docs/java_api"></a>
    <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
      <div>
        <div class="main-subrow ">
          <div class=""><span class="inline-flex">
              <div><a href="gradle/org.gradle.api.capabilities/index.html">org.gradle.api.capabilities</a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="-1003624727%2FPackages%2F-**********"></span>
                <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
              </span></span></div>
          <div class="pull-right">
            <div class="platform-tags no-gutters">
              <div class="platform-tag jvm-like">API</div>
            </div>
          </div>
        </div>
        <div><span class="brief-comment"><a data-name="-1003624727%2FPackages%2F-**********" anchor-label="org.gradle.api.capabilities" id="-1003624727%2FPackages%2F-**********" data-filterable-set=":docs/java_api"></a>
            <p class="paragraph">Classes for dealing with capabilities.</p>
          </span></div>
      </div>
    </div>
<a data-name="-642881682%2FPackages%2F-**********" anchor-label="org.gradle.api.component" id="-642881682%2FPackages%2F-**********" data-filterable-set=":docs/java_api"></a>
    <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
      <div>
        <div class="main-subrow ">
          <div class=""><span class="inline-flex">
              <div><a href="gradle/org.gradle.api.component/index.html">org.gradle.api.component</a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="-642881682%2FPackages%2F-**********"></span>
                <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
              </span></span></div>
          <div class="pull-right">
            <div class="platform-tags no-gutters">
              <div class="platform-tag jvm-like">API</div>
            </div>
          </div>
        </div>
        <div><span class="brief-comment"><a data-name="-642881682%2FPackages%2F-**********" anchor-label="org.gradle.api.component" id="-642881682%2FPackages%2F-**********" data-filterable-set=":docs/java_api"></a>
            <p class="paragraph">Types for declaring and using Software Components.</p>
          </span></div>
      </div>
    </div>
<a data-name="-1300706649%2FPackages%2F-**********" anchor-label="org.gradle.api.configuration" id="-1300706649%2FPackages%2F-**********" data-filterable-set=":docs/java_api"></a>
    <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
      <div>
        <div class="main-subrow ">
          <div class=""><span class="inline-flex">
              <div><a href="gradle/org.gradle.api.configuration/index.html">org.gradle.api.configuration</a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="-1300706649%2FPackages%2F-**********"></span>
                <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
              </span></span></div>
          <div class="pull-right">
            <div class="platform-tags no-gutters">
              <div class="platform-tag jvm-like">API</div>
            </div>
          </div>
        </div>
        <div><span class="brief-comment"><a data-name="-1300706649%2FPackages%2F-**********" anchor-label="org.gradle.api.configuration" id="-1300706649%2FPackages%2F-**********" data-filterable-set=":docs/java_api"></a>
            <p class="paragraph">Classes related to build configuration.</p>
          </span></div>
      </div>
    </div>
<a data-name="-193846035%2FPackages%2F-**********" anchor-label="org.gradle.api.credentials" id="-193846035%2FPackages%2F-**********" data-filterable-set=":docs/java_api"></a>
    <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
      <div>
        <div class="main-subrow ">
          <div class=""><span class="inline-flex">
              <div><a href="gradle/org.gradle.api.credentials/index.html">org.gradle.api.credentials</a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="-193846035%2FPackages%2F-**********"></span>
                <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
              </span></span></div>
          <div class="pull-right">
            <div class="platform-tags no-gutters">
              <div class="platform-tag jvm-like">API</div>
            </div>
          </div>
        </div>
        <div><span class="brief-comment"><a data-name="-193846035%2FPackages%2F-**********" anchor-label="org.gradle.api.credentials" id="-193846035%2FPackages%2F-**********" data-filterable-set=":docs/java_api"></a>
            <p class="paragraph">general credentials related classes.</p>
          </span></div>
      </div>
    </div>
<a data-name="-2043994057%2FPackages%2F-**********" anchor-label="org.gradle.api.distribution" id="-2043994057%2FPackages%2F-**********" data-filterable-set=":docs/java_api"></a>
    <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
      <div>
        <div class="main-subrow ">
          <div class=""><span class="inline-flex">
              <div><a href="gradle/org.gradle.api.distribution/index.html">org.gradle.api.distribution</a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="-2043994057%2FPackages%2F-**********"></span>
                <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
              </span></span></div>
          <div class="pull-right">
            <div class="platform-tags no-gutters">
              <div class="platform-tag jvm-like">API</div>
            </div>
          </div>
        </div>
        <div><span class="brief-comment"><a data-name="-2043994057%2FPackages%2F-**********" anchor-label="org.gradle.api.distribution" id="-2043994057%2FPackages%2F-**********" data-filterable-set=":docs/java_api"></a>
            <p class="paragraph">The main interfaces and classes of the distribution plugin.</p>
          </span></div>
      </div>
    </div>
<a data-name="1522296937%2FPackages%2F-**********" anchor-label="org.gradle.api.distribution.plugins" id="1522296937%2FPackages%2F-**********" data-filterable-set=":docs/java_api"></a>
    <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
      <div>
        <div class="main-subrow ">
          <div class=""><span class="inline-flex">
              <div><a href="gradle/org.gradle.api.distribution.plugins/index.html">org.gradle.api.distribution.plugins</a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="1522296937%2FPackages%2F-**********"></span>
                <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
              </span></span></div>
          <div class="pull-right">
            <div class="platform-tags no-gutters">
              <div class="platform-tag jvm-like">API</div>
            </div>
          </div>
        </div>
        <div><span class="brief-comment"><a data-name="1522296937%2FPackages%2F-**********" anchor-label="org.gradle.api.distribution.plugins" id="1522296937%2FPackages%2F-**********" data-filterable-set=":docs/java_api"></a>
            <p class="paragraph">The distribution plugin itself.</p>
          </span></div>
      </div>
    </div>
<a data-name="-1275618871%2FPackages%2F-**********" anchor-label="org.gradle.api.execution" id="-1275618871%2FPackages%2F-**********" data-filterable-set=":docs/java_api"></a>
    <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
      <div>
        <div class="main-subrow ">
          <div class=""><span class="inline-flex">
              <div><a href="gradle/org.gradle.api.execution/index.html">org.gradle.api.execution</a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="-1275618871%2FPackages%2F-**********"></span>
                <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
              </span></span></div>
          <div class="pull-right">
            <div class="platform-tags no-gutters">
              <div class="platform-tag jvm-like">API</div>
            </div>
          </div>
        </div>
        <div><span class="brief-comment"><a data-name="-1275618871%2FPackages%2F-**********" anchor-label="org.gradle.api.execution" id="-1275618871%2FPackages%2F-**********" data-filterable-set=":docs/java_api"></a>
            <p class="paragraph">Classes for managing and monitoring build execution.</p>
          </span></div>
      </div>
    </div>
<a data-name="1680794159%2FPackages%2F-**********" anchor-label="org.gradle.api.file" id="1680794159%2FPackages%2F-**********" data-filterable-set=":docs/java_api"></a>
    <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
      <div>
        <div class="main-subrow ">
          <div class=""><span class="inline-flex">
              <div><a href="gradle/org.gradle.api.file/index.html">org.gradle.api.file</a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="1680794159%2FPackages%2F-**********"></span>
                <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
              </span></span></div>
          <div class="pull-right">
            <div class="platform-tags no-gutters">
              <div class="platform-tag jvm-like">API</div>
            </div>
          </div>
        </div>
        <div><span class="brief-comment"><a data-name="1680794159%2FPackages%2F-**********" anchor-label="org.gradle.api.file" id="1680794159%2FPackages%2F-**********" data-filterable-set=":docs/java_api"></a>
            <p class="paragraph">Classes for working with files.</p>
          </span></div>
      </div>
    </div>
<a data-name="-1169894559%2FPackages%2F-**********" anchor-label="org.gradle.api.flow" id="-1169894559%2FPackages%2F-**********" data-filterable-set=":docs/java_api"></a>
    <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
      <div>
        <div class="main-subrow ">
          <div class=""><span class="inline-flex">
              <div><a href="gradle/org.gradle.api.flow/index.html">org.gradle.api.flow</a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="-1169894559%2FPackages%2F-**********"></span>
                <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
              </span></span></div>
          <div class="pull-right">
            <div class="platform-tags no-gutters">
              <div class="platform-tag jvm-like">API</div>
            </div>
          </div>
        </div>
        <div><span class="brief-comment"><a data-name="-1169894559%2FPackages%2F-**********" anchor-label="org.gradle.api.flow" id="-1169894559%2FPackages%2F-**********" data-filterable-set=":docs/java_api"></a>
            <p class="paragraph">Gradle Flow API.</p>
          </span></div>
      </div>
    </div>
<a data-name="1732996883%2FPackages%2F-**********" anchor-label="org.gradle.api.initialization" id="1732996883%2FPackages%2F-**********" data-filterable-set=":docs/java_api"></a>
    <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
      <div>
        <div class="main-subrow ">
          <div class=""><span class="inline-flex">
              <div><a href="gradle/org.gradle.api.initialization/index.html">org.gradle.api.initialization</a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="1732996883%2FPackages%2F-**********"></span>
                <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
              </span></span></div>
          <div class="pull-right">
            <div class="platform-tags no-gutters">
              <div class="platform-tag jvm-like">API</div>
            </div>
          </div>
        </div>
        <div><span class="brief-comment"><a data-name="1732996883%2FPackages%2F-**********" anchor-label="org.gradle.api.initialization" id="1732996883%2FPackages%2F-**********" data-filterable-set=":docs/java_api"></a>
            <p class="paragraph">Classes for managing and monitoring build initialization.</p>
          </span></div>
      </div>
    </div>
<a data-name="-1819930926%2FPackages%2F-**********" anchor-label="org.gradle.api.initialization.definition" id="-1819930926%2FPackages%2F-**********" data-filterable-set=":docs/java_api"></a>
    <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
      <div>
        <div class="main-subrow ">
          <div class=""><span class="inline-flex">
              <div><a href="gradle/org.gradle.api.initialization.definition/index.html">org.gradle.api.initialization.definition</a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="-1819930926%2FPackages%2F-**********"></span>
                <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
              </span></span></div>
          <div class="pull-right">
            <div class="platform-tags no-gutters">
              <div class="platform-tag jvm-like">API</div>
            </div>
          </div>
        </div>
        <div><span class="brief-comment"><a data-name="-1819930926%2FPackages%2F-**********" anchor-label="org.gradle.api.initialization.definition" id="-1819930926%2FPackages%2F-**********" data-filterable-set=":docs/java_api"></a>
            <p class="paragraph">Types related to the build definition for included builds.</p>
          </span></div>
      </div>
    </div>
<a data-name="49834146%2FPackages%2F-**********" anchor-label="org.gradle.api.initialization.dsl" id="49834146%2FPackages%2F-**********" data-filterable-set=":docs/java_api"></a>
    <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
      <div>
        <div class="main-subrow ">
          <div class=""><span class="inline-flex">
              <div><a href="gradle/org.gradle.api.initialization.dsl/index.html">org.gradle.api.initialization.dsl</a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="49834146%2FPackages%2F-**********"></span>
                <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
              </span></span></div>
          <div class="pull-right">
            <div class="platform-tags no-gutters">
              <div class="platform-tag jvm-like">API</div>
            </div>
          </div>
        </div>
        <div><span class="brief-comment"><a data-name="49834146%2FPackages%2F-**********" anchor-label="org.gradle.api.initialization.dsl" id="49834146%2FPackages%2F-**********" data-filterable-set=":docs/java_api"></a>
            <p class="paragraph">Classes used in the initialization DSL.</p>
          </span></div>
      </div>
    </div>
<a data-name="1584963217%2FPackages%2F-**********" anchor-label="org.gradle.api.initialization.resolve" id="1584963217%2FPackages%2F-**********" data-filterable-set=":docs/java_api"></a>
    <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
      <div>
        <div class="main-subrow ">
          <div class=""><span class="inline-flex">
              <div><a href="gradle/org.gradle.api.initialization.resolve/index.html">org.gradle.api.initialization.resolve</a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="1584963217%2FPackages%2F-**********"></span>
                <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
              </span></span></div>
          <div class="pull-right">
            <div class="platform-tags no-gutters">
              <div class="platform-tag jvm-like">API</div>
            </div>
          </div>
        </div>
        <div><span class="brief-comment"><a data-name="1584963217%2FPackages%2F-**********" anchor-label="org.gradle.api.initialization.resolve" id="1584963217%2FPackages%2F-**********" data-filterable-set=":docs/java_api"></a>
            <p class="paragraph">Classes for managing cross-project dependency resolution.</p>
          </span></div>
      </div>
    </div>
<a data-name="80663427%2FPackages%2F-**********" anchor-label="org.gradle.api.invocation" id="80663427%2FPackages%2F-**********" data-filterable-set=":docs/java_api"></a>
    <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
      <div>
        <div class="main-subrow ">
          <div class=""><span class="inline-flex">
              <div><a href="gradle/org.gradle.api.invocation/index.html">org.gradle.api.invocation</a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="80663427%2FPackages%2F-**********"></span>
                <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
              </span></span></div>
          <div class="pull-right">
            <div class="platform-tags no-gutters">
              <div class="platform-tag jvm-like">API</div>
            </div>
          </div>
        </div>
        <div><span class="brief-comment"><a data-name="80663427%2FPackages%2F-**********" anchor-label="org.gradle.api.invocation" id="80663427%2FPackages%2F-**********" data-filterable-set=":docs/java_api"></a>
            <p class="paragraph">Classes for invoking and monitoring gradle builds.</p>
          </span></div>
      </div>
    </div>
<a data-name="-94666450%2FPackages%2F-**********" anchor-label="org.gradle.api.java.archives" id="-94666450%2FPackages%2F-**********" data-filterable-set=":docs/java_api"></a>
    <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
      <div>
        <div class="main-subrow ">
          <div class=""><span class="inline-flex">
              <div><a href="gradle/org.gradle.api.java.archives/index.html">org.gradle.api.java.archives</a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="-94666450%2FPackages%2F-**********"></span>
                <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
              </span></span></div>
          <div class="pull-right">
            <div class="platform-tags no-gutters">
              <div class="platform-tag jvm-like">API</div>
            </div>
          </div>
        </div>
        <div><span class="brief-comment"><a data-name="-94666450%2FPackages%2F-**********" anchor-label="org.gradle.api.java.archives" id="-94666450%2FPackages%2F-**********" data-filterable-set=":docs/java_api"></a>
            <p class="paragraph">Classes for working with JAR manifests.</p>
          </span></div>
      </div>
    </div>
<a data-name="-1820481198%2FPackages%2F-**********" anchor-label="org.gradle.api.jvm" id="-1820481198%2FPackages%2F-**********" data-filterable-set=":docs/java_api"></a>
    <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
      <div>
        <div class="main-subrow ">
          <div class=""><span class="inline-flex">
              <div><a href="gradle/org.gradle.api.jvm/index.html">org.gradle.api.jvm</a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="-1820481198%2FPackages%2F-**********"></span>
                <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
              </span></span></div>
          <div class="pull-right">
            <div class="platform-tags no-gutters">
              <div class="platform-tag jvm-like">API</div>
            </div>
          </div>
        </div>
        <div><span class="brief-comment"><a data-name="-1820481198%2FPackages%2F-**********" anchor-label="org.gradle.api.jvm" id="-1820481198%2FPackages%2F-**********" data-filterable-set=":docs/java_api"></a>
            <p class="paragraph">Interfaces for configuring the Java Platform Module System (JPMS).</p>
          </span></div>
      </div>
    </div>
<a data-name="-1578183163%2FPackages%2F-**********" anchor-label="org.gradle.api.launcher.cli" id="-1578183163%2FPackages%2F-**********" data-filterable-set=":docs/java_api"></a>
    <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
      <div>
        <div class="main-subrow ">
          <div class=""><span class="inline-flex">
              <div><a href="gradle/org.gradle.api.launcher.cli/index.html">org.gradle.api.launcher.cli</a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="-1578183163%2FPackages%2F-**********"></span>
                <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
              </span></span></div>
          <div class="pull-right">
            <div class="platform-tags no-gutters">
              <div class="platform-tag jvm-like">API</div>
            </div>
          </div>
        </div>
        <div><span class="brief-comment"><a data-name="-1578183163%2FPackages%2F-**********" anchor-label="org.gradle.api.launcher.cli" id="-1578183163%2FPackages%2F-**********" data-filterable-set=":docs/java_api"></a>
            <p class="paragraph">Interfaces for configuring the cli client.</p>
          </span></div>
      </div>
    </div>
<a data-name="-499800304%2FPackages%2F-**********" anchor-label="org.gradle.api.logging" id="-499800304%2FPackages%2F-**********" data-filterable-set=":docs/java_api"></a>
    <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
      <div>
        <div class="main-subrow ">
          <div class=""><span class="inline-flex">
              <div><a href="gradle/org.gradle.api.logging/index.html">org.gradle.api.logging</a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="-499800304%2FPackages%2F-**********"></span>
                <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
              </span></span></div>
          <div class="pull-right">
            <div class="platform-tags no-gutters">
              <div class="platform-tag jvm-like">API</div>
            </div>
          </div>
        </div>
        <div><span class="brief-comment"><a data-name="-499800304%2FPackages%2F-**********" anchor-label="org.gradle.api.logging" id="-499800304%2FPackages%2F-**********" data-filterable-set=":docs/java_api"></a>
            <p class="paragraph">Classes for managing logging in Gradle.</p>
          </span></div>
      </div>
    </div>
<a data-name="-1291578344%2FPackages%2F-**********" anchor-label="org.gradle.api.logging.configuration" id="-1291578344%2FPackages%2F-**********" data-filterable-set=":docs/java_api"></a>
    <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
      <div>
        <div class="main-subrow ">
          <div class=""><span class="inline-flex">
              <div><a href="gradle/org.gradle.api.logging.configuration/index.html">org.gradle.api.logging.configuration</a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="-1291578344%2FPackages%2F-**********"></span>
                <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
              </span></span></div>
          <div class="pull-right">
            <div class="platform-tags no-gutters">
              <div class="platform-tag jvm-like">API</div>
            </div>
          </div>
        </div>
        <div><span class="brief-comment"><a data-name="-1291578344%2FPackages%2F-**********" anchor-label="org.gradle.api.logging.configuration" id="-1291578344%2FPackages%2F-**********" data-filterable-set=":docs/java_api"></a>
            <p class="paragraph">Classes for logging configuration.</p>
          </span></div>
      </div>
    </div>
<a data-name="-1822890470%2FPackages%2F-**********" anchor-label="org.gradle.api.model" id="-1822890470%2FPackages%2F-**********" data-filterable-set=":docs/java_api"></a>
    <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
      <div>
        <div class="main-subrow ">
          <div class=""><span class="inline-flex">
              <div><a href="gradle/org.gradle.api.model/index.html">org.gradle.api.model</a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="-1822890470%2FPackages%2F-**********"></span>
                <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
              </span></span></div>
          <div class="pull-right">
            <div class="platform-tags no-gutters">
              <div class="platform-tag jvm-like">API</div>
            </div>
          </div>
        </div>
        <div><span class="brief-comment"><a data-name="-1822890470%2FPackages%2F-**********" anchor-label="org.gradle.api.model" id="-1822890470%2FPackages%2F-**********" data-filterable-set=":docs/java_api"></a>
            <p class="paragraph">Classes that operate on the Gradle model.</p>
          </span></div>
      </div>
    </div>
<a data-name="361468945%2FPackages%2F-**********" anchor-label="org.gradle.api.plugins" id="361468945%2FPackages%2F-**********" data-filterable-set=":docs/java_api"></a>
    <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
      <div>
        <div class="main-subrow ">
          <div class=""><span class="inline-flex">
              <div><a href="gradle/org.gradle.api.plugins/index.html">org.gradle.api.plugins</a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="361468945%2FPackages%2F-**********"></span>
                <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
              </span></span></div>
          <div class="pull-right">
            <div class="platform-tags no-gutters">
              <div class="platform-tag jvm-like">API</div>
            </div>
          </div>
        </div>
        <div><span class="brief-comment"><a data-name="361468945%2FPackages%2F-**********" anchor-label="org.gradle.api.plugins" id="361468945%2FPackages%2F-**********" data-filterable-set=":docs/java_api"></a>
            <p class="paragraph">The standard <a href="gradle/org.gradle.api/-plugin/index.html">org.gradle.api.Plugin</a> implementations.</p>
          </span></div>
      </div>
    </div>
<a data-name="435513936%2FPackages%2F-**********" anchor-label="org.gradle.api.plugins.antlr" id="435513936%2FPackages%2F-**********" data-filterable-set=":docs/java_api"></a>
    <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
      <div>
        <div class="main-subrow ">
          <div class=""><span class="inline-flex">
              <div><a href="gradle/org.gradle.api.plugins.antlr/index.html">org.gradle.api.plugins.antlr</a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="435513936%2FPackages%2F-**********"></span>
                <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
              </span></span></div>
          <div class="pull-right">
            <div class="platform-tags no-gutters">
              <div class="platform-tag jvm-like">API</div>
            </div>
          </div>
        </div>
        <div><span class="brief-comment"><a data-name="435513936%2FPackages%2F-**********" anchor-label="org.gradle.api.plugins.antlr" id="435513936%2FPackages%2F-**********" data-filterable-set=":docs/java_api"></a>
            <p class="paragraph">A <a href="gradle/org.gradle.api/-plugin/index.html">org.gradle.api.Plugin</a> for generating parsers from Antlr grammars.</p>
          </span></div>
      </div>
    </div>
<a data-name="-264751748%2FPackages%2F-**********" anchor-label="org.gradle.api.plugins.catalog" id="-264751748%2FPackages%2F-**********" data-filterable-set=":docs/java_api"></a>
    <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
      <div>
        <div class="main-subrow ">
          <div class=""><span class="inline-flex">
              <div><a href="gradle/org.gradle.api.plugins.catalog/index.html">org.gradle.api.plugins.catalog</a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="-264751748%2FPackages%2F-**********"></span>
                <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
              </span></span></div>
          <div class="pull-right">
            <div class="platform-tags no-gutters">
              <div class="platform-tag jvm-like">API</div>
            </div>
          </div>
        </div>
        <div></div>
      </div>
    </div>
<a data-name="-1641717628%2FPackages%2F-**********" anchor-label="org.gradle.api.plugins.jvm" id="-1641717628%2FPackages%2F-**********" data-filterable-set=":docs/java_api"></a>
    <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
      <div>
        <div class="main-subrow ">
          <div class=""><span class="inline-flex">
              <div><a href="gradle/org.gradle.api.plugins.jvm/index.html">org.gradle.api.plugins.jvm</a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="-1641717628%2FPackages%2F-**********"></span>
                <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
              </span></span></div>
          <div class="pull-right">
            <div class="platform-tags no-gutters">
              <div class="platform-tag jvm-like">API</div>
            </div>
          </div>
        </div>
        <div></div>
      </div>
    </div>
<a data-name="801546978%2FPackages%2F-**********" anchor-label="org.gradle.api.plugins.quality" id="801546978%2FPackages%2F-**********" data-filterable-set=":docs/java_api"></a>
    <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
      <div>
        <div class="main-subrow ">
          <div class=""><span class="inline-flex">
              <div><a href="gradle/org.gradle.api.plugins.quality/index.html">org.gradle.api.plugins.quality</a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="801546978%2FPackages%2F-**********"></span>
                <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
              </span></span></div>
          <div class="pull-right">
            <div class="platform-tags no-gutters">
              <div class="platform-tag jvm-like">API</div>
            </div>
          </div>
        </div>
        <div><span class="brief-comment"><a data-name="801546978%2FPackages%2F-**********" anchor-label="org.gradle.api.plugins.quality" id="801546978%2FPackages%2F-**********" data-filterable-set=":docs/java_api"></a>
            <p class="paragraph">Plugins which measure and enforce code quality.</p>
          </span></div>
      </div>
    </div>
<a data-name="-1416098647%2FPackages%2F-**********" anchor-label="org.gradle.api.plugins.scala" id="-1416098647%2FPackages%2F-**********" data-filterable-set=":docs/java_api"></a>
    <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
      <div>
        <div class="main-subrow ">
          <div class=""><span class="inline-flex">
              <div><a href="gradle/org.gradle.api.plugins.scala/index.html">org.gradle.api.plugins.scala</a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="-1416098647%2FPackages%2F-**********"></span>
                <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
              </span></span></div>
          <div class="pull-right">
            <div class="platform-tags no-gutters">
              <div class="platform-tag jvm-like">API</div>
            </div>
          </div>
        </div>
        <div><span class="brief-comment"><a data-name="-1416098647%2FPackages%2F-**********" anchor-label="org.gradle.api.plugins.scala" id="-1416098647%2FPackages%2F-**********" data-filterable-set=":docs/java_api"></a>
            <p class="paragraph">A <a href="gradle/org.gradle.api/-plugin/index.html">org.gradle.api.Plugin</a> which compiles and tests Scala sources.</p>
          </span></div>
      </div>
    </div>
<a data-name="-1785636889%2FPackages%2F-**********" anchor-label="org.gradle.api.problems" id="-1785636889%2FPackages%2F-**********" data-filterable-set=":docs/java_api"></a>
    <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
      <div>
        <div class="main-subrow ">
          <div class=""><span class="inline-flex">
              <div><a href="gradle/org.gradle.api.problems/index.html">org.gradle.api.problems</a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="-1785636889%2FPackages%2F-**********"></span>
                <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
              </span></span></div>
          <div class="pull-right">
            <div class="platform-tags no-gutters">
              <div class="platform-tag jvm-like">API</div>
            </div>
          </div>
        </div>
        <div><span class="brief-comment"><a data-name="-1785636889%2FPackages%2F-**********" anchor-label="org.gradle.api.problems" id="-1785636889%2FPackages%2F-**********" data-filterable-set=":docs/java_api"></a>
            <p class="paragraph">new Problems API</p>
          </span></div>
      </div>
    </div>
<a data-name="517731479%2FPackages%2F-**********" anchor-label="org.gradle.api.problems.locations" id="517731479%2FPackages%2F-**********" data-filterable-set=":docs/java_api"></a>
    <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
      <div>
        <div class="main-subrow ">
          <div class=""><span class="inline-flex">
              <div><a href="gradle/org.gradle.api.problems.locations/index.html">org.gradle.api.problems.locations</a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="517731479%2FPackages%2F-**********"></span>
                <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
              </span></span></div>
          <div class="pull-right">
            <div class="platform-tags no-gutters">
              <div class="platform-tag jvm-like">API</div>
            </div>
          </div>
        </div>
        <div><span class="brief-comment"><a data-name="517731479%2FPackages%2F-**********" anchor-label="org.gradle.api.problems.locations" id="517731479%2FPackages%2F-**********" data-filterable-set=":docs/java_api"></a>
            <p class="paragraph">Location information types, which could be added to a reported problem.</p>
          </span></div>
      </div>
    </div>
<a data-name="**********%2FPackages%2F-**********" anchor-label="org.gradle.api.provider" id="**********%2FPackages%2F-**********" data-filterable-set=":docs/java_api"></a>
    <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
      <div>
        <div class="main-subrow ">
          <div class=""><span class="inline-flex">
              <div><a href="gradle/org.gradle.api.provider/index.html">org.gradle.api.provider</a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="**********%2FPackages%2F-**********"></span>
                <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
              </span></span></div>
          <div class="pull-right">
            <div class="platform-tags no-gutters">
              <div class="platform-tag jvm-like">API</div>
            </div>
          </div>
        </div>
        <div><span class="brief-comment"><a data-name="**********%2FPackages%2F-**********" anchor-label="org.gradle.api.provider" id="**********%2FPackages%2F-**********" data-filterable-set=":docs/java_api"></a>
            <p class="paragraph">The interfaces for value providers.</p>
          </span></div>
      </div>
    </div>
<a data-name="-**********%2FPackages%2F-**********" anchor-label="org.gradle.api.publish" id="-**********%2FPackages%2F-**********" data-filterable-set=":docs/java_api"></a>
    <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
      <div>
        <div class="main-subrow ">
          <div class=""><span class="inline-flex">
              <div><a href="gradle/org.gradle.api.publish/index.html">org.gradle.api.publish</a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="-**********%2FPackages%2F-**********"></span>
                <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
              </span></span></div>
          <div class="pull-right">
            <div class="platform-tags no-gutters">
              <div class="platform-tag jvm-like">API</div>
            </div>
          </div>
        </div>
        <div><span class="brief-comment"><a data-name="-**********%2FPackages%2F-**********" anchor-label="org.gradle.api.publish" id="-**********%2FPackages%2F-**********" data-filterable-set=":docs/java_api"></a>
            <p class="paragraph">Classes that deal with publishing artifacts.</p>
          </span></div>
      </div>
    </div>
<a data-name="-1923842498%2FPackages%2F-**********" anchor-label="org.gradle.api.publish.ivy" id="-1923842498%2FPackages%2F-**********" data-filterable-set=":docs/java_api"></a>
    <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
      <div>
        <div class="main-subrow ">
          <div class=""><span class="inline-flex">
              <div><a href="gradle/org.gradle.api.publish.ivy/index.html">org.gradle.api.publish.ivy</a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="-1923842498%2FPackages%2F-**********"></span>
                <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
              </span></span></div>
          <div class="pull-right">
            <div class="platform-tags no-gutters">
              <div class="platform-tag jvm-like">API</div>
            </div>
          </div>
        </div>
        <div><span class="brief-comment"><a data-name="-1923842498%2FPackages%2F-**********" anchor-label="org.gradle.api.publish.ivy" id="-1923842498%2FPackages%2F-**********" data-filterable-set=":docs/java_api"></a>
            <p class="paragraph">Types that deal with publishing in the Ivy format.</p>
          </span></div>
      </div>
    </div>
<a data-name="1468452720%2FPackages%2F-**********" anchor-label="org.gradle.api.publish.ivy.plugins" id="1468452720%2FPackages%2F-**********" data-filterable-set=":docs/java_api"></a>
    <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
      <div>
        <div class="main-subrow ">
          <div class=""><span class="inline-flex">
              <div><a href="gradle/org.gradle.api.publish.ivy.plugins/index.html">org.gradle.api.publish.ivy.plugins</a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="1468452720%2FPackages%2F-**********"></span>
                <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
              </span></span></div>
          <div class="pull-right">
            <div class="platform-tags no-gutters">
              <div class="platform-tag jvm-like">API</div>
            </div>
          </div>
        </div>
        <div><span class="brief-comment"><a data-name="1468452720%2FPackages%2F-**********" anchor-label="org.gradle.api.publish.ivy.plugins" id="1468452720%2FPackages%2F-**********" data-filterable-set=":docs/java_api"></a>
            <p class="paragraph">Plugins for Ivy publishing.</p>
          </span></div>
      </div>
    </div>
<a data-name="-32405154%2FPackages%2F-**********" anchor-label="org.gradle.api.publish.ivy.tasks" id="-32405154%2FPackages%2F-**********" data-filterable-set=":docs/java_api"></a>
    <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
      <div>
        <div class="main-subrow ">
          <div class=""><span class="inline-flex">
              <div><a href="gradle/org.gradle.api.publish.ivy.tasks/index.html">org.gradle.api.publish.ivy.tasks</a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="-32405154%2FPackages%2F-**********"></span>
                <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
              </span></span></div>
          <div class="pull-right">
            <div class="platform-tags no-gutters">
              <div class="platform-tag jvm-like">API</div>
            </div>
          </div>
        </div>
        <div><span class="brief-comment"><a data-name="-32405154%2FPackages%2F-**********" anchor-label="org.gradle.api.publish.ivy.tasks" id="-32405154%2FPackages%2F-**********" data-filterable-set=":docs/java_api"></a>
            <p class="paragraph">Tasks for Ivy publishing.</p>
          </span></div>
      </div>
    </div>
<a data-name="-389606883%2FPackages%2F-**********" anchor-label="org.gradle.api.publish.maven" id="-389606883%2FPackages%2F-**********" data-filterable-set=":docs/java_api"></a>
    <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
      <div>
        <div class="main-subrow ">
          <div class=""><span class="inline-flex">
              <div><a href="gradle/org.gradle.api.publish.maven/index.html">org.gradle.api.publish.maven</a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="-389606883%2FPackages%2F-**********"></span>
                <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
              </span></span></div>
          <div class="pull-right">
            <div class="platform-tags no-gutters">
              <div class="platform-tag jvm-like">API</div>
            </div>
          </div>
        </div>
        <div><span class="brief-comment"><a data-name="-389606883%2FPackages%2F-**********" anchor-label="org.gradle.api.publish.maven" id="-389606883%2FPackages%2F-**********" data-filterable-set=":docs/java_api"></a>
            <p class="paragraph">Types that deal with publishing in the Maven format.</p>
          </span></div>
      </div>
    </div>
<a data-name="-1806494641%2FPackages%2F-**********" anchor-label="org.gradle.api.publish.maven.plugins" id="-1806494641%2FPackages%2F-**********" data-filterable-set=":docs/java_api"></a>
    <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
      <div>
        <div class="main-subrow ">
          <div class=""><span class="inline-flex">
              <div><a href="gradle/org.gradle.api.publish.maven.plugins/index.html">org.gradle.api.publish.maven.plugins</a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="-1806494641%2FPackages%2F-**********"></span>
                <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
              </span></span></div>
          <div class="pull-right">
            <div class="platform-tags no-gutters">
              <div class="platform-tag jvm-like">API</div>
            </div>
          </div>
        </div>
        <div><span class="brief-comment"><a data-name="-1806494641%2FPackages%2F-**********" anchor-label="org.gradle.api.publish.maven.plugins" id="-1806494641%2FPackages%2F-**********" data-filterable-set=":docs/java_api"></a>
            <p class="paragraph">Plugins for publishing in the Maven format.</p>
          </span></div>
      </div>
    </div>
<a data-name="-996705795%2FPackages%2F-**********" anchor-label="org.gradle.api.publish.maven.tasks" id="-996705795%2FPackages%2F-**********" data-filterable-set=":docs/java_api"></a>
    <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
      <div>
        <div class="main-subrow ">
          <div class=""><span class="inline-flex">
              <div><a href="gradle/org.gradle.api.publish.maven.tasks/index.html">org.gradle.api.publish.maven.tasks</a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="-996705795%2FPackages%2F-**********"></span>
                <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
              </span></span></div>
          <div class="pull-right">
            <div class="platform-tags no-gutters">
              <div class="platform-tag jvm-like">API</div>
            </div>
          </div>
        </div>
        <div><span class="brief-comment"><a data-name="-996705795%2FPackages%2F-**********" anchor-label="org.gradle.api.publish.maven.tasks" id="-996705795%2FPackages%2F-**********" data-filterable-set=":docs/java_api"></a>
            <p class="paragraph">Tasks for publishing in the Maven format.</p>
          </span></div>
      </div>
    </div>
<a data-name="1875143666%2FPackages%2F-**********" anchor-label="org.gradle.api.publish.plugins" id="1875143666%2FPackages%2F-**********" data-filterable-set=":docs/java_api"></a>
    <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
      <div>
        <div class="main-subrow ">
          <div class=""><span class="inline-flex">
              <div><a href="gradle/org.gradle.api.publish.plugins/index.html">org.gradle.api.publish.plugins</a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="1875143666%2FPackages%2F-**********"></span>
                <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
              </span></span></div>
          <div class="pull-right">
            <div class="platform-tags no-gutters">
              <div class="platform-tag jvm-like">API</div>
            </div>
          </div>
        </div>
        <div><span class="brief-comment"><a data-name="1875143666%2FPackages%2F-**********" anchor-label="org.gradle.api.publish.plugins" id="1875143666%2FPackages%2F-**********" data-filterable-set=":docs/java_api"></a>
            <p class="paragraph">Publishing plugin.</p>
          </span></div>
      </div>
    </div>
<a data-name="-1028628896%2FPackages%2F-**********" anchor-label="org.gradle.api.publish.tasks" id="-1028628896%2FPackages%2F-**********" data-filterable-set=":docs/java_api"></a>
    <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
      <div>
        <div class="main-subrow ">
          <div class=""><span class="inline-flex">
              <div><a href="gradle/org.gradle.api.publish.tasks/index.html">org.gradle.api.publish.tasks</a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="-1028628896%2FPackages%2F-**********"></span>
                <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
              </span></span></div>
          <div class="pull-right">
            <div class="platform-tags no-gutters">
              <div class="platform-tag jvm-like">API</div>
            </div>
          </div>
        </div>
        <div><span class="brief-comment"><a data-name="-1028628896%2FPackages%2F-**********" anchor-label="org.gradle.api.publish.tasks" id="-1028628896%2FPackages%2F-**********" data-filterable-set=":docs/java_api"></a>
            <p class="paragraph">Tasks used for publishing to a binary repository.</p>
          </span></div>
      </div>
    </div>
<a data-name="1555765486%2FPackages%2F-**********" anchor-label="org.gradle.api.reflect" id="1555765486%2FPackages%2F-**********" data-filterable-set=":docs/java_api"></a>
    <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
      <div>
        <div class="main-subrow ">
          <div class=""><span class="inline-flex">
              <div><a href="gradle/org.gradle.api.reflect/index.html">org.gradle.api.reflect</a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="1555765486%2FPackages%2F-**********"></span>
                <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
              </span></span></div>
          <div class="pull-right">
            <div class="platform-tags no-gutters">
              <div class="platform-tag jvm-like">API</div>
            </div>
          </div>
        </div>
        <div><span class="brief-comment"><a data-name="1555765486%2FPackages%2F-**********" anchor-label="org.gradle.api.reflect" id="1555765486%2FPackages%2F-**********" data-filterable-set=":docs/java_api"></a>
            <p class="paragraph">Classes and API for the reflection and types.</p>
          </span></div>
      </div>
    </div>
<a data-name="1835205983%2FPackages%2F-**********" anchor-label="org.gradle.api.reporting" id="1835205983%2FPackages%2F-**********" data-filterable-set=":docs/java_api"></a>
    <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
      <div>
        <div class="main-subrow ">
          <div class=""><span class="inline-flex">
              <div><a href="gradle/org.gradle.api.reporting/index.html">org.gradle.api.reporting</a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="1835205983%2FPackages%2F-**********"></span>
                <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
              </span></span></div>
          <div class="pull-right">
            <div class="platform-tags no-gutters">
              <div class="platform-tag jvm-like">API</div>
            </div>
          </div>
        </div>
        <div><span class="brief-comment"><a data-name="1835205983%2FPackages%2F-**********" anchor-label="org.gradle.api.reporting" id="1835205983%2FPackages%2F-**********" data-filterable-set=":docs/java_api"></a>
            <p class="paragraph">Classes for reporting</p>
          </span></div>
      </div>
    </div>
<a data-name="1032233225%2FPackages%2F-**********" anchor-label="org.gradle.api.reporting.components" id="1032233225%2FPackages%2F-**********" data-filterable-set=":docs/java_api"></a>
    <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
      <div>
        <div class="main-subrow ">
          <div class=""><span class="inline-flex">
              <div><a href="gradle/org.gradle.api.reporting.components/index.html">org.gradle.api.reporting.components</a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="1032233225%2FPackages%2F-**********"></span>
                <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
              </span></span></div>
          <div class="pull-right">
            <div class="platform-tags no-gutters">
              <div class="platform-tag jvm-like">API</div>
            </div>
          </div>
        </div>
        <div><span class="brief-comment"><a data-name="1032233225%2FPackages%2F-**********" anchor-label="org.gradle.api.reporting.components" id="1032233225%2FPackages%2F-**********" data-filterable-set=":docs/java_api"></a>
            <p class="paragraph">Component reporting tasks.</p>
          </span></div>
      </div>
    </div>
<a data-name="1746270492%2FPackages%2F-**********" anchor-label="org.gradle.api.reporting.dependencies" id="1746270492%2FPackages%2F-**********" data-filterable-set=":docs/java_api"></a>
    <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
      <div>
        <div class="main-subrow ">
          <div class=""><span class="inline-flex">
              <div><a href="gradle/org.gradle.api.reporting.dependencies/index.html">org.gradle.api.reporting.dependencies</a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="1746270492%2FPackages%2F-**********"></span>
                <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
              </span></span></div>
          <div class="pull-right">
            <div class="platform-tags no-gutters">
              <div class="platform-tag jvm-like">API</div>
            </div>
          </div>
        </div>
        <div><span class="brief-comment"><a data-name="1746270492%2FPackages%2F-**********" anchor-label="org.gradle.api.reporting.dependencies" id="1746270492%2FPackages%2F-**********" data-filterable-set=":docs/java_api"></a>
            <p class="paragraph">Types responsible for generating dependency reports.</p>
          </span></div>
      </div>
    </div>
<a data-name="-1558150713%2FPackages%2F-**********" anchor-label="org.gradle.api.reporting.dependents" id="-1558150713%2FPackages%2F-**********" data-filterable-set=":docs/java_api"></a>
    <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
      <div>
        <div class="main-subrow ">
          <div class=""><span class="inline-flex">
              <div><a href="gradle/org.gradle.api.reporting.dependents/index.html">org.gradle.api.reporting.dependents</a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="-1558150713%2FPackages%2F-**********"></span>
                <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
              </span></span></div>
          <div class="pull-right">
            <div class="platform-tags no-gutters">
              <div class="platform-tag jvm-like">API</div>
            </div>
          </div>
        </div>
        <div><span class="brief-comment"><a data-name="-1558150713%2FPackages%2F-**********" anchor-label="org.gradle.api.reporting.dependents" id="-1558150713%2FPackages%2F-**********" data-filterable-set=":docs/java_api"></a>
            <p class="paragraph">Types responsible for generating dependents components reports.</p>
          </span></div>
      </div>
    </div>
<a data-name="735336346%2FPackages%2F-**********" anchor-label="org.gradle.api.reporting.model" id="735336346%2FPackages%2F-**********" data-filterable-set=":docs/java_api"></a>
    <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
      <div>
        <div class="main-subrow ">
          <div class=""><span class="inline-flex">
              <div><a href="gradle/org.gradle.api.reporting.model/index.html">org.gradle.api.reporting.model</a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="735336346%2FPackages%2F-**********"></span>
                <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
              </span></span></div>
          <div class="pull-right">
            <div class="platform-tags no-gutters">
              <div class="platform-tag jvm-like">API</div>
            </div>
          </div>
        </div>
        <div><span class="brief-comment"><a data-name="735336346%2FPackages%2F-**********" anchor-label="org.gradle.api.reporting.model" id="735336346%2FPackages%2F-**********" data-filterable-set=":docs/java_api"></a>
            <p class="paragraph">Configuration model reporting tasks.</p>
          </span></div>
      </div>
    </div>
<a data-name="2096145809%2FPackages%2F-**********" anchor-label="org.gradle.api.reporting.plugins" id="2096145809%2FPackages%2F-**********" data-filterable-set=":docs/java_api"></a>
    <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
      <div>
        <div class="main-subrow ">
          <div class=""><span class="inline-flex">
              <div><a href="gradle/org.gradle.api.reporting.plugins/index.html">org.gradle.api.reporting.plugins</a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="2096145809%2FPackages%2F-**********"></span>
                <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
              </span></span></div>
          <div class="pull-right">
            <div class="platform-tags no-gutters">
              <div class="platform-tag jvm-like">API</div>
            </div>
          </div>
        </div>
        <div><span class="brief-comment"><a data-name="2096145809%2FPackages%2F-**********" anchor-label="org.gradle.api.reporting.plugins" id="2096145809%2FPackages%2F-**********" data-filterable-set=":docs/java_api"></a>
            <p class="paragraph">Plugins for reporting</p>
          </span></div>
      </div>
    </div>
<a data-name="1692362390%2FPackages%2F-**********" anchor-label="org.gradle.api.resources" id="1692362390%2FPackages%2F-**********" data-filterable-set=":docs/java_api"></a>
    <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
      <div>
        <div class="main-subrow ">
          <div class=""><span class="inline-flex">
              <div><a href="gradle/org.gradle.api.resources/index.html">org.gradle.api.resources</a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="1692362390%2FPackages%2F-**********"></span>
                <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
              </span></span></div>
          <div class="pull-right">
            <div class="platform-tags no-gutters">
              <div class="platform-tag jvm-like">API</div>
            </div>
          </div>
        </div>
        <div><span class="brief-comment"><a data-name="1692362390%2FPackages%2F-**********" anchor-label="org.gradle.api.resources" id="1692362390%2FPackages%2F-**********" data-filterable-set=":docs/java_api"></a>
            <p class="paragraph">Interfaces and API for the 'Resources' concept.</p>
          </span></div>
      </div>
    </div>
<a data-name="1217295889%2FPackages%2F-**********" anchor-label="org.gradle.api.services" id="1217295889%2FPackages%2F-**********" data-filterable-set=":docs/java_api"></a>
    <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
      <div>
        <div class="main-subrow ">
          <div class=""><span class="inline-flex">
              <div><a href="gradle/org.gradle.api.services/index.html">org.gradle.api.services</a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="1217295889%2FPackages%2F-**********"></span>
                <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
              </span></span></div>
          <div class="pull-right">
            <div class="platform-tags no-gutters">
              <div class="platform-tag jvm-like">API</div>
            </div>
          </div>
        </div>
        <div><span class="brief-comment"><a data-name="1217295889%2FPackages%2F-**********" anchor-label="org.gradle.api.services" id="1217295889%2FPackages%2F-**********" data-filterable-set=":docs/java_api"></a>
            <p class="paragraph">Types for defining and using build services.</p>
          </span></div>
      </div>
    </div>
<a data-name="-362836407%2FPackages%2F-**********" anchor-label="org.gradle.api.specs" id="-362836407%2FPackages%2F-**********" data-filterable-set=":docs/java_api"></a>
    <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
      <div>
        <div class="main-subrow ">
          <div class=""><span class="inline-flex">
              <div><a href="gradle/org.gradle.api.specs/index.html">org.gradle.api.specs</a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="-362836407%2FPackages%2F-**********"></span>
                <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
              </span></span></div>
          <div class="pull-right">
            <div class="platform-tags no-gutters">
              <div class="platform-tag jvm-like">API</div>
            </div>
          </div>
        </div>
        <div><span class="brief-comment"><a data-name="-362836407%2FPackages%2F-**********" anchor-label="org.gradle.api.specs" id="-362836407%2FPackages%2F-**********" data-filterable-set=":docs/java_api"></a>
            <p class="paragraph">Classes for defining general purpose criteria.</p>
          </span></div>
      </div>
    </div>
<a data-name="-1106181569%2FPackages%2F-**********" anchor-label="org.gradle.api.tasks" id="-1106181569%2FPackages%2F-**********" data-filterable-set=":docs/java_api"></a>
    <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
      <div>
        <div class="main-subrow ">
          <div class=""><span class="inline-flex">
              <div><a href="gradle/org.gradle.api.tasks/index.html">org.gradle.api.tasks</a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="-1106181569%2FPackages%2F-**********"></span>
                <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
              </span></span></div>
          <div class="pull-right">
            <div class="platform-tags no-gutters">
              <div class="platform-tag jvm-like">API</div>
            </div>
          </div>
        </div>
        <div><span class="brief-comment"><a data-name="-1106181569%2FPackages%2F-**********" anchor-label="org.gradle.api.tasks" id="-1106181569%2FPackages%2F-**********" data-filterable-set=":docs/java_api"></a>
            <p class="paragraph">The standard <a href="gradle/org.gradle.api/-task/index.html">org.gradle.api.Task</a> implementations.</p>
          </span></div>
      </div>
    </div>
<a data-name="598489464%2FPackages%2F-**********" anchor-label="org.gradle.api.tasks.ant" id="598489464%2FPackages%2F-**********" data-filterable-set=":docs/java_api"></a>
    <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
      <div>
        <div class="main-subrow ">
          <div class=""><span class="inline-flex">
              <div><a href="gradle/org.gradle.api.tasks.ant/index.html">org.gradle.api.tasks.ant</a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="598489464%2FPackages%2F-**********"></span>
                <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
              </span></span></div>
          <div class="pull-right">
            <div class="platform-tags no-gutters">
              <div class="platform-tag jvm-like">API</div>
            </div>
          </div>
        </div>
        <div><span class="brief-comment"><a data-name="598489464%2FPackages%2F-**********" anchor-label="org.gradle.api.tasks.ant" id="598489464%2FPackages%2F-**********" data-filterable-set=":docs/java_api"></a>
            <p class="paragraph">The Ant integration <a href="gradle/org.gradle.api/-task/index.html">org.gradle.api.Task</a> implementations.</p>
          </span></div>
      </div>
    </div>
<a data-name="1417282721%2FPackages%2F-**********" anchor-label="org.gradle.api.tasks.application" id="1417282721%2FPackages%2F-**********" data-filterable-set=":docs/java_api"></a>
    <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
      <div>
        <div class="main-subrow ">
          <div class=""><span class="inline-flex">
              <div><a href="gradle/org.gradle.api.tasks.application/index.html">org.gradle.api.tasks.application</a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="1417282721%2FPackages%2F-**********"></span>
                <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
              </span></span></div>
          <div class="pull-right">
            <div class="platform-tags no-gutters">
              <div class="platform-tag jvm-like">API</div>
            </div>
          </div>
        </div>
        <div></div>
      </div>
    </div>
<a data-name="-233926446%2FPackages%2F-**********" anchor-label="org.gradle.api.tasks.bundling" id="-233926446%2FPackages%2F-**********" data-filterable-set=":docs/java_api"></a>
    <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
      <div>
        <div class="main-subrow ">
          <div class=""><span class="inline-flex">
              <div><a href="gradle/org.gradle.api.tasks.bundling/index.html">org.gradle.api.tasks.bundling</a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="-233926446%2FPackages%2F-**********"></span>
                <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
              </span></span></div>
          <div class="pull-right">
            <div class="platform-tags no-gutters">
              <div class="platform-tag jvm-like">API</div>
            </div>
          </div>
        </div>
        <div><span class="brief-comment"><a data-name="-233926446%2FPackages%2F-**********" anchor-label="org.gradle.api.tasks.bundling" id="-233926446%2FPackages%2F-**********" data-filterable-set=":docs/java_api"></a>
            <p class="paragraph">The archive bundling <a href="gradle/org.gradle.api/-task/index.html">org.gradle.api.Task</a> implementations.</p>
          </span></div>
      </div>
    </div>
<a data-name="2031797060%2FPackages%2F-**********" anchor-label="org.gradle.api.tasks.compile" id="2031797060%2FPackages%2F-**********" data-filterable-set=":docs/java_api"></a>
    <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
      <div>
        <div class="main-subrow ">
          <div class=""><span class="inline-flex">
              <div><a href="gradle/org.gradle.api.tasks.compile/index.html">org.gradle.api.tasks.compile</a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="2031797060%2FPackages%2F-**********"></span>
                <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
              </span></span></div>
          <div class="pull-right">
            <div class="platform-tags no-gutters">
              <div class="platform-tag jvm-like">API</div>
            </div>
          </div>
        </div>
        <div><span class="brief-comment"><a data-name="2031797060%2FPackages%2F-**********" anchor-label="org.gradle.api.tasks.compile" id="2031797060%2FPackages%2F-**********" data-filterable-set=":docs/java_api"></a>
            <p class="paragraph">The compilation <a href="gradle/org.gradle.api/-task/index.html">org.gradle.api.Task</a> implementations.</p>
          </span></div>
      </div>
    </div>
<a data-name="1308990973%2FPackages%2F-**********" anchor-label="org.gradle.api.tasks.diagnostics" id="1308990973%2FPackages%2F-**********" data-filterable-set=":docs/java_api"></a>
    <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
      <div>
        <div class="main-subrow ">
          <div class=""><span class="inline-flex">
              <div><a href="gradle/org.gradle.api.tasks.diagnostics/index.html">org.gradle.api.tasks.diagnostics</a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="1308990973%2FPackages%2F-**********"></span>
                <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
              </span></span></div>
          <div class="pull-right">
            <div class="platform-tags no-gutters">
              <div class="platform-tag jvm-like">API</div>
            </div>
          </div>
        </div>
        <div><span class="brief-comment"><a data-name="1308990973%2FPackages%2F-**********" anchor-label="org.gradle.api.tasks.diagnostics" id="1308990973%2FPackages%2F-**********" data-filterable-set=":docs/java_api"></a>
            <p class="paragraph">The diagnostic <a href="gradle/org.gradle.api/-task/index.html">org.gradle.api.Task</a> implementations.</p>
          </span></div>
      </div>
    </div>
<a data-name="441490%2FPackages%2F-**********" anchor-label="org.gradle.api.tasks.diagnostics.configurations" id="441490%2FPackages%2F-**********" data-filterable-set=":docs/java_api"></a>
    <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
      <div>
        <div class="main-subrow ">
          <div class=""><span class="inline-flex">
              <div><a href="gradle/org.gradle.api.tasks.diagnostics.configurations/index.html">org.gradle.api.tasks.diagnostics.configurations</a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="441490%2FPackages%2F-**********"></span>
                <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
              </span></span></div>
          <div class="pull-right">
            <div class="platform-tags no-gutters">
              <div class="platform-tag jvm-like">API</div>
            </div>
          </div>
        </div>
        <div><span class="brief-comment"><a data-name="441490%2FPackages%2F-**********" anchor-label="org.gradle.api.tasks.diagnostics.configurations" id="441490%2FPackages%2F-**********" data-filterable-set=":docs/java_api"></a>
            <p class="paragraph">Diagnostic tasks which report information about configurations.</p>
          </span></div>
      </div>
    </div>
<a data-name="292552363%2FPackages%2F-**********" anchor-label="org.gradle.api.tasks.incremental" id="292552363%2FPackages%2F-**********" data-filterable-set=":docs/java_api"></a>
    <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
      <div>
        <div class="main-subrow ">
          <div class=""><span class="inline-flex">
              <div><a href="gradle/org.gradle.api.tasks.incremental/index.html">org.gradle.api.tasks.incremental</a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="292552363%2FPackages%2F-**********"></span>
                <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
              </span></span></div>
          <div class="pull-right">
            <div class="platform-tags no-gutters">
              <div class="platform-tag jvm-like">API</div>
            </div>
          </div>
        </div>
        <div><span class="brief-comment"><a data-name="292552363%2FPackages%2F-**********" anchor-label="org.gradle.api.tasks.incremental" id="292552363%2FPackages%2F-**********" data-filterable-set=":docs/java_api"></a>
            <p class="paragraph">API classes for implementing incremental tasks.</p>
          </span></div>
      </div>
    </div>
<a data-name="-1162878169%2FPackages%2F-**********" anchor-label="org.gradle.api.tasks.javadoc" id="-1162878169%2FPackages%2F-**********" data-filterable-set=":docs/java_api"></a>
    <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
      <div>
        <div class="main-subrow ">
          <div class=""><span class="inline-flex">
              <div><a href="gradle/org.gradle.api.tasks.javadoc/index.html">org.gradle.api.tasks.javadoc</a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="-1162878169%2FPackages%2F-**********"></span>
                <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
              </span></span></div>
          <div class="pull-right">
            <div class="platform-tags no-gutters">
              <div class="platform-tag jvm-like">API</div>
            </div>
          </div>
        </div>
        <div><span class="brief-comment"><a data-name="-1162878169%2FPackages%2F-**********" anchor-label="org.gradle.api.tasks.javadoc" id="-1162878169%2FPackages%2F-**********" data-filterable-set=":docs/java_api"></a>
            <p class="paragraph">The documentation generation <a href="gradle/org.gradle.api/-task/index.html">org.gradle.api.Task</a> implementations.</p>
          </span></div>
      </div>
    </div>
<a data-name="1895624687%2FPackages%2F-**********" anchor-label="org.gradle.api.tasks.options" id="1895624687%2FPackages%2F-**********" data-filterable-set=":docs/java_api"></a>
    <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
      <div>
        <div class="main-subrow ">
          <div class=""><span class="inline-flex">
              <div><a href="gradle/org.gradle.api.tasks.options/index.html">org.gradle.api.tasks.options</a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="1895624687%2FPackages%2F-**********"></span>
                <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
              </span></span></div>
          <div class="pull-right">
            <div class="platform-tags no-gutters">
              <div class="platform-tag jvm-like">API</div>
            </div>
          </div>
        </div>
        <div><span class="brief-comment"><a data-name="1895624687%2FPackages%2F-**********" anchor-label="org.gradle.api.tasks.options" id="1895624687%2FPackages%2F-**********" data-filterable-set=":docs/java_api"></a>
            <p class="paragraph">Annotations for exposing task properties as command line options.</p>
          </span></div>
      </div>
    </div>
<a data-name="-1959681961%2FPackages%2F-**********" anchor-label="org.gradle.api.tasks.scala" id="-1959681961%2FPackages%2F-**********" data-filterable-set=":docs/java_api"></a>
    <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
      <div>
        <div class="main-subrow ">
          <div class=""><span class="inline-flex">
              <div><a href="gradle/org.gradle.api.tasks.scala/index.html">org.gradle.api.tasks.scala</a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="-1959681961%2FPackages%2F-**********"></span>
                <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
              </span></span></div>
          <div class="pull-right">
            <div class="platform-tags no-gutters">
              <div class="platform-tag jvm-like">API</div>
            </div>
          </div>
        </div>
        <div></div>
      </div>
    </div>
<a data-name="1459048673%2FPackages%2F-**********" anchor-label="org.gradle.api.tasks.testing" id="1459048673%2FPackages%2F-**********" data-filterable-set=":docs/java_api"></a>
    <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
      <div>
        <div class="main-subrow ">
          <div class=""><span class="inline-flex">
              <div><a href="gradle/org.gradle.api.tasks.testing/index.html">org.gradle.api.tasks.testing</a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="1459048673%2FPackages%2F-**********"></span>
                <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
              </span></span></div>
          <div class="pull-right">
            <div class="platform-tags no-gutters">
              <div class="platform-tag jvm-like">API</div>
            </div>
          </div>
        </div>
        <div><span class="brief-comment"><a data-name="1459048673%2FPackages%2F-**********" anchor-label="org.gradle.api.tasks.testing" id="1459048673%2FPackages%2F-**********" data-filterable-set=":docs/java_api"></a>
            <p class="paragraph">The unit testing <a href="gradle/org.gradle.api/-task/index.html">org.gradle.api.Task</a> implementations.</p>
          </span></div>
      </div>
    </div>
<a data-name="-689195231%2FPackages%2F-**********" anchor-label="org.gradle.api.tasks.testing.junit" id="-689195231%2FPackages%2F-**********" data-filterable-set=":docs/java_api"></a>
    <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
      <div>
        <div class="main-subrow ">
          <div class=""><span class="inline-flex">
              <div><a href="gradle/org.gradle.api.tasks.testing.junit/index.html">org.gradle.api.tasks.testing.junit</a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="-689195231%2FPackages%2F-**********"></span>
                <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
              </span></span></div>
          <div class="pull-right">
            <div class="platform-tags no-gutters">
              <div class="platform-tag jvm-like">API</div>
            </div>
          </div>
        </div>
        <div><span class="brief-comment"><a data-name="-689195231%2FPackages%2F-**********" anchor-label="org.gradle.api.tasks.testing.junit" id="-689195231%2FPackages%2F-**********" data-filterable-set=":docs/java_api"></a>
            <p class="paragraph">JUnit specific testing classes.</p>
          </span></div>
      </div>
    </div>
<a data-name="-914688300%2FPackages%2F-**********" anchor-label="org.gradle.api.tasks.testing.junitplatform" id="-914688300%2FPackages%2F-**********" data-filterable-set=":docs/java_api"></a>
    <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
      <div>
        <div class="main-subrow ">
          <div class=""><span class="inline-flex">
              <div><a href="gradle/org.gradle.api.tasks.testing.junitplatform/index.html">org.gradle.api.tasks.testing.junitplatform</a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="-914688300%2FPackages%2F-**********"></span>
                <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
              </span></span></div>
          <div class="pull-right">
            <div class="platform-tags no-gutters">
              <div class="platform-tag jvm-like">API</div>
            </div>
          </div>
        </div>
        <div></div>
      </div>
    </div>
<a data-name="-1233886702%2FPackages%2F-**********" anchor-label="org.gradle.api.tasks.testing.logging" id="-1233886702%2FPackages%2F-**********" data-filterable-set=":docs/java_api"></a>
    <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
      <div>
        <div class="main-subrow ">
          <div class=""><span class="inline-flex">
              <div><a href="gradle/org.gradle.api.tasks.testing.logging/index.html">org.gradle.api.tasks.testing.logging</a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="-1233886702%2FPackages%2F-**********"></span>
                <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
              </span></span></div>
          <div class="pull-right">
            <div class="platform-tags no-gutters">
              <div class="platform-tag jvm-like">API</div>
            </div>
          </div>
        </div>
        <div><span class="brief-comment"><a data-name="-1233886702%2FPackages%2F-**********" anchor-label="org.gradle.api.tasks.testing.logging" id="-1233886702%2FPackages%2F-**********" data-filterable-set=":docs/java_api"></a>
            <p class="paragraph">Types related to logging of test related information to the console.</p>
          </span></div>
      </div>
    </div>
<a data-name="905247292%2FPackages%2F-**********" anchor-label="org.gradle.api.tasks.testing.testng" id="905247292%2FPackages%2F-**********" data-filterable-set=":docs/java_api"></a>
    <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
      <div>
        <div class="main-subrow ">
          <div class=""><span class="inline-flex">
              <div><a href="gradle/org.gradle.api.tasks.testing.testng/index.html">org.gradle.api.tasks.testing.testng</a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="905247292%2FPackages%2F-**********"></span>
                <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
              </span></span></div>
          <div class="pull-right">
            <div class="platform-tags no-gutters">
              <div class="platform-tag jvm-like">API</div>
            </div>
          </div>
        </div>
        <div><span class="brief-comment"><a data-name="905247292%2FPackages%2F-**********" anchor-label="org.gradle.api.tasks.testing.testng" id="905247292%2FPackages%2F-**********" data-filterable-set=":docs/java_api"></a>
            <p class="paragraph">TestNG specific testing classes.</p>
          </span></div>
      </div>
    </div>
<a data-name="603819733%2FPackages%2F-**********" anchor-label="org.gradle.api.tasks.util" id="603819733%2FPackages%2F-**********" data-filterable-set=":docs/java_api"></a>
    <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
      <div>
        <div class="main-subrow ">
          <div class=""><span class="inline-flex">
              <div><a href="gradle/org.gradle.api.tasks.util/index.html">org.gradle.api.tasks.util</a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="603819733%2FPackages%2F-**********"></span>
                <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
              </span></span></div>
          <div class="pull-right">
            <div class="platform-tags no-gutters">
              <div class="platform-tag jvm-like">API</div>
            </div>
          </div>
        </div>
        <div><span class="brief-comment"><a data-name="603819733%2FPackages%2F-**********" anchor-label="org.gradle.api.tasks.util" id="603819733%2FPackages%2F-**********" data-filterable-set=":docs/java_api"></a>
            <p class="paragraph">Utility classes used by the standard task implementations.</p>
          </span></div>
      </div>
    </div>
<a data-name="1613936036%2FPackages%2F-**********" anchor-label="org.gradle.api.tasks.wrapper" id="1613936036%2FPackages%2F-**********" data-filterable-set=":docs/java_api"></a>
    <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
      <div>
        <div class="main-subrow ">
          <div class=""><span class="inline-flex">
              <div><a href="gradle/org.gradle.api.tasks.wrapper/index.html">org.gradle.api.tasks.wrapper</a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="1613936036%2FPackages%2F-**********"></span>
                <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
              </span></span></div>
          <div class="pull-right">
            <div class="platform-tags no-gutters">
              <div class="platform-tag jvm-like">API</div>
            </div>
          </div>
        </div>
        <div><span class="brief-comment"><a data-name="1613936036%2FPackages%2F-**********" anchor-label="org.gradle.api.tasks.wrapper" id="1613936036%2FPackages%2F-**********" data-filterable-set=":docs/java_api"></a>
            <p class="paragraph">The Gradle wrapper <a href="gradle/org.gradle.api/-task/index.html">org.gradle.api.Task</a>.</p>
          </span></div>
      </div>
    </div>
<a data-name="894702299%2FPackages%2F-**********" anchor-label="org.gradle.api.toolchain.management" id="894702299%2FPackages%2F-**********" data-filterable-set=":docs/java_api"></a>
    <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
      <div>
        <div class="main-subrow ">
          <div class=""><span class="inline-flex">
              <div><a href="gradle/org.gradle.api.toolchain.management/index.html">org.gradle.api.toolchain.management</a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="894702299%2FPackages%2F-**********"></span>
                <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
              </span></span></div>
          <div class="pull-right">
            <div class="platform-tags no-gutters">
              <div class="platform-tag jvm-like">API</div>
            </div>
          </div>
        </div>
        <div><span class="brief-comment"><a data-name="894702299%2FPackages%2F-**********" anchor-label="org.gradle.api.toolchain.management" id="894702299%2FPackages%2F-**********" data-filterable-set=":docs/java_api"></a>
            <p class="paragraph">APIs to influence how toolchains are resolved.</p>
          </span></div>
      </div>
    </div>
<a data-name="1444641815%2FPackages%2F-**********" anchor-label="org.gradle.authentication" id="1444641815%2FPackages%2F-**********" data-filterable-set=":docs/java_api"></a>
    <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
      <div>
        <div class="main-subrow ">
          <div class=""><span class="inline-flex">
              <div><a href="gradle/org.gradle.authentication/index.html">org.gradle.authentication</a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="1444641815%2FPackages%2F-**********"></span>
                <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
              </span></span></div>
          <div class="pull-right">
            <div class="platform-tags no-gutters">
              <div class="platform-tag jvm-like">API</div>
            </div>
          </div>
        </div>
        <div><span class="brief-comment"><a data-name="1444641815%2FPackages%2F-**********" anchor-label="org.gradle.authentication" id="1444641815%2FPackages%2F-**********" data-filterable-set=":docs/java_api"></a>
            <p class="paragraph">Classes related to transport authentication protocols.</p>
          </span></div>
      </div>
    </div>
<a data-name="-1715586842%2FPackages%2F-**********" anchor-label="org.gradle.authentication.aws" id="-1715586842%2FPackages%2F-**********" data-filterable-set=":docs/java_api"></a>
    <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
      <div>
        <div class="main-subrow ">
          <div class=""><span class="inline-flex">
              <div><a href="gradle/org.gradle.authentication.aws/index.html">org.gradle.authentication.aws</a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="-1715586842%2FPackages%2F-**********"></span>
                <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
              </span></span></div>
          <div class="pull-right">
            <div class="platform-tags no-gutters">
              <div class="platform-tag jvm-like">API</div>
            </div>
          </div>
        </div>
        <div><span class="brief-comment"><a data-name="-1715586842%2FPackages%2F-**********" anchor-label="org.gradle.authentication.aws" id="-1715586842%2FPackages%2F-**********" data-filterable-set=":docs/java_api"></a>
            <p class="paragraph">Classes related to transport authentication protocols for S3.</p>
          </span></div>
      </div>
    </div>
<a data-name="581458179%2FPackages%2F-**********" anchor-label="org.gradle.authentication.http" id="581458179%2FPackages%2F-**********" data-filterable-set=":docs/java_api"></a>
    <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
      <div>
        <div class="main-subrow ">
          <div class=""><span class="inline-flex">
              <div><a href="gradle/org.gradle.authentication.http/index.html">org.gradle.authentication.http</a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="581458179%2FPackages%2F-**********"></span>
                <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
              </span></span></div>
          <div class="pull-right">
            <div class="platform-tags no-gutters">
              <div class="platform-tag jvm-like">API</div>
            </div>
          </div>
        </div>
        <div><span class="brief-comment"><a data-name="581458179%2FPackages%2F-**********" anchor-label="org.gradle.authentication.http" id="581458179%2FPackages%2F-**********" data-filterable-set=":docs/java_api"></a>
            <p class="paragraph">Classes related to transport authentication protocols for HTTP.</p>
          </span></div>
      </div>
    </div>
<a data-name="-1443380769%2FPackages%2F-**********" anchor-label="org.gradle.build.event" id="-1443380769%2FPackages%2F-**********" data-filterable-set=":docs/java_api"></a>
    <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
      <div>
        <div class="main-subrow ">
          <div class=""><span class="inline-flex">
              <div><a href="gradle/org.gradle.build.event/index.html">org.gradle.build.event</a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="-1443380769%2FPackages%2F-**********"></span>
                <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
              </span></span></div>
          <div class="pull-right">
            <div class="platform-tags no-gutters">
              <div class="platform-tag jvm-like">API</div>
            </div>
          </div>
        </div>
        <div><span class="brief-comment"><a data-name="-1443380769%2FPackages%2F-**********" anchor-label="org.gradle.build.event" id="-1443380769%2FPackages%2F-**********" data-filterable-set=":docs/java_api"></a>
            <p class="paragraph">Types for receiving build events.</p>
          </span></div>
      </div>
    </div>
<a data-name="-1842318589%2FPackages%2F-**********" anchor-label="org.gradle.buildinit" id="-1842318589%2FPackages%2F-**********" data-filterable-set=":docs/java_api"></a>
    <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
      <div>
        <div class="main-subrow ">
          <div class=""><span class="inline-flex">
              <div><a href="gradle/org.gradle.buildinit/index.html">org.gradle.buildinit</a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="-1842318589%2FPackages%2F-**********"></span>
                <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
              </span></span></div>
          <div class="pull-right">
            <div class="platform-tags no-gutters">
              <div class="platform-tag jvm-like">API</div>
            </div>
          </div>
        </div>
        <div><span class="brief-comment"><a data-name="-1842318589%2FPackages%2F-**********" anchor-label="org.gradle.buildinit" id="-1842318589%2FPackages%2F-**********" data-filterable-set=":docs/java_api"></a>
            <p class="paragraph">Build init plugins and tasks. This powers &quot;gradle init&quot;.</p>
          </span></div>
      </div>
    </div>
<a data-name="-1546113227%2FPackages%2F-**********" anchor-label="org.gradle.buildinit.plugins" id="-1546113227%2FPackages%2F-**********" data-filterable-set=":docs/java_api"></a>
    <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
      <div>
        <div class="main-subrow ">
          <div class=""><span class="inline-flex">
              <div><a href="gradle/org.gradle.buildinit.plugins/index.html">org.gradle.buildinit.plugins</a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="-1546113227%2FPackages%2F-**********"></span>
                <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
              </span></span></div>
          <div class="pull-right">
            <div class="platform-tags no-gutters">
              <div class="platform-tag jvm-like">API</div>
            </div>
          </div>
        </div>
        <div><span class="brief-comment"><a data-name="-1546113227%2FPackages%2F-**********" anchor-label="org.gradle.buildinit.plugins" id="-1546113227%2FPackages%2F-**********" data-filterable-set=":docs/java_api"></a>
            <p class="paragraph">Build init plugins.</p>
          </span></div>
      </div>
    </div>
<a data-name="-665708957%2FPackages%2F-**********" anchor-label="org.gradle.buildinit.tasks" id="-665708957%2FPackages%2F-**********" data-filterable-set=":docs/java_api"></a>
    <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
      <div>
        <div class="main-subrow ">
          <div class=""><span class="inline-flex">
              <div><a href="gradle/org.gradle.buildinit.tasks/index.html">org.gradle.buildinit.tasks</a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="-665708957%2FPackages%2F-**********"></span>
                <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
              </span></span></div>
          <div class="pull-right">
            <div class="platform-tags no-gutters">
              <div class="platform-tag jvm-like">API</div>
            </div>
          </div>
        </div>
        <div><span class="brief-comment"><a data-name="-665708957%2FPackages%2F-**********" anchor-label="org.gradle.buildinit.tasks" id="-665708957%2FPackages%2F-**********" data-filterable-set=":docs/java_api"></a>
            <p class="paragraph">Build init plugins.</p>
          </span></div>
      </div>
    </div>
<a data-name="910495684%2FPackages%2F-**********" anchor-label="org.gradle.caching" id="910495684%2FPackages%2F-**********" data-filterable-set=":docs/java_api"></a>
    <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
      <div>
        <div class="main-subrow ">
          <div class=""><span class="inline-flex">
              <div><a href="gradle/org.gradle.caching/index.html">org.gradle.caching</a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="910495684%2FPackages%2F-**********"></span>
                <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
              </span></span></div>
          <div class="pull-right">
            <div class="platform-tags no-gutters">
              <div class="platform-tag jvm-like">API</div>
            </div>
          </div>
        </div>
        <div><span class="brief-comment"><a data-name="910495684%2FPackages%2F-**********" anchor-label="org.gradle.caching" id="910495684%2FPackages%2F-**********" data-filterable-set=":docs/java_api"></a>
            <p class="paragraph">Classes for build caches.</p>
          </span></div>
      </div>
    </div>
<a data-name="-1841024564%2FPackages%2F-**********" anchor-label="org.gradle.caching.configuration" id="-1841024564%2FPackages%2F-**********" data-filterable-set=":docs/java_api"></a>
    <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
      <div>
        <div class="main-subrow ">
          <div class=""><span class="inline-flex">
              <div><a href="gradle/org.gradle.caching.configuration/index.html">org.gradle.caching.configuration</a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="-1841024564%2FPackages%2F-**********"></span>
                <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
              </span></span></div>
          <div class="pull-right">
            <div class="platform-tags no-gutters">
              <div class="platform-tag jvm-like">API</div>
            </div>
          </div>
        </div>
        <div><span class="brief-comment"><a data-name="-1841024564%2FPackages%2F-**********" anchor-label="org.gradle.caching.configuration" id="-1841024564%2FPackages%2F-**********" data-filterable-set=":docs/java_api"></a>
            <p class="paragraph">Classes for configuring build caches.</p>
          </span></div>
      </div>
    </div>
<a data-name="-205977226%2FPackages%2F-**********" anchor-label="org.gradle.caching.http" id="-205977226%2FPackages%2F-**********" data-filterable-set=":docs/java_api"></a>
    <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
      <div>
        <div class="main-subrow ">
          <div class=""><span class="inline-flex">
              <div><a href="gradle/org.gradle.caching.http/index.html">org.gradle.caching.http</a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="-205977226%2FPackages%2F-**********"></span>
                <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
              </span></span></div>
          <div class="pull-right">
            <div class="platform-tags no-gutters">
              <div class="platform-tag jvm-like">API</div>
            </div>
          </div>
        </div>
        <div><span class="brief-comment"><a data-name="-205977226%2FPackages%2F-**********" anchor-label="org.gradle.caching.http" id="-205977226%2FPackages%2F-**********" data-filterable-set=":docs/java_api"></a>
            <p class="paragraph">Classes for HTTP build cache service.</p>
          </span></div>
      </div>
    </div>
<a data-name="517961729%2FPackages%2F-**********" anchor-label="org.gradle.caching.local" id="517961729%2FPackages%2F-**********" data-filterable-set=":docs/java_api"></a>
    <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
      <div>
        <div class="main-subrow ">
          <div class=""><span class="inline-flex">
              <div><a href="gradle/org.gradle.caching.local/index.html">org.gradle.caching.local</a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="517961729%2FPackages%2F-**********"></span>
                <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
              </span></span></div>
          <div class="pull-right">
            <div class="platform-tags no-gutters">
              <div class="platform-tag jvm-like">API</div>
            </div>
          </div>
        </div>
        <div><span class="brief-comment"><a data-name="517961729%2FPackages%2F-**********" anchor-label="org.gradle.caching.local" id="517961729%2FPackages%2F-**********" data-filterable-set=":docs/java_api"></a>
            <p class="paragraph">Classes for local build cache services.</p>
          </span></div>
      </div>
    </div>
<a data-name="-196360938%2FPackages%2F-**********" anchor-label="org.gradle.concurrent" id="-196360938%2FPackages%2F-**********" data-filterable-set=":docs/java_api"></a>
    <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
      <div>
        <div class="main-subrow ">
          <div class=""><span class="inline-flex">
              <div><a href="gradle/org.gradle.concurrent/index.html">org.gradle.concurrent</a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="-196360938%2FPackages%2F-**********"></span>
                <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
              </span></span></div>
          <div class="pull-right">
            <div class="platform-tags no-gutters">
              <div class="platform-tag jvm-like">API</div>
            </div>
          </div>
        </div>
        <div><span class="brief-comment"><a data-name="-196360938%2FPackages%2F-**********" anchor-label="org.gradle.concurrent" id="-196360938%2FPackages%2F-**********" data-filterable-set=":docs/java_api"></a>
            <p class="paragraph">Classes related to Gradle parallelism and concurrency.</p>
          </span></div>
      </div>
    </div>
<a data-name="-1005075630%2FPackages%2F-**********" anchor-label="org.gradle.external.javadoc" id="-1005075630%2FPackages%2F-**********" data-filterable-set=":docs/java_api"></a>
    <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
      <div>
        <div class="main-subrow ">
          <div class=""><span class="inline-flex">
              <div><a href="gradle/org.gradle.external.javadoc/index.html">org.gradle.external.javadoc</a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="-1005075630%2FPackages%2F-**********"></span>
                <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
              </span></span></div>
          <div class="pull-right">
            <div class="platform-tags no-gutters">
              <div class="platform-tag jvm-like">API</div>
            </div>
          </div>
        </div>
        <div><span class="brief-comment"><a data-name="-1005075630%2FPackages%2F-**********" anchor-label="org.gradle.external.javadoc" id="-1005075630%2FPackages%2F-**********" data-filterable-set=":docs/java_api"></a>
            <p class="paragraph">Classes to run Javadoc.</p>
          </span></div>
      </div>
    </div>
<a data-name="859931065%2FPackages%2F-**********" anchor-label="org.gradle.ide.visualstudio" id="859931065%2FPackages%2F-**********" data-filterable-set=":docs/java_api"></a>
    <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
      <div>
        <div class="main-subrow ">
          <div class=""><span class="inline-flex">
              <div><a href="gradle/org.gradle.ide.visualstudio/index.html">org.gradle.ide.visualstudio</a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="859931065%2FPackages%2F-**********"></span>
                <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
              </span></span></div>
          <div class="pull-right">
            <div class="platform-tags no-gutters">
              <div class="platform-tag jvm-like">API</div>
            </div>
          </div>
        </div>
        <div><span class="brief-comment"><a data-name="859931065%2FPackages%2F-**********" anchor-label="org.gradle.ide.visualstudio" id="859931065%2FPackages%2F-**********" data-filterable-set=":docs/java_api"></a>
            <p class="paragraph">Model classes for visual studio.</p>
          </span></div>
      </div>
    </div>
<a data-name="-895342613%2FPackages%2F-**********" anchor-label="org.gradle.ide.visualstudio.plugins" id="-895342613%2FPackages%2F-**********" data-filterable-set=":docs/java_api"></a>
    <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
      <div>
        <div class="main-subrow ">
          <div class=""><span class="inline-flex">
              <div><a href="gradle/org.gradle.ide.visualstudio.plugins/index.html">org.gradle.ide.visualstudio.plugins</a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="-895342613%2FPackages%2F-**********"></span>
                <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
              </span></span></div>
          <div class="pull-right">
            <div class="platform-tags no-gutters">
              <div class="platform-tag jvm-like">API</div>
            </div>
          </div>
        </div>
        <div><span class="brief-comment"><a data-name="-895342613%2FPackages%2F-**********" anchor-label="org.gradle.ide.visualstudio.plugins" id="-895342613%2FPackages%2F-**********" data-filterable-set=":docs/java_api"></a>
            <p class="paragraph">Plugins for Visual Studio integration.</p>
          </span></div>
      </div>
    </div>
<a data-name="-611400551%2FPackages%2F-**********" anchor-label="org.gradle.ide.visualstudio.tasks" id="-611400551%2FPackages%2F-**********" data-filterable-set=":docs/java_api"></a>
    <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
      <div>
        <div class="main-subrow ">
          <div class=""><span class="inline-flex">
              <div><a href="gradle/org.gradle.ide.visualstudio.tasks/index.html">org.gradle.ide.visualstudio.tasks</a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="-611400551%2FPackages%2F-**********"></span>
                <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
              </span></span></div>
          <div class="pull-right">
            <div class="platform-tags no-gutters">
              <div class="platform-tag jvm-like">API</div>
            </div>
          </div>
        </div>
        <div><span class="brief-comment"><a data-name="-611400551%2FPackages%2F-**********" anchor-label="org.gradle.ide.visualstudio.tasks" id="-611400551%2FPackages%2F-**********" data-filterable-set=":docs/java_api"></a>
            <p class="paragraph">Task classes for visual studio.</p>
          </span></div>
      </div>
    </div>
<a data-name="395421414%2FPackages%2F-**********" anchor-label="org.gradle.ide.xcode" id="395421414%2FPackages%2F-**********" data-filterable-set=":docs/java_api"></a>
    <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
      <div>
        <div class="main-subrow ">
          <div class=""><span class="inline-flex">
              <div><a href="gradle/org.gradle.ide.xcode/index.html">org.gradle.ide.xcode</a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="395421414%2FPackages%2F-**********"></span>
                <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
              </span></span></div>
          <div class="pull-right">
            <div class="platform-tags no-gutters">
              <div class="platform-tag jvm-like">API</div>
            </div>
          </div>
        </div>
        <div><span class="brief-comment"><a data-name="395421414%2FPackages%2F-**********" anchor-label="org.gradle.ide.xcode" id="395421414%2FPackages%2F-**********" data-filterable-set=":docs/java_api"></a>
            <p class="paragraph">Model classes for XCode.</p>
          </span></div>
      </div>
    </div>
<a data-name="1392824344%2FPackages%2F-**********" anchor-label="org.gradle.ide.xcode.plugins" id="1392824344%2FPackages%2F-**********" data-filterable-set=":docs/java_api"></a>
    <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
      <div>
        <div class="main-subrow ">
          <div class=""><span class="inline-flex">
              <div><a href="gradle/org.gradle.ide.xcode.plugins/index.html">org.gradle.ide.xcode.plugins</a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="1392824344%2FPackages%2F-**********"></span>
                <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
              </span></span></div>
          <div class="pull-right">
            <div class="platform-tags no-gutters">
              <div class="platform-tag jvm-like">API</div>
            </div>
          </div>
        </div>
        <div><span class="brief-comment"><a data-name="1392824344%2FPackages%2F-**********" anchor-label="org.gradle.ide.xcode.plugins" id="1392824344%2FPackages%2F-**********" data-filterable-set=":docs/java_api"></a>
            <p class="paragraph">Plugins for XCode integration.</p>
          </span></div>
      </div>
    </div>
<a data-name="-689466362%2FPackages%2F-**********" anchor-label="org.gradle.ide.xcode.tasks" id="-689466362%2FPackages%2F-**********" data-filterable-set=":docs/java_api"></a>
    <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
      <div>
        <div class="main-subrow ">
          <div class=""><span class="inline-flex">
              <div><a href="gradle/org.gradle.ide.xcode.tasks/index.html">org.gradle.ide.xcode.tasks</a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="-689466362%2FPackages%2F-**********"></span>
                <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
              </span></span></div>
          <div class="pull-right">
            <div class="platform-tags no-gutters">
              <div class="platform-tag jvm-like">API</div>
            </div>
          </div>
        </div>
        <div><span class="brief-comment"><a data-name="-689466362%2FPackages%2F-**********" anchor-label="org.gradle.ide.xcode.tasks" id="-689466362%2FPackages%2F-**********" data-filterable-set=":docs/java_api"></a>
            <p class="paragraph">Task classes for XCode.</p>
          </span></div>
      </div>
    </div>
<a data-name="1099616305%2FPackages%2F-**********" anchor-label="org.gradle.ivy" id="1099616305%2FPackages%2F-**********" data-filterable-set=":docs/java_api"></a>
    <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
      <div>
        <div class="main-subrow ">
          <div class=""><span class="inline-flex">
              <div><a href="gradle/org.gradle.ivy/index.html">org.gradle.ivy</a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="1099616305%2FPackages%2F-**********"></span>
                <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
              </span></span></div>
          <div class="pull-right">
            <div class="platform-tags no-gutters">
              <div class="platform-tag jvm-like">API</div>
            </div>
          </div>
        </div>
        <div><span class="brief-comment"><a data-name="1099616305%2FPackages%2F-**********" anchor-label="org.gradle.ivy" id="1099616305%2FPackages%2F-**********" data-filterable-set=":docs/java_api"></a>
            <p class="paragraph">Component types for Ivy modules.</p>
          </span></div>
      </div>
    </div>
<a data-name="-1156715610%2FPackages%2F-**********" anchor-label="org.gradle.jvm" id="-1156715610%2FPackages%2F-**********" data-filterable-set=":docs/java_api"></a>
    <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
      <div>
        <div class="main-subrow ">
          <div class=""><span class="inline-flex">
              <div><a href="gradle/org.gradle.jvm/index.html">org.gradle.jvm</a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="-1156715610%2FPackages%2F-**********"></span>
                <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
              </span></span></div>
          <div class="pull-right">
            <div class="platform-tags no-gutters">
              <div class="platform-tag jvm-like">API</div>
            </div>
          </div>
        </div>
        <div><span class="brief-comment"><a data-name="-1156715610%2FPackages%2F-**********" anchor-label="org.gradle.jvm" id="-1156715610%2FPackages%2F-**********" data-filterable-set=":docs/java_api"></a>
            <p class="paragraph">Types for support of JVM runtime.</p>
          </span></div>
      </div>
    </div>
<a data-name="2059391522%2FPackages%2F-**********" anchor-label="org.gradle.jvm.application.scripts" id="2059391522%2FPackages%2F-**********" data-filterable-set=":docs/java_api"></a>
    <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
      <div>
        <div class="main-subrow ">
          <div class=""><span class="inline-flex">
              <div><a href="gradle/org.gradle.jvm.application.scripts/index.html">org.gradle.jvm.application.scripts</a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="2059391522%2FPackages%2F-**********"></span>
                <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
              </span></span></div>
          <div class="pull-right">
            <div class="platform-tags no-gutters">
              <div class="platform-tag jvm-like">API</div>
            </div>
          </div>
        </div>
        <div><span class="brief-comment"><a data-name="2059391522%2FPackages%2F-**********" anchor-label="org.gradle.jvm.application.scripts" id="2059391522%2FPackages%2F-**********" data-filterable-set=":docs/java_api"></a>
            <p class="paragraph">Classes that enable JVM application script generation.</p>
          </span></div>
      </div>
    </div>
<a data-name="369456936%2FPackages%2F-**********" anchor-label="org.gradle.jvm.application.tasks" id="369456936%2FPackages%2F-**********" data-filterable-set=":docs/java_api"></a>
    <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
      <div>
        <div class="main-subrow ">
          <div class=""><span class="inline-flex">
              <div><a href="gradle/org.gradle.jvm.application.tasks/index.html">org.gradle.jvm.application.tasks</a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="369456936%2FPackages%2F-**********"></span>
                <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
              </span></span></div>
          <div class="pull-right">
            <div class="platform-tags no-gutters">
              <div class="platform-tag jvm-like">API</div>
            </div>
          </div>
        </div>
        <div><span class="brief-comment"><a data-name="369456936%2FPackages%2F-**********" anchor-label="org.gradle.jvm.application.tasks" id="369456936%2FPackages%2F-**********" data-filterable-set=":docs/java_api"></a>
            <p class="paragraph">Tasks for the JVM application plugin.</p>
          </span></div>
      </div>
    </div>
<a data-name="-1043511098%2FPackages%2F-**********" anchor-label="org.gradle.jvm.tasks" id="-1043511098%2FPackages%2F-**********" data-filterable-set=":docs/java_api"></a>
    <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
      <div>
        <div class="main-subrow ">
          <div class=""><span class="inline-flex">
              <div><a href="gradle/org.gradle.jvm.tasks/index.html">org.gradle.jvm.tasks</a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="-1043511098%2FPackages%2F-**********"></span>
                <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
              </span></span></div>
          <div class="pull-right">
            <div class="platform-tags no-gutters">
              <div class="platform-tag jvm-like">API</div>
            </div>
          </div>
        </div>
        <div><span class="brief-comment"><a data-name="-1043511098%2FPackages%2F-**********" anchor-label="org.gradle.jvm.tasks" id="-1043511098%2FPackages%2F-**********" data-filterable-set=":docs/java_api"></a>
            <p class="paragraph">Tasks that add support for JVM runtime.</p>
          </span></div>
      </div>
    </div>
<a data-name="-2140618079%2FPackages%2F-**********" anchor-label="org.gradle.jvm.toolchain" id="-2140618079%2FPackages%2F-**********" data-filterable-set=":docs/java_api"></a>
    <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
      <div>
        <div class="main-subrow ">
          <div class=""><span class="inline-flex">
              <div><a href="gradle/org.gradle.jvm.toolchain/index.html">org.gradle.jvm.toolchain</a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="-2140618079%2FPackages%2F-**********"></span>
                <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
              </span></span></div>
          <div class="pull-right">
            <div class="platform-tags no-gutters">
              <div class="platform-tag jvm-like">API</div>
            </div>
          </div>
        </div>
        <div><span class="brief-comment"><a data-name="-2140618079%2FPackages%2F-**********" anchor-label="org.gradle.jvm.toolchain" id="-2140618079%2FPackages%2F-**********" data-filterable-set=":docs/java_api"></a>
            <p class="paragraph">Defines tools that can build things that run on the JVM.</p>
          </span></div>
      </div>
    </div>
<a data-name="1309852015%2FPackages%2F-1867656071" anchor-label="org.gradle.kotlin.dsl" id="1309852015%2FPackages%2F-1867656071" data-filterable-set=":docs/kotlin_dsl"></a>
    <div class="table-row" data-filterable-current=":docs/kotlin_dsl" data-filterable-set=":docs/kotlin_dsl">
      <div>
        <div class="main-subrow ">
          <div class=""><span class="inline-flex">
              <div><a href="gradle/org.gradle.kotlin.dsl/index.html">org.gradle.kotlin.dsl</a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="1309852015%2FPackages%2F-1867656071"></span>
                <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
              </span></span></div>
          <div class="pull-right">
            <div class="platform-tags no-gutters">
              <div class="platform-tag jvm-like">DSL</div>
            </div>
          </div>
        </div>
        <div><span class="brief-comment"><a data-name="1309852015%2FPackages%2F-1867656071" anchor-label="org.gradle.kotlin.dsl" id="1309852015%2FPackages%2F-1867656071" data-filterable-set=":docs/kotlin_dsl"></a>
            <p class="paragraph">The <code class="lang-kotlin">org.gradle.kotlin.dsl</code> package contains the Gradle Kotlin DSL public API.</p>
          </span></div>
      </div>
    </div>
<a data-name="-1546836589%2FPackages%2F-1867656071" anchor-label="org.gradle.kotlin.dsl.precompile" id="-1546836589%2FPackages%2F-1867656071" data-filterable-set=":docs/kotlin_dsl"></a>
    <div class="table-row" data-filterable-current=":docs/kotlin_dsl" data-filterable-set=":docs/kotlin_dsl">
      <div>
        <div class="main-subrow ">
          <div class=""><span class="inline-flex">
              <div><a href="gradle/org.gradle.kotlin.dsl.precompile/index.html">org.gradle.kotlin.dsl.precompile</a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="-1546836589%2FPackages%2F-1867656071"></span>
                <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
              </span></span></div>
          <div class="pull-right">
            <div class="platform-tags no-gutters">
              <div class="platform-tag jvm-like">DSL</div>
            </div>
          </div>
        </div>
        <div></div>
      </div>
    </div>
<a data-name="20440759%2FPackages%2F-**********" anchor-label="org.gradle.language" id="20440759%2FPackages%2F-**********" data-filterable-set=":docs/java_api"></a>
    <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
      <div>
        <div class="main-subrow ">
          <div class=""><span class="inline-flex">
              <div><a href="gradle/org.gradle.language/index.html">org.gradle.language</a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="20440759%2FPackages%2F-**********"></span>
                <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
              </span></span></div>
          <div class="pull-right">
            <div class="platform-tags no-gutters">
              <div class="platform-tag jvm-like">API</div>
            </div>
          </div>
        </div>
        <div><span class="brief-comment"><a data-name="20440759%2FPackages%2F-**********" anchor-label="org.gradle.language" id="20440759%2FPackages%2F-**********" data-filterable-set=":docs/java_api"></a>
            <p class="paragraph">Model classes for managing language sources.</p>
          </span></div>
      </div>
    </div>
<a data-name="1270195817%2FPackages%2F-**********" anchor-label="org.gradle.language.assembler" id="1270195817%2FPackages%2F-**********" data-filterable-set=":docs/java_api"></a>
    <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
      <div>
        <div class="main-subrow ">
          <div class=""><span class="inline-flex">
              <div><a href="gradle/org.gradle.language.assembler/index.html">org.gradle.language.assembler</a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="1270195817%2FPackages%2F-**********"></span>
                <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
              </span></span></div>
          <div class="pull-right">
            <div class="platform-tags no-gutters">
              <div class="platform-tag jvm-like">API</div>
            </div>
          </div>
        </div>
        <div><span class="brief-comment"><a data-name="1270195817%2FPackages%2F-**********" anchor-label="org.gradle.language.assembler" id="1270195817%2FPackages%2F-**********" data-filterable-set=":docs/java_api"></a>
            <p class="paragraph">Model classes for building from Assembler language sources.</p>
          </span></div>
      </div>
    </div>
<a data-name="-1111581541%2FPackages%2F-**********" anchor-label="org.gradle.language.assembler.plugins" id="-1111581541%2FPackages%2F-**********" data-filterable-set=":docs/java_api"></a>
    <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
      <div>
        <div class="main-subrow ">
          <div class=""><span class="inline-flex">
              <div><a href="gradle/org.gradle.language.assembler.plugins/index.html">org.gradle.language.assembler.plugins</a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="-1111581541%2FPackages%2F-**********"></span>
                <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
              </span></span></div>
          <div class="pull-right">
            <div class="platform-tags no-gutters">
              <div class="platform-tag jvm-like">API</div>
            </div>
          </div>
        </div>
        <div><span class="brief-comment"><a data-name="-1111581541%2FPackages%2F-**********" anchor-label="org.gradle.language.assembler.plugins" id="-1111581541%2FPackages%2F-**********" data-filterable-set=":docs/java_api"></a>
            <p class="paragraph">Plugins for building from Assembler language sources.</p>
          </span></div>
      </div>
    </div>
<a data-name="-1952406199%2FPackages%2F-**********" anchor-label="org.gradle.language.assembler.tasks" id="-1952406199%2FPackages%2F-**********" data-filterable-set=":docs/java_api"></a>
    <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
      <div>
        <div class="main-subrow ">
          <div class=""><span class="inline-flex">
              <div><a href="gradle/org.gradle.language.assembler.tasks/index.html">org.gradle.language.assembler.tasks</a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="-1952406199%2FPackages%2F-**********"></span>
                <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
              </span></span></div>
          <div class="pull-right">
            <div class="platform-tags no-gutters">
              <div class="platform-tag jvm-like">API</div>
            </div>
          </div>
        </div>
        <div><span class="brief-comment"><a data-name="-1952406199%2FPackages%2F-**********" anchor-label="org.gradle.language.assembler.tasks" id="-1952406199%2FPackages%2F-**********" data-filterable-set=":docs/java_api"></a>
            <p class="paragraph">Tasks for assembling Assembler sources for a native runtime.</p>
          </span></div>
      </div>
    </div>
<a data-name="2139019116%2FPackages%2F-**********" anchor-label="org.gradle.language.base" id="2139019116%2FPackages%2F-**********" data-filterable-set=":docs/java_api"></a>
    <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
      <div>
        <div class="main-subrow ">
          <div class=""><span class="inline-flex">
              <div><a href="gradle/org.gradle.language.base/index.html">org.gradle.language.base</a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="2139019116%2FPackages%2F-**********"></span>
                <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
              </span></span></div>
          <div class="pull-right">
            <div class="platform-tags no-gutters">
              <div class="platform-tag jvm-like">API</div>
            </div>
          </div>
        </div>
        <div><span class="brief-comment"><a data-name="2139019116%2FPackages%2F-**********" anchor-label="org.gradle.language.base" id="2139019116%2FPackages%2F-**********" data-filterable-set=":docs/java_api"></a>
            <p class="paragraph">General purpose types for language support.</p>
          </span></div>
      </div>
    </div>
<a data-name="-1410921064%2FPackages%2F-**********" anchor-label="org.gradle.language.base.artifact" id="-1410921064%2FPackages%2F-**********" data-filterable-set=":docs/java_api"></a>
    <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
      <div>
        <div class="main-subrow ">
          <div class=""><span class="inline-flex">
              <div><a href="gradle/org.gradle.language.base.artifact/index.html">org.gradle.language.base.artifact</a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="-1410921064%2FPackages%2F-**********"></span>
                <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
              </span></span></div>
          <div class="pull-right">
            <div class="platform-tags no-gutters">
              <div class="platform-tag jvm-like">API</div>
            </div>
          </div>
        </div>
        <div><span class="brief-comment"><a data-name="-1410921064%2FPackages%2F-**********" anchor-label="org.gradle.language.base.artifact" id="-1410921064%2FPackages%2F-**********" data-filterable-set=":docs/java_api"></a>
            <p class="paragraph">Classes representing artifacts relevant to languages in general.</p>
          </span></div>
      </div>
    </div>
<a data-name="2112559985%2FPackages%2F-**********" anchor-label="org.gradle.language.base.compile" id="2112559985%2FPackages%2F-**********" data-filterable-set=":docs/java_api"></a>
    <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
      <div>
        <div class="main-subrow ">
          <div class=""><span class="inline-flex">
              <div><a href="gradle/org.gradle.language.base.compile/index.html">org.gradle.language.base.compile</a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="2112559985%2FPackages%2F-**********"></span>
                <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
              </span></span></div>
          <div class="pull-right">
            <div class="platform-tags no-gutters">
              <div class="platform-tag jvm-like">API</div>
            </div>
          </div>
        </div>
        <div><span class="brief-comment"><a data-name="2112559985%2FPackages%2F-**********" anchor-label="org.gradle.language.base.compile" id="2112559985%2FPackages%2F-**********" data-filterable-set=":docs/java_api"></a>
            <p class="paragraph">General purpose types for related to compiling.</p>
          </span></div>
      </div>
    </div>
<a data-name="-292283746%2FPackages%2F-**********" anchor-label="org.gradle.language.base.plugins" id="-292283746%2FPackages%2F-**********" data-filterable-set=":docs/java_api"></a>
    <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
      <div>
        <div class="main-subrow ">
          <div class=""><span class="inline-flex">
              <div><a href="gradle/org.gradle.language.base.plugins/index.html">org.gradle.language.base.plugins</a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="-292283746%2FPackages%2F-**********"></span>
                <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
              </span></span></div>
          <div class="pull-right">
            <div class="platform-tags no-gutters">
              <div class="platform-tag jvm-like">API</div>
            </div>
          </div>
        </div>
        <div><span class="brief-comment"><a data-name="-292283746%2FPackages%2F-**********" anchor-label="org.gradle.language.base.plugins" id="-292283746%2FPackages%2F-**********" data-filterable-set=":docs/java_api"></a>
            <p class="paragraph">Base plugins for language support.</p>
          </span></div>
      </div>
    </div>
<a data-name="-1730228042%2FPackages%2F-**********" anchor-label="org.gradle.language.base.sources" id="-1730228042%2FPackages%2F-**********" data-filterable-set=":docs/java_api"></a>
    <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
      <div>
        <div class="main-subrow ">
          <div class=""><span class="inline-flex">
              <div><a href="gradle/org.gradle.language.base.sources/index.html">org.gradle.language.base.sources</a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="-1730228042%2FPackages%2F-**********"></span>
                <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
              </span></span></div>
          <div class="pull-right">
            <div class="platform-tags no-gutters">
              <div class="platform-tag jvm-like">API</div>
            </div>
          </div>
        </div>
        <div><span class="brief-comment"><a data-name="-1730228042%2FPackages%2F-**********" anchor-label="org.gradle.language.base.sources" id="-1730228042%2FPackages%2F-**********" data-filterable-set=":docs/java_api"></a>
            <p class="paragraph">General purpose types for language sources support.</p>
          </span></div>
      </div>
    </div>
<a data-name="-192638164%2FPackages%2F-**********" anchor-label="org.gradle.language.c" id="-192638164%2FPackages%2F-**********" data-filterable-set=":docs/java_api"></a>
    <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
      <div>
        <div class="main-subrow ">
          <div class=""><span class="inline-flex">
              <div><a href="gradle/org.gradle.language.c/index.html">org.gradle.language.c</a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="-192638164%2FPackages%2F-**********"></span>
                <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
              </span></span></div>
          <div class="pull-right">
            <div class="platform-tags no-gutters">
              <div class="platform-tag jvm-like">API</div>
            </div>
          </div>
        </div>
        <div><span class="brief-comment"><a data-name="-192638164%2FPackages%2F-**********" anchor-label="org.gradle.language.c" id="-192638164%2FPackages%2F-**********" data-filterable-set=":docs/java_api"></a>
            <p class="paragraph">Model classes for building from C language sources.</p>
          </span></div>
      </div>
    </div>
<a data-name="-1090382242%2FPackages%2F-**********" anchor-label="org.gradle.language.c.plugins" id="-1090382242%2FPackages%2F-**********" data-filterable-set=":docs/java_api"></a>
    <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
      <div>
        <div class="main-subrow ">
          <div class=""><span class="inline-flex">
              <div><a href="gradle/org.gradle.language.c.plugins/index.html">org.gradle.language.c.plugins</a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="-1090382242%2FPackages%2F-**********"></span>
                <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
              </span></span></div>
          <div class="pull-right">
            <div class="platform-tags no-gutters">
              <div class="platform-tag jvm-like">API</div>
            </div>
          </div>
        </div>
        <div><span class="brief-comment"><a data-name="-1090382242%2FPackages%2F-**********" anchor-label="org.gradle.language.c.plugins" id="-1090382242%2FPackages%2F-**********" data-filterable-set=":docs/java_api"></a>
            <p class="paragraph">Plugins for building from C language sources.</p>
          </span></div>
      </div>
    </div>
<a data-name="1810740172%2FPackages%2F-**********" anchor-label="org.gradle.language.c.tasks" id="1810740172%2FPackages%2F-**********" data-filterable-set=":docs/java_api"></a>
    <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
      <div>
        <div class="main-subrow ">
          <div class=""><span class="inline-flex">
              <div><a href="gradle/org.gradle.language.c.tasks/index.html">org.gradle.language.c.tasks</a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="1810740172%2FPackages%2F-**********"></span>
                <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
              </span></span></div>
          <div class="pull-right">
            <div class="platform-tags no-gutters">
              <div class="platform-tag jvm-like">API</div>
            </div>
          </div>
        </div>
        <div><span class="brief-comment"><a data-name="1810740172%2FPackages%2F-**********" anchor-label="org.gradle.language.c.tasks" id="1810740172%2FPackages%2F-**********" data-filterable-set=":docs/java_api"></a>
            <p class="paragraph">Tasks for compiling C sources for a native runtime.</p>
          </span></div>
      </div>
    </div>
<a data-name="1180284588%2FPackages%2F-**********" anchor-label="org.gradle.language.cpp" id="1180284588%2FPackages%2F-**********" data-filterable-set=":docs/java_api"></a>
    <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
      <div>
        <div class="main-subrow ">
          <div class=""><span class="inline-flex">
              <div><a href="gradle/org.gradle.language.cpp/index.html">org.gradle.language.cpp</a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="1180284588%2FPackages%2F-**********"></span>
                <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
              </span></span></div>
          <div class="pull-right">
            <div class="platform-tags no-gutters">
              <div class="platform-tag jvm-like">API</div>
            </div>
          </div>
        </div>
        <div><span class="brief-comment"><a data-name="1180284588%2FPackages%2F-**********" anchor-label="org.gradle.language.cpp" id="1180284588%2FPackages%2F-**********" data-filterable-set=":docs/java_api"></a>
            <p class="paragraph">Model classes for building from C++ language sources.</p>
          </span></div>
      </div>
    </div>
<a data-name="-381961762%2FPackages%2F-**********" anchor-label="org.gradle.language.cpp.plugins" id="-381961762%2FPackages%2F-**********" data-filterable-set=":docs/java_api"></a>
    <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
      <div>
        <div class="main-subrow ">
          <div class=""><span class="inline-flex">
              <div><a href="gradle/org.gradle.language.cpp.plugins/index.html">org.gradle.language.cpp.plugins</a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="-381961762%2FPackages%2F-**********"></span>
                <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
              </span></span></div>
          <div class="pull-right">
            <div class="platform-tags no-gutters">
              <div class="platform-tag jvm-like">API</div>
            </div>
          </div>
        </div>
        <div><span class="brief-comment"><a data-name="-381961762%2FPackages%2F-**********" anchor-label="org.gradle.language.cpp.plugins" id="-381961762%2FPackages%2F-**********" data-filterable-set=":docs/java_api"></a>
            <p class="paragraph">Plugins for building from C++ language sources.</p>
          </span></div>
      </div>
    </div>
<a data-name="926562124%2FPackages%2F-**********" anchor-label="org.gradle.language.cpp.tasks" id="926562124%2FPackages%2F-**********" data-filterable-set=":docs/java_api"></a>
    <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
      <div>
        <div class="main-subrow ">
          <div class=""><span class="inline-flex">
              <div><a href="gradle/org.gradle.language.cpp.tasks/index.html">org.gradle.language.cpp.tasks</a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="926562124%2FPackages%2F-**********"></span>
                <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
              </span></span></div>
          <div class="pull-right">
            <div class="platform-tags no-gutters">
              <div class="platform-tag jvm-like">API</div>
            </div>
          </div>
        </div>
        <div><span class="brief-comment"><a data-name="926562124%2FPackages%2F-**********" anchor-label="org.gradle.language.cpp.tasks" id="926562124%2FPackages%2F-**********" data-filterable-set=":docs/java_api"></a>
            <p class="paragraph">Tasks for compiling C++ sources for a native runtime.</p>
          </span></div>
      </div>
    </div>
<a data-name="1575575719%2FPackages%2F-**********" anchor-label="org.gradle.language.java.artifact" id="1575575719%2FPackages%2F-**********" data-filterable-set=":docs/java_api"></a>
    <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
      <div>
        <div class="main-subrow ">
          <div class=""><span class="inline-flex">
              <div><a href="gradle/org.gradle.language.java.artifact/index.html">org.gradle.language.java.artifact</a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="1575575719%2FPackages%2F-**********"></span>
                <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
              </span></span></div>
          <div class="pull-right">
            <div class="platform-tags no-gutters">
              <div class="platform-tag jvm-like">API</div>
            </div>
          </div>
        </div>
        <div><span class="brief-comment"><a data-name="1575575719%2FPackages%2F-**********" anchor-label="org.gradle.language.java.artifact" id="1575575719%2FPackages%2F-**********" data-filterable-set=":docs/java_api"></a>
            <p class="paragraph">Classes representing artifacts relevant to the Java language.</p>
          </span></div>
      </div>
    </div>
<a data-name="-60892598%2FPackages%2F-**********" anchor-label="org.gradle.language.jvm.tasks" id="-60892598%2FPackages%2F-**********" data-filterable-set=":docs/java_api"></a>
    <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
      <div>
        <div class="main-subrow ">
          <div class=""><span class="inline-flex">
              <div><a href="gradle/org.gradle.language.jvm.tasks/index.html">org.gradle.language.jvm.tasks</a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="-60892598%2FPackages%2F-**********"></span>
                <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
              </span></span></div>
          <div class="pull-right">
            <div class="platform-tags no-gutters">
              <div class="platform-tag jvm-like">API</div>
            </div>
          </div>
        </div>
        <div><span class="brief-comment"><a data-name="-60892598%2FPackages%2F-**********" anchor-label="org.gradle.language.jvm.tasks" id="-60892598%2FPackages%2F-**********" data-filterable-set=":docs/java_api"></a>
            <p class="paragraph">Tasks for support for JVM languages.</p>
          </span></div>
      </div>
    </div>
<a data-name="-834111067%2FPackages%2F-**********" anchor-label="org.gradle.language.nativeplatform" id="-834111067%2FPackages%2F-**********" data-filterable-set=":docs/java_api"></a>
    <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
      <div>
        <div class="main-subrow ">
          <div class=""><span class="inline-flex">
              <div><a href="gradle/org.gradle.language.nativeplatform/index.html">org.gradle.language.nativeplatform</a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="-834111067%2FPackages%2F-**********"></span>
                <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
              </span></span></div>
          <div class="pull-right">
            <div class="platform-tags no-gutters">
              <div class="platform-tag jvm-like">API</div>
            </div>
          </div>
        </div>
        <div></div>
      </div>
    </div>
<a data-name="-2122376315%2FPackages%2F-**********" anchor-label="org.gradle.language.nativeplatform.tasks" id="-2122376315%2FPackages%2F-**********" data-filterable-set=":docs/java_api"></a>
    <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
      <div>
        <div class="main-subrow ">
          <div class=""><span class="inline-flex">
              <div><a href="gradle/org.gradle.language.nativeplatform.tasks/index.html">org.gradle.language.nativeplatform.tasks</a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="-2122376315%2FPackages%2F-**********"></span>
                <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
              </span></span></div>
          <div class="pull-right">
            <div class="platform-tags no-gutters">
              <div class="platform-tag jvm-like">API</div>
            </div>
          </div>
        </div>
        <div><span class="brief-comment"><a data-name="-2122376315%2FPackages%2F-**********" anchor-label="org.gradle.language.nativeplatform.tasks" id="-2122376315%2FPackages%2F-**********" data-filterable-set=":docs/java_api"></a>
            <p class="paragraph">Base classes for native language compile tasks.</p>
          </span></div>
      </div>
    </div>
<a data-name="1442810597%2FPackages%2F-**********" anchor-label="org.gradle.language.objectivec" id="1442810597%2FPackages%2F-**********" data-filterable-set=":docs/java_api"></a>
    <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
      <div>
        <div class="main-subrow ">
          <div class=""><span class="inline-flex">
              <div><a href="gradle/org.gradle.language.objectivec/index.html">org.gradle.language.objectivec</a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="1442810597%2FPackages%2F-**********"></span>
                <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
              </span></span></div>
          <div class="pull-right">
            <div class="platform-tags no-gutters">
              <div class="platform-tag jvm-like">API</div>
            </div>
          </div>
        </div>
        <div><span class="brief-comment"><a data-name="1442810597%2FPackages%2F-**********" anchor-label="org.gradle.language.objectivec" id="1442810597%2FPackages%2F-**********" data-filterable-set=":docs/java_api"></a>
            <p class="paragraph">Model classes for building from Objective-C language sources.</p>
          </span></div>
      </div>
    </div>
<a data-name="856311063%2FPackages%2F-**********" anchor-label="org.gradle.language.objectivec.plugins" id="856311063%2FPackages%2F-**********" data-filterable-set=":docs/java_api"></a>
    <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
      <div>
        <div class="main-subrow ">
          <div class=""><span class="inline-flex">
              <div><a href="gradle/org.gradle.language.objectivec.plugins/index.html">org.gradle.language.objectivec.plugins</a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="856311063%2FPackages%2F-**********"></span>
                <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
              </span></span></div>
          <div class="pull-right">
            <div class="platform-tags no-gutters">
              <div class="platform-tag jvm-like">API</div>
            </div>
          </div>
        </div>
        <div><span class="brief-comment"><a data-name="856311063%2FPackages%2F-**********" anchor-label="org.gradle.language.objectivec.plugins" id="856311063%2FPackages%2F-**********" data-filterable-set=":docs/java_api"></a>
            <p class="paragraph">Plugins for building from Objective-C language sources.</p>
          </span></div>
      </div>
    </div>
<a data-name="-1485554491%2FPackages%2F-**********" anchor-label="org.gradle.language.objectivec.tasks" id="-1485554491%2FPackages%2F-**********" data-filterable-set=":docs/java_api"></a>
    <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
      <div>
        <div class="main-subrow ">
          <div class=""><span class="inline-flex">
              <div><a href="gradle/org.gradle.language.objectivec.tasks/index.html">org.gradle.language.objectivec.tasks</a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="-1485554491%2FPackages%2F-**********"></span>
                <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
              </span></span></div>
          <div class="pull-right">
            <div class="platform-tags no-gutters">
              <div class="platform-tag jvm-like">API</div>
            </div>
          </div>
        </div>
        <div><span class="brief-comment"><a data-name="-1485554491%2FPackages%2F-**********" anchor-label="org.gradle.language.objectivec.tasks" id="-1485554491%2FPackages%2F-**********" data-filterable-set=":docs/java_api"></a>
            <p class="paragraph">Tasks for compiling Objective-C sources for a native runtime.</p>
          </span></div>
      </div>
    </div>
<a data-name="888513573%2FPackages%2F-**********" anchor-label="org.gradle.language.objectivecpp" id="888513573%2FPackages%2F-**********" data-filterable-set=":docs/java_api"></a>
    <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
      <div>
        <div class="main-subrow ">
          <div class=""><span class="inline-flex">
              <div><a href="gradle/org.gradle.language.objectivecpp/index.html">org.gradle.language.objectivecpp</a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="888513573%2FPackages%2F-**********"></span>
                <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
              </span></span></div>
          <div class="pull-right">
            <div class="platform-tags no-gutters">
              <div class="platform-tag jvm-like">API</div>
            </div>
          </div>
        </div>
        <div><span class="brief-comment"><a data-name="888513573%2FPackages%2F-**********" anchor-label="org.gradle.language.objectivecpp" id="888513573%2FPackages%2F-**********" data-filterable-set=":docs/java_api"></a>
            <p class="paragraph">Model classes for building from Objective-C++ language sources.</p>
          </span></div>
      </div>
    </div>
<a data-name="2079530583%2FPackages%2F-**********" anchor-label="org.gradle.language.objectivecpp.plugins" id="2079530583%2FPackages%2F-**********" data-filterable-set=":docs/java_api"></a>
    <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
      <div>
        <div class="main-subrow ">
          <div class=""><span class="inline-flex">
              <div><a href="gradle/org.gradle.language.objectivecpp.plugins/index.html">org.gradle.language.objectivecpp.plugins</a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="2079530583%2FPackages%2F-**********"></span>
                <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
              </span></span></div>
          <div class="pull-right">
            <div class="platform-tags no-gutters">
              <div class="platform-tag jvm-like">API</div>
            </div>
          </div>
        </div>
        <div><span class="brief-comment"><a data-name="2079530583%2FPackages%2F-**********" anchor-label="org.gradle.language.objectivecpp.plugins" id="2079530583%2FPackages%2F-**********" data-filterable-set=":docs/java_api"></a>
            <p class="paragraph">Plugins for building from Objective-C++ language sources.</p>
          </span></div>
      </div>
    </div>
<a data-name="-1421711867%2FPackages%2F-**********" anchor-label="org.gradle.language.objectivecpp.tasks" id="-1421711867%2FPackages%2F-**********" data-filterable-set=":docs/java_api"></a>
    <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
      <div>
        <div class="main-subrow ">
          <div class=""><span class="inline-flex">
              <div><a href="gradle/org.gradle.language.objectivecpp.tasks/index.html">org.gradle.language.objectivecpp.tasks</a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="-1421711867%2FPackages%2F-**********"></span>
                <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
              </span></span></div>
          <div class="pull-right">
            <div class="platform-tags no-gutters">
              <div class="platform-tag jvm-like">API</div>
            </div>
          </div>
        </div>
        <div><span class="brief-comment"><a data-name="-1421711867%2FPackages%2F-**********" anchor-label="org.gradle.language.objectivecpp.tasks" id="-1421711867%2FPackages%2F-**********" data-filterable-set=":docs/java_api"></a>
            <p class="paragraph">Tasks for compiling Objective-C++ sources for a native runtime.</p>
          </span></div>
      </div>
    </div>
<a data-name="433762025%2FPackages%2F-**********" anchor-label="org.gradle.language.plugins" id="433762025%2FPackages%2F-**********" data-filterable-set=":docs/java_api"></a>
    <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
      <div>
        <div class="main-subrow ">
          <div class=""><span class="inline-flex">
              <div><a href="gradle/org.gradle.language.plugins/index.html">org.gradle.language.plugins</a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="433762025%2FPackages%2F-**********"></span>
                <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
              </span></span></div>
          <div class="pull-right">
            <div class="platform-tags no-gutters">
              <div class="platform-tag jvm-like">API</div>
            </div>
          </div>
        </div>
        <div><span class="brief-comment"><a data-name="433762025%2FPackages%2F-**********" anchor-label="org.gradle.language.plugins" id="433762025%2FPackages%2F-**********" data-filterable-set=":docs/java_api"></a>
            <p class="paragraph">Base plugins for the native languages.</p>
          </span></div>
      </div>
    </div>
<a data-name="-634072820%2FPackages%2F-**********" anchor-label="org.gradle.language.rc" id="-634072820%2FPackages%2F-**********" data-filterable-set=":docs/java_api"></a>
    <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
      <div>
        <div class="main-subrow ">
          <div class=""><span class="inline-flex">
              <div><a href="gradle/org.gradle.language.rc/index.html">org.gradle.language.rc</a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="-634072820%2FPackages%2F-**********"></span>
                <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
              </span></span></div>
          <div class="pull-right">
            <div class="platform-tags no-gutters">
              <div class="platform-tag jvm-like">API</div>
            </div>
          </div>
        </div>
        <div><span class="brief-comment"><a data-name="-634072820%2FPackages%2F-**********" anchor-label="org.gradle.language.rc" id="-634072820%2FPackages%2F-**********" data-filterable-set=":docs/java_api"></a>
            <p class="paragraph">Model classes for building from Windows Resource scripts.</p>
          </span></div>
      </div>
    </div>
<a data-name="-16354242%2FPackages%2F-**********" anchor-label="org.gradle.language.rc.plugins" id="-16354242%2FPackages%2F-**********" data-filterable-set=":docs/java_api"></a>
    <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
      <div>
        <div class="main-subrow ">
          <div class=""><span class="inline-flex">
              <div><a href="gradle/org.gradle.language.rc.plugins/index.html">org.gradle.language.rc.plugins</a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="-16354242%2FPackages%2F-**********"></span>
                <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
              </span></span></div>
          <div class="pull-right">
            <div class="platform-tags no-gutters">
              <div class="platform-tag jvm-like">API</div>
            </div>
          </div>
        </div>
        <div><span class="brief-comment"><a data-name="-16354242%2FPackages%2F-**********" anchor-label="org.gradle.language.rc.plugins" id="-16354242%2FPackages%2F-**********" data-filterable-set=":docs/java_api"></a>
            <p class="paragraph">Plugins for building from Windows Resource scripts.</p>
          </span></div>
      </div>
    </div>
<a data-name="1932528044%2FPackages%2F-**********" anchor-label="org.gradle.language.rc.tasks" id="1932528044%2FPackages%2F-**********" data-filterable-set=":docs/java_api"></a>
    <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
      <div>
        <div class="main-subrow ">
          <div class=""><span class="inline-flex">
              <div><a href="gradle/org.gradle.language.rc.tasks/index.html">org.gradle.language.rc.tasks</a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="1932528044%2FPackages%2F-**********"></span>
                <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
              </span></span></div>
          <div class="pull-right">
            <div class="platform-tags no-gutters">
              <div class="platform-tag jvm-like">API</div>
            </div>
          </div>
        </div>
        <div><span class="brief-comment"><a data-name="1932528044%2FPackages%2F-**********" anchor-label="org.gradle.language.rc.tasks" id="1932528044%2FPackages%2F-**********" data-filterable-set=":docs/java_api"></a>
            <p class="paragraph">Tasks for compiling Windows resources for a native runtime.</p>
          </span></div>
      </div>
    </div>
<a data-name="1508173103%2FPackages%2F-**********" anchor-label="org.gradle.language.scala.tasks" id="1508173103%2FPackages%2F-**********" data-filterable-set=":docs/java_api"></a>
    <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
      <div>
        <div class="main-subrow ">
          <div class=""><span class="inline-flex">
              <div><a href="gradle/org.gradle.language.scala.tasks/index.html">org.gradle.language.scala.tasks</a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="1508173103%2FPackages%2F-**********"></span>
                <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
              </span></span></div>
          <div class="pull-right">
            <div class="platform-tags no-gutters">
              <div class="platform-tag jvm-like">API</div>
            </div>
          </div>
        </div>
        <div><span class="brief-comment"><a data-name="1508173103%2FPackages%2F-**********" anchor-label="org.gradle.language.scala.tasks" id="1508173103%2FPackages%2F-**********" data-filterable-set=":docs/java_api"></a>
            <p class="paragraph">Tasks that add support for Scala language.</p>
          </span></div>
      </div>
    </div>
<a data-name="648276316%2FPackages%2F-**********" anchor-label="org.gradle.language.swift" id="648276316%2FPackages%2F-**********" data-filterable-set=":docs/java_api"></a>
    <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
      <div>
        <div class="main-subrow ">
          <div class=""><span class="inline-flex">
              <div><a href="gradle/org.gradle.language.swift/index.html">org.gradle.language.swift</a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="648276316%2FPackages%2F-**********"></span>
                <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
              </span></span></div>
          <div class="pull-right">
            <div class="platform-tags no-gutters">
              <div class="platform-tag jvm-like">API</div>
            </div>
          </div>
        </div>
        <div><span class="brief-comment"><a data-name="648276316%2FPackages%2F-**********" anchor-label="org.gradle.language.swift" id="648276316%2FPackages%2F-**********" data-filterable-set=":docs/java_api"></a>
            <p class="paragraph">Model classes for building from Swift language sources.</p>
          </span></div>
      </div>
    </div>
<a data-name="742407310%2FPackages%2F-**********" anchor-label="org.gradle.language.swift.plugins" id="742407310%2FPackages%2F-**********" data-filterable-set=":docs/java_api"></a>
    <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
      <div>
        <div class="main-subrow ">
          <div class=""><span class="inline-flex">
              <div><a href="gradle/org.gradle.language.swift.plugins/index.html">org.gradle.language.swift.plugins</a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="742407310%2FPackages%2F-**********"></span>
                <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
              </span></span></div>
          <div class="pull-right">
            <div class="platform-tags no-gutters">
              <div class="platform-tag jvm-like">API</div>
            </div>
          </div>
        </div>
        <div><span class="brief-comment"><a data-name="742407310%2FPackages%2F-**********" anchor-label="org.gradle.language.swift.plugins" id="742407310%2FPackages%2F-**********" data-filterable-set=":docs/java_api"></a>
            <p class="paragraph">Plugins for building from Swift language sources.</p>
          </span></div>
      </div>
    </div>
<a data-name="-1914722820%2FPackages%2F-**********" anchor-label="org.gradle.language.swift.tasks" id="-1914722820%2FPackages%2F-**********" data-filterable-set=":docs/java_api"></a>
    <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
      <div>
        <div class="main-subrow ">
          <div class=""><span class="inline-flex">
              <div><a href="gradle/org.gradle.language.swift.tasks/index.html">org.gradle.language.swift.tasks</a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="-1914722820%2FPackages%2F-**********"></span>
                <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
              </span></span></div>
          <div class="pull-right">
            <div class="platform-tags no-gutters">
              <div class="platform-tag jvm-like">API</div>
            </div>
          </div>
        </div>
        <div><span class="brief-comment"><a data-name="-1914722820%2FPackages%2F-**********" anchor-label="org.gradle.language.swift.tasks" id="-1914722820%2FPackages%2F-**********" data-filterable-set=":docs/java_api"></a>
            <p class="paragraph">Tasks for compiling Swift sources for a native runtime.</p>
          </span></div>
      </div>
    </div>
<a data-name="1756410704%2FPackages%2F-**********" anchor-label="org.gradle.maven" id="1756410704%2FPackages%2F-**********" data-filterable-set=":docs/java_api"></a>
    <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
      <div>
        <div class="main-subrow ">
          <div class=""><span class="inline-flex">
              <div><a href="gradle/org.gradle.maven/index.html">org.gradle.maven</a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="1756410704%2FPackages%2F-**********"></span>
                <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
              </span></span></div>
          <div class="pull-right">
            <div class="platform-tags no-gutters">
              <div class="platform-tag jvm-like">API</div>
            </div>
          </div>
        </div>
        <div><span class="brief-comment"><a data-name="1756410704%2FPackages%2F-**********" anchor-label="org.gradle.maven" id="1756410704%2FPackages%2F-**********" data-filterable-set=":docs/java_api"></a>
            <p class="paragraph">Component types for Maven modules.</p>
          </span></div>
      </div>
    </div>
<a data-name="400679790%2FPackages%2F-**********" anchor-label="org.gradle.model" id="400679790%2FPackages%2F-**********" data-filterable-set=":docs/java_api"></a>
    <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
      <div>
        <div class="main-subrow ">
          <div class=""><span class="inline-flex">
              <div><a href="gradle/org.gradle.model/index.html">org.gradle.model</a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="400679790%2FPackages%2F-**********"></span>
                <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
              </span></span></div>
          <div class="pull-right">
            <div class="platform-tags no-gutters">
              <div class="platform-tag jvm-like">API</div>
            </div>
          </div>
        </div>
        <div><span class="brief-comment"><a data-name="400679790%2FPackages%2F-**********" anchor-label="org.gradle.model" id="400679790%2FPackages%2F-**********" data-filterable-set=":docs/java_api"></a>
            <p class="paragraph">Classes that operate upon the Gradle model.</p>
          </span></div>
      </div>
    </div>
<a data-name="-968378199%2FPackages%2F-**********" anchor-label="org.gradle.nativeplatform" id="-968378199%2FPackages%2F-**********" data-filterable-set=":docs/java_api"></a>
    <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
      <div>
        <div class="main-subrow ">
          <div class=""><span class="inline-flex">
              <div><a href="gradle/org.gradle.nativeplatform/index.html">org.gradle.nativeplatform</a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="-968378199%2FPackages%2F-**********"></span>
                <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
              </span></span></div>
          <div class="pull-right">
            <div class="platform-tags no-gutters">
              <div class="platform-tag jvm-like">API</div>
            </div>
          </div>
        </div>
        <div><span class="brief-comment"><a data-name="-968378199%2FPackages%2F-**********" anchor-label="org.gradle.nativeplatform" id="-968378199%2FPackages%2F-**********" data-filterable-set=":docs/java_api"></a>
            <p class="paragraph">Classes that model aspects of native component projects.</p>
          </span></div>
      </div>
    </div>
<a data-name="-2119455652%2FPackages%2F-**********" anchor-label="org.gradle.nativeplatform.platform" id="-2119455652%2FPackages%2F-**********" data-filterable-set=":docs/java_api"></a>
    <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
      <div>
        <div class="main-subrow ">
          <div class=""><span class="inline-flex">
              <div><a href="gradle/org.gradle.nativeplatform.platform/index.html">org.gradle.nativeplatform.platform</a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="-2119455652%2FPackages%2F-**********"></span>
                <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
              </span></span></div>
          <div class="pull-right">
            <div class="platform-tags no-gutters">
              <div class="platform-tag jvm-like">API</div>
            </div>
          </div>
        </div>
        <div><span class="brief-comment"><a data-name="-2119455652%2FPackages%2F-**********" anchor-label="org.gradle.nativeplatform.platform" id="-2119455652%2FPackages%2F-**********" data-filterable-set=":docs/java_api"></a>
            <p class="paragraph">Classes that allow defining a native binary platform.</p>
          </span></div>
      </div>
    </div>
<a data-name="1731882715%2FPackages%2F-**********" anchor-label="org.gradle.nativeplatform.plugins" id="1731882715%2FPackages%2F-**********" data-filterable-set=":docs/java_api"></a>
    <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
      <div>
        <div class="main-subrow ">
          <div class=""><span class="inline-flex">
              <div><a href="gradle/org.gradle.nativeplatform.plugins/index.html">org.gradle.nativeplatform.plugins</a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="1731882715%2FPackages%2F-**********"></span>
                <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
              </span></span></div>
          <div class="pull-right">
            <div class="platform-tags no-gutters">
              <div class="platform-tag jvm-like">API</div>
            </div>
          </div>
        </div>
        <div><span class="brief-comment"><a data-name="1731882715%2FPackages%2F-**********" anchor-label="org.gradle.nativeplatform.plugins" id="1731882715%2FPackages%2F-**********" data-filterable-set=":docs/java_api"></a>
            <p class="paragraph">Plugins for building native component projects.</p>
          </span></div>
      </div>
    </div>
<a data-name="-1167325303%2FPackages%2F-**********" anchor-label="org.gradle.nativeplatform.tasks" id="-1167325303%2FPackages%2F-**********" data-filterable-set=":docs/java_api"></a>
    <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
      <div>
        <div class="main-subrow ">
          <div class=""><span class="inline-flex">
              <div><a href="gradle/org.gradle.nativeplatform.tasks/index.html">org.gradle.nativeplatform.tasks</a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="-1167325303%2FPackages%2F-**********"></span>
                <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
              </span></span></div>
          <div class="pull-right">
            <div class="platform-tags no-gutters">
              <div class="platform-tag jvm-like">API</div>
            </div>
          </div>
        </div>
        <div><span class="brief-comment"><a data-name="-1167325303%2FPackages%2F-**********" anchor-label="org.gradle.nativeplatform.tasks" id="-1167325303%2FPackages%2F-**********" data-filterable-set=":docs/java_api"></a>
            <p class="paragraph">Tasks for building native component projects.</p>
          </span></div>
      </div>
    </div>
<a data-name="-624817349%2FPackages%2F-**********" anchor-label="org.gradle.nativeplatform.test" id="-624817349%2FPackages%2F-**********" data-filterable-set=":docs/java_api"></a>
    <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
      <div>
        <div class="main-subrow ">
          <div class=""><span class="inline-flex">
              <div><a href="gradle/org.gradle.nativeplatform.test/index.html">org.gradle.nativeplatform.test</a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="-624817349%2FPackages%2F-**********"></span>
                <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
              </span></span></div>
          <div class="pull-right">
            <div class="platform-tags no-gutters">
              <div class="platform-tag jvm-like">API</div>
            </div>
          </div>
        </div>
        <div><span class="brief-comment"><a data-name="-624817349%2FPackages%2F-**********" anchor-label="org.gradle.nativeplatform.test" id="-624817349%2FPackages%2F-**********" data-filterable-set=":docs/java_api"></a>
            <p class="paragraph">API classes for testing native binaries.</p>
          </span></div>
      </div>
    </div>
<a data-name="1299577136%2FPackages%2F-**********" anchor-label="org.gradle.nativeplatform.test.cpp" id="1299577136%2FPackages%2F-**********" data-filterable-set=":docs/java_api"></a>
    <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
      <div>
        <div class="main-subrow ">
          <div class=""><span class="inline-flex">
              <div><a href="gradle/org.gradle.nativeplatform.test.cpp/index.html">org.gradle.nativeplatform.test.cpp</a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="1299577136%2FPackages%2F-**********"></span>
                <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
              </span></span></div>
          <div class="pull-right">
            <div class="platform-tags no-gutters">
              <div class="platform-tag jvm-like">API</div>
            </div>
          </div>
        </div>
        <div><span class="brief-comment"><a data-name="1299577136%2FPackages%2F-**********" anchor-label="org.gradle.nativeplatform.test.cpp" id="1299577136%2FPackages%2F-**********" data-filterable-set=":docs/java_api"></a>
            <p class="paragraph">API classes for C++ test integration.</p>
          </span></div>
      </div>
    </div>
<a data-name="742340706%2FPackages%2F-**********" anchor-label="org.gradle.nativeplatform.test.cpp.plugins" id="742340706%2FPackages%2F-**********" data-filterable-set=":docs/java_api"></a>
    <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
      <div>
        <div class="main-subrow ">
          <div class=""><span class="inline-flex">
              <div><a href="gradle/org.gradle.nativeplatform.test.cpp.plugins/index.html">org.gradle.nativeplatform.test.cpp.plugins</a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="742340706%2FPackages%2F-**********"></span>
                <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
              </span></span></div>
          <div class="pull-right">
            <div class="platform-tags no-gutters">
              <div class="platform-tag jvm-like">API</div>
            </div>
          </div>
        </div>
        <div><span class="brief-comment"><a data-name="742340706%2FPackages%2F-**********" anchor-label="org.gradle.nativeplatform.test.cpp.plugins" id="742340706%2FPackages%2F-**********" data-filterable-set=":docs/java_api"></a>
            <p class="paragraph">Plugins for C++ test integration.</p>
          </span></div>
      </div>
    </div>
<a data-name="685132852%2FPackages%2F-**********" anchor-label="org.gradle.nativeplatform.test.cunit" id="685132852%2FPackages%2F-**********" data-filterable-set=":docs/java_api"></a>
    <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
      <div>
        <div class="main-subrow ">
          <div class=""><span class="inline-flex">
              <div><a href="gradle/org.gradle.nativeplatform.test.cunit/index.html">org.gradle.nativeplatform.test.cunit</a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="685132852%2FPackages%2F-**********"></span>
                <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
              </span></span></div>
          <div class="pull-right">
            <div class="platform-tags no-gutters">
              <div class="platform-tag jvm-like">API</div>
            </div>
          </div>
        </div>
        <div><span class="brief-comment"><a data-name="685132852%2FPackages%2F-**********" anchor-label="org.gradle.nativeplatform.test.cunit" id="685132852%2FPackages%2F-**********" data-filterable-set=":docs/java_api"></a>
            <p class="paragraph">API classes for cunit integration.</p>
          </span></div>
      </div>
    </div>
<a data-name="682510182%2FPackages%2F-**********" anchor-label="org.gradle.nativeplatform.test.cunit.plugins" id="682510182%2FPackages%2F-**********" data-filterable-set=":docs/java_api"></a>
    <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
      <div>
        <div class="main-subrow ">
          <div class=""><span class="inline-flex">
              <div><a href="gradle/org.gradle.nativeplatform.test.cunit.plugins/index.html">org.gradle.nativeplatform.test.cunit.plugins</a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="682510182%2FPackages%2F-**********"></span>
                <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
              </span></span></div>
          <div class="pull-right">
            <div class="platform-tags no-gutters">
              <div class="platform-tag jvm-like">API</div>
            </div>
          </div>
        </div>
        <div><span class="brief-comment"><a data-name="682510182%2FPackages%2F-**********" anchor-label="org.gradle.nativeplatform.test.cunit.plugins" id="682510182%2FPackages%2F-**********" data-filterable-set=":docs/java_api"></a>
            <p class="paragraph">Plugins for cunit testing.</p>
          </span></div>
      </div>
    </div>
<a data-name="1736607444%2FPackages%2F-**********" anchor-label="org.gradle.nativeplatform.test.cunit.tasks" id="1736607444%2FPackages%2F-**********" data-filterable-set=":docs/java_api"></a>
    <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
      <div>
        <div class="main-subrow ">
          <div class=""><span class="inline-flex">
              <div><a href="gradle/org.gradle.nativeplatform.test.cunit.tasks/index.html">org.gradle.nativeplatform.test.cunit.tasks</a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="1736607444%2FPackages%2F-**********"></span>
                <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
              </span></span></div>
          <div class="pull-right">
            <div class="platform-tags no-gutters">
              <div class="platform-tag jvm-like">API</div>
            </div>
          </div>
        </div>
        <div><span class="brief-comment"><a data-name="1736607444%2FPackages%2F-**********" anchor-label="org.gradle.nativeplatform.test.cunit.tasks" id="1736607444%2FPackages%2F-**********" data-filterable-set=":docs/java_api"></a>
            <p class="paragraph">Tasks for cunit integration.</p>
          </span></div>
      </div>
    </div>
<a data-name="-1939690334%2FPackages%2F-**********" anchor-label="org.gradle.nativeplatform.test.googletest" id="-1939690334%2FPackages%2F-**********" data-filterable-set=":docs/java_api"></a>
    <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
      <div>
        <div class="main-subrow ">
          <div class=""><span class="inline-flex">
              <div><a href="gradle/org.gradle.nativeplatform.test.googletest/index.html">org.gradle.nativeplatform.test.googletest</a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="-1939690334%2FPackages%2F-**********"></span>
                <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
              </span></span></div>
          <div class="pull-right">
            <div class="platform-tags no-gutters">
              <div class="platform-tag jvm-like">API</div>
            </div>
          </div>
        </div>
        <div><span class="brief-comment"><a data-name="-1939690334%2FPackages%2F-**********" anchor-label="org.gradle.nativeplatform.test.googletest" id="-1939690334%2FPackages%2F-**********" data-filterable-set=":docs/java_api"></a>
            <p class="paragraph">API classes for Google Test integration.</p>
          </span></div>
      </div>
    </div>
<a data-name="-1623041580%2FPackages%2F-**********" anchor-label="org.gradle.nativeplatform.test.googletest.plugins" id="-1623041580%2FPackages%2F-**********" data-filterable-set=":docs/java_api"></a>
    <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
      <div>
        <div class="main-subrow ">
          <div class=""><span class="inline-flex">
              <div><a href="gradle/org.gradle.nativeplatform.test.googletest.plugins/index.html">org.gradle.nativeplatform.test.googletest.plugins</a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="-1623041580%2FPackages%2F-**********"></span>
                <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
              </span></span></div>
          <div class="pull-right">
            <div class="platform-tags no-gutters">
              <div class="platform-tag jvm-like">API</div>
            </div>
          </div>
        </div>
        <div><span class="brief-comment"><a data-name="-1623041580%2FPackages%2F-**********" anchor-label="org.gradle.nativeplatform.test.googletest.plugins" id="-1623041580%2FPackages%2F-**********" data-filterable-set=":docs/java_api"></a>
            <p class="paragraph">Plugins for Google Test testing.</p>
          </span></div>
      </div>
    </div>
<a data-name="-599126163%2FPackages%2F-**********" anchor-label="org.gradle.nativeplatform.test.plugins" id="-599126163%2FPackages%2F-**********" data-filterable-set=":docs/java_api"></a>
    <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
      <div>
        <div class="main-subrow ">
          <div class=""><span class="inline-flex">
              <div><a href="gradle/org.gradle.nativeplatform.test.plugins/index.html">org.gradle.nativeplatform.test.plugins</a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="-599126163%2FPackages%2F-**********"></span>
                <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
              </span></span></div>
          <div class="pull-right">
            <div class="platform-tags no-gutters">
              <div class="platform-tag jvm-like">API</div>
            </div>
          </div>
        </div>
        <div><span class="brief-comment"><a data-name="-599126163%2FPackages%2F-**********" anchor-label="org.gradle.nativeplatform.test.plugins" id="-599126163%2FPackages%2F-**********" data-filterable-set=":docs/java_api"></a>
            <p class="paragraph">Plugin classes for generic support for testing native binaries.</p>
          </span></div>
      </div>
    </div>
<a data-name="-173103973%2FPackages%2F-**********" anchor-label="org.gradle.nativeplatform.test.tasks" id="-173103973%2FPackages%2F-**********" data-filterable-set=":docs/java_api"></a>
    <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
      <div>
        <div class="main-subrow ">
          <div class=""><span class="inline-flex">
              <div><a href="gradle/org.gradle.nativeplatform.test.tasks/index.html">org.gradle.nativeplatform.test.tasks</a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="-173103973%2FPackages%2F-**********"></span>
                <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
              </span></span></div>
          <div class="pull-right">
            <div class="platform-tags no-gutters">
              <div class="platform-tag jvm-like">API</div>
            </div>
          </div>
        </div>
        <div><span class="brief-comment"><a data-name="-173103973%2FPackages%2F-**********" anchor-label="org.gradle.nativeplatform.test.tasks" id="-173103973%2FPackages%2F-**********" data-filterable-set=":docs/java_api"></a>
            <p class="paragraph">Tasks for test execution.</p>
          </span></div>
      </div>
    </div>
<a data-name="63929428%2FPackages%2F-**********" anchor-label="org.gradle.nativeplatform.test.xctest" id="63929428%2FPackages%2F-**********" data-filterable-set=":docs/java_api"></a>
    <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
      <div>
        <div class="main-subrow ">
          <div class=""><span class="inline-flex">
              <div><a href="gradle/org.gradle.nativeplatform.test.xctest/index.html">org.gradle.nativeplatform.test.xctest</a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="63929428%2FPackages%2F-**********"></span>
                <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
              </span></span></div>
          <div class="pull-right">
            <div class="platform-tags no-gutters">
              <div class="platform-tag jvm-like">API</div>
            </div>
          </div>
        </div>
        <div><span class="brief-comment"><a data-name="63929428%2FPackages%2F-**********" anchor-label="org.gradle.nativeplatform.test.xctest" id="63929428%2FPackages%2F-**********" data-filterable-set=":docs/java_api"></a>
            <p class="paragraph">Model classes for the XCTest plugins.</p>
          </span></div>
      </div>
    </div>
<a data-name="1476745094%2FPackages%2F-**********" anchor-label="org.gradle.nativeplatform.test.xctest.plugins" id="1476745094%2FPackages%2F-**********" data-filterable-set=":docs/java_api"></a>
    <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
      <div>
        <div class="main-subrow ">
          <div class=""><span class="inline-flex">
              <div><a href="gradle/org.gradle.nativeplatform.test.xctest.plugins/index.html">org.gradle.nativeplatform.test.xctest.plugins</a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="1476745094%2FPackages%2F-**********"></span>
                <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
              </span></span></div>
          <div class="pull-right">
            <div class="platform-tags no-gutters">
              <div class="platform-tag jvm-like">API</div>
            </div>
          </div>
        </div>
        <div><span class="brief-comment"><a data-name="1476745094%2FPackages%2F-**********" anchor-label="org.gradle.nativeplatform.test.xctest.plugins" id="1476745094%2FPackages%2F-**********" data-filterable-set=":docs/java_api"></a>
            <p class="paragraph">Plugins for XCTest testing.</p>
          </span></div>
      </div>
    </div>
<a data-name="731848436%2FPackages%2F-**********" anchor-label="org.gradle.nativeplatform.test.xctest.tasks" id="731848436%2FPackages%2F-**********" data-filterable-set=":docs/java_api"></a>
    <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
      <div>
        <div class="main-subrow ">
          <div class=""><span class="inline-flex">
              <div><a href="gradle/org.gradle.nativeplatform.test.xctest.tasks/index.html">org.gradle.nativeplatform.test.xctest.tasks</a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="731848436%2FPackages%2F-**********"></span>
                <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
              </span></span></div>
          <div class="pull-right">
            <div class="platform-tags no-gutters">
              <div class="platform-tag jvm-like">API</div>
            </div>
          </div>
        </div>
        <div><span class="brief-comment"><a data-name="731848436%2FPackages%2F-**********" anchor-label="org.gradle.nativeplatform.test.xctest.tasks" id="731848436%2FPackages%2F-**********" data-filterable-set=":docs/java_api"></a>
            <p class="paragraph">Tasks for XCTest execution.</p>
          </span></div>
      </div>
    </div>
<a data-name="2050254820%2FPackages%2F-**********" anchor-label="org.gradle.nativeplatform.toolchain" id="2050254820%2FPackages%2F-**********" data-filterable-set=":docs/java_api"></a>
    <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
      <div>
        <div class="main-subrow ">
          <div class=""><span class="inline-flex">
              <div><a href="gradle/org.gradle.nativeplatform.toolchain/index.html">org.gradle.nativeplatform.toolchain</a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="2050254820%2FPackages%2F-**********"></span>
                <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
              </span></span></div>
          <div class="pull-right">
            <div class="platform-tags no-gutters">
              <div class="platform-tag jvm-like">API</div>
            </div>
          </div>
        </div>
        <div><span class="brief-comment"><a data-name="2050254820%2FPackages%2F-**********" anchor-label="org.gradle.nativeplatform.toolchain" id="2050254820%2FPackages%2F-**********" data-filterable-set=":docs/java_api"></a>
            <p class="paragraph">Classes that allow C++ tool chains to be configured.</p>
          </span></div>
      </div>
    </div>
<a data-name="1657647894%2FPackages%2F-**********" anchor-label="org.gradle.nativeplatform.toolchain.plugins" id="1657647894%2FPackages%2F-**********" data-filterable-set=":docs/java_api"></a>
    <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
      <div>
        <div class="main-subrow ">
          <div class=""><span class="inline-flex">
              <div><a href="gradle/org.gradle.nativeplatform.toolchain.plugins/index.html">org.gradle.nativeplatform.toolchain.plugins</a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="1657647894%2FPackages%2F-**********"></span>
                <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
              </span></span></div>
          <div class="pull-right">
            <div class="platform-tags no-gutters">
              <div class="platform-tag jvm-like">API</div>
            </div>
          </div>
        </div>
        <div><span class="brief-comment"><a data-name="1657647894%2FPackages%2F-**********" anchor-label="org.gradle.nativeplatform.toolchain.plugins" id="1657647894%2FPackages%2F-**********" data-filterable-set=":docs/java_api"></a>
            <p class="paragraph">Built-in tool chain support.</p>
          </span></div>
      </div>
    </div>
<a data-name="-322194974%2FPackages%2F-**********" anchor-label="org.gradle.normalization" id="-322194974%2FPackages%2F-**********" data-filterable-set=":docs/java_api"></a>
    <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
      <div>
        <div class="main-subrow ">
          <div class=""><span class="inline-flex">
              <div><a href="gradle/org.gradle.normalization/index.html">org.gradle.normalization</a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="-322194974%2FPackages%2F-**********"></span>
                <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
              </span></span></div>
          <div class="pull-right">
            <div class="platform-tags no-gutters">
              <div class="platform-tag jvm-like">API</div>
            </div>
          </div>
        </div>
        <div><span class="brief-comment"><a data-name="-322194974%2FPackages%2F-**********" anchor-label="org.gradle.normalization" id="-322194974%2FPackages%2F-**********" data-filterable-set=":docs/java_api"></a>
            <p class="paragraph">Interfaces and API for input normalization.</p>
          </span></div>
      </div>
    </div>
<a data-name="-551600110%2FPackages%2F-**********" anchor-label="org.gradle.platform" id="-551600110%2FPackages%2F-**********" data-filterable-set=":docs/java_api"></a>
    <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
      <div>
        <div class="main-subrow ">
          <div class=""><span class="inline-flex">
              <div><a href="gradle/org.gradle.platform/index.html">org.gradle.platform</a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="-551600110%2FPackages%2F-**********"></span>
                <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
              </span></span></div>
          <div class="pull-right">
            <div class="platform-tags no-gutters">
              <div class="platform-tag jvm-like">API</div>
            </div>
          </div>
        </div>
        <div><span class="brief-comment"><a data-name="-551600110%2FPackages%2F-**********" anchor-label="org.gradle.platform" id="-551600110%2FPackages%2F-**********" data-filterable-set=":docs/java_api"></a>
            <p class="paragraph">Types to define build environment.</p>
          </span></div>
      </div>
    </div>
<a data-name="-1265623311%2FPackages%2F-**********" anchor-label="org.gradle.platform.base" id="-1265623311%2FPackages%2F-**********" data-filterable-set=":docs/java_api"></a>
    <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
      <div>
        <div class="main-subrow ">
          <div class=""><span class="inline-flex">
              <div><a href="gradle/org.gradle.platform.base/index.html">org.gradle.platform.base</a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="-1265623311%2FPackages%2F-**********"></span>
                <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
              </span></span></div>
          <div class="pull-right">
            <div class="platform-tags no-gutters">
              <div class="platform-tag jvm-like">API</div>
            </div>
          </div>
        </div>
        <div><span class="brief-comment"><a data-name="-1265623311%2FPackages%2F-**********" anchor-label="org.gradle.platform.base" id="-1265623311%2FPackages%2F-**********" data-filterable-set=":docs/java_api"></a>
            <p class="paragraph">General purpose types for runtime support.</p>
          </span></div>
      </div>
    </div>
<a data-name="74701026%2FPackages%2F-**********" anchor-label="org.gradle.platform.base.binary" id="74701026%2FPackages%2F-**********" data-filterable-set=":docs/java_api"></a>
    <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
      <div>
        <div class="main-subrow ">
          <div class=""><span class="inline-flex">
              <div><a href="gradle/org.gradle.platform.base.binary/index.html">org.gradle.platform.base.binary</a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="74701026%2FPackages%2F-**********"></span>
                <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
              </span></span></div>
          <div class="pull-right">
            <div class="platform-tags no-gutters">
              <div class="platform-tag jvm-like">API</div>
            </div>
          </div>
        </div>
        <div><span class="brief-comment"><a data-name="74701026%2FPackages%2F-**********" anchor-label="org.gradle.platform.base.binary" id="74701026%2FPackages%2F-**********" data-filterable-set=":docs/java_api"></a>
            <p class="paragraph">General purpose types for binary support.</p>
          </span></div>
      </div>
    </div>
<a data-name="786752512%2FPackages%2F-**********" anchor-label="org.gradle.platform.base.component" id="786752512%2FPackages%2F-**********" data-filterable-set=":docs/java_api"></a>
    <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
      <div>
        <div class="main-subrow ">
          <div class=""><span class="inline-flex">
              <div><a href="gradle/org.gradle.platform.base.component/index.html">org.gradle.platform.base.component</a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="786752512%2FPackages%2F-**********"></span>
                <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
              </span></span></div>
          <div class="pull-right">
            <div class="platform-tags no-gutters">
              <div class="platform-tag jvm-like">API</div>
            </div>
          </div>
        </div>
        <div><span class="brief-comment"><a data-name="786752512%2FPackages%2F-**********" anchor-label="org.gradle.platform.base.component" id="786752512%2FPackages%2F-**********" data-filterable-set=":docs/java_api"></a>
            <p class="paragraph">General purpose types for library support.</p>
          </span></div>
      </div>
    </div>
<a data-name="-2063856349%2FPackages%2F-**********" anchor-label="org.gradle.platform.base.plugins" id="-2063856349%2FPackages%2F-**********" data-filterable-set=":docs/java_api"></a>
    <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
      <div>
        <div class="main-subrow ">
          <div class=""><span class="inline-flex">
              <div><a href="gradle/org.gradle.platform.base.plugins/index.html">org.gradle.platform.base.plugins</a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="-2063856349%2FPackages%2F-**********"></span>
                <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
              </span></span></div>
          <div class="pull-right">
            <div class="platform-tags no-gutters">
              <div class="platform-tag jvm-like">API</div>
            </div>
          </div>
        </div>
        <div><span class="brief-comment"><a data-name="-2063856349%2FPackages%2F-**********" anchor-label="org.gradle.platform.base.plugins" id="-2063856349%2FPackages%2F-**********" data-filterable-set=":docs/java_api"></a>
            <p class="paragraph">Base plugins for software model support.</p>
          </span></div>
      </div>
    </div>
<a data-name="-399743840%2FPackages%2F-**********" anchor-label="org.gradle.plugin.devel" id="-399743840%2FPackages%2F-**********" data-filterable-set=":docs/java_api"></a>
    <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
      <div>
        <div class="main-subrow ">
          <div class=""><span class="inline-flex">
              <div><a href="gradle/org.gradle.plugin.devel/index.html">org.gradle.plugin.devel</a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="-399743840%2FPackages%2F-**********"></span>
                <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
              </span></span></div>
          <div class="pull-right">
            <div class="platform-tags no-gutters">
              <div class="platform-tag jvm-like">API</div>
            </div>
          </div>
        </div>
        <div><span class="brief-comment"><a data-name="-399743840%2FPackages%2F-**********" anchor-label="org.gradle.plugin.devel" id="-399743840%2FPackages%2F-**********" data-filterable-set=":docs/java_api"></a>
            <p class="paragraph">Classes for assisting with plugin development.</p>
          </span></div>
      </div>
    </div>
<a data-name="692794322%2FPackages%2F-**********" anchor-label="org.gradle.plugin.devel.plugins" id="692794322%2FPackages%2F-**********" data-filterable-set=":docs/java_api"></a>
    <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
      <div>
        <div class="main-subrow ">
          <div class=""><span class="inline-flex">
              <div><a href="gradle/org.gradle.plugin.devel.plugins/index.html">org.gradle.plugin.devel.plugins</a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="692794322%2FPackages%2F-**********"></span>
                <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
              </span></span></div>
          <div class="pull-right">
            <div class="platform-tags no-gutters">
              <div class="platform-tag jvm-like">API</div>
            </div>
          </div>
        </div>
        <div><span class="brief-comment"><a data-name="692794322%2FPackages%2F-**********" anchor-label="org.gradle.plugin.devel.plugins" id="692794322%2FPackages%2F-**********" data-filterable-set=":docs/java_api"></a>
            <p class="paragraph">Plugins for assisting with plugin development.</p>
          </span></div>
      </div>
    </div>
<a data-name="-1257791936%2FPackages%2F-**********" anchor-label="org.gradle.plugin.devel.tasks" id="-1257791936%2FPackages%2F-**********" data-filterable-set=":docs/java_api"></a>
    <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
      <div>
        <div class="main-subrow ">
          <div class=""><span class="inline-flex">
              <div><a href="gradle/org.gradle.plugin.devel.tasks/index.html">org.gradle.plugin.devel.tasks</a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="-1257791936%2FPackages%2F-**********"></span>
                <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
              </span></span></div>
          <div class="pull-right">
            <div class="platform-tags no-gutters">
              <div class="platform-tag jvm-like">API</div>
            </div>
          </div>
        </div>
        <div><span class="brief-comment"><a data-name="-1257791936%2FPackages%2F-**********" anchor-label="org.gradle.plugin.devel.tasks" id="-1257791936%2FPackages%2F-**********" data-filterable-set=":docs/java_api"></a>
            <p class="paragraph">Tasks for assisting with plugin development.</p>
          </span></div>
      </div>
    </div>
<a data-name="1883905379%2FPackages%2F-**********" anchor-label="org.gradle.plugin.management" id="1883905379%2FPackages%2F-**********" data-filterable-set=":docs/java_api"></a>
    <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
      <div>
        <div class="main-subrow ">
          <div class=""><span class="inline-flex">
              <div><a href="gradle/org.gradle.plugin.management/index.html">org.gradle.plugin.management</a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="1883905379%2FPackages%2F-**********"></span>
                <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
              </span></span></div>
          <div class="pull-right">
            <div class="platform-tags no-gutters">
              <div class="platform-tag jvm-like">API</div>
            </div>
          </div>
        </div>
        <div><span class="brief-comment"><a data-name="1883905379%2FPackages%2F-**********" anchor-label="org.gradle.plugin.management" id="1883905379%2FPackages%2F-**********" data-filterable-set=":docs/java_api"></a>
            <p class="paragraph">APIs to influence how plugins are resolved.</p>
          </span></div>
      </div>
    </div>
<a data-name="1405381483%2FPackages%2F-**********" anchor-label="org.gradle.plugin.use" id="1405381483%2FPackages%2F-**********" data-filterable-set=":docs/java_api"></a>
    <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
      <div>
        <div class="main-subrow ">
          <div class=""><span class="inline-flex">
              <div><a href="gradle/org.gradle.plugin.use/index.html">org.gradle.plugin.use</a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="1405381483%2FPackages%2F-**********"></span>
                <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
              </span></span></div>
          <div class="pull-right">
            <div class="platform-tags no-gutters">
              <div class="platform-tag jvm-like">API</div>
            </div>
          </div>
        </div>
        <div><span class="brief-comment"><a data-name="1405381483%2FPackages%2F-**********" anchor-label="org.gradle.plugin.use" id="1405381483%2FPackages%2F-**********" data-filterable-set=":docs/java_api"></a>
            <p class="paragraph">Classes for managing plugin resolution and use.</p>
          </span></div>
      </div>
    </div>
<a data-name="262689101%2FPackages%2F-**********" anchor-label="org.gradle.plugins.ear" id="262689101%2FPackages%2F-**********" data-filterable-set=":docs/java_api"></a>
    <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
      <div>
        <div class="main-subrow ">
          <div class=""><span class="inline-flex">
              <div><a href="gradle/org.gradle.plugins.ear/index.html">org.gradle.plugins.ear</a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="262689101%2FPackages%2F-**********"></span>
                <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
              </span></span></div>
          <div class="pull-right">
            <div class="platform-tags no-gutters">
              <div class="platform-tag jvm-like">API</div>
            </div>
          </div>
        </div>
        <div><span class="brief-comment"><a data-name="262689101%2FPackages%2F-**********" anchor-label="org.gradle.plugins.ear" id="262689101%2FPackages%2F-**********" data-filterable-set=":docs/java_api"></a>
            <p class="paragraph">Support for generating EAR archives in a Gradle build</p>
          </span></div>
      </div>
    </div>
<a data-name="-583575692%2FPackages%2F-**********" anchor-label="org.gradle.plugins.ear.descriptor" id="-583575692%2FPackages%2F-**********" data-filterable-set=":docs/java_api"></a>
    <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
      <div>
        <div class="main-subrow ">
          <div class=""><span class="inline-flex">
              <div><a href="gradle/org.gradle.plugins.ear.descriptor/index.html">org.gradle.plugins.ear.descriptor</a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="-583575692%2FPackages%2F-**********"></span>
                <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
              </span></span></div>
          <div class="pull-right">
            <div class="platform-tags no-gutters">
              <div class="platform-tag jvm-like">API</div>
            </div>
          </div>
        </div>
        <div><span class="brief-comment"><a data-name="-583575692%2FPackages%2F-**********" anchor-label="org.gradle.plugins.ear.descriptor" id="-583575692%2FPackages%2F-**********" data-filterable-set=":docs/java_api"></a>
            <p class="paragraph">Classes for working with EAR deployment descriptors.</p>
          </span></div>
      </div>
    </div>
<a data-name="-1640161375%2FPackages%2F-**********" anchor-label="org.gradle.plugins.ide" id="-1640161375%2FPackages%2F-**********" data-filterable-set=":docs/java_api"></a>
    <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
      <div>
        <div class="main-subrow ">
          <div class=""><span class="inline-flex">
              <div><a href="gradle/org.gradle.plugins.ide/index.html">org.gradle.plugins.ide</a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="-1640161375%2FPackages%2F-**********"></span>
                <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
              </span></span></div>
          <div class="pull-right">
            <div class="platform-tags no-gutters">
              <div class="platform-tag jvm-like">API</div>
            </div>
          </div>
        </div>
        <div><span class="brief-comment"><a data-name="-1640161375%2FPackages%2F-**********" anchor-label="org.gradle.plugins.ide" id="-1640161375%2FPackages%2F-**********" data-filterable-set=":docs/java_api"></a>
            <p class="paragraph">General purpose IDE types.</p>
          </span></div>
      </div>
    </div>
<a data-name="386739277%2FPackages%2F-**********" anchor-label="org.gradle.plugins.ide.api" id="386739277%2FPackages%2F-**********" data-filterable-set=":docs/java_api"></a>
    <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
      <div>
        <div class="main-subrow ">
          <div class=""><span class="inline-flex">
              <div><a href="gradle/org.gradle.plugins.ide.api/index.html">org.gradle.plugins.ide.api</a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="386739277%2FPackages%2F-**********"></span>
                <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
              </span></span></div>
          <div class="pull-right">
            <div class="platform-tags no-gutters">
              <div class="platform-tag jvm-like">API</div>
            </div>
          </div>
        </div>
        <div><span class="brief-comment"><a data-name="386739277%2FPackages%2F-**********" anchor-label="org.gradle.plugins.ide.api" id="386739277%2FPackages%2F-**********" data-filterable-set=":docs/java_api"></a>
            <p class="paragraph">General ide plugin api.</p>
          </span></div>
      </div>
    </div>
<a data-name="649974202%2FPackages%2F-**********" anchor-label="org.gradle.plugins.ide.eclipse" id="649974202%2FPackages%2F-**********" data-filterable-set=":docs/java_api"></a>
    <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
      <div>
        <div class="main-subrow ">
          <div class=""><span class="inline-flex">
              <div><a href="gradle/org.gradle.plugins.ide.eclipse/index.html">org.gradle.plugins.ide.eclipse</a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="649974202%2FPackages%2F-**********"></span>
                <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
              </span></span></div>
          <div class="pull-right">
            <div class="platform-tags no-gutters">
              <div class="platform-tag jvm-like">API</div>
            </div>
          </div>
        </div>
        <div><span class="brief-comment"><a data-name="649974202%2FPackages%2F-**********" anchor-label="org.gradle.plugins.ide.eclipse" id="649974202%2FPackages%2F-**********" data-filterable-set=":docs/java_api"></a>
            <p class="paragraph">A <a href="gradle/org.gradle.api/-plugin/index.html">org.gradle.api.Plugin</a> for generating Eclipse files.</p>
          </span></div>
      </div>
    </div>
<a data-name="1052586933%2FPackages%2F-**********" anchor-label="org.gradle.plugins.ide.eclipse.model" id="1052586933%2FPackages%2F-**********" data-filterable-set=":docs/java_api"></a>
    <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
      <div>
        <div class="main-subrow ">
          <div class=""><span class="inline-flex">
              <div><a href="gradle/org.gradle.plugins.ide.eclipse.model/index.html">org.gradle.plugins.ide.eclipse.model</a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="1052586933%2FPackages%2F-**********"></span>
                <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
              </span></span></div>
          <div class="pull-right">
            <div class="platform-tags no-gutters">
              <div class="platform-tag jvm-like">API</div>
            </div>
          </div>
        </div>
        <div><span class="brief-comment"><a data-name="1052586933%2FPackages%2F-**********" anchor-label="org.gradle.plugins.ide.eclipse.model" id="1052586933%2FPackages%2F-**********" data-filterable-set=":docs/java_api"></a>
            <p class="paragraph">Classes for the model used by the eclipse plugins.</p>
          </span></div>
      </div>
    </div>
<a data-name="-868199064%2FPackages%2F-**********" anchor-label="org.gradle.plugins.ide.idea" id="-868199064%2FPackages%2F-**********" data-filterable-set=":docs/java_api"></a>
    <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
      <div>
        <div class="main-subrow ">
          <div class=""><span class="inline-flex">
              <div><a href="gradle/org.gradle.plugins.ide.idea/index.html">org.gradle.plugins.ide.idea</a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="-868199064%2FPackages%2F-**********"></span>
                <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
              </span></span></div>
          <div class="pull-right">
            <div class="platform-tags no-gutters">
              <div class="platform-tag jvm-like">API</div>
            </div>
          </div>
        </div>
        <div></div>
      </div>
    </div>
<a data-name="-7488285%2FPackages%2F-**********" anchor-label="org.gradle.plugins.ide.idea.model" id="-7488285%2FPackages%2F-**********" data-filterable-set=":docs/java_api"></a>
    <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
      <div>
        <div class="main-subrow ">
          <div class=""><span class="inline-flex">
              <div><a href="gradle/org.gradle.plugins.ide.idea.model/index.html">org.gradle.plugins.ide.idea.model</a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="-7488285%2FPackages%2F-**********"></span>
                <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
              </span></span></div>
          <div class="pull-right">
            <div class="platform-tags no-gutters">
              <div class="platform-tag jvm-like">API</div>
            </div>
          </div>
        </div>
        <div></div>
      </div>
    </div>
<a data-name="-173854788%2FPackages%2F-**********" anchor-label="org.gradle.plugins.signing" id="-173854788%2FPackages%2F-**********" data-filterable-set=":docs/java_api"></a>
    <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
      <div>
        <div class="main-subrow ">
          <div class=""><span class="inline-flex">
              <div><a href="gradle/org.gradle.plugins.signing/index.html">org.gradle.plugins.signing</a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="-173854788%2FPackages%2F-**********"></span>
                <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
              </span></span></div>
          <div class="pull-right">
            <div class="platform-tags no-gutters">
              <div class="platform-tag jvm-like">API</div>
            </div>
          </div>
        </div>
        <div><span class="brief-comment"><a data-name="-173854788%2FPackages%2F-**********" anchor-label="org.gradle.plugins.signing" id="-173854788%2FPackages%2F-**********" data-filterable-set=":docs/java_api"></a>
            <p class="paragraph">The signing plugin.</p>
          </span></div>
      </div>
    </div>
<a data-name="711302932%2FPackages%2F-**********" anchor-label="org.gradle.plugins.signing.signatory" id="711302932%2FPackages%2F-**********" data-filterable-set=":docs/java_api"></a>
    <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
      <div>
        <div class="main-subrow ">
          <div class=""><span class="inline-flex">
              <div><a href="gradle/org.gradle.plugins.signing.signatory/index.html">org.gradle.plugins.signing.signatory</a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="711302932%2FPackages%2F-**********"></span>
                <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
              </span></span></div>
          <div class="pull-right">
            <div class="platform-tags no-gutters">
              <div class="platform-tag jvm-like">API</div>
            </div>
          </div>
        </div>
        <div></div>
      </div>
    </div>
<a data-name="-658550593%2FPackages%2F-**********" anchor-label="org.gradle.plugins.signing.signatory.pgp" id="-658550593%2FPackages%2F-**********" data-filterable-set=":docs/java_api"></a>
    <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
      <div>
        <div class="main-subrow ">
          <div class=""><span class="inline-flex">
              <div><a href="gradle/org.gradle.plugins.signing.signatory.pgp/index.html">org.gradle.plugins.signing.signatory.pgp</a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="-658550593%2FPackages%2F-**********"></span>
                <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
              </span></span></div>
          <div class="pull-right">
            <div class="platform-tags no-gutters">
              <div class="platform-tag jvm-like">API</div>
            </div>
          </div>
        </div>
        <div></div>
      </div>
    </div>
<a data-name="1232064688%2FPackages%2F-**********" anchor-label="org.gradle.plugins.signing.type" id="1232064688%2FPackages%2F-**********" data-filterable-set=":docs/java_api"></a>
    <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
      <div>
        <div class="main-subrow ">
          <div class=""><span class="inline-flex">
              <div><a href="gradle/org.gradle.plugins.signing.type/index.html">org.gradle.plugins.signing.type</a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="1232064688%2FPackages%2F-**********"></span>
                <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
              </span></span></div>
          <div class="pull-right">
            <div class="platform-tags no-gutters">
              <div class="platform-tag jvm-like">API</div>
            </div>
          </div>
        </div>
        <div><span class="brief-comment"><a data-name="1232064688%2FPackages%2F-**********" anchor-label="org.gradle.plugins.signing.type" id="1232064688%2FPackages%2F-**********" data-filterable-set=":docs/java_api"></a>
            <p class="paragraph">The signing plugin signature types.</p>
          </span></div>
      </div>
    </div>
<a data-name="501175387%2FPackages%2F-**********" anchor-label="org.gradle.plugins.signing.type.pgp" id="501175387%2FPackages%2F-**********" data-filterable-set=":docs/java_api"></a>
    <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
      <div>
        <div class="main-subrow ">
          <div class=""><span class="inline-flex">
              <div><a href="gradle/org.gradle.plugins.signing.type.pgp/index.html">org.gradle.plugins.signing.type.pgp</a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="501175387%2FPackages%2F-**********"></span>
                <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
              </span></span></div>
          <div class="pull-right">
            <div class="platform-tags no-gutters">
              <div class="platform-tag jvm-like">API</div>
            </div>
          </div>
        </div>
        <div><span class="brief-comment"><a data-name="501175387%2FPackages%2F-**********" anchor-label="org.gradle.plugins.signing.type.pgp" id="501175387%2FPackages%2F-**********" data-filterable-set=":docs/java_api"></a>
            <p class="paragraph">PGP signing support.</p>
          </span></div>
      </div>
    </div>
<a data-name="1536835188%2FPackages%2F-**********" anchor-label="org.gradle.process" id="1536835188%2FPackages%2F-**********" data-filterable-set=":docs/java_api"></a>
    <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
      <div>
        <div class="main-subrow ">
          <div class=""><span class="inline-flex">
              <div><a href="gradle/org.gradle.process/index.html">org.gradle.process</a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="1536835188%2FPackages%2F-**********"></span>
                <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
              </span></span></div>
          <div class="pull-right">
            <div class="platform-tags no-gutters">
              <div class="platform-tag jvm-like">API</div>
            </div>
          </div>
        </div>
        <div><span class="brief-comment"><a data-name="1536835188%2FPackages%2F-**********" anchor-label="org.gradle.process" id="1536835188%2FPackages%2F-**********" data-filterable-set=":docs/java_api"></a>
            <p class="paragraph">Classes for executing system and Java processes.</p>
          </span></div>
      </div>
    </div>
<a data-name="1140386837%2FPackages%2F-**********" anchor-label="org.gradle.swiftpm" id="1140386837%2FPackages%2F-**********" data-filterable-set=":docs/java_api"></a>
    <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
      <div>
        <div class="main-subrow ">
          <div class=""><span class="inline-flex">
              <div><a href="gradle/org.gradle.swiftpm/index.html">org.gradle.swiftpm</a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="1140386837%2FPackages%2F-**********"></span>
                <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
              </span></span></div>
          <div class="pull-right">
            <div class="platform-tags no-gutters">
              <div class="platform-tag jvm-like">API</div>
            </div>
          </div>
        </div>
        <div><span class="brief-comment"><a data-name="1140386837%2FPackages%2F-**********" anchor-label="org.gradle.swiftpm" id="1140386837%2FPackages%2F-**********" data-filterable-set=":docs/java_api"></a>
            <p class="paragraph">Model classes that describe a Swift Package Manager package.</p>
          </span></div>
      </div>
    </div>
<a data-name="-756582841%2FPackages%2F-**********" anchor-label="org.gradle.swiftpm.plugins" id="-756582841%2FPackages%2F-**********" data-filterable-set=":docs/java_api"></a>
    <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
      <div>
        <div class="main-subrow ">
          <div class=""><span class="inline-flex">
              <div><a href="gradle/org.gradle.swiftpm.plugins/index.html">org.gradle.swiftpm.plugins</a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="-756582841%2FPackages%2F-**********"></span>
                <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
              </span></span></div>
          <div class="pull-right">
            <div class="platform-tags no-gutters">
              <div class="platform-tag jvm-like">API</div>
            </div>
          </div>
        </div>
        <div><span class="brief-comment"><a data-name="-756582841%2FPackages%2F-**********" anchor-label="org.gradle.swiftpm.plugins" id="-756582841%2FPackages%2F-**********" data-filterable-set=":docs/java_api"></a>
            <p class="paragraph">Plugins that produce Swift Package Manager manifests from the Gradle model.</p>
          </span></div>
      </div>
    </div>
<a data-name="-1639187979%2FPackages%2F-**********" anchor-label="org.gradle.swiftpm.tasks" id="-1639187979%2FPackages%2F-**********" data-filterable-set=":docs/java_api"></a>
    <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
      <div>
        <div class="main-subrow ">
          <div class=""><span class="inline-flex">
              <div><a href="gradle/org.gradle.swiftpm.tasks/index.html">org.gradle.swiftpm.tasks</a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="-1639187979%2FPackages%2F-**********"></span>
                <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
              </span></span></div>
          <div class="pull-right">
            <div class="platform-tags no-gutters">
              <div class="platform-tag jvm-like">API</div>
            </div>
          </div>
        </div>
        <div><span class="brief-comment"><a data-name="-1639187979%2FPackages%2F-**********" anchor-label="org.gradle.swiftpm.tasks" id="-1639187979%2FPackages%2F-**********" data-filterable-set=":docs/java_api"></a>
            <p class="paragraph">Tasks that produce Swift Package Manager manifests from the Gradle model.</p>
          </span></div>
      </div>
    </div>
<a data-name="-1425081861%2FPackages%2F-**********" anchor-label="org.gradle.testfixtures" id="-1425081861%2FPackages%2F-**********" data-filterable-set=":docs/java_api"></a>
    <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
      <div>
        <div class="main-subrow ">
          <div class=""><span class="inline-flex">
              <div><a href="gradle/org.gradle.testfixtures/index.html">org.gradle.testfixtures</a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="-1425081861%2FPackages%2F-**********"></span>
                <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
              </span></span></div>
          <div class="pull-right">
            <div class="platform-tags no-gutters">
              <div class="platform-tag jvm-like">API</div>
            </div>
          </div>
        </div>
        <div></div>
      </div>
    </div>
<a data-name="1386907150%2FPackages%2F-**********" anchor-label="org.gradle.testing.base" id="1386907150%2FPackages%2F-**********" data-filterable-set=":docs/java_api"></a>
    <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
      <div>
        <div class="main-subrow ">
          <div class=""><span class="inline-flex">
              <div><a href="gradle/org.gradle.testing.base/index.html">org.gradle.testing.base</a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="1386907150%2FPackages%2F-**********"></span>
                <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
              </span></span></div>
          <div class="pull-right">
            <div class="platform-tags no-gutters">
              <div class="platform-tag jvm-like">API</div>
            </div>
          </div>
        </div>
        <div><span class="brief-comment"><a data-name="1386907150%2FPackages%2F-**********" anchor-label="org.gradle.testing.base" id="1386907150%2FPackages%2F-**********" data-filterable-set=":docs/java_api"></a>
            <p class="paragraph">General purpose types for test suite support.</p>
          </span></div>
      </div>
    </div>
<a data-name="-1115683008%2FPackages%2F-**********" anchor-label="org.gradle.testing.base.plugins" id="-1115683008%2FPackages%2F-**********" data-filterable-set=":docs/java_api"></a>
    <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
      <div>
        <div class="main-subrow ">
          <div class=""><span class="inline-flex">
              <div><a href="gradle/org.gradle.testing.base.plugins/index.html">org.gradle.testing.base.plugins</a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="-1115683008%2FPackages%2F-**********"></span>
                <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
              </span></span></div>
          <div class="pull-right">
            <div class="platform-tags no-gutters">
              <div class="platform-tag jvm-like">API</div>
            </div>
          </div>
        </div>
        <div></div>
      </div>
    </div>
<a data-name="1514560958%2FPackages%2F-**********" anchor-label="org.gradle.testing.jacoco.plugins" id="1514560958%2FPackages%2F-**********" data-filterable-set=":docs/java_api"></a>
    <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
      <div>
        <div class="main-subrow ">
          <div class=""><span class="inline-flex">
              <div><a href="gradle/org.gradle.testing.jacoco.plugins/index.html">org.gradle.testing.jacoco.plugins</a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="1514560958%2FPackages%2F-**********"></span>
                <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
              </span></span></div>
          <div class="pull-right">
            <div class="platform-tags no-gutters">
              <div class="platform-tag jvm-like">API</div>
            </div>
          </div>
        </div>
        <div><span class="brief-comment"><a data-name="1514560958%2FPackages%2F-**********" anchor-label="org.gradle.testing.jacoco.plugins" id="1514560958%2FPackages%2F-**********" data-filterable-set=":docs/java_api"></a>
            <p class="paragraph">Plugins to work with the JaCoCo code coverage library.</p>
          </span></div>
      </div>
    </div>
<a data-name="1750881068%2FPackages%2F-**********" anchor-label="org.gradle.testing.jacoco.tasks" id="1750881068%2FPackages%2F-**********" data-filterable-set=":docs/java_api"></a>
    <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
      <div>
        <div class="main-subrow ">
          <div class=""><span class="inline-flex">
              <div><a href="gradle/org.gradle.testing.jacoco.tasks/index.html">org.gradle.testing.jacoco.tasks</a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="1750881068%2FPackages%2F-**********"></span>
                <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
              </span></span></div>
          <div class="pull-right">
            <div class="platform-tags no-gutters">
              <div class="platform-tag jvm-like">API</div>
            </div>
          </div>
        </div>
        <div><span class="brief-comment"><a data-name="1750881068%2FPackages%2F-**********" anchor-label="org.gradle.testing.jacoco.tasks" id="1750881068%2FPackages%2F-**********" data-filterable-set=":docs/java_api"></a>
            <p class="paragraph">Tasks to work with the JaCoCo code coverage library.</p>
          </span></div>
      </div>
    </div>
<a data-name="747364341%2FPackages%2F-**********" anchor-label="org.gradle.testing.jacoco.tasks.rules" id="747364341%2FPackages%2F-**********" data-filterable-set=":docs/java_api"></a>
    <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
      <div>
        <div class="main-subrow ">
          <div class=""><span class="inline-flex">
              <div><a href="gradle/org.gradle.testing.jacoco.tasks.rules/index.html">org.gradle.testing.jacoco.tasks.rules</a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="747364341%2FPackages%2F-**********"></span>
                <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
              </span></span></div>
          <div class="pull-right">
            <div class="platform-tags no-gutters">
              <div class="platform-tag jvm-like">API</div>
            </div>
          </div>
        </div>
        <div><span class="brief-comment"><a data-name="747364341%2FPackages%2F-**********" anchor-label="org.gradle.testing.jacoco.tasks.rules" id="747364341%2FPackages%2F-**********" data-filterable-set=":docs/java_api"></a>
            <p class="paragraph">Implementations for Jacoco code coverage rules.</p>
          </span></div>
      </div>
    </div>
<a data-name="1660437113%2FPackages%2F-**********" anchor-label="org.gradle.testkit.runner" id="1660437113%2FPackages%2F-**********" data-filterable-set=":docs/java_api"></a>
    <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
      <div>
        <div class="main-subrow ">
          <div class=""><span class="inline-flex">
              <div><a href="gradle/org.gradle.testkit.runner/index.html">org.gradle.testkit.runner</a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="1660437113%2FPackages%2F-**********"></span>
                <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
              </span></span></div>
          <div class="pull-right">
            <div class="platform-tags no-gutters">
              <div class="platform-tag jvm-like">API</div>
            </div>
          </div>
        </div>
        <div><span class="brief-comment"><a data-name="1660437113%2FPackages%2F-**********" anchor-label="org.gradle.testkit.runner" id="1660437113%2FPackages%2F-**********" data-filterable-set=":docs/java_api"></a>
            <p class="paragraph">Support for executing contrived Gradle builds for the purpose of testing build logic.</p>
          </span></div>
      </div>
    </div>
<a data-name="30940047%2FPackages%2F-**********" anchor-label="org.gradle.tooling" id="30940047%2FPackages%2F-**********" data-filterable-set=":docs/java_api"></a>
    <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
      <div>
        <div class="main-subrow ">
          <div class=""><span class="inline-flex">
              <div><a href="gradle/org.gradle.tooling/index.html">org.gradle.tooling</a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="30940047%2FPackages%2F-**********"></span>
                <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
              </span></span></div>
          <div class="pull-right">
            <div class="platform-tags no-gutters">
              <div class="platform-tag jvm-like">API</div>
            </div>
          </div>
        </div>
        <div><span class="brief-comment"><a data-name="30940047%2FPackages%2F-**********" anchor-label="org.gradle.tooling" id="30940047%2FPackages%2F-**********" data-filterable-set=":docs/java_api"></a>
            <p class="paragraph">The main interfaces and classes of the Gradle tooling API.</p>
          </span></div>
      </div>
    </div>
<a data-name="-1819462244%2FPackages%2F-**********" anchor-label="org.gradle.tooling.events" id="-1819462244%2FPackages%2F-**********" data-filterable-set=":docs/java_api"></a>
    <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
      <div>
        <div class="main-subrow ">
          <div class=""><span class="inline-flex">
              <div><a href="gradle/org.gradle.tooling.events/index.html">org.gradle.tooling.events</a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="-1819462244%2FPackages%2F-**********"></span>
                <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
              </span></span></div>
          <div class="pull-right">
            <div class="platform-tags no-gutters">
              <div class="platform-tag jvm-like">API</div>
            </div>
          </div>
        </div>
        <div><span class="brief-comment"><a data-name="-1819462244%2FPackages%2F-**********" anchor-label="org.gradle.tooling.events" id="-1819462244%2FPackages%2F-**********" data-filterable-set=":docs/java_api"></a>
            <p class="paragraph">The interfaces and classes related to registering for event notifications and listening to dispatched events.</p>
          </span></div>
      </div>
    </div>
<a data-name="-1727447644%2FPackages%2F-**********" anchor-label="org.gradle.tooling.events.configuration" id="-1727447644%2FPackages%2F-**********" data-filterable-set=":docs/java_api"></a>
    <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
      <div>
        <div class="main-subrow ">
          <div class=""><span class="inline-flex">
              <div><a href="gradle/org.gradle.tooling.events.configuration/index.html">org.gradle.tooling.events.configuration</a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="-1727447644%2FPackages%2F-**********"></span>
                <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
              </span></span></div>
          <div class="pull-right">
            <div class="platform-tags no-gutters">
              <div class="platform-tag jvm-like">API</div>
            </div>
          </div>
        </div>
        <div><span class="brief-comment"><a data-name="-1727447644%2FPackages%2F-**********" anchor-label="org.gradle.tooling.events.configuration" id="-1727447644%2FPackages%2F-**********" data-filterable-set=":docs/java_api"></a>
            <p class="paragraph">Project configuration specific interfaces and classes related to event notifications.</p>
          </span></div>
      </div>
    </div>
<a data-name="462672030%2FPackages%2F-**********" anchor-label="org.gradle.tooling.events.download" id="462672030%2FPackages%2F-**********" data-filterable-set=":docs/java_api"></a>
    <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
      <div>
        <div class="main-subrow ">
          <div class=""><span class="inline-flex">
              <div><a href="gradle/org.gradle.tooling.events.download/index.html">org.gradle.tooling.events.download</a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="462672030%2FPackages%2F-**********"></span>
                <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
              </span></span></div>
          <div class="pull-right">
            <div class="platform-tags no-gutters">
              <div class="platform-tag jvm-like">API</div>
            </div>
          </div>
        </div>
        <div><span class="brief-comment"><a data-name="462672030%2FPackages%2F-**********" anchor-label="org.gradle.tooling.events.download" id="462672030%2FPackages%2F-**********" data-filterable-set=":docs/java_api"></a>
            <p class="paragraph">File download specific interfaces and classes related to event notifications.</p>
          </span></div>
      </div>
    </div>
<a data-name="-1305916136%2FPackages%2F-**********" anchor-label="org.gradle.tooling.events.lifecycle" id="-1305916136%2FPackages%2F-**********" data-filterable-set=":docs/java_api"></a>
    <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
      <div>
        <div class="main-subrow ">
          <div class=""><span class="inline-flex">
              <div><a href="gradle/org.gradle.tooling.events.lifecycle/index.html">org.gradle.tooling.events.lifecycle</a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="-1305916136%2FPackages%2F-**********"></span>
                <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
              </span></span></div>
          <div class="pull-right">
            <div class="platform-tags no-gutters">
              <div class="platform-tag jvm-like">API</div>
            </div>
          </div>
        </div>
        <div><span class="brief-comment"><a data-name="-1305916136%2FPackages%2F-**********" anchor-label="org.gradle.tooling.events.lifecycle" id="-1305916136%2FPackages%2F-**********" data-filterable-set=":docs/java_api"></a>
            <p class="paragraph">Build lifecycle interfaces and classes related to event notifications.</p>
          </span></div>
      </div>
    </div>
<a data-name="1050294986%2FPackages%2F-**********" anchor-label="org.gradle.tooling.events.problems" id="1050294986%2FPackages%2F-**********" data-filterable-set=":docs/java_api"></a>
    <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
      <div>
        <div class="main-subrow ">
          <div class=""><span class="inline-flex">
              <div><a href="gradle/org.gradle.tooling.events.problems/index.html">org.gradle.tooling.events.problems</a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="1050294986%2FPackages%2F-**********"></span>
                <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
              </span></span></div>
          <div class="pull-right">
            <div class="platform-tags no-gutters">
              <div class="platform-tag jvm-like">API</div>
            </div>
          </div>
        </div>
        <div><span class="brief-comment"><a data-name="1050294986%2FPackages%2F-**********" anchor-label="org.gradle.tooling.events.problems" id="1050294986%2FPackages%2F-**********" data-filterable-set=":docs/java_api"></a>
            <p class="paragraph">Problem specific interfaces and classes related to event notifications.</p>
          </span></div>
      </div>
    </div>
<a data-name="521831771%2FPackages%2F-**********" anchor-label="org.gradle.tooling.events.task" id="521831771%2FPackages%2F-**********" data-filterable-set=":docs/java_api"></a>
    <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
      <div>
        <div class="main-subrow ">
          <div class=""><span class="inline-flex">
              <div><a href="gradle/org.gradle.tooling.events.task/index.html">org.gradle.tooling.events.task</a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="521831771%2FPackages%2F-**********"></span>
                <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
              </span></span></div>
          <div class="pull-right">
            <div class="platform-tags no-gutters">
              <div class="platform-tag jvm-like">API</div>
            </div>
          </div>
        </div>
        <div><span class="brief-comment"><a data-name="521831771%2FPackages%2F-**********" anchor-label="org.gradle.tooling.events.task" id="521831771%2FPackages%2F-**********" data-filterable-set=":docs/java_api"></a>
            <p class="paragraph">Task execution specific interfaces and classes related to event notifications.</p>
          </span></div>
      </div>
    </div>
<a data-name="1727917913%2FPackages%2F-**********" anchor-label="org.gradle.tooling.events.task.java" id="1727917913%2FPackages%2F-**********" data-filterable-set=":docs/java_api"></a>
    <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
      <div>
        <div class="main-subrow ">
          <div class=""><span class="inline-flex">
              <div><a href="gradle/org.gradle.tooling.events.task.java/index.html">org.gradle.tooling.events.task.java</a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="1727917913%2FPackages%2F-**********"></span>
                <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
              </span></span></div>
          <div class="pull-right">
            <div class="platform-tags no-gutters">
              <div class="platform-tag jvm-like">API</div>
            </div>
          </div>
        </div>
        <div><span class="brief-comment"><a data-name="1727917913%2FPackages%2F-**********" anchor-label="org.gradle.tooling.events.task.java" id="1727917913%2FPackages%2F-**********" data-filterable-set=":docs/java_api"></a>
            <p class="paragraph">Task execution result interfaces specific to Java projects.</p>
          </span></div>
      </div>
    </div>
<a data-name="-936137176%2FPackages%2F-**********" anchor-label="org.gradle.tooling.events.test" id="-936137176%2FPackages%2F-**********" data-filterable-set=":docs/java_api"></a>
    <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
      <div>
        <div class="main-subrow ">
          <div class=""><span class="inline-flex">
              <div><a href="gradle/org.gradle.tooling.events.test/index.html">org.gradle.tooling.events.test</a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="-936137176%2FPackages%2F-**********"></span>
                <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
              </span></span></div>
          <div class="pull-right">
            <div class="platform-tags no-gutters">
              <div class="platform-tag jvm-like">API</div>
            </div>
          </div>
        </div>
        <div><span class="brief-comment"><a data-name="-936137176%2FPackages%2F-**********" anchor-label="org.gradle.tooling.events.test" id="-936137176%2FPackages%2F-**********" data-filterable-set=":docs/java_api"></a>
            <p class="paragraph">Test execution specific interfaces and classes related to event notifications.</p>
          </span></div>
      </div>
    </div>
<a data-name="74576538%2FPackages%2F-**********" anchor-label="org.gradle.tooling.events.transform" id="74576538%2FPackages%2F-**********" data-filterable-set=":docs/java_api"></a>
    <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
      <div>
        <div class="main-subrow ">
          <div class=""><span class="inline-flex">
              <div><a href="gradle/org.gradle.tooling.events.transform/index.html">org.gradle.tooling.events.transform</a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="74576538%2FPackages%2F-**********"></span>
                <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
              </span></span></div>
          <div class="pull-right">
            <div class="platform-tags no-gutters">
              <div class="platform-tag jvm-like">API</div>
            </div>
          </div>
        </div>
        <div><span class="brief-comment"><a data-name="74576538%2FPackages%2F-**********" anchor-label="org.gradle.tooling.events.transform" id="74576538%2FPackages%2F-**********" data-filterable-set=":docs/java_api"></a>
            <p class="paragraph">Artifact transform execution specific interfaces and classes related to event notifications.</p>
          </span></div>
      </div>
    </div>
<a data-name="1924005863%2FPackages%2F-**********" anchor-label="org.gradle.tooling.events.work" id="1924005863%2FPackages%2F-**********" data-filterable-set=":docs/java_api"></a>
    <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
      <div>
        <div class="main-subrow ">
          <div class=""><span class="inline-flex">
              <div><a href="gradle/org.gradle.tooling.events.work/index.html">org.gradle.tooling.events.work</a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="1924005863%2FPackages%2F-**********"></span>
                <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
              </span></span></div>
          <div class="pull-right">
            <div class="platform-tags no-gutters">
              <div class="platform-tag jvm-like">API</div>
            </div>
          </div>
        </div>
        <div><span class="brief-comment"><a data-name="1924005863%2FPackages%2F-**********" anchor-label="org.gradle.tooling.events.work" id="1924005863%2FPackages%2F-**********" data-filterable-set=":docs/java_api"></a>
            <p class="paragraph">Work item execution specific interfaces and classes related to event notifications.</p>
          </span></div>
      </div>
    </div>
<a data-name="-1160604089%2FPackages%2F-**********" anchor-label="org.gradle.tooling.exceptions" id="-1160604089%2FPackages%2F-**********" data-filterable-set=":docs/java_api"></a>
    <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
      <div>
        <div class="main-subrow ">
          <div class=""><span class="inline-flex">
              <div><a href="gradle/org.gradle.tooling.exceptions/index.html">org.gradle.tooling.exceptions</a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="-1160604089%2FPackages%2F-**********"></span>
                <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
              </span></span></div>
          <div class="pull-right">
            <div class="platform-tags no-gutters">
              <div class="platform-tag jvm-like">API</div>
            </div>
          </div>
        </div>
        <div><span class="brief-comment"><a data-name="-1160604089%2FPackages%2F-**********" anchor-label="org.gradle.tooling.exceptions" id="-1160604089%2FPackages%2F-**********" data-filterable-set=":docs/java_api"></a>
            <p class="paragraph">Exceptions thrown when using the tooling API.</p>
          </span></div>
      </div>
    </div>
<a data-name="-1999894070%2FPackages%2F-**********" anchor-label="org.gradle.tooling.model" id="-1999894070%2FPackages%2F-**********" data-filterable-set=":docs/java_api"></a>
    <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
      <div>
        <div class="main-subrow ">
          <div class=""><span class="inline-flex">
              <div><a href="gradle/org.gradle.tooling.model/index.html">org.gradle.tooling.model</a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="-1999894070%2FPackages%2F-**********"></span>
                <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
              </span></span></div>
          <div class="pull-right">
            <div class="platform-tags no-gutters">
              <div class="platform-tag jvm-like">API</div>
            </div>
          </div>
        </div>
        <div><span class="brief-comment"><a data-name="-1999894070%2FPackages%2F-**********" anchor-label="org.gradle.tooling.model" id="-1999894070%2FPackages%2F-**********" data-filterable-set=":docs/java_api"></a>
            <p class="paragraph">The general-purpose tooling model types, provided by the tooling API.</p>
          </span></div>
      </div>
    </div>
<a data-name="-1049710902%2FPackages%2F-**********" anchor-label="org.gradle.tooling.model.build" id="-1049710902%2FPackages%2F-**********" data-filterable-set=":docs/java_api"></a>
    <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
      <div>
        <div class="main-subrow ">
          <div class=""><span class="inline-flex">
              <div><a href="gradle/org.gradle.tooling.model.build/index.html">org.gradle.tooling.model.build</a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="-1049710902%2FPackages%2F-**********"></span>
                <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
              </span></span></div>
          <div class="pull-right">
            <div class="platform-tags no-gutters">
              <div class="platform-tag jvm-like">API</div>
            </div>
          </div>
        </div>
        <div><span class="brief-comment"><a data-name="-1049710902%2FPackages%2F-**********" anchor-label="org.gradle.tooling.model.build" id="-1049710902%2FPackages%2F-**********" data-filterable-set=":docs/java_api"></a>
            <p class="paragraph">Tooling models for the build environment, which includes information such as Gradle or Java versions.</p>
          </span></div>
      </div>
    </div>
<a data-name="-768600001%2FPackages%2F-**********" anchor-label="org.gradle.tooling.model.cpp" id="-768600001%2FPackages%2F-**********" data-filterable-set=":docs/java_api"></a>
    <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
      <div>
        <div class="main-subrow ">
          <div class=""><span class="inline-flex">
              <div><a href="gradle/org.gradle.tooling.model.cpp/index.html">org.gradle.tooling.model.cpp</a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="-768600001%2FPackages%2F-**********"></span>
                <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
              </span></span></div>
          <div class="pull-right">
            <div class="platform-tags no-gutters">
              <div class="platform-tag jvm-like">API</div>
            </div>
          </div>
        </div>
        <div><span class="brief-comment"><a data-name="-768600001%2FPackages%2F-**********" anchor-label="org.gradle.tooling.model.cpp" id="-768600001%2FPackages%2F-**********" data-filterable-set=":docs/java_api"></a>
            <p class="paragraph">Types that represent the tooling model for C++ projects. </p>
          </span></div>
      </div>
    </div>
<a data-name="-273120541%2FPackages%2F-**********" anchor-label="org.gradle.tooling.model.eclipse" id="-273120541%2FPackages%2F-**********" data-filterable-set=":docs/java_api"></a>
    <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
      <div>
        <div class="main-subrow ">
          <div class=""><span class="inline-flex">
              <div><a href="gradle/org.gradle.tooling.model.eclipse/index.html">org.gradle.tooling.model.eclipse</a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="-273120541%2FPackages%2F-**********"></span>
                <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
              </span></span></div>
          <div class="pull-right">
            <div class="platform-tags no-gutters">
              <div class="platform-tag jvm-like">API</div>
            </div>
          </div>
        </div>
        <div><span class="brief-comment"><a data-name="-273120541%2FPackages%2F-**********" anchor-label="org.gradle.tooling.model.eclipse" id="-273120541%2FPackages%2F-**********" data-filterable-set=":docs/java_api"></a>
            <p class="paragraph">Eclipse-centric tooling models.</p>
          </span></div>
      </div>
    </div>
<a data-name="-1580499793%2FPackages%2F-**********" anchor-label="org.gradle.tooling.model.gradle" id="-1580499793%2FPackages%2F-**********" data-filterable-set=":docs/java_api"></a>
    <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
      <div>
        <div class="main-subrow ">
          <div class=""><span class="inline-flex">
              <div><a href="gradle/org.gradle.tooling.model.gradle/index.html">org.gradle.tooling.model.gradle</a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="-1580499793%2FPackages%2F-**********"></span>
                <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
              </span></span></div>
          <div class="pull-right">
            <div class="platform-tags no-gutters">
              <div class="platform-tag jvm-like">API</div>
            </div>
          </div>
        </div>
        <div><span class="brief-comment"><a data-name="-1580499793%2FPackages%2F-**********" anchor-label="org.gradle.tooling.model.gradle" id="-1580499793%2FPackages%2F-**********" data-filterable-set=":docs/java_api"></a>
            <p class="paragraph">The tooling models for Gradle builds and projects.</p>
          </span></div>
      </div>
    </div>
<a data-name="-563454753%2FPackages%2F-**********" anchor-label="org.gradle.tooling.model.idea" id="-563454753%2FPackages%2F-**********" data-filterable-set=":docs/java_api"></a>
    <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
      <div>
        <div class="main-subrow ">
          <div class=""><span class="inline-flex">
              <div><a href="gradle/org.gradle.tooling.model.idea/index.html">org.gradle.tooling.model.idea</a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="-563454753%2FPackages%2F-**********"></span>
                <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
              </span></span></div>
          <div class="pull-right">
            <div class="platform-tags no-gutters">
              <div class="platform-tag jvm-like">API</div>
            </div>
          </div>
        </div>
        <div><span class="brief-comment"><a data-name="-563454753%2FPackages%2F-**********" anchor-label="org.gradle.tooling.model.idea" id="-563454753%2FPackages%2F-**********" data-filterable-set=":docs/java_api"></a>
            <p class="paragraph">IntelliJ IDEA centric tooling models.</p>
          </span></div>
      </div>
    </div>
<a data-name="1627969610%2FPackages%2F-**********" anchor-label="org.gradle.tooling.model.java" id="1627969610%2FPackages%2F-**********" data-filterable-set=":docs/java_api"></a>
    <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
      <div>
        <div class="main-subrow ">
          <div class=""><span class="inline-flex">
              <div><a href="gradle/org.gradle.tooling.model.java/index.html">org.gradle.tooling.model.java</a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="1627969610%2FPackages%2F-**********"></span>
                <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
              </span></span></div>
          <div class="pull-right">
            <div class="platform-tags no-gutters">
              <div class="platform-tag jvm-like">API</div>
            </div>
          </div>
        </div>
        <div><span class="brief-comment"><a data-name="1627969610%2FPackages%2F-**********" anchor-label="org.gradle.tooling.model.java" id="1627969610%2FPackages%2F-**********" data-filterable-set=":docs/java_api"></a>
            <p class="paragraph">Java-specific details for tooling models.</p>
          </span></div>
      </div>
    </div>
<a data-name="-177560712%2FPackages%2F-**********" anchor-label="org.gradle.tooling.model.kotlin.dsl" id="-177560712%2FPackages%2F-**********" data-filterable-set=":docs/java_api"></a>
    <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
      <div>
        <div class="main-subrow ">
          <div class=""><span class="inline-flex">
              <div><a href="gradle/org.gradle.tooling.model.kotlin.dsl/index.html">org.gradle.tooling.model.kotlin.dsl</a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="-177560712%2FPackages%2F-**********"></span>
                <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
              </span></span></div>
          <div class="pull-right">
            <div class="platform-tags no-gutters">
              <div class="platform-tag jvm-like">API</div>
            </div>
          </div>
        </div>
        <div><span class="brief-comment"><a data-name="-177560712%2FPackages%2F-**********" anchor-label="org.gradle.tooling.model.kotlin.dsl" id="-177560712%2FPackages%2F-**********" data-filterable-set=":docs/java_api"></a>
            <p class="paragraph">Kotlin DSL related tooling models.</p>
          </span></div>
      </div>
    </div>
<a data-name="-**********%2FPackages%2F-**********" anchor-label="org.gradle.tooling.provider.model" id="-**********%2FPackages%2F-**********" data-filterable-set=":docs/java_api"></a>
    <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
      <div>
        <div class="main-subrow ">
          <div class=""><span class="inline-flex">
              <div><a href="gradle/org.gradle.tooling.provider.model/index.html">org.gradle.tooling.provider.model</a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="-**********%2FPackages%2F-**********"></span>
                <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
              </span></span></div>
          <div class="pull-right">
            <div class="platform-tags no-gutters">
              <div class="platform-tag jvm-like">API</div>
            </div>
          </div>
        </div>
        <div><span class="brief-comment"><a data-name="-**********%2FPackages%2F-**********" anchor-label="org.gradle.tooling.provider.model" id="-**********%2FPackages%2F-**********" data-filterable-set=":docs/java_api"></a>
            <p class="paragraph">Interfaces and classes that allow tooling models to be made available to the tooling API client.</p>
          </span></div>
      </div>
    </div>
<a data-name="-**********%2FPackages%2F-**********" anchor-label="org.gradle.util" id="-**********%2FPackages%2F-**********" data-filterable-set=":docs/java_api"></a>
    <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
      <div>
        <div class="main-subrow ">
          <div class=""><span class="inline-flex">
              <div><a href="gradle/org.gradle.util/index.html">org.gradle.util</a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="-**********%2FPackages%2F-**********"></span>
                <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
              </span></span></div>
          <div class="pull-right">
            <div class="platform-tags no-gutters">
              <div class="platform-tag jvm-like">API</div>
            </div>
          </div>
        </div>
        <div></div>
      </div>
    </div>
<a data-name="590314027%2FPackages%2F-**********" anchor-label="org.gradle.vcs" id="590314027%2FPackages%2F-**********" data-filterable-set=":docs/java_api"></a>
    <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
      <div>
        <div class="main-subrow ">
          <div class=""><span class="inline-flex">
              <div><a href="gradle/org.gradle.vcs/index.html">org.gradle.vcs</a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="590314027%2FPackages%2F-**********"></span>
                <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
              </span></span></div>
          <div class="pull-right">
            <div class="platform-tags no-gutters">
              <div class="platform-tag jvm-like">API</div>
            </div>
          </div>
        </div>
        <div><span class="brief-comment"><a data-name="590314027%2FPackages%2F-**********" anchor-label="org.gradle.vcs" id="590314027%2FPackages%2F-**********" data-filterable-set=":docs/java_api"></a>
            <p class="paragraph">Packages for version control systems.</p>
          </span></div>
      </div>
    </div>
<a data-name="-1484844657%2FPackages%2F-**********" anchor-label="org.gradle.vcs.git" id="-1484844657%2FPackages%2F-**********" data-filterable-set=":docs/java_api"></a>
    <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
      <div>
        <div class="main-subrow ">
          <div class=""><span class="inline-flex">
              <div><a href="gradle/org.gradle.vcs.git/index.html">org.gradle.vcs.git</a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="-1484844657%2FPackages%2F-**********"></span>
                <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
              </span></span></div>
          <div class="pull-right">
            <div class="platform-tags no-gutters">
              <div class="platform-tag jvm-like">API</div>
            </div>
          </div>
        </div>
        <div><span class="brief-comment"><a data-name="-1484844657%2FPackages%2F-**********" anchor-label="org.gradle.vcs.git" id="-1484844657%2FPackages%2F-**********" data-filterable-set=":docs/java_api"></a>
            <p class="paragraph">The API for dealing with Version Control Systems in Gradle.</p>
          </span></div>
      </div>
    </div>
<a data-name="785005488%2FPackages%2F-**********" anchor-label="org.gradle.work" id="785005488%2FPackages%2F-**********" data-filterable-set=":docs/java_api"></a>
    <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
      <div>
        <div class="main-subrow ">
          <div class=""><span class="inline-flex">
              <div><a href="gradle/org.gradle.work/index.html">org.gradle.work</a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="785005488%2FPackages%2F-**********"></span>
                <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
              </span></span></div>
          <div class="pull-right">
            <div class="platform-tags no-gutters">
              <div class="platform-tag jvm-like">API</div>
            </div>
          </div>
        </div>
        <div><span class="brief-comment"><a data-name="785005488%2FPackages%2F-**********" anchor-label="org.gradle.work" id="785005488%2FPackages%2F-**********" data-filterable-set=":docs/java_api"></a>
            <p class="paragraph">Classes used for implementing units of work.</p>
          </span></div>
      </div>
    </div>
<a data-name="-2025959110%2FPackages%2F-**********" anchor-label="org.gradle.workers" id="-2025959110%2FPackages%2F-**********" data-filterable-set=":docs/java_api"></a>
    <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
      <div>
        <div class="main-subrow ">
          <div class=""><span class="inline-flex">
              <div><a href="gradle/org.gradle.workers/index.html">org.gradle.workers</a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="-2025959110%2FPackages%2F-**********"></span>
                <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
              </span></span></div>
          <div class="pull-right">
            <div class="platform-tags no-gutters">
              <div class="platform-tag jvm-like">API</div>
            </div>
          </div>
        </div>
        <div><span class="brief-comment"><a data-name="-2025959110%2FPackages%2F-**********" anchor-label="org.gradle.workers" id="-2025959110%2FPackages%2F-**********" data-filterable-set=":docs/java_api"></a>
            <p class="paragraph">Workers allow running pieces of work in the background, either in-process in isolated classloaders or out-of-process in reusable daemons.</p>
          </span></div>
      </div>
    </div>
  </div>
</div>
      <div class="footer">
        <span class="go-to-top-icon"><a href="#content" id="go-to-top-link"></a></span><span>Gradle Kotlin DSL Reference</span><span
                class="pull-right"><span>Generated by </span><a
                href="https://github.com/Kotlin/dokka"><span>dokka</span><span class="padded-icon"></span></a></span>
      </div>
    </div>
</div>
</body>
</html>
