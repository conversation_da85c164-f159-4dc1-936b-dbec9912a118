<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc -->
<title>TextResource (Gradle API 8.5)</title>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<link rel="stylesheet" type="text/css" href="../../../../javadoc.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../jquery/jquery-ui.min.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../jquery-ui.overrides.css" title="Style">
<script type="text/javascript" src="../../../../script.js"></script>
<script type="text/javascript" src="../../../../jquery/jszip/dist/jszip.min.js"></script>
<script type="text/javascript" src="../../../../jquery/jszip-utils/dist/jszip-utils.min.js"></script>
<!--[if IE]>
<script type="text/javascript" src="../../../../jquery/jszip-utils/dist/jszip-utils-ie.min.js"></script>
<![endif]-->
<script type="text/javascript" src="../../../../jquery/jquery-3.6.1.min.js"></script>
<script type="text/javascript" src="../../../../jquery/jquery-ui.min.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="TextResource (Gradle API 8.5)";
        }
    }
    catch(err) {
    }
//-->
var data = {"i0":6,"i1":6,"i2":6,"i3":6,"i4":6,"i5":6,"i6":6};
var tabs = {65535:["t0","All Methods"],2:["t2","Instance Methods"],4:["t3","Abstract Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
var pathtoroot = "../../../../";
var useModuleDirectories = false;
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<div class="fixedNav">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../allclasses.html">All&nbsp;Classes</a></li>
</ul>
<ul class="navListSearch">
<li><label for="search">SEARCH:</label>
<input type="text" id="search" value="search" disabled="disabled">
<input type="reset" id="reset" value="reset" disabled="disabled">
</li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
</div>
<div class="navPadding">&nbsp;</div>
<script type="text/javascript"><!--
$('.navPadding').css('padding-top', $('.fixedNav').css("height"));
//-->
</script>
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle"><span class="packageLabelInType">Package</span>&nbsp;<a href="package-summary.html">org.gradle.api.resources</a></div>
<h2 title="Interface TextResource" class="title">Interface TextResource</h2>
</div>
<div class="contentContainer">
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt>All Superinterfaces:</dt>
<dd><code><a href="../Buildable.html" title="interface in org.gradle.api">Buildable</a></code></dd>
</dl>
<hr>
<pre>public interface <span class="typeNameLabel">TextResource</span>
extends <a href="../Buildable.html" title="interface in org.gradle.api">Buildable</a></pre>
<div class="block">A read-only body of text backed by a string, file, archive entry, or other source.
 To create a text resource, use one of the factory methods in <a href="TextResourceFactory.html" title="interface in org.gradle.api.resources"><code>TextResourceFactory</code></a>
 (e.g. <code>project.resources.text.fromFile(myFile)</code>).</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>2.2</dd>
</dl>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t3" class="tableTab"><span><a href="javascript:show(4);">Abstract Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colSecond" scope="col">Method</th>
<th class="colLast" scope="col">Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>java.io.File</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#asFile--">asFile</a></span>()</code></th>
<td class="colLast">
<div class="block">Same as <code>asFile(Charset.defaultCharset().name())</code>.</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>java.io.File</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#asFile-java.lang.String-">asFile</a></span>&#8203;(java.lang.String&nbsp;charset)</code></th>
<td class="colLast">
<div class="block">Returns a file containing the resource's text and using the given character encoding.</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>java.io.Reader</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#asReader--">asReader</a></span>()</code></th>
<td class="colLast">
<div class="block">Returns an unbuffered <code>Reader</code> that allows the resource's text to be read.</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>java.lang.String</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#asString--">asString</a></span>()</code></th>
<td class="colLast">
<div class="block">Returns a string containing the resource's text</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code><a href="../tasks/TaskDependency.html" title="interface in org.gradle.api.tasks">TaskDependency</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getBuildDependencies--">getBuildDependencies</a></span>()</code></th>
<td class="colLast">
<div class="block">Returns a dependency which contains the tasks which build this artifact.</div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code><a href="../file/FileCollection.html" title="interface in org.gradle.api.file">FileCollection</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getInputFiles--">getInputFiles</a></span>()</code></th>
<td class="colLast">
<div class="block">Returns the input files registered when this resource is used as task input.</div>
</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code>java.lang.Object</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getInputProperties--">getInputProperties</a></span>()</code></th>
<td class="colLast">
<div class="block">Returns the input properties registered when this resource is used as task input.</div>
</td>
</tr>
</table>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="asString--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>asString</h4>
<pre class="methodSignature">java.lang.String&nbsp;asString()</pre>
<div class="block">Returns a string containing the resource's text</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>a string containing the resource's text</dd>
</dl>
</li>
</ul>
<a name="asReader--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>asReader</h4>
<pre class="methodSignature">java.io.Reader&nbsp;asReader()</pre>
<div class="block">Returns an unbuffered <code>Reader</code> that allows the resource's text to be read. The caller is responsible for closing the reader.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>a reader that allows the resource's text to be read.</dd>
</dl>
</li>
</ul>
<a name="asFile-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>asFile</h4>
<pre class="methodSignature">java.io.File&nbsp;asFile&#8203;(java.lang.String&nbsp;charset)</pre>
<div class="block">Returns a file containing the resource's text and using the given character encoding.
 If this resource is backed by a file with a matching encoding, that file may be
 returned. Otherwise, a temporary file will be created and returned.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>charset</code> - a character encoding (e.g. <code>"utf-8"</code>)</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>a file containing the resource's text and using the given character encoding</dd>
</dl>
</li>
</ul>
<a name="asFile--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>asFile</h4>
<pre class="methodSignature">java.io.File&nbsp;asFile()</pre>
<div class="block">Same as <code>asFile(Charset.defaultCharset().name())</code>.</div>
</li>
</ul>
<a name="getInputProperties--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getInputProperties</h4>
<pre class="methodSignature">@Nullable
<a href="../tasks/Optional.html" title="annotation in org.gradle.api.tasks">@Optional</a>
<a href="../tasks/Input.html" title="annotation in org.gradle.api.tasks">@Input</a>
java.lang.Object&nbsp;getInputProperties()</pre>
<div class="block">Returns the input properties registered when this resource is used as task input.
 Not typically used directly.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>the input properties registered when this resource is used as task input</dd>
</dl>
</li>
</ul>
<a name="getInputFiles--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getInputFiles</h4>
<pre class="methodSignature">@Nullable
<a href="../tasks/Optional.html" title="annotation in org.gradle.api.tasks">@Optional</a>
<a href="../tasks/PathSensitive.html" title="annotation in org.gradle.api.tasks">@PathSensitive</a>(<a href="../tasks/PathSensitivity.html#NONE">NONE</a>)
<a href="../tasks/InputFiles.html" title="annotation in org.gradle.api.tasks">@InputFiles</a>
<a href="../file/FileCollection.html" title="interface in org.gradle.api.file">FileCollection</a>&nbsp;getInputFiles()</pre>
<div class="block">Returns the input files registered when this resource is used as task input.
 Not typically used directly.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>the input files registered when this resource is used as task input</dd>
</dl>
</li>
</ul>
<a name="getBuildDependencies--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>getBuildDependencies</h4>
<pre class="methodSignature"><a href="../tasks/Internal.html" title="annotation in org.gradle.api.tasks">@Internal</a>
<a href="../tasks/TaskDependency.html" title="interface in org.gradle.api.tasks">TaskDependency</a>&nbsp;getBuildDependencies()</pre>
<div class="block"><span class="descfrmTypeLabel">Description copied from interface:&nbsp;<code><a href="../Buildable.html#getBuildDependencies--">Buildable</a></code></span></div>
<div class="block">Returns a dependency which contains the tasks which build this artifact. All <code>Buildable</code> implementations
 must ensure that the returned dependency object is live, so that it tracks changes to the dependencies of this
 buildable.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../Buildable.html#getBuildDependencies--">getBuildDependencies</a></code>&nbsp;in interface&nbsp;<code><a href="../Buildable.html" title="interface in org.gradle.api">Buildable</a></code></dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>The dependency. Never returns null. Returns an empty dependency when this artifact is not built by any
         tasks.</dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../allclasses.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
