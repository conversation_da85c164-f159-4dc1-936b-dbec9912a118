<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc -->
<title>MetadataSupplierAware (Gradle API 8.5)</title>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<link rel="stylesheet" type="text/css" href="../../../../../javadoc.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../../jquery/jquery-ui.min.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../../jquery-ui.overrides.css" title="Style">
<script type="text/javascript" src="../../../../../script.js"></script>
<script type="text/javascript" src="../../../../../jquery/jszip/dist/jszip.min.js"></script>
<script type="text/javascript" src="../../../../../jquery/jszip-utils/dist/jszip-utils.min.js"></script>
<!--[if IE]>
<script type="text/javascript" src="../../../../../jquery/jszip-utils/dist/jszip-utils-ie.min.js"></script>
<![endif]-->
<script type="text/javascript" src="../../../../../jquery/jquery-3.6.1.min.js"></script>
<script type="text/javascript" src="../../../../../jquery/jquery-ui.min.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="MetadataSupplierAware (Gradle API 8.5)";
        }
    }
    catch(err) {
    }
//-->
var data = {"i0":6,"i1":6,"i2":6,"i3":6};
var tabs = {65535:["t0","All Methods"],2:["t2","Instance Methods"],4:["t3","Abstract Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
var pathtoroot = "../../../../../";
var useModuleDirectories = false;
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<div class="fixedNav">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../index-all.html">Index</a></li>
<li><a href="../../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../../allclasses.html">All&nbsp;Classes</a></li>
</ul>
<ul class="navListSearch">
<li><label for="search">SEARCH:</label>
<input type="text" id="search" value="search" disabled="disabled">
<input type="reset" id="reset" value="reset" disabled="disabled">
</li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
</div>
<div class="navPadding">&nbsp;</div>
<script type="text/javascript"><!--
$('.navPadding').css('padding-top', $('.fixedNav').css("height"));
//-->
</script>
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle"><span class="packageLabelInType">Package</span>&nbsp;<a href="package-summary.html">org.gradle.api.artifacts.repositories</a></div>
<h2 title="Interface MetadataSupplierAware" class="title">Interface MetadataSupplierAware</h2>
</div>
<div class="contentContainer">
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt>All Known Subinterfaces:</dt>
<dd><code><a href="IvyArtifactRepository.html" title="interface in org.gradle.api.artifacts.repositories">IvyArtifactRepository</a></code>, <code><a href="MavenArtifactRepository.html" title="interface in org.gradle.api.artifacts.repositories">MavenArtifactRepository</a></code></dd>
</dl>
<hr>
<pre>public interface <span class="typeNameLabel">MetadataSupplierAware</span></pre>
<div class="block">Interface for repositories which support custom metadata suppliers and/or version listers.
 A custom version lister or metadata supplier can be used as an optimization technique to
 avoid too many requests on a server. By providing such rules, a plugin or build author can
 provide the necessary information to perform component selection without having to actually
 fetch the component metadata on a server.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>4.9</dd>
</dl>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t3" class="tableTab"><span><a href="javascript:show(4);">Abstract Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colSecond" scope="col">Method</th>
<th class="colLast" scope="col">Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#setComponentVersionsLister-java.lang.Class-">setComponentVersionsLister</a></span>&#8203;(java.lang.Class&lt;? extends <a href="../ComponentMetadataVersionLister.html" title="interface in org.gradle.api.artifacts">ComponentMetadataVersionLister</a>&gt;&nbsp;lister)</code></th>
<td class="colLast">
<div class="block">Sets a custom component versions lister.</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#setComponentVersionsLister-java.lang.Class-org.gradle.api.Action-">setComponentVersionsLister</a></span>&#8203;(java.lang.Class&lt;? extends <a href="../ComponentMetadataVersionLister.html" title="interface in org.gradle.api.artifacts">ComponentMetadataVersionLister</a>&gt;&nbsp;lister,
                          <a href="../../Action.html" title="interface in org.gradle.api">Action</a>&lt;? super <a href="../../ActionConfiguration.html" title="interface in org.gradle.api">ActionConfiguration</a>&gt;&nbsp;configureAction)</code></th>
<td class="colLast">
<div class="block">Sets a custom component versions lister.</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#setMetadataSupplier-java.lang.Class-">setMetadataSupplier</a></span>&#8203;(java.lang.Class&lt;? extends <a href="../ComponentMetadataSupplier.html" title="interface in org.gradle.api.artifacts">ComponentMetadataSupplier</a>&gt;&nbsp;rule)</code></th>
<td class="colLast">
<div class="block">Sets a custom metadata rule, which is capable of supplying the metadata of a component (status, status scheme, changing flag)
 whenever a dynamic version is requested.</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#setMetadataSupplier-java.lang.Class-org.gradle.api.Action-">setMetadataSupplier</a></span>&#8203;(java.lang.Class&lt;? extends <a href="../ComponentMetadataSupplier.html" title="interface in org.gradle.api.artifacts">ComponentMetadataSupplier</a>&gt;&nbsp;rule,
                   <a href="../../Action.html" title="interface in org.gradle.api">Action</a>&lt;? super <a href="../../ActionConfiguration.html" title="interface in org.gradle.api">ActionConfiguration</a>&gt;&nbsp;configureAction)</code></th>
<td class="colLast">
<div class="block">Sets a custom metadata rule, possibly configuring the rule.</div>
</td>
</tr>
</table>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="setMetadataSupplier-java.lang.Class-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setMetadataSupplier</h4>
<pre class="methodSignature">void&nbsp;setMetadataSupplier&#8203;(java.lang.Class&lt;? extends <a href="../ComponentMetadataSupplier.html" title="interface in org.gradle.api.artifacts">ComponentMetadataSupplier</a>&gt;&nbsp;rule)</pre>
<div class="block">Sets a custom metadata rule, which is capable of supplying the metadata of a component (status, status scheme, changing flag)
 whenever a dynamic version is requested. It can be used to provide metadata directly, instead of having to parse the Ivy
 descriptor.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>rule</code> - the class of the rule. Gradle will instantiate a new rule for each dependency which requires metadata.</dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>4.9</dd>
</dl>
</li>
</ul>
<a name="setMetadataSupplier-java.lang.Class-org.gradle.api.Action-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setMetadataSupplier</h4>
<pre class="methodSignature">void&nbsp;setMetadataSupplier&#8203;(java.lang.Class&lt;? extends <a href="../ComponentMetadataSupplier.html" title="interface in org.gradle.api.artifacts">ComponentMetadataSupplier</a>&gt;&nbsp;rule,
                         <a href="../../Action.html" title="interface in org.gradle.api">Action</a>&lt;? super <a href="../../ActionConfiguration.html" title="interface in org.gradle.api">ActionConfiguration</a>&gt;&nbsp;configureAction)</pre>
<div class="block">Sets a custom metadata rule, possibly configuring the rule.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>rule</code> - the class of the rule. Gradle will instantiate a new rule for each dependency which requires metadata.</dd>
<dd><code>configureAction</code> - the action to use to configure the rule.</dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>4.9</dd>
</dl>
</li>
</ul>
<a name="setComponentVersionsLister-java.lang.Class-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setComponentVersionsLister</h4>
<pre class="methodSignature">void&nbsp;setComponentVersionsLister&#8203;(java.lang.Class&lt;? extends <a href="../ComponentMetadataVersionLister.html" title="interface in org.gradle.api.artifacts">ComponentMetadataVersionLister</a>&gt;&nbsp;lister)</pre>
<div class="block">Sets a custom component versions lister. A versions lister will be called whenever a dynamic version is requested.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>lister</code> - the class of the lister.</dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>4.9</dd>
</dl>
</li>
</ul>
<a name="setComponentVersionsLister-java.lang.Class-org.gradle.api.Action-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>setComponentVersionsLister</h4>
<pre class="methodSignature">void&nbsp;setComponentVersionsLister&#8203;(java.lang.Class&lt;? extends <a href="../ComponentMetadataVersionLister.html" title="interface in org.gradle.api.artifacts">ComponentMetadataVersionLister</a>&gt;&nbsp;lister,
                                <a href="../../Action.html" title="interface in org.gradle.api">Action</a>&lt;? super <a href="../../ActionConfiguration.html" title="interface in org.gradle.api">ActionConfiguration</a>&gt;&nbsp;configureAction)</pre>
<div class="block">Sets a custom component versions lister. A versions lister will be called whenever a dynamic version is requested.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>lister</code> - the class of the lister.</dd>
<dd><code>configureAction</code> - the action to use to configure the lister.</dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>4.9</dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../index-all.html">Index</a></li>
<li><a href="../../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../../allclasses.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
