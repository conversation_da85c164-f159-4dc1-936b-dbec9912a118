<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc -->
<title>ArtifactTypeDefinition (Gradle API 8.5)</title>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<link rel="stylesheet" type="text/css" href="../../../../../javadoc.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../../jquery/jquery-ui.min.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../../jquery-ui.overrides.css" title="Style">
<script type="text/javascript" src="../../../../../script.js"></script>
<script type="text/javascript" src="../../../../../jquery/jszip/dist/jszip.min.js"></script>
<script type="text/javascript" src="../../../../../jquery/jszip-utils/dist/jszip-utils.min.js"></script>
<!--[if IE]>
<script type="text/javascript" src="../../../../../jquery/jszip-utils/dist/jszip-utils-ie.min.js"></script>
<![endif]-->
<script type="text/javascript" src="../../../../../jquery/jquery-3.6.1.min.js"></script>
<script type="text/javascript" src="../../../../../jquery/jquery-ui.min.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="ArtifactTypeDefinition (Gradle API 8.5)";
        }
    }
    catch(err) {
    }
//-->
var data = {"i0":6,"i1":6};
var tabs = {65535:["t0","All Methods"],2:["t2","Instance Methods"],4:["t3","Abstract Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
var pathtoroot = "../../../../../";
var useModuleDirectories = false;
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<div class="fixedNav">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../index-all.html">Index</a></li>
<li><a href="../../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../../allclasses.html">All&nbsp;Classes</a></li>
</ul>
<ul class="navListSearch">
<li><label for="search">SEARCH:</label>
<input type="text" id="search" value="search" disabled="disabled">
<input type="reset" id="reset" value="reset" disabled="disabled">
</li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li><a href="#nested.class.summary">Nested</a>&nbsp;|&nbsp;</li>
<li><a href="#field.summary">Field</a>&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li><a href="#field.detail">Field</a>&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
</div>
<div class="navPadding">&nbsp;</div>
<script type="text/javascript"><!--
$('.navPadding').css('padding-top', $('.fixedNav').css("height"));
//-->
</script>
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle"><span class="packageLabelInType">Package</span>&nbsp;<a href="package-summary.html">org.gradle.api.artifacts.type</a></div>
<h2 title="Interface ArtifactTypeDefinition" class="title">Interface ArtifactTypeDefinition</h2>
</div>
<div class="contentContainer">
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt>All Superinterfaces:</dt>
<dd><code><a href="../../attributes/HasAttributes.html" title="interface in org.gradle.api.attributes">HasAttributes</a></code>, <code><a href="../../Named.html" title="interface in org.gradle.api">Named</a></code></dd>
</dl>
<hr>
<pre>public interface <span class="typeNameLabel">ArtifactTypeDefinition</span>
extends <a href="../../attributes/HasAttributes.html" title="interface in org.gradle.api.attributes">HasAttributes</a>, <a href="../../Named.html" title="interface in org.gradle.api">Named</a></pre>
<div class="block">Meta-data about a particular type of artifacts.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>4.0</dd>
</dl>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ======== NESTED CLASS SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="nested.class.summary">
<!--   -->
</a>
<h3>Nested Class Summary</h3>
<ul class="blockList">
<li class="blockList"><a name="nested.classes.inherited.from.class.org.gradle.api.Named">
<!--   -->
</a>
<h3>Nested classes/interfaces inherited from interface&nbsp;org.gradle.api.<a href="../../Named.html" title="interface in org.gradle.api">Named</a></h3>
<code><a href="../../Named.Namer.html" title="class in org.gradle.api">Named.Namer</a></code></li>
</ul>
</li>
</ul>
<!-- =========== FIELD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="field.summary">
<!--   -->
</a>
<h3>Field Summary</h3>
<table class="memberSummary" summary="Field Summary table, listing fields, and an explanation">
<caption><span>Fields</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colSecond" scope="col">Field</th>
<th class="colLast" scope="col">Description</th>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static <a href="../../attributes/Attribute.html" title="class in org.gradle.api.attributes">Attribute</a>&lt;java.lang.String&gt;</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#ARTIFACT_TYPE_ATTRIBUTE">ARTIFACT_TYPE_ATTRIBUTE</a></span></code></th>
<td class="colLast">
<div class="block">The attribute that represents the type of the artifact.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#BINARY_DATA_TYPE">BINARY_DATA_TYPE</a></span></code></th>
<td class="colLast">
<div class="block">Represents a binary file</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#DIRECTORY_TYPE">DIRECTORY_TYPE</a></span></code></th>
<td class="colLast">
<div class="block">Represents a raw directory</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#JAR_TYPE">JAR_TYPE</a></span></code></th>
<td class="colLast">
<div class="block">Represents a JAR file.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#JVM_CLASS_DIRECTORY">JVM_CLASS_DIRECTORY</a></span></code></th>
<td class="colLast">
<div class="block">Represents a directory tree containing class files.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#JVM_RESOURCES_DIRECTORY">JVM_RESOURCES_DIRECTORY</a></span></code></th>
<td class="colLast">
<div class="block">Represents a directory tree containing jvm classpath resource files.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#ZIP_TYPE">ZIP_TYPE</a></span></code></th>
<td class="colLast">
<div class="block">Represents a zip file</div>
</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t3" class="tableTab"><span><a href="javascript:show(4);">Abstract Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colSecond" scope="col">Method</th>
<th class="colLast" scope="col">Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code><a href="../../attributes/AttributeContainer.html" title="interface in org.gradle.api.attributes">AttributeContainer</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getAttributes--">getAttributes</a></span>()</code></th>
<td class="colLast">
<div class="block">Defines the set of attributes to apply to a component that is packaged as an artifact of this type, when no other attributes are defined.</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>java.util.Set&lt;java.lang.String&gt;</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getFileNameExtensions--">getFileNameExtensions</a></span>()</code></th>
<td class="colLast">
<div class="block">Returns the set of file name extensions that should be mapped to this artifact type.</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.org.gradle.api.Named">
<!--   -->
</a>
<h3>Methods inherited from interface&nbsp;org.gradle.api.<a href="../../Named.html" title="interface in org.gradle.api">Named</a></h3>
<code><a href="../../Named.html#getName--">getName</a></code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ FIELD DETAIL =========== -->
<ul class="blockList">
<li class="blockList"><a name="field.detail">
<!--   -->
</a>
<h3>Field Detail</h3>
<a name="ARTIFACT_TYPE_ATTRIBUTE">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ARTIFACT_TYPE_ATTRIBUTE</h4>
<pre>static final&nbsp;<a href="../../attributes/Attribute.html" title="class in org.gradle.api.attributes">Attribute</a>&lt;java.lang.String&gt; ARTIFACT_TYPE_ATTRIBUTE</pre>
<div class="block">The attribute that represents the type of the artifact.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>7.3</dd>
</dl>
</li>
</ul>
<a name="JAR_TYPE">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>JAR_TYPE</h4>
<pre>static final&nbsp;java.lang.String JAR_TYPE</pre>
<div class="block">Represents a JAR file.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>4.0</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../../../constant-values.html#org.gradle.api.artifacts.type.ArtifactTypeDefinition.JAR_TYPE">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="JVM_CLASS_DIRECTORY">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>JVM_CLASS_DIRECTORY</h4>
<pre>static final&nbsp;java.lang.String JVM_CLASS_DIRECTORY</pre>
<div class="block">Represents a directory tree containing class files.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>4.0</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../../../constant-values.html#org.gradle.api.artifacts.type.ArtifactTypeDefinition.JVM_CLASS_DIRECTORY">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="JVM_RESOURCES_DIRECTORY">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>JVM_RESOURCES_DIRECTORY</h4>
<pre>static final&nbsp;java.lang.String JVM_RESOURCES_DIRECTORY</pre>
<div class="block">Represents a directory tree containing jvm classpath resource files.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>4.0</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../../../constant-values.html#org.gradle.api.artifacts.type.ArtifactTypeDefinition.JVM_RESOURCES_DIRECTORY">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="ZIP_TYPE">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ZIP_TYPE</h4>
<pre>static final&nbsp;java.lang.String ZIP_TYPE</pre>
<div class="block">Represents a zip file</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>5.3</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../../../constant-values.html#org.gradle.api.artifacts.type.ArtifactTypeDefinition.ZIP_TYPE">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="DIRECTORY_TYPE">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>DIRECTORY_TYPE</h4>
<pre>static final&nbsp;java.lang.String DIRECTORY_TYPE</pre>
<div class="block">Represents a raw directory</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>5.3</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../../../constant-values.html#org.gradle.api.artifacts.type.ArtifactTypeDefinition.DIRECTORY_TYPE">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="BINARY_DATA_TYPE">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>BINARY_DATA_TYPE</h4>
<pre><a href="../../Incubating.html" title="annotation in org.gradle.api">@Incubating</a>
static final&nbsp;java.lang.String BINARY_DATA_TYPE</pre>
<div class="block">Represents a binary file</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>7.4</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../../../constant-values.html#org.gradle.api.artifacts.type.ArtifactTypeDefinition.BINARY_DATA_TYPE">Constant Field Values</a></dd>
</dl>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="getFileNameExtensions--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getFileNameExtensions</h4>
<pre class="methodSignature">java.util.Set&lt;java.lang.String&gt;&nbsp;getFileNameExtensions()</pre>
<div class="block">Returns the set of file name extensions that should be mapped to this artifact type. Defaults to the name of this type.</div>
</li>
</ul>
<a name="getAttributes--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>getAttributes</h4>
<pre class="methodSignature"><a href="../../attributes/AttributeContainer.html" title="interface in org.gradle.api.attributes">AttributeContainer</a>&nbsp;getAttributes()</pre>
<div class="block">Defines the set of attributes to apply to a component that is packaged as an artifact of this type, when no other attributes are defined. For example, these attributes are applied when a Maven module contains an artifact with one of the extensions listed in <a href="#getFileNameExtensions--"><code>getFileNameExtensions()</code></a>.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../attributes/HasAttributes.html#getAttributes--">getAttributes</a></code>&nbsp;in interface&nbsp;<code><a href="../../attributes/HasAttributes.html" title="interface in org.gradle.api.attributes">HasAttributes</a></code></dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../index-all.html">Index</a></li>
<li><a href="../../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../../allclasses.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li><a href="#nested.class.summary">Nested</a>&nbsp;|&nbsp;</li>
<li><a href="#field.summary">Field</a>&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li><a href="#field.detail">Field</a>&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
