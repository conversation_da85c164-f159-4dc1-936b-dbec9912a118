<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc -->
<title>Provider (Gradle API 8.5)</title>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<link rel="stylesheet" type="text/css" href="../../../../javadoc.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../jquery/jquery-ui.min.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../jquery-ui.overrides.css" title="Style">
<script type="text/javascript" src="../../../../script.js"></script>
<script type="text/javascript" src="../../../../jquery/jszip/dist/jszip.min.js"></script>
<script type="text/javascript" src="../../../../jquery/jszip-utils/dist/jszip-utils.min.js"></script>
<!--[if IE]>
<script type="text/javascript" src="../../../../jquery/jszip-utils/dist/jszip-utils-ie.min.js"></script>
<![endif]-->
<script type="text/javascript" src="../../../../jquery/jquery-3.6.1.min.js"></script>
<script type="text/javascript" src="../../../../jquery/jquery-ui.min.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="Provider (Gradle API 8.5)";
        }
    }
    catch(err) {
    }
//-->
var data = {"i0":6,"i1":6,"i2":38,"i3":6,"i4":6,"i5":6,"i6":6,"i7":6,"i8":6,"i9":6,"i10":6};
var tabs = {65535:["t0","All Methods"],2:["t2","Instance Methods"],4:["t3","Abstract Methods"],32:["t6","Deprecated Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
var pathtoroot = "../../../../";
var useModuleDirectories = false;
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<div class="fixedNav">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../allclasses.html">All&nbsp;Classes</a></li>
</ul>
<ul class="navListSearch">
<li><label for="search">SEARCH:</label>
<input type="text" id="search" value="search" disabled="disabled">
<input type="reset" id="reset" value="reset" disabled="disabled">
</li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
</div>
<div class="navPadding">&nbsp;</div>
<script type="text/javascript"><!--
$('.navPadding').css('padding-top', $('.fixedNav').css("height"));
//-->
</script>
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle"><span class="packageLabelInType">Package</span>&nbsp;<a href="package-summary.html">org.gradle.api.provider</a></div>
<h2 title="Interface Provider" class="title">Interface Provider&lt;T&gt;</h2>
</div>
<div class="contentContainer">
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt><span class="paramLabel">Type Parameters:</span></dt>
<dd><code>T</code> - Type of value represented by provider</dd>
</dl>
<dl>
<dt>All Known Subinterfaces:</dt>
<dd><code><a href="../../language/BinaryProvider.html" title="interface in org.gradle.language">BinaryProvider</a>&lt;T&gt;</code>, <code><a href="../file/DirectoryProperty.html" title="interface in org.gradle.api.file">DirectoryProperty</a></code>, <code><a href="../file/FileSystemLocationProperty.html" title="interface in org.gradle.api.file">FileSystemLocationProperty</a>&lt;T&gt;</code>, <code><a href="ListProperty.html" title="interface in org.gradle.api.provider">ListProperty</a>&lt;T&gt;</code>, <code><a href="MapProperty.html" title="interface in org.gradle.api.provider">MapProperty</a>&lt;K,&#8203;V&gt;</code>, <code><a href="../NamedDomainObjectProvider.html" title="interface in org.gradle.api">NamedDomainObjectProvider</a>&lt;T&gt;</code>, <code><a href="Property.html" title="interface in org.gradle.api.provider">Property</a>&lt;T&gt;</code>, <code><a href="../file/RegularFileProperty.html" title="interface in org.gradle.api.file">RegularFileProperty</a></code>, <code><a href="SetProperty.html" title="interface in org.gradle.api.provider">SetProperty</a>&lt;T&gt;</code>, <code><a href="../tasks/TaskProvider.html" title="interface in org.gradle.api.tasks">TaskProvider</a>&lt;T&gt;</code></dd>
</dl>
<hr>
<pre><a href="../NonExtensible.html" title="annotation in org.gradle.api">@NonExtensible</a>
public interface <span class="typeNameLabel">Provider&lt;T&gt;</span></pre>
<div class="block">A container object that provides a value of a specific type. The value can be retrieved using one of the query methods
 such as <a href="#get--"><code>get()</code></a> or <a href="#getOrNull--"><code>getOrNull()</code></a>.

 <p>
 A provider may not always have a value available, for example when the value may not yet be known but will be known
 at some point in the future. When a value is not available, <a href="#isPresent--"><code>isPresent()</code></a> returns <code>false</code> and retrieving
 the value will fail with an exception.
 </p>

 <p>
 A provider may not always provide the same value. Although there are no methods on this interface to change the value,
 the provider implementation may be mutable or use values from some changing source. A provider may also provide a value
 that is mutable and that changes over time.
 </p>

 <p>
 A provider may represent a task output. Such a provider carries information about the task producing its value. When
 this provider is attached to an input of another task, Gradle will automatically determine the task dependencies based
 on this connection.
 </p>

 <p>
 A typical use of a provider is to pass values from one Gradle model element to another, e.g. from a project extension
 to a task, or between tasks. Providers also allow expensive computations to be deferred until their value is actually
 needed, usually at task execution time.
 </p>

 <p>
 There are a number of ways to create a <a href="Provider.html" title="interface in org.gradle.api.provider"><code>Provider</code></a> instance. Some common methods:
 </p>

 <ul>
     <li>A number of Gradle types, such as <a href="Property.html" title="interface in org.gradle.api.provider"><code>Property</code></a>, extend <a href="Provider.html" title="interface in org.gradle.api.provider"><code>Provider</code></a> and can be used directly as a provider.</li>
     <li>Calling <a href="#map-org.gradle.api.Transformer-"><code>map(Transformer)</code></a> to create a new provider from an existing provider.</li>
     <li>Using the return value of <a href="../tasks/TaskContainer.html#register-java.lang.String-"><code>TaskContainer.register(String)</code></a>, which is a provider that represents the task instance.</li>
     <li>Using the methods on <a href="../file/Directory.html" title="interface in org.gradle.api.file"><code>Directory</code></a> and <a href="../file/DirectoryProperty.html" title="interface in org.gradle.api.file"><code>DirectoryProperty</code></a> to produce file providers.</li>
     <li>By calling <a href="ProviderFactory.html#provider-java.util.concurrent.Callable-"><code>ProviderFactory.provider(Callable)</code></a> or <a href="../Project.html#provider-java.util.concurrent.Callable-"><code>Project.provider(Callable)</code></a> to create a new provider from a <code>Callable</code>.</li>
 </ul>

 <p>
 For a provider whose value can be mutated, see <a href="Property.html" title="interface in org.gradle.api.provider"><code>Property</code></a> and the methods on <a href="../model/ObjectFactory.html" title="interface in org.gradle.api.model"><code>ObjectFactory</code></a>.
 </p>

 <p>
 <b>Note:</b> This interface is not intended for implementation by build script or plugin authors.
 </p></div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>4.0</dd>
</dl>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t3" class="tableTab"><span><a href="javascript:show(4);">Abstract Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t6" class="tableTab"><span><a href="javascript:show(32);">Deprecated Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colSecond" scope="col">Method</th>
<th class="colLast" scope="col">Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code><a href="Provider.html" title="interface in org.gradle.api.provider">Provider</a>&lt;<a href="Provider.html" title="type parameter in Provider">T</a>&gt;</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#filter-org.gradle.api.specs.Spec-">filter</a></span>&#8203;(<a href="../specs/Spec.html" title="interface in org.gradle.api.specs">Spec</a>&lt;? super <a href="Provider.html" title="type parameter in Provider">T</a>&gt;&nbsp;spec)</code></th>
<td class="colLast">
<div class="block">Returns a new <a href="Provider.html" title="interface in org.gradle.api.provider"><code>Provider</code></a> with the value of this provider if the passed spec is satisfied and no value otherwise.</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>&lt;S&gt;&nbsp;<a href="Provider.html" title="interface in org.gradle.api.provider">Provider</a>&lt;S&gt;</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#flatMap-org.gradle.api.Transformer-">flatMap</a></span>&#8203;(<a href="../Transformer.html" title="interface in org.gradle.api">Transformer</a>&lt;? extends @Nullable <a href="Provider.html" title="interface in org.gradle.api.provider">Provider</a>&lt;? extends S&gt;,&#8203;? super <a href="Provider.html" title="type parameter in Provider">T</a>&gt;&nbsp;transformer)</code></th>
<td class="colLast">
<div class="block">Returns a new <a href="Provider.html" title="interface in org.gradle.api.provider"><code>Provider</code></a> from the value of this provider transformed using the given function.</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code><a href="Provider.html" title="interface in org.gradle.api.provider">Provider</a>&lt;<a href="Provider.html" title="type parameter in Provider">T</a>&gt;</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#forUseAtConfigurationTime--">forUseAtConfigurationTime</a></span>()</code></th>
<td class="colLast">
<div class="block"><span class="deprecatedLabel">Deprecated.</span></div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code><a href="Provider.html" title="type parameter in Provider">T</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#get--">get</a></span>()</code></th>
<td class="colLast">
<div class="block">Returns the value of this provider if it has a value present, otherwise throws <code>java.lang.IllegalStateException</code>.</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code><a href="Provider.html" title="type parameter in Provider">T</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getOrElse-T-">getOrElse</a></span>&#8203;(<a href="Provider.html" title="type parameter in Provider">T</a>&nbsp;defaultValue)</code></th>
<td class="colLast">
<div class="block">Returns the value of this provider if it has a value present.</div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code><a href="Provider.html" title="type parameter in Provider">T</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getOrNull--">getOrNull</a></span>()</code></th>
<td class="colLast">
<div class="block">Returns the value of this provider if it has a value present.</div>
</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#isPresent--">isPresent</a></span>()</code></th>
<td class="colLast">
<div class="block">Returns <code>true</code> if there is a value present, otherwise <code>false</code>.</div>
</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code>&lt;S&gt;&nbsp;<a href="Provider.html" title="interface in org.gradle.api.provider">Provider</a>&lt;S&gt;</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#map-org.gradle.api.Transformer-">map</a></span>&#8203;(<a href="../Transformer.html" title="interface in org.gradle.api">Transformer</a>&lt;? extends @Nullable S,&#8203;? super <a href="Provider.html" title="type parameter in Provider">T</a>&gt;&nbsp;transformer)</code></th>
<td class="colLast">
<div class="block">Returns a new <a href="Provider.html" title="interface in org.gradle.api.provider"><code>Provider</code></a> whose value is the value of this provider transformed using the given function.</div>
</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code><a href="Provider.html" title="interface in org.gradle.api.provider">Provider</a>&lt;<a href="Provider.html" title="type parameter in Provider">T</a>&gt;</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#orElse-org.gradle.api.provider.Provider-">orElse</a></span>&#8203;(<a href="Provider.html" title="interface in org.gradle.api.provider">Provider</a>&lt;? extends <a href="Provider.html" title="type parameter in Provider">T</a>&gt;&nbsp;provider)</code></th>
<td class="colLast">
<div class="block">Returns a <a href="Provider.html" title="interface in org.gradle.api.provider"><code>Provider</code></a> whose value is the value of this provider, if present, otherwise uses the
 value from the given provider, if present.</div>
</td>
</tr>
<tr id="i9" class="rowColor">
<td class="colFirst"><code><a href="Provider.html" title="interface in org.gradle.api.provider">Provider</a>&lt;<a href="Provider.html" title="type parameter in Provider">T</a>&gt;</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#orElse-T-">orElse</a></span>&#8203;(<a href="Provider.html" title="type parameter in Provider">T</a>&nbsp;value)</code></th>
<td class="colLast">
<div class="block">Returns a <a href="Provider.html" title="interface in org.gradle.api.provider"><code>Provider</code></a> whose value is the value of this provider, if present, otherwise the
 given default value.</div>
</td>
</tr>
<tr id="i10" class="altColor">
<td class="colFirst"><code>&lt;U,&#8203;R&gt;<br><a href="Provider.html" title="interface in org.gradle.api.provider">Provider</a>&lt;R&gt;</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#zip-org.gradle.api.provider.Provider-java.util.function.BiFunction-">zip</a></span>&#8203;(<a href="Provider.html" title="interface in org.gradle.api.provider">Provider</a>&lt;U&gt;&nbsp;right,
   java.util.function.BiFunction&lt;? super <a href="Provider.html" title="type parameter in Provider">T</a>,&#8203;? super U,&#8203;? extends @Nullable R&gt;&nbsp;combiner)</code></th>
<td class="colLast">
<div class="block">Returns a provider which value will be computed by combining this provider value with another
 provider value using the supplied combiner function.</div>
</td>
</tr>
</table>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="get--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>get</h4>
<pre class="methodSignature"><a href="Provider.html" title="type parameter in Provider">T</a>&nbsp;get()</pre>
<div class="block">Returns the value of this provider if it has a value present, otherwise throws <code>java.lang.IllegalStateException</code>.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>the current value of this provider.</dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code>java.lang.IllegalStateException</code> - if there is no value present</dd>
</dl>
</li>
</ul>
<a name="getOrNull--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getOrNull</h4>
<pre class="methodSignature">@Nullable
<a href="Provider.html" title="type parameter in Provider">T</a>&nbsp;getOrNull()</pre>
<div class="block">Returns the value of this provider if it has a value present. Returns <code>null</code> a value is not available.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>the value or <code>null</code></dd>
</dl>
</li>
</ul>
<a name="getOrElse-java.lang.Object-">
<!--   -->
</a><a name="getOrElse-T-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getOrElse</h4>
<pre class="methodSignature"><a href="Provider.html" title="type parameter in Provider">T</a>&nbsp;getOrElse&#8203;(<a href="Provider.html" title="type parameter in Provider">T</a>&nbsp;defaultValue)</pre>
<div class="block">Returns the value of this provider if it has a value present. Returns the given default value if a value is
 not available.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>the value or the default value.</dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>4.3</dd>
</dl>
</li>
</ul>
<a name="map-org.gradle.api.Transformer-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>map</h4>
<pre class="methodSignature">&lt;S&gt;&nbsp;<a href="Provider.html" title="interface in org.gradle.api.provider">Provider</a>&lt;S&gt;&nbsp;map&#8203;(<a href="../Transformer.html" title="interface in org.gradle.api">Transformer</a>&lt;? extends @Nullable S,&#8203;? super <a href="Provider.html" title="type parameter in Provider">T</a>&gt;&nbsp;transformer)</pre>
<div class="block">Returns a new <a href="Provider.html" title="interface in org.gradle.api.provider"><code>Provider</code></a> whose value is the value of this provider transformed using the given function.

 <p>
 The resulting provider will be live, so that each time it is queried, it queries the original (this) provider
 and applies the transformation to the result. Whenever the original provider has no value, the new provider
 will also have no value and the transformation will not be called.
 </p>

 <p>
 When this provider represents a task or the output of a task, the new provider will be considered an output
 of the task and will carry dependency information that Gradle can use to automatically attach task dependencies
 to tasks that use the new provider for input values.
 </p></div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>transformer</code> - The transformer to apply to values. May return <code>null</code>, in which case the provider will have no value.</dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>4.3</dd>
</dl>
</li>
</ul>
<a name="filter-org.gradle.api.specs.Spec-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>filter</h4>
<pre class="methodSignature"><a href="../Incubating.html" title="annotation in org.gradle.api">@Incubating</a>
<a href="Provider.html" title="interface in org.gradle.api.provider">Provider</a>&lt;<a href="Provider.html" title="type parameter in Provider">T</a>&gt;&nbsp;filter&#8203;(<a href="../specs/Spec.html" title="interface in org.gradle.api.specs">Spec</a>&lt;? super <a href="Provider.html" title="type parameter in Provider">T</a>&gt;&nbsp;spec)</pre>
<div class="block">Returns a new <a href="Provider.html" title="interface in org.gradle.api.provider"><code>Provider</code></a> with the value of this provider if the passed spec is satisfied and no value otherwise.

 <p>
 The resulting provider will be live, so that each time it is queried, it queries the original (this) provider
 and applies the spec to the result. Whenever the original provider has no value, the new provider
 will also have no value and the spec will not be called.
 </p></div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>spec</code> - The spec to test the value.</dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>8.5</dd>
</dl>
</li>
</ul>
<a name="flatMap-org.gradle.api.Transformer-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>flatMap</h4>
<pre class="methodSignature">&lt;S&gt;&nbsp;<a href="Provider.html" title="interface in org.gradle.api.provider">Provider</a>&lt;S&gt;&nbsp;flatMap&#8203;(<a href="../Transformer.html" title="interface in org.gradle.api">Transformer</a>&lt;? extends @Nullable <a href="Provider.html" title="interface in org.gradle.api.provider">Provider</a>&lt;? extends S&gt;,&#8203;? super <a href="Provider.html" title="type parameter in Provider">T</a>&gt;&nbsp;transformer)</pre>
<div class="block">Returns a new <a href="Provider.html" title="interface in org.gradle.api.provider"><code>Provider</code></a> from the value of this provider transformed using the given function.

 <p>
 While very similar in functionality to the regular <a href="#map-org.gradle.api.Transformer-"><code>map</code></a> operation, this method
 offers a convenient way of connecting together task inputs and outputs. (For a deeper understanding of
 the topic see the <a href="https://docs.gradle.org/current/userguide/lazy_configuration.html">Lazy Configuration</a>
 section of the Gradle manual.)</p>

 <p>
 Task inputs and outputs often take the form of <a href="Provider.html" title="interface in org.gradle.api.provider"><code>providers</code></a> or <a href="Property.html" title="interface in org.gradle.api.provider"><code>properties</code></a>,
 the latter being a special case of provider whose value can be changed at will. An example of using
 <code>flatMap</code> for connecting such properties would be following:
 </p>

 <pre><code>
 class Producer extends DefaultTask {
     @OutputFile
     abstract RegularFileProperty getOutputFile()

     //irrelevant details omitted
 }

 class Consumer extends DefaultTask {
     @InputFile
     abstract RegularFileProperty getInputFile()

     //irrelevant details omitted
 }

 def producer = tasks.register("producer", Producer)
 def consumer = tasks.register("consumer", Consumer)

 consumer.configure {
     inputFile = producer.flatMap { it.outputFile }
 }
 </code></pre>

 <p>
 An added benefit of connecting input and output properties like this is that Gradle can automatically
 detect task dependencies based on such connections. To make this happen at code level, any task
 details associated with this provider (the one on which <code>flatMap</code> is being called) are ignored.
 The new provider will use whatever task details are associated with the return value of the transformation.
 </p>

 <p>
 The new provider returned by <code>flatMap</code> will be live, so that each time it is queried, it queries
 this provider and applies the transformation to the result. Whenever this provider has no value, the new
 provider will also have no value and the transformation will not be called.
 </p></div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>transformer</code> - The transformer to apply to values. May return <code>null</code>, in which case the
 provider will have no value.</dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>5.0</dd>
</dl>
</li>
</ul>
<a name="isPresent--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isPresent</h4>
<pre class="methodSignature">boolean&nbsp;isPresent()</pre>
<div class="block">Returns <code>true</code> if there is a value present, otherwise <code>false</code>.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd><code>true</code> if there is a value present, otherwise <code>false</code></dd>
</dl>
</li>
</ul>
<a name="orElse-java.lang.Object-">
<!--   -->
</a><a name="orElse-T-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>orElse</h4>
<pre class="methodSignature"><a href="Provider.html" title="interface in org.gradle.api.provider">Provider</a>&lt;<a href="Provider.html" title="type parameter in Provider">T</a>&gt;&nbsp;orElse&#8203;(<a href="Provider.html" title="type parameter in Provider">T</a>&nbsp;value)</pre>
<div class="block">Returns a <a href="Provider.html" title="interface in org.gradle.api.provider"><code>Provider</code></a> whose value is the value of this provider, if present, otherwise the
 given default value.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - The default value to use when this provider has no value.</dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>5.6</dd>
</dl>
</li>
</ul>
<a name="orElse-org.gradle.api.provider.Provider-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>orElse</h4>
<pre class="methodSignature"><a href="Provider.html" title="interface in org.gradle.api.provider">Provider</a>&lt;<a href="Provider.html" title="type parameter in Provider">T</a>&gt;&nbsp;orElse&#8203;(<a href="Provider.html" title="interface in org.gradle.api.provider">Provider</a>&lt;? extends <a href="Provider.html" title="type parameter in Provider">T</a>&gt;&nbsp;provider)</pre>
<div class="block">Returns a <a href="Provider.html" title="interface in org.gradle.api.provider"><code>Provider</code></a> whose value is the value of this provider, if present, otherwise uses the
 value from the given provider, if present.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>provider</code> - The provider whose value should be used when this provider has no value.</dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>5.6</dd>
</dl>
</li>
</ul>
<a name="forUseAtConfigurationTime--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>forUseAtConfigurationTime</h4>
<pre class="methodSignature">@Deprecated
<a href="Provider.html" title="interface in org.gradle.api.provider">Provider</a>&lt;<a href="Provider.html" title="type parameter in Provider">T</a>&gt;&nbsp;forUseAtConfigurationTime()</pre>
<div class="deprecationBlock"><span class="deprecatedLabel">Deprecated.</span></div>
<div class="block">Returns a view of this <a href="Provider.html" title="interface in org.gradle.api.provider"><code>Provider</code></a> which can be safely read at configuration time.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>6.5</dd>
</dl>
</li>
</ul>
<a name="zip-org.gradle.api.provider.Provider-java.util.function.BiFunction-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>zip</h4>
<pre class="methodSignature">&lt;U,&#8203;R&gt;&nbsp;<a href="Provider.html" title="interface in org.gradle.api.provider">Provider</a>&lt;R&gt;&nbsp;zip&#8203;(<a href="Provider.html" title="interface in org.gradle.api.provider">Provider</a>&lt;U&gt;&nbsp;right,
                            java.util.function.BiFunction&lt;? super <a href="Provider.html" title="type parameter in Provider">T</a>,&#8203;? super U,&#8203;? extends @Nullable R&gt;&nbsp;combiner)</pre>
<div class="block">Returns a provider which value will be computed by combining this provider value with another
 provider value using the supplied combiner function.

 <p>
 If the supplied providers represents a task or the output of a task, the resulting provider
 will carry the dependency information.
 </p></div>
<dl>
<dt><span class="paramLabel">Type Parameters:</span></dt>
<dd><code>U</code> - the type of the second provider</dd>
<dd><code>R</code> - the type of the result of the combiner</dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>right</code> - the second provider to combine with</dd>
<dd><code>combiner</code> - the combiner of values. May return <code>null</code>, in which case the provider
 will have no value.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>a combined provider</dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>6.6</dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../allclasses.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
