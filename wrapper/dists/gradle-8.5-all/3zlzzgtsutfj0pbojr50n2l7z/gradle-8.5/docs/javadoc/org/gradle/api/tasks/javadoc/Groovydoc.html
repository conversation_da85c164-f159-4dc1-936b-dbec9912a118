<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc -->
<title>Groovydoc (Gradle API 8.5)</title>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<link rel="stylesheet" type="text/css" href="../../../../../javadoc.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../../jquery/jquery-ui.min.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../../jquery-ui.overrides.css" title="Style">
<script type="text/javascript" src="../../../../../script.js"></script>
<script type="text/javascript" src="../../../../../jquery/jszip/dist/jszip.min.js"></script>
<script type="text/javascript" src="../../../../../jquery/jszip-utils/dist/jszip-utils.min.js"></script>
<!--[if IE]>
<script type="text/javascript" src="../../../../../jquery/jszip-utils/dist/jszip-utils-ie.min.js"></script>
<![endif]-->
<script type="text/javascript" src="../../../../../jquery/jquery-3.6.1.min.js"></script>
<script type="text/javascript" src="../../../../../jquery/jquery-ui.min.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="Groovydoc (Gradle API 8.5)";
        }
    }
    catch(err) {
    }
//-->
var data = {"i0":10,"i1":10,"i2":10,"i3":10,"i4":10,"i5":10,"i6":10,"i7":10,"i8":10,"i9":10,"i10":10,"i11":10,"i12":10,"i13":10,"i14":10,"i15":10,"i16":10,"i17":10,"i18":10,"i19":10,"i20":10,"i21":10,"i22":10,"i23":10,"i24":10,"i25":10,"i26":10,"i27":10,"i28":10,"i29":10,"i30":10,"i31":10,"i32":10,"i33":10};
var tabs = {65535:["t0","All Methods"],2:["t2","Instance Methods"],8:["t4","Concrete Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
var pathtoroot = "../../../../../";
var useModuleDirectories = false;
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<div class="fixedNav">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../index-all.html">Index</a></li>
<li><a href="../../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../../allclasses.html">All&nbsp;Classes</a></li>
</ul>
<ul class="navListSearch">
<li><label for="search">SEARCH:</label>
<input type="text" id="search" value="search" disabled="disabled">
<input type="reset" id="reset" value="reset" disabled="disabled">
</li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li><a href="#nested.class.summary">Nested</a>&nbsp;|&nbsp;</li>
<li><a href="#field.summary">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
</div>
<div class="navPadding">&nbsp;</div>
<script type="text/javascript"><!--
$('.navPadding').css('padding-top', $('.fixedNav').css("height"));
//-->
</script>
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle"><span class="packageLabelInType">Package</span>&nbsp;<a href="package-summary.html">org.gradle.api.tasks.javadoc</a></div>
<h2 title="Class Groovydoc" class="title">Class Groovydoc</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li>org.gradle.api.internal.AbstractTask</li>
<li>
<ul class="inheritance">
<li><a href="../../DefaultTask.html" title="class in org.gradle.api">org.gradle.api.DefaultTask</a></li>
<li>
<ul class="inheritance">
<li>org.gradle.api.internal.ConventionTask</li>
<li>
<ul class="inheritance">
<li><a href="../SourceTask.html" title="class in org.gradle.api.tasks">org.gradle.api.tasks.SourceTask</a></li>
<li>
<ul class="inheritance">
<li>org.gradle.api.tasks.javadoc.Groovydoc</li>
</ul>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt>All Implemented Interfaces:</dt>
<dd><code>java.lang.Comparable&lt;<a href="../../Task.html" title="interface in org.gradle.api">Task</a>&gt;</code>, <code>org.gradle.api.internal.DynamicObjectAware</code>, <code>org.gradle.api.internal.IConventionAware</code>, <code>org.gradle.api.internal.TaskInternal</code>, <code><a href="../../plugins/ExtensionAware.html" title="interface in org.gradle.api.plugins">ExtensionAware</a></code>, <code><a href="../../Task.html" title="interface in org.gradle.api">Task</a></code>, <code><a href="../util/PatternFilterable.html" title="interface in org.gradle.api.tasks.util">PatternFilterable</a></code>, <code><a href="../../../util/Configurable.html" title="interface in org.gradle.util">Configurable</a>&lt;<a href="../../Task.html" title="interface in org.gradle.api">Task</a>&gt;</code></dd>
</dl>
<hr>
<pre><a href="../CacheableTask.html" title="annotation in org.gradle.api.tasks">@CacheableTask</a>
public abstract class <span class="typeNameLabel">Groovydoc</span>
extends <a href="../SourceTask.html" title="class in org.gradle.api.tasks">SourceTask</a></pre>
<div class="block"><p>Generates HTML API documentation for Groovy source, and optionally, Java source.

 <p>This task uses Groovy's Groovydoc tool to generate the API documentation. Please note
 that the Groovydoc tool has some limitations at the moment. The version of the Groovydoc
 that is used, is the one from the Groovy dependency defined in the build script.</div>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ======== NESTED CLASS SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="nested.class.summary">
<!--   -->
</a>
<h3>Nested Class Summary</h3>
<table class="memberSummary" summary="Nested Class Summary table, listing nested classes, and an explanation">
<caption><span>Nested Classes</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colSecond" scope="col">Class</th>
<th class="colLast" scope="col">Description</th>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static class&nbsp;</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="Groovydoc.Link.html" title="class in org.gradle.api.tasks.javadoc">Groovydoc.Link</a></span></code></th>
<td class="colLast">
<div class="block">A Link class represent a link between groovydoc/javadoc output and url.</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="nested.classes.inherited.from.class.org.gradle.api.Task">
<!--   -->
</a>
<h3>Nested classes/interfaces inherited from interface&nbsp;org.gradle.api.<a href="../../Task.html" title="interface in org.gradle.api">Task</a></h3>
<code><a href="../../Task.Namer.html" title="class in org.gradle.api">Task.Namer</a></code></li>
</ul>
</li>
</ul>
<!-- =========== FIELD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="field.summary">
<!--   -->
</a>
<h3>Field Summary</h3>
<ul class="blockList">
<li class="blockList"><a name="fields.inherited.from.class.org.gradle.api.Task">
<!--   -->
</a>
<h3>Fields inherited from interface&nbsp;org.gradle.api.<a href="../../Task.html" title="interface in org.gradle.api">Task</a></h3>
<code><a href="../../Task.html#TASK_ACTION">TASK_ACTION</a>, <a href="../../Task.html#TASK_CONSTRUCTOR_ARGS">TASK_CONSTRUCTOR_ARGS</a>, <a href="../../Task.html#TASK_DEPENDS_ON">TASK_DEPENDS_ON</a>, <a href="../../Task.html#TASK_DESCRIPTION">TASK_DESCRIPTION</a>, <a href="../../Task.html#TASK_GROUP">TASK_GROUP</a>, <a href="../../Task.html#TASK_NAME">TASK_NAME</a>, <a href="../../Task.html#TASK_OVERWRITE">TASK_OVERWRITE</a>, <a href="../../Task.html#TASK_TYPE">TASK_TYPE</a></code></li>
</ul>
</li>
</ul>
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.summary">
<!--   -->
</a>
<h3>Constructor Summary</h3>
<table class="memberSummary" summary="Constructor Summary table, listing constructors, and an explanation">
<caption><span>Constructors</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Constructor</th>
<th class="colLast" scope="col">Description</th>
</tr>
<tr class="altColor">
<th class="colConstructorName" scope="row"><code><span class="memberNameLink"><a href="#Groovydoc--">Groovydoc</a></span>()</code></th>
<td class="colLast">&nbsp;</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colSecond" scope="col">Method</th>
<th class="colLast" scope="col">Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>protected void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#generate--">generate</a></span>()</code></th>
<td class="colLast">&nbsp;</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code><a href="../../provider/Property.html" title="interface in org.gradle.api.provider">Property</a>&lt;<a href="GroovydocAccess.html" title="enum in org.gradle.api.tasks.javadoc">GroovydocAccess</a>&gt;</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getAccess--">getAccess</a></span>()</code></th>
<td class="colLast">
<div class="block">The most restrictive access level to include in the Groovydoc.</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>org.gradle.api.internal.tasks.AntGroovydoc</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getAntGroovydoc--">getAntGroovydoc</a></span>()</code></th>
<td class="colLast">&nbsp;</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code><a href="../../file/FileCollection.html" title="interface in org.gradle.api.file">FileCollection</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getClasspath--">getClasspath</a></span>()</code></th>
<td class="colLast">
<div class="block">Returns the classpath used to locate classes referenced by the documented sources.</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code>protected org.gradle.internal.file.Deleter</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getDeleter--">getDeleter</a></span>()</code></th>
<td class="colLast">&nbsp;</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code>java.io.File</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getDestinationDir--">getDestinationDir</a></span>()</code></th>
<td class="colLast">
<div class="block">Returns the directory to generate the documentation into.</div>
</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code>java.lang.String</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getDocTitle--">getDocTitle</a></span>()</code></th>
<td class="colLast">
<div class="block">Returns the title for the package index(first) page.</div>
</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code>java.lang.String</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getFooter--">getFooter</a></span>()</code></th>
<td class="colLast">
<div class="block">Returns the HTML footer for each page.</div>
</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code><a href="../../file/FileCollection.html" title="interface in org.gradle.api.file">FileCollection</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getGroovyClasspath--">getGroovyClasspath</a></span>()</code></th>
<td class="colLast">
<div class="block">Returns the classpath containing the Groovy library to be used.</div>
</td>
</tr>
<tr id="i9" class="rowColor">
<td class="colFirst"><code>java.lang.String</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getHeader--">getHeader</a></span>()</code></th>
<td class="colLast">
<div class="block">Returns the HTML header for each page.</div>
</td>
</tr>
<tr id="i10" class="altColor">
<td class="colFirst"><code><a href="../../provider/Property.html" title="interface in org.gradle.api.provider">Property</a>&lt;java.lang.Boolean&gt;</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getIncludeAuthor--">getIncludeAuthor</a></span>()</code></th>
<td class="colLast">
<div class="block">Whether to include author paragraphs.</div>
</td>
</tr>
<tr id="i11" class="rowColor">
<td class="colFirst"><code><a href="../../provider/Property.html" title="interface in org.gradle.api.provider">Property</a>&lt;java.lang.Boolean&gt;</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getIncludeMainForScripts--">getIncludeMainForScripts</a></span>()</code></th>
<td class="colLast">
<div class="block">Whether to include main method for scripts.</div>
</td>
</tr>
<tr id="i12" class="altColor">
<td class="colFirst"><code>java.util.Set&lt;<a href="Groovydoc.Link.html" title="class in org.gradle.api.tasks.javadoc">Groovydoc.Link</a>&gt;</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getLinks--">getLinks</a></span>()</code></th>
<td class="colLast">
<div class="block">Returns the links to groovydoc/javadoc output at the given URL.</div>
</td>
</tr>
<tr id="i13" class="rowColor">
<td class="colFirst"><code><a href="../../resources/TextResource.html" title="interface in org.gradle.api.resources">TextResource</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getOverviewText--">getOverviewText</a></span>()</code></th>
<td class="colLast">
<div class="block">Returns a HTML text to be used for overview documentation.</div>
</td>
</tr>
<tr id="i14" class="altColor">
<td class="colFirst"><code><a href="../../provider/Property.html" title="interface in org.gradle.api.provider">Property</a>&lt;java.lang.Boolean&gt;</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getProcessScripts--">getProcessScripts</a></span>()</code></th>
<td class="colLast">
<div class="block">Whether to process scripts.</div>
</td>
</tr>
<tr id="i15" class="rowColor">
<td class="colFirst"><code><a href="../../file/FileTree.html" title="interface in org.gradle.api.file">FileTree</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getSource--">getSource</a></span>()</code></th>
<td class="colLast">
<div class="block">Returns the source for this task, after the include and exclude patterns have been applied.</div>
</td>
</tr>
<tr id="i16" class="altColor">
<td class="colFirst"><code>java.lang.String</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getWindowTitle--">getWindowTitle</a></span>()</code></th>
<td class="colLast">
<div class="block">Returns the browser window title for the documentation.</div>
</td>
</tr>
<tr id="i17" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#isNoTimestamp--">isNoTimestamp</a></span>()</code></th>
<td class="colLast">
<div class="block">Returns whether to include timestamp within hidden comment in generated HTML (Groovy &gt;= 2.4.6).</div>
</td>
</tr>
<tr id="i18" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#isNoVersionStamp--">isNoVersionStamp</a></span>()</code></th>
<td class="colLast">
<div class="block">Returns whether to include version stamp within hidden comment in generated HTML (Groovy &gt;= 2.4.6).</div>
</td>
</tr>
<tr id="i19" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#isUse--">isUse</a></span>()</code></th>
<td class="colLast">
<div class="block">Returns whether to create class and package usage pages.</div>
</td>
</tr>
<tr id="i20" class="altColor">
<td class="colFirst"><code>void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#link-java.lang.String-java.lang.String...-">link</a></span>&#8203;(java.lang.String&nbsp;url,
    java.lang.String...&nbsp;packages)</code></th>
<td class="colLast">
<div class="block">Add links to groovydoc/javadoc output at the given URL.</div>
</td>
</tr>
<tr id="i21" class="rowColor">
<td class="colFirst"><code>void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#setAntGroovydoc-org.gradle.api.internal.tasks.AntGroovydoc-">setAntGroovydoc</a></span>&#8203;(org.gradle.api.internal.tasks.AntGroovydoc&nbsp;antGroovydoc)</code></th>
<td class="colLast">&nbsp;</td>
</tr>
<tr id="i22" class="altColor">
<td class="colFirst"><code>void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#setClasspath-org.gradle.api.file.FileCollection-">setClasspath</a></span>&#8203;(<a href="../../file/FileCollection.html" title="interface in org.gradle.api.file">FileCollection</a>&nbsp;classpath)</code></th>
<td class="colLast">
<div class="block">Sets the classpath used to locate classes referenced by the documented sources.</div>
</td>
</tr>
<tr id="i23" class="rowColor">
<td class="colFirst"><code>void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#setDestinationDir-java.io.File-">setDestinationDir</a></span>&#8203;(java.io.File&nbsp;destinationDir)</code></th>
<td class="colLast">
<div class="block">Sets the directory to generate the documentation into.</div>
</td>
</tr>
<tr id="i24" class="altColor">
<td class="colFirst"><code>void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#setDocTitle-java.lang.String-">setDocTitle</a></span>&#8203;(java.lang.String&nbsp;docTitle)</code></th>
<td class="colLast">
<div class="block">Sets title for the package index(first) page (optional).</div>
</td>
</tr>
<tr id="i25" class="rowColor">
<td class="colFirst"><code>void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#setFooter-java.lang.String-">setFooter</a></span>&#8203;(java.lang.String&nbsp;footer)</code></th>
<td class="colLast">
<div class="block">Sets footer text for each page (optional).</div>
</td>
</tr>
<tr id="i26" class="altColor">
<td class="colFirst"><code>void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#setGroovyClasspath-org.gradle.api.file.FileCollection-">setGroovyClasspath</a></span>&#8203;(<a href="../../file/FileCollection.html" title="interface in org.gradle.api.file">FileCollection</a>&nbsp;groovyClasspath)</code></th>
<td class="colLast">
<div class="block">Sets the classpath containing the Groovy library to be used.</div>
</td>
</tr>
<tr id="i27" class="rowColor">
<td class="colFirst"><code>void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#setHeader-java.lang.String-">setHeader</a></span>&#8203;(java.lang.String&nbsp;header)</code></th>
<td class="colLast">
<div class="block">Sets header text for each page (optional).</div>
</td>
</tr>
<tr id="i28" class="altColor">
<td class="colFirst"><code>void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#setLinks-java.util.Set-">setLinks</a></span>&#8203;(java.util.Set&lt;<a href="Groovydoc.Link.html" title="class in org.gradle.api.tasks.javadoc">Groovydoc.Link</a>&gt;&nbsp;links)</code></th>
<td class="colLast">
<div class="block">Sets links to groovydoc/javadoc output at the given URL.</div>
</td>
</tr>
<tr id="i29" class="rowColor">
<td class="colFirst"><code>void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#setNoTimestamp-boolean-">setNoTimestamp</a></span>&#8203;(boolean&nbsp;noTimestamp)</code></th>
<td class="colLast">
<div class="block">Sets whether to include timestamp within hidden comment in generated HTML (Groovy &gt;= 2.4.6).</div>
</td>
</tr>
<tr id="i30" class="altColor">
<td class="colFirst"><code>void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#setNoVersionStamp-boolean-">setNoVersionStamp</a></span>&#8203;(boolean&nbsp;noVersionStamp)</code></th>
<td class="colLast">
<div class="block">Sets whether to include version stamp within hidden comment in generated HTML (Groovy &gt;= 2.4.6).</div>
</td>
</tr>
<tr id="i31" class="rowColor">
<td class="colFirst"><code>void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#setOverviewText-org.gradle.api.resources.TextResource-">setOverviewText</a></span>&#8203;(<a href="../../resources/TextResource.html" title="interface in org.gradle.api.resources">TextResource</a>&nbsp;overviewText)</code></th>
<td class="colLast">
<div class="block">Sets a HTML text to be used for overview documentation (optional).</div>
</td>
</tr>
<tr id="i32" class="altColor">
<td class="colFirst"><code>void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#setUse-boolean-">setUse</a></span>&#8203;(boolean&nbsp;use)</code></th>
<td class="colLast">
<div class="block">Sets whether to create class and package usage pages.</div>
</td>
</tr>
<tr id="i33" class="rowColor">
<td class="colFirst"><code>void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#setWindowTitle-java.lang.String-">setWindowTitle</a></span>&#8203;(java.lang.String&nbsp;windowTitle)</code></th>
<td class="colLast">
<div class="block">Sets the browser window title for the documentation.</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.org.gradle.api.tasks.SourceTask">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;org.gradle.api.tasks.<a href="../SourceTask.html" title="class in org.gradle.api.tasks">SourceTask</a></h3>
<code><a href="../SourceTask.html#exclude-groovy.lang.Closure-">exclude</a>, <a href="../SourceTask.html#exclude-java.lang.Iterable-">exclude</a>, <a href="../SourceTask.html#exclude-java.lang.String...-">exclude</a>, <a href="../SourceTask.html#exclude-org.gradle.api.specs.Spec-">exclude</a>, <a href="../SourceTask.html#getExcludes--">getExcludes</a>, <a href="../SourceTask.html#getIncludes--">getIncludes</a>, <a href="../SourceTask.html#getPatternSet--">getPatternSet</a>, <a href="../SourceTask.html#getPatternSetFactory--">getPatternSetFactory</a>, <a href="../SourceTask.html#include-groovy.lang.Closure-">include</a>, <a href="../SourceTask.html#include-java.lang.Iterable-">include</a>, <a href="../SourceTask.html#include-java.lang.String...-">include</a>, <a href="../SourceTask.html#include-org.gradle.api.specs.Spec-">include</a>, <a href="../SourceTask.html#setExcludes-java.lang.Iterable-">setExcludes</a>, <a href="../SourceTask.html#setIncludes-java.lang.Iterable-">setIncludes</a>, <a href="../SourceTask.html#setSource-java.lang.Object-">setSource</a>, <a href="../SourceTask.html#setSource-org.gradle.api.file.FileTree-">setSource</a>, <a href="../SourceTask.html#source-java.lang.Object...-">source</a></code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.org.gradle.api.internal.ConventionTask">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;org.gradle.api.internal.ConventionTask</h3>
<code>conventionMapping, conventionMapping, getConventionMapping</code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.org.gradle.api.DefaultTask">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;org.gradle.api.<a href="../../DefaultTask.html" title="class in org.gradle.api">DefaultTask</a></h3>
<code><a href="../../DefaultTask.html#compareTo-org.gradle.api.Task-">compareTo</a>, <a href="../../DefaultTask.html#configure-groovy.lang.Closure-">configure</a>, <a href="../../DefaultTask.html#dependsOn-java.lang.Object...-">dependsOn</a>, <a href="../../DefaultTask.html#doFirst-groovy.lang.Closure-">doFirst</a>, <a href="../../DefaultTask.html#doFirst-java.lang.String-org.gradle.api.Action-">doFirst</a>, <a href="../../DefaultTask.html#doFirst-org.gradle.api.Action-">doFirst</a>, <a href="../../DefaultTask.html#doLast-groovy.lang.Closure-">doLast</a>, <a href="../../DefaultTask.html#doLast-java.lang.String-org.gradle.api.Action-">doLast</a>, <a href="../../DefaultTask.html#doLast-org.gradle.api.Action-">doLast</a>, <a href="../../DefaultTask.html#finalizedBy-java.lang.Object...-">finalizedBy</a>, <a href="../../DefaultTask.html#getActions--">getActions</a>, <a href="../../DefaultTask.html#getAnt--">getAnt</a>, <a href="../../DefaultTask.html#getDependsOn--">getDependsOn</a>, <a href="../../DefaultTask.html#getDescription--">getDescription</a>, <a href="../../DefaultTask.html#getDestroyables--">getDestroyables</a>, <a href="../../DefaultTask.html#getDidWork--">getDidWork</a>, <a href="../../DefaultTask.html#getEnabled--">getEnabled</a>, <a href="../../DefaultTask.html#getExtensions--">getExtensions</a>, <a href="../../DefaultTask.html#getFinalizedBy--">getFinalizedBy</a>, <a href="../../DefaultTask.html#getGroup--">getGroup</a>, <a href="../../DefaultTask.html#getInputs--">getInputs</a>, <a href="../../DefaultTask.html#getLocalState--">getLocalState</a>, <a href="../../DefaultTask.html#getLogger--">getLogger</a>, <a href="../../DefaultTask.html#getLogging--">getLogging</a>, <a href="../../DefaultTask.html#getMustRunAfter--">getMustRunAfter</a>, <a href="../../DefaultTask.html#getName--">getName</a>, <a href="../../DefaultTask.html#getOutputs--">getOutputs</a>, <a href="../../DefaultTask.html#getPath--">getPath</a>, <a href="../../DefaultTask.html#getProject--">getProject</a>, <a href="../../DefaultTask.html#getShouldRunAfter--">getShouldRunAfter</a>, <a href="../../DefaultTask.html#getState--">getState</a>, <a href="../../DefaultTask.html#getTaskDependencies--">getTaskDependencies</a>, <a href="../../DefaultTask.html#getTemporaryDir--">getTemporaryDir</a>, <a href="../../DefaultTask.html#getTimeout--">getTimeout</a>, <a href="../../DefaultTask.html#hasProperty-java.lang.String-">hasProperty</a>, <a href="../../DefaultTask.html#mustRunAfter-java.lang.Object...-">mustRunAfter</a>, <a href="../../DefaultTask.html#onlyIf-groovy.lang.Closure-">onlyIf</a>, <a href="../../DefaultTask.html#onlyIf-java.lang.String-org.gradle.api.specs.Spec-">onlyIf</a>, <a href="../../DefaultTask.html#onlyIf-org.gradle.api.specs.Spec-">onlyIf</a>, <a href="../../DefaultTask.html#property-java.lang.String-">property</a>, <a href="../../DefaultTask.html#setActions-java.util.List-">setActions</a>, <a href="../../DefaultTask.html#setDependsOn-java.lang.Iterable-">setDependsOn</a>, <a href="../../DefaultTask.html#setDescription-java.lang.String-">setDescription</a>, <a href="../../DefaultTask.html#setDidWork-boolean-">setDidWork</a>, <a href="../../DefaultTask.html#setEnabled-boolean-">setEnabled</a>, <a href="../../DefaultTask.html#setFinalizedBy-java.lang.Iterable-">setFinalizedBy</a>, <a href="../../DefaultTask.html#setGroup-java.lang.String-">setGroup</a>, <a href="../../DefaultTask.html#setMustRunAfter-java.lang.Iterable-">setMustRunAfter</a>, <a href="../../DefaultTask.html#setOnlyIf-groovy.lang.Closure-">setOnlyIf</a>, <a href="../../DefaultTask.html#setOnlyIf-java.lang.String-org.gradle.api.specs.Spec-">setOnlyIf</a>, <a href="../../DefaultTask.html#setOnlyIf-org.gradle.api.specs.Spec-">setOnlyIf</a>, <a href="../../DefaultTask.html#setProperty-java.lang.String-java.lang.Object-">setProperty</a>, <a href="../../DefaultTask.html#setShouldRunAfter-java.lang.Iterable-">setShouldRunAfter</a>, <a href="../../DefaultTask.html#shouldRunAfter-java.lang.Object...-">shouldRunAfter</a>, <a href="../../DefaultTask.html#usesService-org.gradle.api.provider.Provider-">usesService</a></code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.org.gradle.api.internal.AbstractTask">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;org.gradle.api.internal.AbstractTask</h3>
<code>acceptServiceReferences, appendParallelSafeAction, doNotTrackState, getAsDynamicObject, getConvention, getIdentityPath, getImpliesSubProjects, getLifecycleDependencies, getOnlyIf, getReasonNotToTrackState, getReasonTaskIsIncompatibleWithConfigurationCache, getRequiredServices, getServices, getSharedResources, getStandardOutputCapture, getTaskActions, getTaskIdentity, getTemporaryDirFactory, hasTaskActions, injectIntoNewInstance, isCompatibleWithConfigurationCache, isEnabled, isHasCustomActions, notCompatibleWithConfigurationCache, prependParallelSafeAction, setImpliesSubProjects</code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.Object</h3>
<code>clone, equals, finalize, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.org.gradle.api.Task">
<!--   -->
</a>
<h3>Methods inherited from interface&nbsp;org.gradle.api.<a href="../../Task.html" title="interface in org.gradle.api">Task</a></h3>
<code><a href="../../Task.html#doNotTrackState-java.lang.String-">doNotTrackState</a>, <a href="../../Task.html#getConvention--">getConvention</a>, <a href="../../Task.html#notCompatibleWithConfigurationCache-java.lang.String-">notCompatibleWithConfigurationCache</a></code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.detail">
<!--   -->
</a>
<h3>Constructor Detail</h3>
<a name="Groovydoc--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>Groovydoc</h4>
<pre>public&nbsp;Groovydoc()</pre>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="generate--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>generate</h4>
<pre class="methodSignature">protected&nbsp;void&nbsp;generate()</pre>
</li>
</ul>
<a name="getSource--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getSource</h4>
<pre class="methodSignature"><a href="../PathSensitive.html" title="annotation in org.gradle.api.tasks">@PathSensitive</a>(<a href="../PathSensitivity.html#RELATIVE">RELATIVE</a>)
public&nbsp;<a href="../../file/FileTree.html" title="interface in org.gradle.api.file">FileTree</a>&nbsp;getSource()</pre>
<div class="block">Returns the source for this task, after the include and exclude patterns have been applied. Ignores source files which do not exist.

 <p>
 The <a href="../PathSensitivity.html" title="enum in org.gradle.api.tasks"><code>PathSensitivity</code></a> for the sources is configured to be <a href="../PathSensitivity.html#ABSOLUTE"><code>PathSensitivity.ABSOLUTE</code></a>.
 If your sources are less strict, please change it accordingly by overriding this method in your subclass.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Overrides:</span></dt>
<dd><code><a href="../SourceTask.html#getSource--">getSource</a></code>&nbsp;in class&nbsp;<code><a href="../SourceTask.html" title="class in org.gradle.api.tasks">SourceTask</a></code></dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>The source.</dd>
</dl>
</li>
</ul>
<a name="getDestinationDir--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getDestinationDir</h4>
<pre class="methodSignature"><a href="../OutputDirectory.html" title="annotation in org.gradle.api.tasks">@OutputDirectory</a>
public&nbsp;java.io.File&nbsp;getDestinationDir()</pre>
<div class="block">Returns the directory to generate the documentation into.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>The directory to generate the documentation into</dd>
</dl>
</li>
</ul>
<a name="setDestinationDir-java.io.File-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setDestinationDir</h4>
<pre class="methodSignature">public&nbsp;void&nbsp;setDestinationDir&#8203;(java.io.File&nbsp;destinationDir)</pre>
<div class="block">Sets the directory to generate the documentation into.</div>
</li>
</ul>
<a name="getGroovyClasspath--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getGroovyClasspath</h4>
<pre class="methodSignature"><a href="../Classpath.html" title="annotation in org.gradle.api.tasks">@Classpath</a>
public&nbsp;<a href="../../file/FileCollection.html" title="interface in org.gradle.api.file">FileCollection</a>&nbsp;getGroovyClasspath()</pre>
<div class="block">Returns the classpath containing the Groovy library to be used.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>The classpath containing the Groovy library to be used</dd>
</dl>
</li>
</ul>
<a name="setGroovyClasspath-org.gradle.api.file.FileCollection-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setGroovyClasspath</h4>
<pre class="methodSignature">public&nbsp;void&nbsp;setGroovyClasspath&#8203;(<a href="../../file/FileCollection.html" title="interface in org.gradle.api.file">FileCollection</a>&nbsp;groovyClasspath)</pre>
<div class="block">Sets the classpath containing the Groovy library to be used.</div>
</li>
</ul>
<a name="getClasspath--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getClasspath</h4>
<pre class="methodSignature"><a href="../Classpath.html" title="annotation in org.gradle.api.tasks">@Classpath</a>
public&nbsp;<a href="../../file/FileCollection.html" title="interface in org.gradle.api.file">FileCollection</a>&nbsp;getClasspath()</pre>
<div class="block">Returns the classpath used to locate classes referenced by the documented sources.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>The classpath used to locate classes referenced by the documented sources</dd>
</dl>
</li>
</ul>
<a name="setClasspath-org.gradle.api.file.FileCollection-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setClasspath</h4>
<pre class="methodSignature">public&nbsp;void&nbsp;setClasspath&#8203;(<a href="../../file/FileCollection.html" title="interface in org.gradle.api.file">FileCollection</a>&nbsp;classpath)</pre>
<div class="block">Sets the classpath used to locate classes referenced by the documented sources.</div>
</li>
</ul>
<a name="getAntGroovydoc--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getAntGroovydoc</h4>
<pre class="methodSignature"><a href="../Internal.html" title="annotation in org.gradle.api.tasks">@Internal</a>
public&nbsp;org.gradle.api.internal.tasks.AntGroovydoc&nbsp;getAntGroovydoc()</pre>
</li>
</ul>
<a name="setAntGroovydoc-org.gradle.api.internal.tasks.AntGroovydoc-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setAntGroovydoc</h4>
<pre class="methodSignature">public&nbsp;void&nbsp;setAntGroovydoc&#8203;(org.gradle.api.internal.tasks.AntGroovydoc&nbsp;antGroovydoc)</pre>
</li>
</ul>
<a name="isUse--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isUse</h4>
<pre class="methodSignature"><a href="../Input.html" title="annotation in org.gradle.api.tasks">@Input</a>
public&nbsp;boolean&nbsp;isUse()</pre>
<div class="block">Returns whether to create class and package usage pages.</div>
</li>
</ul>
<a name="setUse-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setUse</h4>
<pre class="methodSignature">public&nbsp;void&nbsp;setUse&#8203;(boolean&nbsp;use)</pre>
<div class="block">Sets whether to create class and package usage pages.</div>
</li>
</ul>
<a name="isNoTimestamp--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isNoTimestamp</h4>
<pre class="methodSignature"><a href="../Input.html" title="annotation in org.gradle.api.tasks">@Input</a>
public&nbsp;boolean&nbsp;isNoTimestamp()</pre>
<div class="block">Returns whether to include timestamp within hidden comment in generated HTML (Groovy &gt;= 2.4.6).</div>
</li>
</ul>
<a name="setNoTimestamp-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setNoTimestamp</h4>
<pre class="methodSignature">public&nbsp;void&nbsp;setNoTimestamp&#8203;(boolean&nbsp;noTimestamp)</pre>
<div class="block">Sets whether to include timestamp within hidden comment in generated HTML (Groovy &gt;= 2.4.6).</div>
</li>
</ul>
<a name="isNoVersionStamp--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isNoVersionStamp</h4>
<pre class="methodSignature"><a href="../Input.html" title="annotation in org.gradle.api.tasks">@Input</a>
public&nbsp;boolean&nbsp;isNoVersionStamp()</pre>
<div class="block">Returns whether to include version stamp within hidden comment in generated HTML (Groovy &gt;= 2.4.6).</div>
</li>
</ul>
<a name="setNoVersionStamp-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setNoVersionStamp</h4>
<pre class="methodSignature">public&nbsp;void&nbsp;setNoVersionStamp&#8203;(boolean&nbsp;noVersionStamp)</pre>
<div class="block">Sets whether to include version stamp within hidden comment in generated HTML (Groovy &gt;= 2.4.6).</div>
</li>
</ul>
<a name="getWindowTitle--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getWindowTitle</h4>
<pre class="methodSignature">@Nullable
<a href="../Optional.html" title="annotation in org.gradle.api.tasks">@Optional</a>
<a href="../Input.html" title="annotation in org.gradle.api.tasks">@Input</a>
public&nbsp;java.lang.String&nbsp;getWindowTitle()</pre>
<div class="block">Returns the browser window title for the documentation. Set to <code>null</code> when there is no window title.</div>
</li>
</ul>
<a name="setWindowTitle-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setWindowTitle</h4>
<pre class="methodSignature">public&nbsp;void&nbsp;setWindowTitle&#8203;(@Nullable
                           java.lang.String&nbsp;windowTitle)</pre>
<div class="block">Sets the browser window title for the documentation.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>windowTitle</code> - A text for the windows title</dd>
</dl>
</li>
</ul>
<a name="getDocTitle--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getDocTitle</h4>
<pre class="methodSignature">@Nullable
<a href="../Optional.html" title="annotation in org.gradle.api.tasks">@Optional</a>
<a href="../Input.html" title="annotation in org.gradle.api.tasks">@Input</a>
public&nbsp;java.lang.String&nbsp;getDocTitle()</pre>
<div class="block">Returns the title for the package index(first) page. Set to <code>null</code> when there is no document title.</div>
</li>
</ul>
<a name="setDocTitle-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setDocTitle</h4>
<pre class="methodSignature">public&nbsp;void&nbsp;setDocTitle&#8203;(@Nullable
                        java.lang.String&nbsp;docTitle)</pre>
<div class="block">Sets title for the package index(first) page (optional).</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>docTitle</code> - the docTitle as HTML</dd>
</dl>
</li>
</ul>
<a name="getHeader--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getHeader</h4>
<pre class="methodSignature">@Nullable
<a href="../Optional.html" title="annotation in org.gradle.api.tasks">@Optional</a>
<a href="../Input.html" title="annotation in org.gradle.api.tasks">@Input</a>
public&nbsp;java.lang.String&nbsp;getHeader()</pre>
<div class="block">Returns the HTML header for each page. Set to <code>null</code> when there is no header.</div>
</li>
</ul>
<a name="setHeader-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setHeader</h4>
<pre class="methodSignature">public&nbsp;void&nbsp;setHeader&#8203;(@Nullable
                      java.lang.String&nbsp;header)</pre>
<div class="block">Sets header text for each page (optional).</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>header</code> - the header as HTML</dd>
</dl>
</li>
</ul>
<a name="getFooter--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getFooter</h4>
<pre class="methodSignature">@Nullable
<a href="../Optional.html" title="annotation in org.gradle.api.tasks">@Optional</a>
<a href="../Input.html" title="annotation in org.gradle.api.tasks">@Input</a>
public&nbsp;java.lang.String&nbsp;getFooter()</pre>
<div class="block">Returns the HTML footer for each page. Set to <code>null</code> when there is no footer.</div>
</li>
</ul>
<a name="setFooter-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setFooter</h4>
<pre class="methodSignature">public&nbsp;void&nbsp;setFooter&#8203;(@Nullable
                      java.lang.String&nbsp;footer)</pre>
<div class="block">Sets footer text for each page (optional).</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>footer</code> - the footer as HTML</dd>
</dl>
</li>
</ul>
<a name="getOverviewText--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getOverviewText</h4>
<pre class="methodSignature">@Nullable
<a href="../Optional.html" title="annotation in org.gradle.api.tasks">@Optional</a>
public&nbsp;<a href="../../resources/TextResource.html" title="interface in org.gradle.api.resources">TextResource</a>&nbsp;getOverviewText()</pre>
<div class="block">Returns a HTML text to be used for overview documentation. Set to <code>null</code> when there is no overview text.</div>
</li>
</ul>
<a name="setOverviewText-org.gradle.api.resources.TextResource-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setOverviewText</h4>
<pre class="methodSignature">public&nbsp;void&nbsp;setOverviewText&#8203;(@Nullable
                            <a href="../../resources/TextResource.html" title="interface in org.gradle.api.resources">TextResource</a>&nbsp;overviewText)</pre>
<div class="block">Sets a HTML text to be used for overview documentation (optional).
 <p>
 <b>Example:</b> <code>overviewText = resources.text.fromFile("/overview.html")</code></div>
</li>
</ul>
<a name="getAccess--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getAccess</h4>
<pre class="methodSignature"><a href="../Input.html" title="annotation in org.gradle.api.tasks">@Input</a>
public&nbsp;<a href="../../provider/Property.html" title="interface in org.gradle.api.provider">Property</a>&lt;<a href="GroovydocAccess.html" title="enum in org.gradle.api.tasks.javadoc">GroovydocAccess</a>&gt;&nbsp;getAccess()</pre>
<div class="block">The most restrictive access level to include in the Groovydoc.

 <p>
 For example, to include classes and members with package, protected, and public access, use <a href="GroovydocAccess.html#PACKAGE"><code>GroovydocAccess.PACKAGE</code></a>.
 </p></div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>the access property</dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>7.5</dd>
</dl>
</li>
</ul>
<a name="getIncludeAuthor--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getIncludeAuthor</h4>
<pre class="methodSignature"><a href="../Input.html" title="annotation in org.gradle.api.tasks">@Input</a>
public&nbsp;<a href="../../provider/Property.html" title="interface in org.gradle.api.provider">Property</a>&lt;java.lang.Boolean&gt;&nbsp;getIncludeAuthor()</pre>
<div class="block">Whether to include author paragraphs.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>7.5</dd>
</dl>
</li>
</ul>
<a name="getProcessScripts--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getProcessScripts</h4>
<pre class="methodSignature"><a href="../Input.html" title="annotation in org.gradle.api.tasks">@Input</a>
public&nbsp;<a href="../../provider/Property.html" title="interface in org.gradle.api.provider">Property</a>&lt;java.lang.Boolean&gt;&nbsp;getProcessScripts()</pre>
<div class="block">Whether to process scripts.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>7.5</dd>
</dl>
</li>
</ul>
<a name="getIncludeMainForScripts--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getIncludeMainForScripts</h4>
<pre class="methodSignature"><a href="../Input.html" title="annotation in org.gradle.api.tasks">@Input</a>
public&nbsp;<a href="../../provider/Property.html" title="interface in org.gradle.api.provider">Property</a>&lt;java.lang.Boolean&gt;&nbsp;getIncludeMainForScripts()</pre>
<div class="block">Whether to include main method for scripts.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>7.5</dd>
</dl>
</li>
</ul>
<a name="getLinks--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getLinks</h4>
<pre class="methodSignature"><a href="../Input.html" title="annotation in org.gradle.api.tasks">@Input</a>
public&nbsp;java.util.Set&lt;<a href="Groovydoc.Link.html" title="class in org.gradle.api.tasks.javadoc">Groovydoc.Link</a>&gt;&nbsp;getLinks()</pre>
<div class="block">Returns the links to groovydoc/javadoc output at the given URL.</div>
</li>
</ul>
<a name="setLinks-java.util.Set-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setLinks</h4>
<pre class="methodSignature">public&nbsp;void&nbsp;setLinks&#8203;(java.util.Set&lt;<a href="Groovydoc.Link.html" title="class in org.gradle.api.tasks.javadoc">Groovydoc.Link</a>&gt;&nbsp;links)</pre>
<div class="block">Sets links to groovydoc/javadoc output at the given URL.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>links</code> - The links to set</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="#link-java.lang.String-java.lang.String...-"><code>link(String, String...)</code></a></dd>
</dl>
</li>
</ul>
<a name="link-java.lang.String-java.lang.String...-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>link</h4>
<pre class="methodSignature">public&nbsp;void&nbsp;link&#8203;(java.lang.String&nbsp;url,
                 java.lang.String...&nbsp;packages)</pre>
<div class="block">Add links to groovydoc/javadoc output at the given URL.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>url</code> - Base URL of external site</dd>
<dd><code>packages</code> - list of package prefixes</dd>
</dl>
</li>
</ul>
<a name="getDeleter--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>getDeleter</h4>
<pre class="methodSignature">@Inject
protected&nbsp;org.gradle.internal.file.Deleter&nbsp;getDeleter()</pre>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../index-all.html">Index</a></li>
<li><a href="../../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../../allclasses.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li><a href="#nested.class.summary">Nested</a>&nbsp;|&nbsp;</li>
<li><a href="#field.summary">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
