<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc -->
<title>org.gradle.ide.visualstudio Class Hierarchy (Gradle API 8.5)</title>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<link rel="stylesheet" type="text/css" href="../../../../javadoc.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../jquery/jquery-ui.min.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../jquery-ui.overrides.css" title="Style">
<script type="text/javascript" src="../../../../script.js"></script>
<script type="text/javascript" src="../../../../jquery/jszip/dist/jszip.min.js"></script>
<script type="text/javascript" src="../../../../jquery/jszip-utils/dist/jszip-utils.min.js"></script>
<!--[if IE]>
<script type="text/javascript" src="../../../../jquery/jszip-utils/dist/jszip-utils-ie.min.js"></script>
<![endif]-->
<script type="text/javascript" src="../../../../jquery/jquery-3.6.1.min.js"></script>
<script type="text/javascript" src="../../../../jquery/jquery-ui.min.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="org.gradle.ide.visualstudio Class Hierarchy (Gradle API 8.5)";
        }
    }
    catch(err) {
    }
//-->
var pathtoroot = "../../../../";
var useModuleDirectories = false;
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<div class="fixedNav">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li>Class</li>
<li class="navBarCell1Rev">Tree</li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../allclasses.html">All&nbsp;Classes</a></li>
</ul>
<ul class="navListSearch">
<li><label for="search">SEARCH:</label>
<input type="text" id="search" value="search" disabled="disabled">
<input type="reset" id="reset" value="reset" disabled="disabled">
</li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
</div>
<div class="navPadding">&nbsp;</div>
<script type="text/javascript"><!--
$('.navPadding').css('padding-top', $('.fixedNav').css("height"));
//-->
</script>
<div class="header">
<h1 class="title">Hierarchy For Package org.gradle.ide.visualstudio</h1>
<span class="packageHierarchyLabel">Package Hierarchies:</span>
<ul class="horizontal">
<li><a href="../../../../overview-tree.html">All Packages</a></li>
</ul>
</div>
<div class="contentContainer">
<h2 title="Interface Hierarchy">Interface Hierarchy</h2>
<ul>
<li class="circle">org.gradle.api.<a href="../../api/Buildable.html" title="interface in org.gradle.api"><span class="typeNameLink">Buildable</span></a>
<ul>
<li class="circle">org.gradle.ide.visualstudio.<a href="VisualStudioProject.html" title="interface in org.gradle.ide.visualstudio"><span class="typeNameLink">VisualStudioProject</span></a> (also extends org.gradle.api.<a href="../../api/Named.html" title="interface in org.gradle.api">Named</a>)</li>
<li class="circle">org.gradle.ide.visualstudio.<a href="VisualStudioSolution.html" title="interface in org.gradle.ide.visualstudio"><span class="typeNameLink">VisualStudioSolution</span></a> (also extends org.gradle.plugins.ide.<a href="../../plugins/ide/IdeWorkspace.html" title="interface in org.gradle.plugins.ide">IdeWorkspace</a>, org.gradle.api.<a href="../../api/Named.html" title="interface in org.gradle.api">Named</a>)</li>
</ul>
</li>
<li class="circle">org.gradle.ide.visualstudio.<a href="ConfigFile.html" title="interface in org.gradle.ide.visualstudio"><span class="typeNameLink">ConfigFile</span></a>
<ul>
<li class="circle">org.gradle.ide.visualstudio.<a href="TextConfigFile.html" title="interface in org.gradle.ide.visualstudio"><span class="typeNameLink">TextConfigFile</span></a></li>
<li class="circle">org.gradle.ide.visualstudio.<a href="XmlConfigFile.html" title="interface in org.gradle.ide.visualstudio"><span class="typeNameLink">XmlConfigFile</span></a></li>
</ul>
</li>
<li class="circle">org.gradle.api.<a href="../../api/Describable.html" title="interface in org.gradle.api"><span class="typeNameLink">Describable</span></a>
<ul>
<li class="circle">org.gradle.plugins.ide.<a href="../../plugins/ide/IdeWorkspace.html" title="interface in org.gradle.plugins.ide"><span class="typeNameLink">IdeWorkspace</span></a>
<ul>
<li class="circle">org.gradle.ide.visualstudio.<a href="VisualStudioSolution.html" title="interface in org.gradle.ide.visualstudio"><span class="typeNameLink">VisualStudioSolution</span></a> (also extends org.gradle.api.<a href="../../api/Buildable.html" title="interface in org.gradle.api">Buildable</a>, org.gradle.api.<a href="../../api/Named.html" title="interface in org.gradle.api">Named</a>)</li>
</ul>
</li>
</ul>
</li>
<li class="circle">org.gradle.api.<a href="../../api/Named.html" title="interface in org.gradle.api"><span class="typeNameLink">Named</span></a>
<ul>
<li class="circle">org.gradle.ide.visualstudio.<a href="VisualStudioProject.html" title="interface in org.gradle.ide.visualstudio"><span class="typeNameLink">VisualStudioProject</span></a> (also extends org.gradle.api.<a href="../../api/Buildable.html" title="interface in org.gradle.api">Buildable</a>)</li>
<li class="circle">org.gradle.ide.visualstudio.<a href="VisualStudioSolution.html" title="interface in org.gradle.ide.visualstudio"><span class="typeNameLink">VisualStudioSolution</span></a> (also extends org.gradle.api.<a href="../../api/Buildable.html" title="interface in org.gradle.api">Buildable</a>, org.gradle.plugins.ide.<a href="../../plugins/ide/IdeWorkspace.html" title="interface in org.gradle.plugins.ide">IdeWorkspace</a>)</li>
</ul>
</li>
<li class="circle">org.gradle.ide.visualstudio.<a href="TextProvider.html" title="interface in org.gradle.ide.visualstudio"><span class="typeNameLink">TextProvider</span></a></li>
<li class="circle">org.gradle.ide.visualstudio.<a href="VisualStudioExtension.html" title="interface in org.gradle.ide.visualstudio"><span class="typeNameLink">VisualStudioExtension</span></a>
<ul>
<li class="circle">org.gradle.ide.visualstudio.<a href="VisualStudioRootExtension.html" title="interface in org.gradle.ide.visualstudio"><span class="typeNameLink">VisualStudioRootExtension</span></a></li>
</ul>
</li>
</ul>
</div>
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li>Class</li>
<li class="navBarCell1Rev">Tree</li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../allclasses.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
