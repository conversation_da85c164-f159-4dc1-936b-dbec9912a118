<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc -->
<title>SourceFolder (Gradle API 8.5)</title>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<link rel="stylesheet" type="text/css" href="../../../../../../javadoc.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../../../jquery/jquery-ui.min.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../../../jquery-ui.overrides.css" title="Style">
<script type="text/javascript" src="../../../../../../script.js"></script>
<script type="text/javascript" src="../../../../../../jquery/jszip/dist/jszip.min.js"></script>
<script type="text/javascript" src="../../../../../../jquery/jszip-utils/dist/jszip-utils.min.js"></script>
<!--[if IE]>
<script type="text/javascript" src="../../../../../../jquery/jszip-utils/dist/jszip-utils-ie.min.js"></script>
<![endif]-->
<script type="text/javascript" src="../../../../../../jquery/jquery-3.6.1.min.js"></script>
<script type="text/javascript" src="../../../../../../jquery/jquery-ui.min.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="SourceFolder (Gradle API 8.5)";
        }
    }
    catch(err) {
    }
//-->
var data = {"i0":10,"i1":10,"i2":10,"i3":10,"i4":10,"i5":10,"i6":10,"i7":10,"i8":10,"i9":10,"i10":10,"i11":10,"i12":10,"i13":10,"i14":10,"i15":10,"i16":10,"i17":10};
var tabs = {65535:["t0","All Methods"],2:["t2","Instance Methods"],8:["t4","Concrete Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
var pathtoroot = "../../../../../../";
var useModuleDirectories = false;
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<div class="fixedNav">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../../index-all.html">Index</a></li>
<li><a href="../../../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../../../allclasses.html">All&nbsp;Classes</a></li>
</ul>
<ul class="navListSearch">
<li><label for="search">SEARCH:</label>
<input type="text" id="search" value="search" disabled="disabled">
<input type="reset" id="reset" value="reset" disabled="disabled">
</li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li><a href="#field.summary">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
</div>
<div class="navPadding">&nbsp;</div>
<script type="text/javascript"><!--
$('.navPadding').css('padding-top', $('.fixedNav').css("height"));
//-->
</script>
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle"><span class="packageLabelInType">Package</span>&nbsp;<a href="package-summary.html">org.gradle.plugins.ide.eclipse.model</a></div>
<h2 title="Class SourceFolder" class="title">Class SourceFolder</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li><a href="AbstractClasspathEntry.html" title="class in org.gradle.plugins.ide.eclipse.model">org.gradle.plugins.ide.eclipse.model.AbstractClasspathEntry</a></li>
<li>
<ul class="inheritance">
<li>org.gradle.plugins.ide.eclipse.model.SourceFolder</li>
</ul>
</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt>All Implemented Interfaces:</dt>
<dd><code><a href="ClasspathEntry.html" title="interface in org.gradle.plugins.ide.eclipse.model">ClasspathEntry</a></code></dd>
</dl>
<hr>
<pre>public class <span class="typeNameLabel">SourceFolder</span>
extends <a href="AbstractClasspathEntry.html" title="class in org.gradle.plugins.ide.eclipse.model">AbstractClasspathEntry</a></pre>
<div class="block">SourceFolder.path contains only project relative path.</div>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- =========== FIELD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="field.summary">
<!--   -->
</a>
<h3>Field Summary</h3>
<ul class="blockList">
<li class="blockList"><a name="fields.inherited.from.class.org.gradle.plugins.ide.eclipse.model.AbstractClasspathEntry">
<!--   -->
</a>
<h3>Fields inherited from class&nbsp;org.gradle.plugins.ide.eclipse.model.<a href="AbstractClasspathEntry.html" title="class in org.gradle.plugins.ide.eclipse.model">AbstractClasspathEntry</a></h3>
<code><a href="AbstractClasspathEntry.html#accessRules">accessRules</a>, <a href="AbstractClasspathEntry.html#COMPONENT_DEPENDENCY_ATTRIBUTE">COMPONENT_DEPENDENCY_ATTRIBUTE</a>, <a href="AbstractClasspathEntry.html#COMPONENT_NON_DEPENDENCY_ATTRIBUTE">COMPONENT_NON_DEPENDENCY_ATTRIBUTE</a>, <a href="AbstractClasspathEntry.html#entryAttributes">entryAttributes</a>, <a href="AbstractClasspathEntry.html#exported">exported</a>, <a href="AbstractClasspathEntry.html#path">path</a></code></li>
</ul>
</li>
</ul>
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.summary">
<!--   -->
</a>
<h3>Constructor Summary</h3>
<table class="memberSummary" summary="Constructor Summary table, listing constructors, and an explanation">
<caption><span>Constructors</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Constructor</th>
<th class="colLast" scope="col">Description</th>
</tr>
<tr class="altColor">
<th class="colConstructorName" scope="row"><code><span class="memberNameLink"><a href="#SourceFolder-groovy.util.Node-">SourceFolder</a></span>&#8203;(<a href="https://docs.groovy-lang.org/docs/groovy-3.0.17/html/gapi/groovy/util/Node.html?is-external=true" title="class or interface in groovy.util" class="externalLink">Node</a>&nbsp;node)</code></th>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="rowColor">
<th class="colConstructorName" scope="row"><code><span class="memberNameLink"><a href="#SourceFolder-java.lang.String-java.lang.String-">SourceFolder</a></span>&#8203;(java.lang.String&nbsp;projectRelativePath,
            java.lang.String&nbsp;output)</code></th>
<td class="colLast">&nbsp;</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colSecond" scope="col">Method</th>
<th class="colLast" scope="col">Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#appendNode-groovy.util.Node-">appendNode</a></span>&#8203;(<a href="https://docs.groovy-lang.org/docs/groovy-3.0.17/html/gapi/groovy/util/Node.html?is-external=true" title="class or interface in groovy.util" class="externalLink">Node</a>&nbsp;node)</code></th>
<td class="colLast">&nbsp;</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#equals-java.lang.Object-">equals</a></span>&#8203;(java.lang.Object&nbsp;o)</code></th>
<td class="colLast">&nbsp;</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>java.lang.String</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getAbsolutePath--">getAbsolutePath</a></span>()</code></th>
<td class="colLast">&nbsp;</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>java.io.File</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getDir--">getDir</a></span>()</code></th>
<td class="colLast">&nbsp;</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code>java.util.List&lt;java.lang.String&gt;</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getExcludes--">getExcludes</a></span>()</code></th>
<td class="colLast">&nbsp;</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code>java.util.List&lt;java.lang.String&gt;</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getIncludes--">getIncludes</a></span>()</code></th>
<td class="colLast">&nbsp;</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code>java.lang.String</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getKind--">getKind</a></span>()</code></th>
<td class="colLast">&nbsp;</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code>java.lang.String</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getName--">getName</a></span>()</code></th>
<td class="colLast">&nbsp;</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code>java.lang.String</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getOutput--">getOutput</a></span>()</code></th>
<td class="colLast">&nbsp;</td>
</tr>
<tr id="i9" class="rowColor">
<td class="colFirst"><code>int</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#hashCode--">hashCode</a></span>()</code></th>
<td class="colLast">&nbsp;</td>
</tr>
<tr id="i10" class="altColor">
<td class="colFirst"><code>void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#setDir-java.io.File-">setDir</a></span>&#8203;(java.io.File&nbsp;dir)</code></th>
<td class="colLast">&nbsp;</td>
</tr>
<tr id="i11" class="rowColor">
<td class="colFirst"><code>void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#setExcludes-java.util.List-">setExcludes</a></span>&#8203;(java.util.List&lt;java.lang.String&gt;&nbsp;excludes)</code></th>
<td class="colLast">&nbsp;</td>
</tr>
<tr id="i12" class="altColor">
<td class="colFirst"><code>void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#setIncludes-java.util.List-">setIncludes</a></span>&#8203;(java.util.List&lt;java.lang.String&gt;&nbsp;includes)</code></th>
<td class="colLast">&nbsp;</td>
</tr>
<tr id="i13" class="rowColor">
<td class="colFirst"><code>void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#setName-java.lang.String-">setName</a></span>&#8203;(java.lang.String&nbsp;name)</code></th>
<td class="colLast">&nbsp;</td>
</tr>
<tr id="i14" class="altColor">
<td class="colFirst"><code>void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#setOutput-java.lang.String-">setOutput</a></span>&#8203;(java.lang.String&nbsp;output)</code></th>
<td class="colLast">&nbsp;</td>
</tr>
<tr id="i15" class="rowColor">
<td class="colFirst"><code>java.lang.String</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#toString--">toString</a></span>()</code></th>
<td class="colLast">&nbsp;</td>
</tr>
<tr id="i16" class="altColor">
<td class="colFirst"><code>void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#trim--">trim</a></span>()</code></th>
<td class="colLast">&nbsp;</td>
</tr>
<tr id="i17" class="rowColor">
<td class="colFirst"><code>void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#trim-java.lang.String-">trim</a></span>&#8203;(java.lang.String&nbsp;prefix)</code></th>
<td class="colLast">&nbsp;</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.org.gradle.plugins.ide.eclipse.model.AbstractClasspathEntry">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;org.gradle.plugins.ide.eclipse.model.<a href="AbstractClasspathEntry.html" title="class in org.gradle.plugins.ide.eclipse.model">AbstractClasspathEntry</a></h3>
<code><a href="AbstractClasspathEntry.html#addClasspathEntry-groovy.util.Node-java.util.Map-">addClasspathEntry</a>, <a href="AbstractClasspathEntry.html#getAccessRules--">getAccessRules</a>, <a href="AbstractClasspathEntry.html#getEntryAttributes--">getEntryAttributes</a>, <a href="AbstractClasspathEntry.html#getNativeLibraryLocation--">getNativeLibraryLocation</a>, <a href="AbstractClasspathEntry.html#getPath--">getPath</a>, <a href="AbstractClasspathEntry.html#isExported--">isExported</a>, <a href="AbstractClasspathEntry.html#normalizePath-java.lang.String-">normalizePath</a>, <a href="AbstractClasspathEntry.html#setAccessRules-java.util.Set-">setAccessRules</a>, <a href="AbstractClasspathEntry.html#setExported-boolean-">setExported</a>, <a href="AbstractClasspathEntry.html#setNativeLibraryLocation-java.lang.String-">setNativeLibraryLocation</a>, <a href="AbstractClasspathEntry.html#setPath-java.lang.String-">setPath</a>, <a href="AbstractClasspathEntry.html#writeEntryAttributes-groovy.util.Node-">writeEntryAttributes</a></code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.Object</h3>
<code>clone, finalize, getClass, notify, notifyAll, wait, wait, wait</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.detail">
<!--   -->
</a>
<h3>Constructor Detail</h3>
<a name="SourceFolder-groovy.util.Node-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>SourceFolder</h4>
<pre>public&nbsp;SourceFolder&#8203;(<a href="https://docs.groovy-lang.org/docs/groovy-3.0.17/html/gapi/groovy/util/Node.html?is-external=true" title="class or interface in groovy.util" class="externalLink">Node</a>&nbsp;node)</pre>
</li>
</ul>
<a name="SourceFolder-java.lang.String-java.lang.String-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>SourceFolder</h4>
<pre>public&nbsp;SourceFolder&#8203;(java.lang.String&nbsp;projectRelativePath,
                    java.lang.String&nbsp;output)</pre>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="getOutput--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getOutput</h4>
<pre class="methodSignature">public&nbsp;java.lang.String&nbsp;getOutput()</pre>
</li>
</ul>
<a name="setOutput-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setOutput</h4>
<pre class="methodSignature">public&nbsp;void&nbsp;setOutput&#8203;(java.lang.String&nbsp;output)</pre>
</li>
</ul>
<a name="getIncludes--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getIncludes</h4>
<pre class="methodSignature">public&nbsp;java.util.List&lt;java.lang.String&gt;&nbsp;getIncludes()</pre>
</li>
</ul>
<a name="setIncludes-java.util.List-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setIncludes</h4>
<pre class="methodSignature">public&nbsp;void&nbsp;setIncludes&#8203;(java.util.List&lt;java.lang.String&gt;&nbsp;includes)</pre>
</li>
</ul>
<a name="getExcludes--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getExcludes</h4>
<pre class="methodSignature">public&nbsp;java.util.List&lt;java.lang.String&gt;&nbsp;getExcludes()</pre>
</li>
</ul>
<a name="setExcludes-java.util.List-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setExcludes</h4>
<pre class="methodSignature">public&nbsp;void&nbsp;setExcludes&#8203;(java.util.List&lt;java.lang.String&gt;&nbsp;excludes)</pre>
</li>
</ul>
<a name="getDir--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getDir</h4>
<pre class="methodSignature">public&nbsp;java.io.File&nbsp;getDir()</pre>
</li>
</ul>
<a name="setDir-java.io.File-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setDir</h4>
<pre class="methodSignature">public&nbsp;void&nbsp;setDir&#8203;(java.io.File&nbsp;dir)</pre>
</li>
</ul>
<a name="getName--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getName</h4>
<pre class="methodSignature">public&nbsp;java.lang.String&nbsp;getName()</pre>
</li>
</ul>
<a name="setName-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setName</h4>
<pre class="methodSignature">public&nbsp;void&nbsp;setName&#8203;(java.lang.String&nbsp;name)</pre>
</li>
</ul>
<a name="getKind--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getKind</h4>
<pre class="methodSignature">public&nbsp;java.lang.String&nbsp;getKind()</pre>
</li>
</ul>
<a name="getAbsolutePath--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getAbsolutePath</h4>
<pre class="methodSignature">public&nbsp;java.lang.String&nbsp;getAbsolutePath()</pre>
</li>
</ul>
<a name="trim--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>trim</h4>
<pre class="methodSignature">public&nbsp;void&nbsp;trim()</pre>
</li>
</ul>
<a name="trim-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>trim</h4>
<pre class="methodSignature">public&nbsp;void&nbsp;trim&#8203;(java.lang.String&nbsp;prefix)</pre>
</li>
</ul>
<a name="appendNode-groovy.util.Node-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>appendNode</h4>
<pre class="methodSignature">public&nbsp;void&nbsp;appendNode&#8203;(<a href="https://docs.groovy-lang.org/docs/groovy-3.0.17/html/gapi/groovy/util/Node.html?is-external=true" title="class or interface in groovy.util" class="externalLink">Node</a>&nbsp;node)</pre>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="ClasspathEntry.html#appendNode-groovy.util.Node-">appendNode</a></code>&nbsp;in interface&nbsp;<code><a href="ClasspathEntry.html" title="interface in org.gradle.plugins.ide.eclipse.model">ClasspathEntry</a></code></dd>
<dt><span class="overrideSpecifyLabel">Overrides:</span></dt>
<dd><code><a href="AbstractClasspathEntry.html#appendNode-groovy.util.Node-">appendNode</a></code>&nbsp;in class&nbsp;<code><a href="AbstractClasspathEntry.html" title="class in org.gradle.plugins.ide.eclipse.model">AbstractClasspathEntry</a></code></dd>
</dl>
</li>
</ul>
<a name="equals-java.lang.Object-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>equals</h4>
<pre class="methodSignature">public&nbsp;boolean&nbsp;equals&#8203;(java.lang.Object&nbsp;o)</pre>
<dl>
<dt><span class="overrideSpecifyLabel">Overrides:</span></dt>
<dd><code><a href="AbstractClasspathEntry.html#equals-java.lang.Object-">equals</a></code>&nbsp;in class&nbsp;<code><a href="AbstractClasspathEntry.html" title="class in org.gradle.plugins.ide.eclipse.model">AbstractClasspathEntry</a></code></dd>
</dl>
</li>
</ul>
<a name="hashCode--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>hashCode</h4>
<pre class="methodSignature">public&nbsp;int&nbsp;hashCode()</pre>
<dl>
<dt><span class="overrideSpecifyLabel">Overrides:</span></dt>
<dd><code><a href="AbstractClasspathEntry.html#hashCode--">hashCode</a></code>&nbsp;in class&nbsp;<code><a href="AbstractClasspathEntry.html" title="class in org.gradle.plugins.ide.eclipse.model">AbstractClasspathEntry</a></code></dd>
</dl>
</li>
</ul>
<a name="toString--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>toString</h4>
<pre class="methodSignature">public&nbsp;java.lang.String&nbsp;toString()</pre>
<dl>
<dt><span class="overrideSpecifyLabel">Overrides:</span></dt>
<dd><code><a href="AbstractClasspathEntry.html#toString--">toString</a></code>&nbsp;in class&nbsp;<code><a href="AbstractClasspathEntry.html" title="class in org.gradle.plugins.ide.eclipse.model">AbstractClasspathEntry</a></code></dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../../index-all.html">Index</a></li>
<li><a href="../../../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../../../allclasses.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li><a href="#field.summary">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
