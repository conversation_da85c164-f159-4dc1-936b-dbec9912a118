<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc -->
<title>Managed (Gradle API 8.5)</title>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<link rel="stylesheet" type="text/css" href="../../../javadoc.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../jquery/jquery-ui.min.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../jquery-ui.overrides.css" title="Style">
<script type="text/javascript" src="../../../script.js"></script>
<script type="text/javascript" src="../../../jquery/jszip/dist/jszip.min.js"></script>
<script type="text/javascript" src="../../../jquery/jszip-utils/dist/jszip-utils.min.js"></script>
<!--[if IE]>
<script type="text/javascript" src="../../../jquery/jszip-utils/dist/jszip-utils-ie.min.js"></script>
<![endif]-->
<script type="text/javascript" src="../../../jquery/jquery-3.6.1.min.js"></script>
<script type="text/javascript" src="../../../jquery/jquery-ui.min.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="Managed (Gradle API 8.5)";
        }
    }
    catch(err) {
    }
//-->
var pathtoroot = "../../../";
var useModuleDirectories = false;
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<div class="fixedNav">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../index-all.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../allclasses.html">All&nbsp;Classes</a></li>
</ul>
<ul class="navListSearch">
<li><label for="search">SEARCH:</label>
<input type="text" id="search" value="search" disabled="disabled">
<input type="reset" id="reset" value="reset" disabled="disabled">
</li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Required&nbsp;|&nbsp;</li>
<li>Optional</li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Element</li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
</div>
<div class="navPadding">&nbsp;</div>
<script type="text/javascript"><!--
$('.navPadding').css('padding-top', $('.fixedNav').css("height"));
//-->
</script>
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle"><span class="packageLabelInType">Package</span>&nbsp;<a href="package-summary.html">org.gradle.model</a></div>
<h2 title="Annotation Type Managed" class="title">Annotation Type Managed</h2>
</div>
<div class="contentContainer">
<div class="description">
<ul class="blockList">
<li class="blockList">
<hr>
<pre>@Retention(RUNTIME)
@Target(TYPE)
<a href="../api/Incubating.html" title="annotation in org.gradle.api">@Incubating</a>
public @interface <span class="memberNameLabel">Managed</span></pre>
<div class="block">A managed type is transparent to the model space, and enforces immutability at the appropriate times in the object's lifecycle.
 <p>
 Gradle generates implementations for managed types.
 As such, managed types are declared either as interfaces or abstract classes.
 The generated implementation integrates with the model space mechanisms, and manages mutability.
 <p>
 Managed types are mostly behaviour-less, as they are data.
 Instances of managed types should effectively be considered value objects.

 <h3>Properties</h3>
 <p>
 Managed types declare their structure as properties, via getter and setter methods.
 Getter and setter methods are expected to conform to the well-known Java Bean naming conventions.
 A read/write “name” property would be expressed via the following methods:
 <pre>
 void setName(String name);
 String getName();
 </pre>
 <p>
 A getter and setter must be declared for each property that is not of a managed type or of <a href="ModelSet.html" title="interface in org.gradle.model"><code>ModelSet</code></a>.
 For properties of managed types or of <a href="ModelSet.html" title="interface in org.gradle.model"><code>ModelSet</code></a> the getter is mandatory and the setter is optional.
 If no setter is provided the property is considered inherent and defaults to an "empty" instance of the type.
 In addition to the traditional getter method, properties of type <code>boolean</code> (but not <code>Boolean</code>)
 also support a getter method which name starts with <code>is</code>, for example:

 <pre>
 void setEnabled(boolean enabled);
 boolean isEnabled();
 </pre>

 <h4>Supported property types</h4>
 <p>
 The following JDK types are allowed:
 <ul>
 <li><code>String</code></li>
 <li><code>Boolean</code></li>
 <li><code>Character</code></li>
 <li><code>Byte</code></li>
 <li><code>Short</code></li>
 <li><code>Integer</code></li>
 <li><code>Long</code></li>
 <li><code>Float</code></li>
 <li><code>Double</code></li>
 <li><code>BigInteger</code></li>
 <li><code>BigDecimal</code></li>
 <li><code>File</code></li>
 </ul>
 <p>
 All primitive types and <code>Enum</code> types are also allowed.
 <p>
 Properties that are themselves of a managed type are also supported.
 <p>
 Currently, the only collection types that are supported are <a href="ModelSet.html" title="interface in org.gradle.model"><code>ModelSet</code></a> and <a href="ModelMap.html" title="interface in org.gradle.model"><code>ModelMap</code></a>, as well as <code>Set</code> or <code>List</code>
 of <code>scalar types</code>, where scalar types is either one of the supported immutable JDK types above or an enumeration.
 <p>
 Properties of any other type must have their getter annotated with <a href="Unmanaged.html" title="annotation in org.gradle.model"><code>Unmanaged</code></a>.
 An unmanaged property is not transparent to the model infrastructure and is guaranteed to be immutable when realized.

 <h3>Named types</h3>
 <p>
 Managed types may implement/extend the <a href="../api/Named.html" title="interface in org.gradle.api"><code>Named</code></a> interface.
 Any managed type implementing this interface will have its <code>name</code> attribute populated automatically
 based on the name of the corresponding node in the model graph.
 <p>
 The <a href="ModelMap.html" title="interface in org.gradle.model"><code>ModelMap</code></a> type requires that its elements are <a href="../api/Named.html" title="interface in org.gradle.api"><code>Named</code></a>.

 <h3>Inheritance</h3>
 <p>
 Managed types can be arranged into an inheritance hierarchy.
 Every type in the hierarchy must conform to the constraints of managed types.

 <h3>Calculated read-only properties</h3>
 <p>
 Managed types can contain getter methods that return calculated values, based on other properties.
 For example, a “name” property may return the concatenation of a “firstName” and “lastName” property.
 When using Java 8 or later, such properties can be implemented as interface default methods.
 Alternatively, the managed type can be implemented as an abstract class with the calculated property implemented as a non-abstract getter method.
 In both cases, the implementation of the calculated property getter may not call any setter method.

 <h3>Abstract classes</h3>
 <p>
 A managed type can be implemented as an abstract class.
 All property getters and setters must be declared <code>abstract</code> (with the exception of calculated read-only properties).
 The class cannot contain instance variables, constructors, or any methods that are not a getter or setter.

 <h3>Creating managed model elements</h3>
 <p>
 Please see <a href="Model.html" title="annotation in org.gradle.model"><code>Model</code></a> for information on creating model elements of managed types.</div>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../index-all.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../allclasses.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Required&nbsp;|&nbsp;</li>
<li>Optional</li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Element</li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
