<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc -->
<title>TaskContainer (Gradle API 8.5)</title>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<link rel="stylesheet" type="text/css" href="../../../../javadoc.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../jquery/jquery-ui.min.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../jquery-ui.overrides.css" title="Style">
<script type="text/javascript" src="../../../../script.js"></script>
<script type="text/javascript" src="../../../../jquery/jszip/dist/jszip.min.js"></script>
<script type="text/javascript" src="../../../../jquery/jszip-utils/dist/jszip-utils.min.js"></script>
<!--[if IE]>
<script type="text/javascript" src="../../../../jquery/jszip-utils/dist/jszip-utils-ie.min.js"></script>
<![endif]-->
<script type="text/javascript" src="../../../../jquery/jquery-3.6.1.min.js"></script>
<script type="text/javascript" src="../../../../jquery/jquery-ui.min.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="TaskContainer (Gradle API 8.5)";
        }
    }
    catch(err) {
    }
//-->
var data = {"i0":6,"i1":6,"i2":6,"i3":6,"i4":6,"i5":6,"i6":6,"i7":6,"i8":6,"i9":6,"i10":6,"i11":6,"i12":6,"i13":6,"i14":6,"i15":6};
var tabs = {65535:["t0","All Methods"],2:["t2","Instance Methods"],4:["t3","Abstract Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
var pathtoroot = "../../../../";
var useModuleDirectories = false;
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<div class="fixedNav">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../allclasses.html">All&nbsp;Classes</a></li>
</ul>
<ul class="navListSearch">
<li><label for="search">SEARCH:</label>
<input type="text" id="search" value="search" disabled="disabled">
<input type="reset" id="reset" value="reset" disabled="disabled">
</li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
</div>
<div class="navPadding">&nbsp;</div>
<script type="text/javascript"><!--
$('.navPadding').css('padding-top', $('.fixedNav').css("height"));
//-->
</script>
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle"><span class="packageLabelInType">Package</span>&nbsp;<a href="package-summary.html">org.gradle.api.tasks</a></div>
<h2 title="Interface TaskContainer" class="title">Interface TaskContainer</h2>
</div>
<div class="contentContainer">
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt>All Superinterfaces:</dt>
<dd><code>java.util.Collection&lt;<a href="../Task.html" title="interface in org.gradle.api">Task</a>&gt;</code>, <code><a href="../../util/Configurable.html" title="interface in org.gradle.util">Configurable</a>&lt;<a href="../NamedDomainObjectContainer.html" title="interface in org.gradle.api">NamedDomainObjectContainer</a>&lt;<a href="../Task.html" title="interface in org.gradle.api">Task</a>&gt;&gt;</code>, <code><a href="../DomainObjectCollection.html" title="interface in org.gradle.api">DomainObjectCollection</a>&lt;<a href="../Task.html" title="interface in org.gradle.api">Task</a>&gt;</code>, <code><a href="../DomainObjectSet.html" title="interface in org.gradle.api">DomainObjectSet</a>&lt;<a href="../Task.html" title="interface in org.gradle.api">Task</a>&gt;</code>, <code>java.lang.Iterable&lt;<a href="../Task.html" title="interface in org.gradle.api">Task</a>&gt;</code>, <code><a href="../NamedDomainObjectCollection.html" title="interface in org.gradle.api">NamedDomainObjectCollection</a>&lt;<a href="../Task.html" title="interface in org.gradle.api">Task</a>&gt;</code>, <code><a href="../NamedDomainObjectContainer.html" title="interface in org.gradle.api">NamedDomainObjectContainer</a>&lt;<a href="../Task.html" title="interface in org.gradle.api">Task</a>&gt;</code>, <code><a href="../NamedDomainObjectSet.html" title="interface in org.gradle.api">NamedDomainObjectSet</a>&lt;<a href="../Task.html" title="interface in org.gradle.api">Task</a>&gt;</code>, <code><a href="../PolymorphicDomainObjectContainer.html" title="interface in org.gradle.api">PolymorphicDomainObjectContainer</a>&lt;<a href="../Task.html" title="interface in org.gradle.api">Task</a>&gt;</code>, <code>java.util.Set&lt;<a href="../Task.html" title="interface in org.gradle.api">Task</a>&gt;</code>, <code><a href="TaskCollection.html" title="interface in org.gradle.api.tasks">TaskCollection</a>&lt;<a href="../Task.html" title="interface in org.gradle.api">Task</a>&gt;</code></dd>
</dl>
<hr>
<pre>public interface <span class="typeNameLabel">TaskContainer</span>
extends <a href="TaskCollection.html" title="interface in org.gradle.api.tasks">TaskCollection</a>&lt;<a href="../Task.html" title="interface in org.gradle.api">Task</a>&gt;, <a href="../PolymorphicDomainObjectContainer.html" title="interface in org.gradle.api">PolymorphicDomainObjectContainer</a>&lt;<a href="../Task.html" title="interface in org.gradle.api">Task</a>&gt;</pre>
<div class="block"><p>A <code>TaskContainer</code> is responsible for managing a set of <a href="../Task.html" title="interface in org.gradle.api"><code>Task</code></a> instances.</p>

 <p>You can obtain a <code>TaskContainer</code> instance by calling <a href="../Project.html#getTasks--"><code>Project.getTasks()</code></a>, or using the
 <code>tasks</code> property in your build script.</p></div>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t3" class="tableTab"><span><a href="javascript:show(4);">Abstract Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colSecond" scope="col">Method</th>
<th class="colLast" scope="col">Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code><a href="../Task.html" title="interface in org.gradle.api">Task</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#create-java.lang.String-">create</a></span>&#8203;(java.lang.String&nbsp;name)</code></th>
<td class="colLast">
<div class="block">Creates a <a href="../Task.html" title="interface in org.gradle.api"><code>Task</code></a> with the given name and adds it to this container.</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code><a href="../Task.html" title="interface in org.gradle.api">Task</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#create-java.lang.String-groovy.lang.Closure-">create</a></span>&#8203;(java.lang.String&nbsp;name,
      <a href="https://docs.groovy-lang.org/docs/groovy-3.0.17/html/gapi/groovy/lang/Closure.html?is-external=true" title="class or interface in groovy.lang" class="externalLink">Closure</a>&nbsp;configureClosure)</code></th>
<td class="colLast">
<div class="block">Creates a <a href="../Task.html" title="interface in org.gradle.api"><code>Task</code></a> with the given name adds it to this container.</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>&lt;T extends <a href="../Task.html" title="interface in org.gradle.api">Task</a>&gt;<br>T</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#create-java.lang.String-java.lang.Class-">create</a></span>&#8203;(java.lang.String&nbsp;name,
      java.lang.Class&lt;T&gt;&nbsp;type)</code></th>
<td class="colLast">
<div class="block">Creates a <a href="../Task.html" title="interface in org.gradle.api"><code>Task</code></a> with the given name and type, and adds it to this container.</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>&lt;T extends <a href="../Task.html" title="interface in org.gradle.api">Task</a>&gt;<br>T</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#create-java.lang.String-java.lang.Class-java.lang.Object...-">create</a></span>&#8203;(java.lang.String&nbsp;name,
      java.lang.Class&lt;T&gt;&nbsp;type,
      java.lang.Object...&nbsp;constructorArgs)</code></th>
<td class="colLast">
<div class="block">Creates a <a href="../Task.html" title="interface in org.gradle.api"><code>Task</code></a> with the given name and type, passing the given arguments to the <code>@Inject</code>-annotated constructor,
 and adds it to this container.</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code>&lt;T extends <a href="../Task.html" title="interface in org.gradle.api">Task</a>&gt;<br>T</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#create-java.lang.String-java.lang.Class-org.gradle.api.Action-">create</a></span>&#8203;(java.lang.String&nbsp;name,
      java.lang.Class&lt;T&gt;&nbsp;type,
      <a href="../Action.html" title="interface in org.gradle.api">Action</a>&lt;? super T&gt;&nbsp;configuration)</code></th>
<td class="colLast">
<div class="block">Creates a <a href="../Task.html" title="interface in org.gradle.api"><code>Task</code></a> with the given name and type, configures it with the given action, and adds it to this container.</div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code><a href="../Task.html" title="interface in org.gradle.api">Task</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#create-java.util.Map-">create</a></span>&#8203;(java.util.Map&lt;java.lang.String,&#8203;?&gt;&nbsp;options)</code></th>
<td class="colLast">
<div class="block">Creates a <a href="../Task.html" title="interface in org.gradle.api"><code>Task</code></a> and adds it to this container.</div>
</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code><a href="../Task.html" title="interface in org.gradle.api">Task</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#create-java.util.Map-groovy.lang.Closure-">create</a></span>&#8203;(java.util.Map&lt;java.lang.String,&#8203;?&gt;&nbsp;options,
      <a href="https://docs.groovy-lang.org/docs/groovy-3.0.17/html/gapi/groovy/lang/Closure.html?is-external=true" title="class or interface in groovy.lang" class="externalLink">Closure</a>&nbsp;configureClosure)</code></th>
<td class="colLast">
<div class="block">Creates a <a href="../Task.html" title="interface in org.gradle.api"><code>Task</code></a> adds it to this container.</div>
</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code><a href="../Task.html" title="interface in org.gradle.api">Task</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#findByPath-java.lang.String-">findByPath</a></span>&#8203;(java.lang.String&nbsp;path)</code></th>
<td class="colLast">
<div class="block">Locates a task by path.</div>
</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code><a href="../Task.html" title="interface in org.gradle.api">Task</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getByPath-java.lang.String-">getByPath</a></span>&#8203;(java.lang.String&nbsp;path)</code></th>
<td class="colLast">
<div class="block">Locates a task by path.</div>
</td>
</tr>
<tr id="i9" class="rowColor">
<td class="colFirst"><code><a href="TaskProvider.html" title="interface in org.gradle.api.tasks">TaskProvider</a>&lt;<a href="../Task.html" title="interface in org.gradle.api">Task</a>&gt;</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#register-java.lang.String-">register</a></span>&#8203;(java.lang.String&nbsp;name)</code></th>
<td class="colLast">
<div class="block">Defines a new task, which will be created when it is required.</div>
</td>
</tr>
<tr id="i10" class="altColor">
<td class="colFirst"><code>&lt;T extends <a href="../Task.html" title="interface in org.gradle.api">Task</a>&gt;<br><a href="TaskProvider.html" title="interface in org.gradle.api.tasks">TaskProvider</a>&lt;T&gt;</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#register-java.lang.String-java.lang.Class-">register</a></span>&#8203;(java.lang.String&nbsp;name,
        java.lang.Class&lt;T&gt;&nbsp;type)</code></th>
<td class="colLast">
<div class="block">Defines a new task, which will be created when it is required.</div>
</td>
</tr>
<tr id="i11" class="rowColor">
<td class="colFirst"><code>&lt;T extends <a href="../Task.html" title="interface in org.gradle.api">Task</a>&gt;<br><a href="TaskProvider.html" title="interface in org.gradle.api.tasks">TaskProvider</a>&lt;T&gt;</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#register-java.lang.String-java.lang.Class-java.lang.Object...-">register</a></span>&#8203;(java.lang.String&nbsp;name,
        java.lang.Class&lt;T&gt;&nbsp;type,
        java.lang.Object...&nbsp;constructorArgs)</code></th>
<td class="colLast">
<div class="block">Defines a new task, which will be created when it is required passing the given arguments to the <code>@Inject</code>-annotated constructor.</div>
</td>
</tr>
<tr id="i12" class="altColor">
<td class="colFirst"><code>&lt;T extends <a href="../Task.html" title="interface in org.gradle.api">Task</a>&gt;<br><a href="TaskProvider.html" title="interface in org.gradle.api.tasks">TaskProvider</a>&lt;T&gt;</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#register-java.lang.String-java.lang.Class-org.gradle.api.Action-">register</a></span>&#8203;(java.lang.String&nbsp;name,
        java.lang.Class&lt;T&gt;&nbsp;type,
        <a href="../Action.html" title="interface in org.gradle.api">Action</a>&lt;? super T&gt;&nbsp;configurationAction)</code></th>
<td class="colLast">
<div class="block">Defines a new task, which will be created and configured when it is required.</div>
</td>
</tr>
<tr id="i13" class="rowColor">
<td class="colFirst"><code><a href="TaskProvider.html" title="interface in org.gradle.api.tasks">TaskProvider</a>&lt;<a href="../Task.html" title="interface in org.gradle.api">Task</a>&gt;</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#register-java.lang.String-org.gradle.api.Action-">register</a></span>&#8203;(java.lang.String&nbsp;name,
        <a href="../Action.html" title="interface in org.gradle.api">Action</a>&lt;? super <a href="../Task.html" title="interface in org.gradle.api">Task</a>&gt;&nbsp;configurationAction)</code></th>
<td class="colLast">
<div class="block">Defines a new task, which will be created and configured when it is required.</div>
</td>
</tr>
<tr id="i14" class="altColor">
<td class="colFirst"><code><a href="../Task.html" title="interface in org.gradle.api">Task</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#replace-java.lang.String-">replace</a></span>&#8203;(java.lang.String&nbsp;name)</code></th>
<td class="colLast">
<div class="block">Creates a <a href="../Task.html" title="interface in org.gradle.api"><code>Task</code></a> with the given name and adds it to this container, replacing any existing task with the
 same name.</div>
</td>
</tr>
<tr id="i15" class="rowColor">
<td class="colFirst"><code>&lt;T extends <a href="../Task.html" title="interface in org.gradle.api">Task</a>&gt;<br>T</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#replace-java.lang.String-java.lang.Class-">replace</a></span>&#8203;(java.lang.String&nbsp;name,
       java.lang.Class&lt;T&gt;&nbsp;type)</code></th>
<td class="colLast">
<div class="block">Creates a <a href="../Task.html" title="interface in org.gradle.api"><code>Task</code></a> with the given name and type, and adds it to this container, replacing any existing
 task of the same name.</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.util.Collection">
<!--   -->
</a>
<h3>Methods inherited from interface&nbsp;java.util.Collection</h3>
<code>parallelStream, removeIf, stream, toArray</code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.org.gradle.api.DomainObjectCollection">
<!--   -->
</a>
<h3>Methods inherited from interface&nbsp;org.gradle.api.<a href="../DomainObjectCollection.html" title="interface in org.gradle.api">DomainObjectCollection</a></h3>
<code><a href="../DomainObjectCollection.html#addAllLater-org.gradle.api.provider.Provider-">addAllLater</a>, <a href="../DomainObjectCollection.html#addLater-org.gradle.api.provider.Provider-">addLater</a>, <a href="../DomainObjectCollection.html#all-groovy.lang.Closure-">all</a>, <a href="../DomainObjectCollection.html#all-org.gradle.api.Action-">all</a>, <a href="../DomainObjectCollection.html#configureEach-org.gradle.api.Action-">configureEach</a>, <a href="../DomainObjectCollection.html#whenObjectAdded-groovy.lang.Closure-">whenObjectAdded</a>, <a href="../DomainObjectCollection.html#whenObjectAdded-org.gradle.api.Action-">whenObjectAdded</a>, <a href="../DomainObjectCollection.html#whenObjectRemoved-groovy.lang.Closure-">whenObjectRemoved</a>, <a href="../DomainObjectCollection.html#whenObjectRemoved-org.gradle.api.Action-">whenObjectRemoved</a>, <a href="../DomainObjectCollection.html#withType-java.lang.Class-groovy.lang.Closure-">withType</a>, <a href="../DomainObjectCollection.html#withType-java.lang.Class-org.gradle.api.Action-">withType</a></code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Iterable">
<!--   -->
</a>
<h3>Methods inherited from interface&nbsp;java.lang.Iterable</h3>
<code>forEach</code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.org.gradle.api.NamedDomainObjectCollection">
<!--   -->
</a>
<h3>Methods inherited from interface&nbsp;org.gradle.api.<a href="../NamedDomainObjectCollection.html" title="interface in org.gradle.api">NamedDomainObjectCollection</a></h3>
<code><a href="../NamedDomainObjectCollection.html#add-T-">add</a>, <a href="../NamedDomainObjectCollection.html#addAll-java.util.Collection-">addAll</a>, <a href="../NamedDomainObjectCollection.html#addRule-java.lang.String-groovy.lang.Closure-">addRule</a>, <a href="../NamedDomainObjectCollection.html#addRule-java.lang.String-org.gradle.api.Action-">addRule</a>, <a href="../NamedDomainObjectCollection.html#addRule-org.gradle.api.Rule-">addRule</a>, <a href="../NamedDomainObjectCollection.html#findByName-java.lang.String-">findByName</a>, <a href="../NamedDomainObjectCollection.html#getAsMap--">getAsMap</a>, <a href="../NamedDomainObjectCollection.html#getByName-java.lang.String-org.gradle.api.Action-">getByName</a>, <a href="../NamedDomainObjectCollection.html#getCollectionSchema--">getCollectionSchema</a>, <a href="../NamedDomainObjectCollection.html#getNamer--">getNamer</a>, <a href="../NamedDomainObjectCollection.html#getNames--">getNames</a>, <a href="../NamedDomainObjectCollection.html#getRules--">getRules</a></code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.org.gradle.api.NamedDomainObjectContainer">
<!--   -->
</a>
<h3>Methods inherited from interface&nbsp;org.gradle.api.<a href="../NamedDomainObjectContainer.html" title="interface in org.gradle.api">NamedDomainObjectContainer</a></h3>
<code><a href="../NamedDomainObjectContainer.html#configure-groovy.lang.Closure-">configure</a>, <a href="../NamedDomainObjectContainer.html#create-java.lang.String-org.gradle.api.Action-">create</a>, <a href="../NamedDomainObjectContainer.html#maybeCreate-java.lang.String-">maybeCreate</a></code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.org.gradle.api.NamedDomainObjectSet">
<!--   -->
</a>
<h3>Methods inherited from interface&nbsp;org.gradle.api.<a href="../NamedDomainObjectSet.html" title="interface in org.gradle.api">NamedDomainObjectSet</a></h3>
<code><a href="../NamedDomainObjectSet.html#findAll-groovy.lang.Closure-">findAll</a></code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.org.gradle.api.PolymorphicDomainObjectContainer">
<!--   -->
</a>
<h3>Methods inherited from interface&nbsp;org.gradle.api.<a href="../PolymorphicDomainObjectContainer.html" title="interface in org.gradle.api">PolymorphicDomainObjectContainer</a></h3>
<code><a href="../PolymorphicDomainObjectContainer.html#containerWithType-java.lang.Class-">containerWithType</a>, <a href="../PolymorphicDomainObjectContainer.html#maybeCreate-java.lang.String-java.lang.Class-">maybeCreate</a></code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.util.Set">
<!--   -->
</a>
<h3>Methods inherited from interface&nbsp;java.util.Set</h3>
<code>add, addAll, clear, contains, containsAll, equals, hashCode, isEmpty, iterator, remove, removeAll, retainAll, size, spliterator, toArray, toArray</code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.org.gradle.api.tasks.TaskCollection">
<!--   -->
</a>
<h3>Methods inherited from interface&nbsp;org.gradle.api.tasks.<a href="TaskCollection.html" title="interface in org.gradle.api.tasks">TaskCollection</a></h3>
<code><a href="TaskCollection.html#getAt-java.lang.String-">getAt</a>, <a href="TaskCollection.html#getByName-java.lang.String-">getByName</a>, <a href="TaskCollection.html#getByName-java.lang.String-groovy.lang.Closure-">getByName</a>, <a href="TaskCollection.html#matching-groovy.lang.Closure-">matching</a>, <a href="TaskCollection.html#matching-org.gradle.api.specs.Spec-">matching</a>, <a href="TaskCollection.html#named-java.lang.String-">named</a>, <a href="TaskCollection.html#named-java.lang.String-java.lang.Class-">named</a>, <a href="TaskCollection.html#named-java.lang.String-java.lang.Class-org.gradle.api.Action-">named</a>, <a href="TaskCollection.html#named-java.lang.String-org.gradle.api.Action-">named</a>, <a href="TaskCollection.html#whenTaskAdded-groovy.lang.Closure-">whenTaskAdded</a>, <a href="TaskCollection.html#whenTaskAdded-org.gradle.api.Action-">whenTaskAdded</a>, <a href="TaskCollection.html#withType-java.lang.Class-">withType</a></code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="findByPath-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>findByPath</h4>
<pre class="methodSignature">@Nullable
<a href="../Task.html" title="interface in org.gradle.api">Task</a>&nbsp;findByPath&#8203;(java.lang.String&nbsp;path)</pre>
<div class="block"><p>Locates a task by path. You can supply a task name, a relative path, or an absolute path. Relative paths are
 interpreted relative to the project for this container. This method returns null if no task with the given path
 exists.</p></div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>path</code> - the path of the task to be returned</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>The task. Returns null if so such task exists.</dd>
</dl>
</li>
</ul>
<a name="getByPath-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getByPath</h4>
<pre class="methodSignature"><a href="../Task.html" title="interface in org.gradle.api">Task</a>&nbsp;getByPath&#8203;(java.lang.String&nbsp;path)
        throws <a href="../UnknownTaskException.html" title="class in org.gradle.api">UnknownTaskException</a></pre>
<div class="block"><p>Locates a task by path. You can supply a task name, a relative path, or an absolute path. Relative paths are
 interpreted relative to the project for this container. This method throws an exception if no task with the given
 path exists.</p></div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>path</code> - the path of the task to be returned</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>The task. Never returns null</dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code><a href="../UnknownTaskException.html" title="class in org.gradle.api">UnknownTaskException</a></code> - If no task with the given path exists.</dd>
</dl>
</li>
</ul>
<a name="create-java.util.Map-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>create</h4>
<pre class="methodSignature"><a href="../Task.html" title="interface in org.gradle.api">Task</a>&nbsp;create&#8203;(java.util.Map&lt;java.lang.String,&#8203;?&gt;&nbsp;options)
     throws <a href="../InvalidUserDataException.html" title="class in org.gradle.api">InvalidUserDataException</a></pre>
<div class="block"><p>Creates a <a href="../Task.html" title="interface in org.gradle.api"><code>Task</code></a> and adds it to this container. A map of creation options can be passed to this method
 to control how the task is created. The following options are available:</p>

 <table>

 <tr><th>Option</th><th>Description</th><th>Default Value</th></tr>

 <tr><td><code><a href="../Task.html#TASK_NAME">"name"</a></code></td><td>The name of the task to create.</td><td>None.
 Must be specified.</td></tr>

 <tr><td><code><a href="../Task.html#TASK_TYPE">"type"</a></code></td><td>The class of the task to
 create.</td><td><a href="../DefaultTask.html" title="class in org.gradle.api"><code>DefaultTask</code></a></td></tr>

 <tr><td><code><a href="../Task.html#TASK_ACTION">"action"</a></code></td><td>The closure or <a href="../Action.html" title="interface in org.gradle.api"><code>Action</code></a> to
 execute when the task executes. See <a href="../Task.html#doFirst-org.gradle.api.Action-"><code>Task.doFirst(Action)</code></a>.</td><td><code>null</code></td></tr>

 <tr><td><code><a href="../Task.html#TASK_OVERWRITE">"overwrite"</a></code></td><td>Replace an existing
 task?</td><td><code>false</code></td></tr>

 <tr><td><code><a href="../Task.html#TASK_DEPENDS_ON">"dependsOn"</a></code></td><td>The dependencies of the task. See <a href="../Task.html#dependencies">here</a> for more details.</td><td><code>[]</code></td></tr>

 <tr><td><code><a href="../Task.html#TASK_GROUP">"group"</a></code></td><td>The group of the task.</td><td><code>null
 </code></td></tr>

 <tr><td><code><a href="../Task.html#TASK_DESCRIPTION">"description"</a></code></td><td>The description of the task.</td><td>
 <code>null</code></td></tr>

 <tr><td><code><a href="../Task.html#TASK_CONSTRUCTOR_ARGS">"constructorArgs"</a></code></td><td>The arguments to pass to the task class constructor.</td><td>
 <code>null</code></td></tr>

 </table>

 <p>After the task is added, it is made available as a property of the project, so that you can reference the task
 by name in your build file.</p>

 <p>If a task with the given name already exists in this container and the <code><a href="../Task.html#TASK_OVERWRITE">"overwrite"</a></code>
 option is not set to true, an exception is thrown.</p></div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>options</code> - The task creation options.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>The newly created task object</dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code><a href="../InvalidUserDataException.html" title="class in org.gradle.api">InvalidUserDataException</a></code> - If a task with the given name already exists in this project.</dd>
<dd><code>java.lang.NullPointerException</code> - If any of the values in <code><a href="../Task.html#TASK_CONSTRUCTOR_ARGS">"constructorArgs"</a></code> is null.</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../Project.html#getProperties--"><code>More information about how tasks are exposed by name in build scripts</code></a></dd>
</dl>
</li>
</ul>
<a name="create-java.util.Map-groovy.lang.Closure-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>create</h4>
<pre class="methodSignature"><a href="../Task.html" title="interface in org.gradle.api">Task</a>&nbsp;create&#8203;(java.util.Map&lt;java.lang.String,&#8203;?&gt;&nbsp;options,
            <a href="https://docs.groovy-lang.org/docs/groovy-3.0.17/html/gapi/groovy/lang/Closure.html?is-external=true" title="class or interface in groovy.lang" class="externalLink">Closure</a>&nbsp;configureClosure)
     throws <a href="../InvalidUserDataException.html" title="class in org.gradle.api">InvalidUserDataException</a></pre>
<div class="block"><p>Creates a <a href="../Task.html" title="interface in org.gradle.api"><code>Task</code></a> adds it to this container. A map of creation options can be passed to this method to
 control how the task is created. See <a href="#create-java.util.Map-"><code>create(java.util.Map)</code></a> for the list of options available. The given
 closure is used to configure the task before it is returned by this method.</p>

 <p>After the task is added, it is made available as a property of the project, so that you can reference the task
 by name in your build file.</p></div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>options</code> - The task creation options.</dd>
<dd><code>configureClosure</code> - The closure to use to configure the task.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>The newly created task object</dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code><a href="../InvalidUserDataException.html" title="class in org.gradle.api">InvalidUserDataException</a></code> - If a task with the given name already exists in this project.</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../Project.html#getProperties--"><code>More information about how tasks are exposed by name in build scripts</code></a></dd>
</dl>
</li>
</ul>
<a name="create-java.lang.String-groovy.lang.Closure-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>create</h4>
<pre class="methodSignature"><a href="../Task.html" title="interface in org.gradle.api">Task</a>&nbsp;create&#8203;(java.lang.String&nbsp;name,
            <a href="https://docs.groovy-lang.org/docs/groovy-3.0.17/html/gapi/groovy/lang/Closure.html?is-external=true" title="class or interface in groovy.lang" class="externalLink">Closure</a>&nbsp;configureClosure)
     throws <a href="../InvalidUserDataException.html" title="class in org.gradle.api">InvalidUserDataException</a></pre>
<div class="block"><p>Creates a <a href="../Task.html" title="interface in org.gradle.api"><code>Task</code></a> with the given name adds it to this container. The given closure is used to configure
 the task before it is returned by this method.</p>

 <p>After the task is added, it is made available as a property of the project, so that you can reference the task
 by name in your build file.</p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../NamedDomainObjectContainer.html#create-java.lang.String-groovy.lang.Closure-">create</a></code>&nbsp;in interface&nbsp;<code><a href="../NamedDomainObjectContainer.html" title="interface in org.gradle.api">NamedDomainObjectContainer</a>&lt;<a href="../Task.html" title="interface in org.gradle.api">Task</a>&gt;</code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>name</code> - The name of the task to be created</dd>
<dd><code>configureClosure</code> - The closure to use to configure the task.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>The newly created task object</dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code><a href="../InvalidUserDataException.html" title="class in org.gradle.api">InvalidUserDataException</a></code> - If a task with the given name already exists in this project.</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../Project.html#getProperties--"><code>More information about how tasks are exposed by name in build scripts</code></a></dd>
</dl>
</li>
</ul>
<a name="create-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>create</h4>
<pre class="methodSignature"><a href="../Task.html" title="interface in org.gradle.api">Task</a>&nbsp;create&#8203;(java.lang.String&nbsp;name)
     throws <a href="../InvalidUserDataException.html" title="class in org.gradle.api">InvalidUserDataException</a></pre>
<div class="block"><p>Creates a <a href="../Task.html" title="interface in org.gradle.api"><code>Task</code></a> with the given name and adds it to this container.</p>

 <p>After the task is added, it is made available as a property of the project, so that you can reference the task
 by name in your build file.</p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../NamedDomainObjectContainer.html#create-java.lang.String-">create</a></code>&nbsp;in interface&nbsp;<code><a href="../NamedDomainObjectContainer.html" title="interface in org.gradle.api">NamedDomainObjectContainer</a>&lt;<a href="../Task.html" title="interface in org.gradle.api">Task</a>&gt;</code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>name</code> - The name of the task to be created</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>The newly created task object</dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code><a href="../InvalidUserDataException.html" title="class in org.gradle.api">InvalidUserDataException</a></code> - If a task with the given name already exists in this project.</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../Project.html#getProperties--"><code>More information about how tasks are exposed by name in build scripts</code></a></dd>
</dl>
</li>
</ul>
<a name="create-java.lang.String-java.lang.Class-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>create</h4>
<pre class="methodSignature">&lt;T extends <a href="../Task.html" title="interface in org.gradle.api">Task</a>&gt;&nbsp;T&nbsp;create&#8203;(java.lang.String&nbsp;name,
                          java.lang.Class&lt;T&gt;&nbsp;type)
                   throws <a href="../InvalidUserDataException.html" title="class in org.gradle.api">InvalidUserDataException</a></pre>
<div class="block"><p>Creates a <a href="../Task.html" title="interface in org.gradle.api"><code>Task</code></a> with the given name and type, and adds it to this container.</p>

 <p>After the task is added, it is made available as a property of the project, so that you can reference the task
 by name in your build file.</p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../PolymorphicDomainObjectContainer.html#create-java.lang.String-java.lang.Class-">create</a></code>&nbsp;in interface&nbsp;<code><a href="../PolymorphicDomainObjectContainer.html" title="interface in org.gradle.api">PolymorphicDomainObjectContainer</a>&lt;<a href="../Task.html" title="interface in org.gradle.api">Task</a>&gt;</code></dd>
<dt><span class="paramLabel">Type Parameters:</span></dt>
<dd><code>T</code> - the type of the domain object to be created</dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>name</code> - The name of the task to be created.</dd>
<dd><code>type</code> - The type of task to create.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>The newly created task object</dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code><a href="../InvalidUserDataException.html" title="class in org.gradle.api">InvalidUserDataException</a></code> - If a task with the given name already exists in this project.</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../Project.html#getProperties--"><code>More information about how tasks are exposed by name in build scripts</code></a></dd>
</dl>
</li>
</ul>
<a name="create-java.lang.String-java.lang.Class-java.lang.Object...-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>create</h4>
<pre class="methodSignature">&lt;T extends <a href="../Task.html" title="interface in org.gradle.api">Task</a>&gt;&nbsp;T&nbsp;create&#8203;(java.lang.String&nbsp;name,
                          java.lang.Class&lt;T&gt;&nbsp;type,
                          java.lang.Object...&nbsp;constructorArgs)
                   throws <a href="../InvalidUserDataException.html" title="class in org.gradle.api">InvalidUserDataException</a></pre>
<div class="block"><p>Creates a <a href="../Task.html" title="interface in org.gradle.api"><code>Task</code></a> with the given name and type, passing the given arguments to the <code>@Inject</code>-annotated constructor,
 and adds it to this container.  All values passed to the task constructor must be non-null; otherwise a
 <code>NullPointerException</code> will be thrown</p>

 <p>After the task is added, it is made available as a property of the project, so that you can reference the task
 by name in your build file.</p></div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>name</code> - The name of the task to be created.</dd>
<dd><code>type</code> - The type of task to create.</dd>
<dd><code>constructorArgs</code> - The arguments to pass to the task constructor</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>The newly created task object</dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code><a href="../InvalidUserDataException.html" title="class in org.gradle.api">InvalidUserDataException</a></code> - If a task with the given name already exists in this project.</dd>
<dd><code>java.lang.NullPointerException</code> - If any of the values in <code>constructorArgs</code> is null.</dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>4.7</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../Project.html#getProperties--"><code>More information about how tasks are exposed by name in build scripts</code></a></dd>
</dl>
</li>
</ul>
<a name="create-java.lang.String-java.lang.Class-org.gradle.api.Action-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>create</h4>
<pre class="methodSignature">&lt;T extends <a href="../Task.html" title="interface in org.gradle.api">Task</a>&gt;&nbsp;T&nbsp;create&#8203;(java.lang.String&nbsp;name,
                          java.lang.Class&lt;T&gt;&nbsp;type,
                          <a href="../Action.html" title="interface in org.gradle.api">Action</a>&lt;? super T&gt;&nbsp;configuration)
                   throws <a href="../InvalidUserDataException.html" title="class in org.gradle.api">InvalidUserDataException</a></pre>
<div class="block"><p>Creates a <a href="../Task.html" title="interface in org.gradle.api"><code>Task</code></a> with the given name and type, configures it with the given action, and adds it to this container.</p>

 <p>After the task is added, it is made available as a property of the project, so that you can reference the task
 by name in your build file.</p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../PolymorphicDomainObjectContainer.html#create-java.lang.String-java.lang.Class-org.gradle.api.Action-">create</a></code>&nbsp;in interface&nbsp;<code><a href="../PolymorphicDomainObjectContainer.html" title="interface in org.gradle.api">PolymorphicDomainObjectContainer</a>&lt;<a href="../Task.html" title="interface in org.gradle.api">Task</a>&gt;</code></dd>
<dt><span class="paramLabel">Type Parameters:</span></dt>
<dd><code>T</code> - the type of the domain object to be created</dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>name</code> - The name of the task to be created.</dd>
<dd><code>type</code> - The type of task to create.</dd>
<dd><code>configuration</code> - The action to configure the task with.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>The newly created task object.</dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code><a href="../InvalidUserDataException.html" title="class in org.gradle.api">InvalidUserDataException</a></code> - If a task with the given name already exists in this project.</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../Project.html#getProperties--"><code>More information about how tasks are exposed by name in build scripts</code></a></dd>
</dl>
</li>
</ul>
<a name="register-java.lang.String-org.gradle.api.Action-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>register</h4>
<pre class="methodSignature"><a href="TaskProvider.html" title="interface in org.gradle.api.tasks">TaskProvider</a>&lt;<a href="../Task.html" title="interface in org.gradle.api">Task</a>&gt;&nbsp;register&#8203;(java.lang.String&nbsp;name,
                            <a href="../Action.html" title="interface in org.gradle.api">Action</a>&lt;? super <a href="../Task.html" title="interface in org.gradle.api">Task</a>&gt;&nbsp;configurationAction)
                     throws <a href="../InvalidUserDataException.html" title="class in org.gradle.api">InvalidUserDataException</a></pre>
<div class="block">Defines a new task, which will be created and configured when it is required. A task is 'required' when the task is located using query methods such as <a href="TaskCollection.html#getByName-java.lang.String-"><code>TaskCollection.getByName(java.lang.String)</code></a>, when the task is added to the task graph for execution or when <a href="../provider/Provider.html#get--"><code>Provider.get()</code></a> is called on the return value of this method.

 <p>It is generally more efficient to use this method instead of <a href="../NamedDomainObjectContainer.html#create-java.lang.String-org.gradle.api.Action-"><code>NamedDomainObjectContainer.create(java.lang.String, org.gradle.api.Action)</code></a> or <a href="#create-java.lang.String-"><code>create(java.lang.String)</code></a>, as those methods will eagerly create and configure the task, regardless of whether that task is required for the current build or not. This method, on the other hand, will defer creation and configuration until required.</p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../NamedDomainObjectContainer.html#register-java.lang.String-org.gradle.api.Action-">register</a></code>&nbsp;in interface&nbsp;<code><a href="../NamedDomainObjectContainer.html" title="interface in org.gradle.api">NamedDomainObjectContainer</a>&lt;<a href="../Task.html" title="interface in org.gradle.api">Task</a>&gt;</code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>name</code> - The name of the task.</dd>
<dd><code>configurationAction</code> - The action to run to configure the task. This action runs when the task is required.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>A <a href="../provider/Provider.html" title="interface in org.gradle.api.provider"><code>Provider</code></a> whose value will be the task, when queried.</dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code><a href="../InvalidUserDataException.html" title="class in org.gradle.api">InvalidUserDataException</a></code> - If a task with the given name already exists in this project.</dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>4.9</dd>
</dl>
</li>
</ul>
<a name="register-java.lang.String-java.lang.Class-org.gradle.api.Action-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>register</h4>
<pre class="methodSignature">&lt;T extends <a href="../Task.html" title="interface in org.gradle.api">Task</a>&gt;&nbsp;<a href="TaskProvider.html" title="interface in org.gradle.api.tasks">TaskProvider</a>&lt;T&gt;&nbsp;register&#8203;(java.lang.String&nbsp;name,
                                          java.lang.Class&lt;T&gt;&nbsp;type,
                                          <a href="../Action.html" title="interface in org.gradle.api">Action</a>&lt;? super T&gt;&nbsp;configurationAction)
                                   throws <a href="../InvalidUserDataException.html" title="class in org.gradle.api">InvalidUserDataException</a></pre>
<div class="block">Defines a new task, which will be created and configured when it is required. A task is 'required' when the task is located using query methods such as <a href="TaskCollection.html#getByName-java.lang.String-"><code>TaskCollection.getByName(java.lang.String)</code></a>, when the task is added to the task graph for execution or when <a href="../provider/Provider.html#get--"><code>Provider.get()</code></a> is called on the return value of this method.

 <p>It is generally more efficient to use this method instead of <a href="#create-java.lang.String-java.lang.Class-org.gradle.api.Action-"><code>create(java.lang.String, java.lang.Class, org.gradle.api.Action)</code></a> or <a href="#create-java.lang.String-java.lang.Class-"><code>create(java.lang.String, java.lang.Class)</code></a>, as those methods will eagerly create and configure the task, regardless of whether that task is required for the current build or not. This method, on the other hand, will defer creation and configuration until required.</p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../PolymorphicDomainObjectContainer.html#register-java.lang.String-java.lang.Class-org.gradle.api.Action-">register</a></code>&nbsp;in interface&nbsp;<code><a href="../PolymorphicDomainObjectContainer.html" title="interface in org.gradle.api">PolymorphicDomainObjectContainer</a>&lt;<a href="../Task.html" title="interface in org.gradle.api">Task</a>&gt;</code></dd>
<dt><span class="paramLabel">Type Parameters:</span></dt>
<dd><code>T</code> - The task type</dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>name</code> - The name of the task.</dd>
<dd><code>type</code> - The task type.</dd>
<dd><code>configurationAction</code> - The action to run to configure the task. This action runs when the task is required.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>A <a href="../provider/Provider.html" title="interface in org.gradle.api.provider"><code>Provider</code></a> whose value will be the task, when queried.</dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code><a href="../InvalidUserDataException.html" title="class in org.gradle.api">InvalidUserDataException</a></code> - If a task with the given name already exists in this project.</dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>4.9</dd>
</dl>
</li>
</ul>
<a name="register-java.lang.String-java.lang.Class-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>register</h4>
<pre class="methodSignature">&lt;T extends <a href="../Task.html" title="interface in org.gradle.api">Task</a>&gt;&nbsp;<a href="TaskProvider.html" title="interface in org.gradle.api.tasks">TaskProvider</a>&lt;T&gt;&nbsp;register&#8203;(java.lang.String&nbsp;name,
                                          java.lang.Class&lt;T&gt;&nbsp;type)
                                   throws <a href="../InvalidUserDataException.html" title="class in org.gradle.api">InvalidUserDataException</a></pre>
<div class="block">Defines a new task, which will be created when it is required. A task is 'required' when the task is located using query methods such as <a href="TaskCollection.html#getByName-java.lang.String-"><code>TaskCollection.getByName(java.lang.String)</code></a>, when the task is added to the task graph for execution or when <a href="../provider/Provider.html#get--"><code>Provider.get()</code></a> is called on the return value of this method.

 <p>It is generally more efficient to use this method instead of <a href="#create-java.lang.String-java.lang.Class-org.gradle.api.Action-"><code>create(java.lang.String, java.lang.Class, org.gradle.api.Action)</code></a> or <a href="#create-java.lang.String-java.lang.Class-"><code>create(java.lang.String, java.lang.Class)</code></a>, as those methods will eagerly create and configure the task, regardless of whether that task is required for the current build or not. This method, on the other hand, will defer creation until required.</p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../PolymorphicDomainObjectContainer.html#register-java.lang.String-java.lang.Class-">register</a></code>&nbsp;in interface&nbsp;<code><a href="../PolymorphicDomainObjectContainer.html" title="interface in org.gradle.api">PolymorphicDomainObjectContainer</a>&lt;<a href="../Task.html" title="interface in org.gradle.api">Task</a>&gt;</code></dd>
<dt><span class="paramLabel">Type Parameters:</span></dt>
<dd><code>T</code> - The task type</dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>name</code> - The name of the task.</dd>
<dd><code>type</code> - The task type.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>A <a href="../provider/Provider.html" title="interface in org.gradle.api.provider"><code>Provider</code></a> whose value will be the task, when queried.</dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code><a href="../InvalidUserDataException.html" title="class in org.gradle.api">InvalidUserDataException</a></code> - If a task with the given name already exists in this project.</dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>4.9</dd>
</dl>
</li>
</ul>
<a name="register-java.lang.String-java.lang.Class-java.lang.Object...-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>register</h4>
<pre class="methodSignature">&lt;T extends <a href="../Task.html" title="interface in org.gradle.api">Task</a>&gt;&nbsp;<a href="TaskProvider.html" title="interface in org.gradle.api.tasks">TaskProvider</a>&lt;T&gt;&nbsp;register&#8203;(java.lang.String&nbsp;name,
                                          java.lang.Class&lt;T&gt;&nbsp;type,
                                          java.lang.Object...&nbsp;constructorArgs)
                                   throws <a href="../InvalidUserDataException.html" title="class in org.gradle.api">InvalidUserDataException</a></pre>
<div class="block">Defines a new task, which will be created when it is required passing the given arguments to the <code>@Inject</code>-annotated constructor. A task is 'required' when the task is located using query methods such as <a href="TaskCollection.html#getByName-java.lang.String-"><code>TaskCollection.getByName(java.lang.String)</code></a>, when the task is added to the task graph for execution or when <a href="../provider/Provider.html#get--"><code>Provider.get()</code></a> is called on the return value of this method. All values passed to the task constructor must be non-null; otherwise a <code>NullPointerException</code> will be thrown

 <p>It is generally more efficient to use this method instead of <a href="#create-java.lang.String-java.lang.Class-org.gradle.api.Action-"><code>create(java.lang.String, java.lang.Class, org.gradle.api.Action)</code></a> or <a href="#create-java.lang.String-java.lang.Class-"><code>create(java.lang.String, java.lang.Class)</code></a>, as those methods will eagerly create and configure the task, regardless of whether that task is required for the current build or not. This method, on the other hand, will defer creation until required.</p></div>
<dl>
<dt><span class="paramLabel">Type Parameters:</span></dt>
<dd><code>T</code> - The task type</dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>name</code> - The name of the task.</dd>
<dd><code>type</code> - The task type.</dd>
<dd><code>constructorArgs</code> - The arguments to pass to the task constructor</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>A <a href="../provider/Provider.html" title="interface in org.gradle.api.provider"><code>Provider</code></a> whose value will be the task, when queried.</dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code>java.lang.NullPointerException</code> - If any of the values in <code>constructorArgs</code> is null.</dd>
<dd><code><a href="../InvalidUserDataException.html" title="class in org.gradle.api">InvalidUserDataException</a></code></dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>4.9</dd>
</dl>
</li>
</ul>
<a name="register-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>register</h4>
<pre class="methodSignature"><a href="TaskProvider.html" title="interface in org.gradle.api.tasks">TaskProvider</a>&lt;<a href="../Task.html" title="interface in org.gradle.api">Task</a>&gt;&nbsp;register&#8203;(java.lang.String&nbsp;name)
                     throws <a href="../InvalidUserDataException.html" title="class in org.gradle.api">InvalidUserDataException</a></pre>
<div class="block">Defines a new task, which will be created when it is required. A task is 'required' when the task is located using query methods such as <a href="TaskCollection.html#getByName-java.lang.String-"><code>TaskCollection.getByName(java.lang.String)</code></a>, when the task is added to the task graph for execution or when <a href="../provider/Provider.html#get--"><code>Provider.get()</code></a> is called on the return value of this method.

 <p>It is generally more efficient to use this method instead of <a href="#create-java.lang.String-"><code>create(java.lang.String)</code></a>, as that method will eagerly create the task, regardless of whether that task is required for the current build or not. This method, on the other hand, will defer creation until required.</p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../NamedDomainObjectContainer.html#register-java.lang.String-">register</a></code>&nbsp;in interface&nbsp;<code><a href="../NamedDomainObjectContainer.html" title="interface in org.gradle.api">NamedDomainObjectContainer</a>&lt;<a href="../Task.html" title="interface in org.gradle.api">Task</a>&gt;</code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>name</code> - The name of the task.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>A <a href="../provider/Provider.html" title="interface in org.gradle.api.provider"><code>Provider</code></a> whose value will be the task, when queried.</dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code><a href="../InvalidUserDataException.html" title="class in org.gradle.api">InvalidUserDataException</a></code> - If a task with the given name already exists in this project.</dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>4.9</dd>
</dl>
</li>
</ul>
<a name="replace-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>replace</h4>
<pre class="methodSignature"><a href="../Task.html" title="interface in org.gradle.api">Task</a>&nbsp;replace&#8203;(java.lang.String&nbsp;name)</pre>
<div class="block"><p>Creates a <a href="../Task.html" title="interface in org.gradle.api"><code>Task</code></a> with the given name and adds it to this container, replacing any existing task with the
 same name.</p>

 <p>After the task is added, it is made available as a property of the project, so that you can reference the task
 by name in your build file.</p></div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>name</code> - The name of the task to be created</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>The newly created task object</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../Project.html#getProperties--"><code>More information about how tasks are exposed by name in build scripts</code></a></dd>
</dl>
</li>
</ul>
<a name="replace-java.lang.String-java.lang.Class-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>replace</h4>
<pre class="methodSignature">&lt;T extends <a href="../Task.html" title="interface in org.gradle.api">Task</a>&gt;&nbsp;T&nbsp;replace&#8203;(java.lang.String&nbsp;name,
                           java.lang.Class&lt;T&gt;&nbsp;type)</pre>
<div class="block"><p>Creates a <a href="../Task.html" title="interface in org.gradle.api"><code>Task</code></a> with the given name and type, and adds it to this container, replacing any existing
 task of the same name.</p>

 <p>After the task is added, it is made available as a property of the project, so that you can reference the task
 by name in your build file.</p></div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>name</code> - The name of the task to be created.</dd>
<dd><code>type</code> - The type of task to create.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>The newly created task object</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../Project.html#getProperties--"><code>More information about how tasks are exposed by name in build scripts</code></a></dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../allclasses.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
