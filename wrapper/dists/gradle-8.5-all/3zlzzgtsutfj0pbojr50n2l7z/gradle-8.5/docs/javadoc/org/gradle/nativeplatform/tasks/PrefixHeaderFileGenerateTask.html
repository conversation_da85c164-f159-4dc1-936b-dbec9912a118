<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc -->
<title>PrefixHeaderFileGenerateTask (Gradle API 8.5)</title>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<link rel="stylesheet" type="text/css" href="../../../../javadoc.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../jquery/jquery-ui.min.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../jquery-ui.overrides.css" title="Style">
<script type="text/javascript" src="../../../../script.js"></script>
<script type="text/javascript" src="../../../../jquery/jszip/dist/jszip.min.js"></script>
<script type="text/javascript" src="../../../../jquery/jszip-utils/dist/jszip-utils.min.js"></script>
<!--[if IE]>
<script type="text/javascript" src="../../../../jquery/jszip-utils/dist/jszip-utils-ie.min.js"></script>
<![endif]-->
<script type="text/javascript" src="../../../../jquery/jquery-3.6.1.min.js"></script>
<script type="text/javascript" src="../../../../jquery/jquery-ui.min.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="PrefixHeaderFileGenerateTask (Gradle API 8.5)";
        }
    }
    catch(err) {
    }
//-->
var data = {"i0":10,"i1":10,"i2":10,"i3":10};
var tabs = {65535:["t0","All Methods"],2:["t2","Instance Methods"],8:["t4","Concrete Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
var pathtoroot = "../../../../";
var useModuleDirectories = false;
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<div class="fixedNav">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../allclasses.html">All&nbsp;Classes</a></li>
</ul>
<ul class="navListSearch">
<li><label for="search">SEARCH:</label>
<input type="text" id="search" value="search" disabled="disabled">
<input type="reset" id="reset" value="reset" disabled="disabled">
</li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li><a href="#nested.class.summary">Nested</a>&nbsp;|&nbsp;</li>
<li><a href="#field.summary">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
</div>
<div class="navPadding">&nbsp;</div>
<script type="text/javascript"><!--
$('.navPadding').css('padding-top', $('.fixedNav').css("height"));
//-->
</script>
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle"><span class="packageLabelInType">Package</span>&nbsp;<a href="package-summary.html">org.gradle.nativeplatform.tasks</a></div>
<h2 title="Class PrefixHeaderFileGenerateTask" class="title">Class PrefixHeaderFileGenerateTask</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li>org.gradle.api.internal.AbstractTask</li>
<li>
<ul class="inheritance">
<li><a href="../../api/DefaultTask.html" title="class in org.gradle.api">org.gradle.api.DefaultTask</a></li>
<li>
<ul class="inheritance">
<li>org.gradle.nativeplatform.tasks.PrefixHeaderFileGenerateTask</li>
</ul>
</li>
</ul>
</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt>All Implemented Interfaces:</dt>
<dd><code>java.lang.Comparable&lt;<a href="../../api/Task.html" title="interface in org.gradle.api">Task</a>&gt;</code>, <code>org.gradle.api.internal.DynamicObjectAware</code>, <code>org.gradle.api.internal.TaskInternal</code>, <code><a href="../../api/plugins/ExtensionAware.html" title="interface in org.gradle.api.plugins">ExtensionAware</a></code>, <code><a href="../../api/Task.html" title="interface in org.gradle.api">Task</a></code>, <code><a href="../../util/Configurable.html" title="interface in org.gradle.util">Configurable</a>&lt;<a href="../../api/Task.html" title="interface in org.gradle.api">Task</a>&gt;</code></dd>
</dl>
<hr>
<pre><a href="../../api/Incubating.html" title="annotation in org.gradle.api">@Incubating</a>
<a href="../../work/DisableCachingByDefault.html" title="annotation in org.gradle.work">@DisableCachingByDefault</a>(<a href="../../work/DisableCachingByDefault.html#because--">because</a>="Not made cacheable, yet")
public abstract class <span class="typeNameLabel">PrefixHeaderFileGenerateTask</span>
extends <a href="../../api/DefaultTask.html" title="class in org.gradle.api">DefaultTask</a></pre>
<div class="block">Generates a prefix header file from a list of headers to be precompiled.</div>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ======== NESTED CLASS SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="nested.class.summary">
<!--   -->
</a>
<h3>Nested Class Summary</h3>
<ul class="blockList">
<li class="blockList"><a name="nested.classes.inherited.from.class.org.gradle.api.Task">
<!--   -->
</a>
<h3>Nested classes/interfaces inherited from interface&nbsp;org.gradle.api.<a href="../../api/Task.html" title="interface in org.gradle.api">Task</a></h3>
<code><a href="../../api/Task.Namer.html" title="class in org.gradle.api">Task.Namer</a></code></li>
</ul>
</li>
</ul>
<!-- =========== FIELD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="field.summary">
<!--   -->
</a>
<h3>Field Summary</h3>
<ul class="blockList">
<li class="blockList"><a name="fields.inherited.from.class.org.gradle.api.Task">
<!--   -->
</a>
<h3>Fields inherited from interface&nbsp;org.gradle.api.<a href="../../api/Task.html" title="interface in org.gradle.api">Task</a></h3>
<code><a href="../../api/Task.html#TASK_ACTION">TASK_ACTION</a>, <a href="../../api/Task.html#TASK_CONSTRUCTOR_ARGS">TASK_CONSTRUCTOR_ARGS</a>, <a href="../../api/Task.html#TASK_DEPENDS_ON">TASK_DEPENDS_ON</a>, <a href="../../api/Task.html#TASK_DESCRIPTION">TASK_DESCRIPTION</a>, <a href="../../api/Task.html#TASK_GROUP">TASK_GROUP</a>, <a href="../../api/Task.html#TASK_NAME">TASK_NAME</a>, <a href="../../api/Task.html#TASK_OVERWRITE">TASK_OVERWRITE</a>, <a href="../../api/Task.html#TASK_TYPE">TASK_TYPE</a></code></li>
</ul>
</li>
</ul>
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.summary">
<!--   -->
</a>
<h3>Constructor Summary</h3>
<table class="memberSummary" summary="Constructor Summary table, listing constructors, and an explanation">
<caption><span>Constructors</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Constructor</th>
<th class="colLast" scope="col">Description</th>
</tr>
<tr class="altColor">
<th class="colConstructorName" scope="row"><code><span class="memberNameLink"><a href="#PrefixHeaderFileGenerateTask-org.gradle.workers.WorkerExecutor-">PrefixHeaderFileGenerateTask</a></span>&#8203;(<a href="../../workers/WorkerExecutor.html" title="interface in org.gradle.workers">WorkerExecutor</a>&nbsp;workerExecutor)</code></th>
<td class="colLast">
<div class="block">Injects a <a href="../../workers/WorkerExecutor.html" title="interface in org.gradle.workers"><code>WorkerExecutor</code></a> instance.</div>
</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colSecond" scope="col">Method</th>
<th class="colLast" scope="col">Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>java.lang.String</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getHeader--">getHeader</a></span>()</code></th>
<td class="colLast">&nbsp;</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>java.io.File</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getPrefixHeaderFile--">getPrefixHeaderFile</a></span>()</code></th>
<td class="colLast">&nbsp;</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#setHeader-java.lang.String-">setHeader</a></span>&#8203;(java.lang.String&nbsp;header)</code></th>
<td class="colLast">&nbsp;</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#setPrefixHeaderFile-java.io.File-">setPrefixHeaderFile</a></span>&#8203;(java.io.File&nbsp;prefixHeaderFile)</code></th>
<td class="colLast">&nbsp;</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.org.gradle.api.DefaultTask">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;org.gradle.api.<a href="../../api/DefaultTask.html" title="class in org.gradle.api">DefaultTask</a></h3>
<code><a href="../../api/DefaultTask.html#compareTo-org.gradle.api.Task-">compareTo</a>, <a href="../../api/DefaultTask.html#configure-groovy.lang.Closure-">configure</a>, <a href="../../api/DefaultTask.html#dependsOn-java.lang.Object...-">dependsOn</a>, <a href="../../api/DefaultTask.html#doFirst-groovy.lang.Closure-">doFirst</a>, <a href="../../api/DefaultTask.html#doFirst-java.lang.String-org.gradle.api.Action-">doFirst</a>, <a href="../../api/DefaultTask.html#doFirst-org.gradle.api.Action-">doFirst</a>, <a href="../../api/DefaultTask.html#doLast-groovy.lang.Closure-">doLast</a>, <a href="../../api/DefaultTask.html#doLast-java.lang.String-org.gradle.api.Action-">doLast</a>, <a href="../../api/DefaultTask.html#doLast-org.gradle.api.Action-">doLast</a>, <a href="../../api/DefaultTask.html#finalizedBy-java.lang.Object...-">finalizedBy</a>, <a href="../../api/DefaultTask.html#getActions--">getActions</a>, <a href="../../api/DefaultTask.html#getAnt--">getAnt</a>, <a href="../../api/DefaultTask.html#getDependsOn--">getDependsOn</a>, <a href="../../api/DefaultTask.html#getDescription--">getDescription</a>, <a href="../../api/DefaultTask.html#getDestroyables--">getDestroyables</a>, <a href="../../api/DefaultTask.html#getDidWork--">getDidWork</a>, <a href="../../api/DefaultTask.html#getEnabled--">getEnabled</a>, <a href="../../api/DefaultTask.html#getExtensions--">getExtensions</a>, <a href="../../api/DefaultTask.html#getFinalizedBy--">getFinalizedBy</a>, <a href="../../api/DefaultTask.html#getGroup--">getGroup</a>, <a href="../../api/DefaultTask.html#getInputs--">getInputs</a>, <a href="../../api/DefaultTask.html#getLocalState--">getLocalState</a>, <a href="../../api/DefaultTask.html#getLogger--">getLogger</a>, <a href="../../api/DefaultTask.html#getLogging--">getLogging</a>, <a href="../../api/DefaultTask.html#getMustRunAfter--">getMustRunAfter</a>, <a href="../../api/DefaultTask.html#getName--">getName</a>, <a href="../../api/DefaultTask.html#getOutputs--">getOutputs</a>, <a href="../../api/DefaultTask.html#getPath--">getPath</a>, <a href="../../api/DefaultTask.html#getProject--">getProject</a>, <a href="../../api/DefaultTask.html#getShouldRunAfter--">getShouldRunAfter</a>, <a href="../../api/DefaultTask.html#getState--">getState</a>, <a href="../../api/DefaultTask.html#getTaskDependencies--">getTaskDependencies</a>, <a href="../../api/DefaultTask.html#getTemporaryDir--">getTemporaryDir</a>, <a href="../../api/DefaultTask.html#getTimeout--">getTimeout</a>, <a href="../../api/DefaultTask.html#hasProperty-java.lang.String-">hasProperty</a>, <a href="../../api/DefaultTask.html#mustRunAfter-java.lang.Object...-">mustRunAfter</a>, <a href="../../api/DefaultTask.html#onlyIf-groovy.lang.Closure-">onlyIf</a>, <a href="../../api/DefaultTask.html#onlyIf-java.lang.String-org.gradle.api.specs.Spec-">onlyIf</a>, <a href="../../api/DefaultTask.html#onlyIf-org.gradle.api.specs.Spec-">onlyIf</a>, <a href="../../api/DefaultTask.html#property-java.lang.String-">property</a>, <a href="../../api/DefaultTask.html#setActions-java.util.List-">setActions</a>, <a href="../../api/DefaultTask.html#setDependsOn-java.lang.Iterable-">setDependsOn</a>, <a href="../../api/DefaultTask.html#setDescription-java.lang.String-">setDescription</a>, <a href="../../api/DefaultTask.html#setDidWork-boolean-">setDidWork</a>, <a href="../../api/DefaultTask.html#setEnabled-boolean-">setEnabled</a>, <a href="../../api/DefaultTask.html#setFinalizedBy-java.lang.Iterable-">setFinalizedBy</a>, <a href="../../api/DefaultTask.html#setGroup-java.lang.String-">setGroup</a>, <a href="../../api/DefaultTask.html#setMustRunAfter-java.lang.Iterable-">setMustRunAfter</a>, <a href="../../api/DefaultTask.html#setOnlyIf-groovy.lang.Closure-">setOnlyIf</a>, <a href="../../api/DefaultTask.html#setOnlyIf-java.lang.String-org.gradle.api.specs.Spec-">setOnlyIf</a>, <a href="../../api/DefaultTask.html#setOnlyIf-org.gradle.api.specs.Spec-">setOnlyIf</a>, <a href="../../api/DefaultTask.html#setProperty-java.lang.String-java.lang.Object-">setProperty</a>, <a href="../../api/DefaultTask.html#setShouldRunAfter-java.lang.Iterable-">setShouldRunAfter</a>, <a href="../../api/DefaultTask.html#shouldRunAfter-java.lang.Object...-">shouldRunAfter</a>, <a href="../../api/DefaultTask.html#usesService-org.gradle.api.provider.Provider-">usesService</a></code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.org.gradle.api.internal.AbstractTask">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;org.gradle.api.internal.AbstractTask</h3>
<code>acceptServiceReferences, appendParallelSafeAction, doNotTrackState, getAsDynamicObject, getConvention, getIdentityPath, getImpliesSubProjects, getLifecycleDependencies, getOnlyIf, getReasonNotToTrackState, getReasonTaskIsIncompatibleWithConfigurationCache, getRequiredServices, getServices, getSharedResources, getStandardOutputCapture, getTaskActions, getTaskIdentity, getTemporaryDirFactory, hasTaskActions, injectIntoNewInstance, isCompatibleWithConfigurationCache, isEnabled, isHasCustomActions, notCompatibleWithConfigurationCache, prependParallelSafeAction, setImpliesSubProjects</code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.Object</h3>
<code>clone, equals, finalize, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.org.gradle.api.Task">
<!--   -->
</a>
<h3>Methods inherited from interface&nbsp;org.gradle.api.<a href="../../api/Task.html" title="interface in org.gradle.api">Task</a></h3>
<code><a href="../../api/Task.html#doNotTrackState-java.lang.String-">doNotTrackState</a>, <a href="../../api/Task.html#getConvention--">getConvention</a>, <a href="../../api/Task.html#notCompatibleWithConfigurationCache-java.lang.String-">notCompatibleWithConfigurationCache</a></code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.detail">
<!--   -->
</a>
<h3>Constructor Detail</h3>
<a name="PrefixHeaderFileGenerateTask-org.gradle.workers.WorkerExecutor-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>PrefixHeaderFileGenerateTask</h4>
<pre>@Inject
public&nbsp;PrefixHeaderFileGenerateTask&#8203;(<a href="../../workers/WorkerExecutor.html" title="interface in org.gradle.workers">WorkerExecutor</a>&nbsp;workerExecutor)</pre>
<div class="block">Injects a <a href="../../workers/WorkerExecutor.html" title="interface in org.gradle.workers"><code>WorkerExecutor</code></a> instance.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>4.2</dd>
</dl>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="getHeader--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getHeader</h4>
<pre class="methodSignature"><a href="../../api/tasks/Input.html" title="annotation in org.gradle.api.tasks">@Input</a>
public&nbsp;java.lang.String&nbsp;getHeader()</pre>
</li>
</ul>
<a name="setHeader-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setHeader</h4>
<pre class="methodSignature">public&nbsp;void&nbsp;setHeader&#8203;(java.lang.String&nbsp;header)</pre>
</li>
</ul>
<a name="getPrefixHeaderFile--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getPrefixHeaderFile</h4>
<pre class="methodSignature"><a href="../../api/tasks/OutputFile.html" title="annotation in org.gradle.api.tasks">@OutputFile</a>
public&nbsp;java.io.File&nbsp;getPrefixHeaderFile()</pre>
</li>
</ul>
<a name="setPrefixHeaderFile-java.io.File-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>setPrefixHeaderFile</h4>
<pre class="methodSignature">public&nbsp;void&nbsp;setPrefixHeaderFile&#8203;(java.io.File&nbsp;prefixHeaderFile)</pre>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../allclasses.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li><a href="#nested.class.summary">Nested</a>&nbsp;|&nbsp;</li>
<li><a href="#field.summary">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
