<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc -->
<title>Configuration (Gradle API 8.5)</title>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<link rel="stylesheet" type="text/css" href="../../../../javadoc.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../jquery/jquery-ui.min.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../jquery-ui.overrides.css" title="Style">
<script type="text/javascript" src="../../../../script.js"></script>
<script type="text/javascript" src="../../../../jquery/jszip/dist/jszip.min.js"></script>
<script type="text/javascript" src="../../../../jquery/jszip-utils/dist/jszip-utils.min.js"></script>
<!--[if IE]>
<script type="text/javascript" src="../../../../jquery/jszip-utils/dist/jszip-utils-ie.min.js"></script>
<![endif]-->
<script type="text/javascript" src="../../../../jquery/jquery-3.6.1.min.js"></script>
<script type="text/javascript" src="../../../../jquery/jquery-ui.min.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="Configuration (Gradle API 8.5)";
        }
    }
    catch(err) {
    }
//-->
var data = {"i0":6,"i1":6,"i2":6,"i3":6,"i4":6,"i5":6,"i6":6,"i7":6,"i8":6,"i9":6,"i10":6,"i11":6,"i12":6,"i13":6,"i14":6,"i15":6,"i16":38,"i17":6,"i18":6,"i19":6,"i20":6,"i21":6,"i22":6,"i23":6,"i24":6,"i25":6,"i26":6,"i27":6,"i28":6,"i29":6,"i30":6,"i31":6,"i32":6,"i33":6,"i34":38,"i35":6,"i36":6,"i37":6,"i38":6,"i39":6,"i40":6,"i41":6,"i42":6,"i43":6,"i44":6,"i45":6,"i46":6,"i47":6,"i48":6,"i49":6,"i50":6,"i51":6,"i52":6};
var tabs = {65535:["t0","All Methods"],2:["t2","Instance Methods"],4:["t3","Abstract Methods"],32:["t6","Deprecated Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
var pathtoroot = "../../../../";
var useModuleDirectories = false;
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<div class="fixedNav">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../allclasses.html">All&nbsp;Classes</a></li>
</ul>
<ul class="navListSearch">
<li><label for="search">SEARCH:</label>
<input type="text" id="search" value="search" disabled="disabled">
<input type="reset" id="reset" value="reset" disabled="disabled">
</li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li><a href="#nested.class.summary">Nested</a>&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
</div>
<div class="navPadding">&nbsp;</div>
<script type="text/javascript"><!--
$('.navPadding').css('padding-top', $('.fixedNav').css("height"));
//-->
</script>
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle"><span class="packageLabelInType">Package</span>&nbsp;<a href="package-summary.html">org.gradle.api.artifacts</a></div>
<h2 title="Interface Configuration" class="title">Interface Configuration</h2>
</div>
<div class="contentContainer">
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt>All Superinterfaces:</dt>
<dd><code><a href="../tasks/AntBuilderAware.html" title="interface in org.gradle.api.tasks">AntBuilderAware</a></code>, <code><a href="../Buildable.html" title="interface in org.gradle.api">Buildable</a></code>, <code><a href="../file/FileCollection.html" title="interface in org.gradle.api.file">FileCollection</a></code>, <code><a href="../attributes/HasAttributes.html" title="interface in org.gradle.api.attributes">HasAttributes</a></code>, <code><a href="../attributes/HasConfigurableAttributes.html" title="interface in org.gradle.api.attributes">HasConfigurableAttributes</a>&lt;<a href="Configuration.html" title="interface in org.gradle.api.artifacts">Configuration</a>&gt;</code>, <code>java.lang.Iterable&lt;java.io.File&gt;</code>, <code><a href="../Named.html" title="interface in org.gradle.api">Named</a></code></dd>
</dl>
<dl>
<dt>All Known Subinterfaces:</dt>
<dd><code><a href="ConsumableConfiguration.html" title="interface in org.gradle.api.artifacts">ConsumableConfiguration</a></code>, <code><a href="DependencyScopeConfiguration.html" title="interface in org.gradle.api.artifacts">DependencyScopeConfiguration</a></code>, <code><a href="ResolvableConfiguration.html" title="interface in org.gradle.api.artifacts">ResolvableConfiguration</a></code></dd>
</dl>
<hr>
<pre>public interface <span class="typeNameLabel">Configuration</span>
extends <a href="../file/FileCollection.html" title="interface in org.gradle.api.file">FileCollection</a>, <a href="../attributes/HasConfigurableAttributes.html" title="interface in org.gradle.api.attributes">HasConfigurableAttributes</a>&lt;<a href="Configuration.html" title="interface in org.gradle.api.artifacts">Configuration</a>&gt;, <a href="../Named.html" title="interface in org.gradle.api">Named</a></pre>
<div class="block">A <code>Configuration</code> represents a group of artifacts and their dependencies.
 Find more information about declaring dependencies to a configuration
 or about managing configurations in docs for <a href="ConfigurationContainer.html" title="interface in org.gradle.api.artifacts"><code>ConfigurationContainer</code></a>
 <p>
 Configuration is an instance of a <a href="../file/FileCollection.html" title="interface in org.gradle.api.file"><code>FileCollection</code></a>
 that contains all dependencies (see also <a href="#getAllDependencies--"><code>getAllDependencies()</code></a>) but not artifacts.
 If you want to refer to the artifacts declared in this configuration
 please use <a href="#getArtifacts--"><code>getArtifacts()</code></a> or <a href="#getAllArtifacts--"><code>getAllArtifacts()</code></a>.
 Read more about declaring artifacts in the configuration in docs for <a href="dsl/ArtifactHandler.html" title="interface in org.gradle.api.artifacts.dsl"><code>ArtifactHandler</code></a>

 Please see the <a href="https://docs.gradle.org/current/userguide/declaring_dependencies.html" target="_top">Declaring Dependencies</a> User Manual chapter for more information.</div>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ======== NESTED CLASS SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="nested.class.summary">
<!--   -->
</a>
<h3>Nested Class Summary</h3>
<table class="memberSummary" summary="Nested Class Summary table, listing nested classes, and an explanation">
<caption><span>Nested Classes</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colSecond" scope="col">Interface</th>
<th class="colLast" scope="col">Description</th>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static class&nbsp;</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="Configuration.Namer.html" title="class in org.gradle.api.artifacts">Configuration.Namer</a></span></code></th>
<td class="colLast">
<div class="block">A <a href="../Namer.html" title="interface in org.gradle.api"><code>Namer</code></a> namer for configurations that returns <a href="../Named.html#getName--"><code>Named.getName()</code></a>.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static class&nbsp;</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="Configuration.State.html" title="enum in org.gradle.api.artifacts">Configuration.State</a></span></code></th>
<td class="colLast">
<div class="block">The states a configuration can be into.</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="nested.classes.inherited.from.class.org.gradle.api.file.FileCollection">
<!--   -->
</a>
<h3>Nested classes/interfaces inherited from interface&nbsp;org.gradle.api.file.<a href="../file/FileCollection.html" title="interface in org.gradle.api.file">FileCollection</a></h3>
<code><a href="../file/FileCollection.AntType.html" title="enum in org.gradle.api.file">FileCollection.AntType</a></code></li>
</ul>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t3" class="tableTab"><span><a href="javascript:show(4);">Abstract Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t6" class="tableTab"><span><a href="javascript:show(32);">Deprecated Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colSecond" scope="col">Method</th>
<th class="colLast" scope="col">Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code><a href="Configuration.html" title="interface in org.gradle.api.artifacts">Configuration</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#copy--">copy</a></span>()</code></th>
<td class="colLast">
<div class="block">Creates a copy of this configuration that only contains the dependencies directly in this configuration
 (without contributions from superconfigurations).</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code><a href="Configuration.html" title="interface in org.gradle.api.artifacts">Configuration</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#copy-groovy.lang.Closure-">copy</a></span>&#8203;(<a href="https://docs.groovy-lang.org/docs/groovy-3.0.17/html/gapi/groovy/lang/Closure.html?is-external=true" title="class or interface in groovy.lang" class="externalLink">Closure</a>&nbsp;dependencySpec)</code></th>
<td class="colLast">
<div class="block">Takes a closure which gets coerced into a <a href="../specs/Spec.html" title="interface in org.gradle.api.specs"><code>Spec</code></a>.</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code><a href="Configuration.html" title="interface in org.gradle.api.artifacts">Configuration</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#copy-org.gradle.api.specs.Spec-">copy</a></span>&#8203;(<a href="../specs/Spec.html" title="interface in org.gradle.api.specs">Spec</a>&lt;? super <a href="Dependency.html" title="interface in org.gradle.api.artifacts">Dependency</a>&gt;&nbsp;dependencySpec)</code></th>
<td class="colLast">
<div class="block">Creates a copy of this configuration ignoring superconfigurations (see <a href="#copy--"><code>copy()</code></a> but filtering
 the dependencies using the specified dependency spec.</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code><a href="Configuration.html" title="interface in org.gradle.api.artifacts">Configuration</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#copyRecursive--">copyRecursive</a></span>()</code></th>
<td class="colLast">
<div class="block">Creates a copy of this configuration that contains the dependencies directly in this configuration
 and those derived from superconfigurations.</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code><a href="Configuration.html" title="interface in org.gradle.api.artifacts">Configuration</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#copyRecursive-groovy.lang.Closure-">copyRecursive</a></span>&#8203;(<a href="https://docs.groovy-lang.org/docs/groovy-3.0.17/html/gapi/groovy/lang/Closure.html?is-external=true" title="class or interface in groovy.lang" class="externalLink">Closure</a>&nbsp;dependencySpec)</code></th>
<td class="colLast">
<div class="block">Takes a closure which gets coerced into a <a href="../specs/Spec.html" title="interface in org.gradle.api.specs"><code>Spec</code></a>.</div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code><a href="Configuration.html" title="interface in org.gradle.api.artifacts">Configuration</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#copyRecursive-org.gradle.api.specs.Spec-">copyRecursive</a></span>&#8203;(<a href="../specs/Spec.html" title="interface in org.gradle.api.specs">Spec</a>&lt;? super <a href="Dependency.html" title="interface in org.gradle.api.artifacts">Dependency</a>&gt;&nbsp;dependencySpec)</code></th>
<td class="colLast">
<div class="block">Creates a copy of this configuration with dependencies from superconfigurations (see <a href="#copyRecursive--"><code>copyRecursive()</code></a>)
 but filtering the dependencies using the dependencySpec.</div>
</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code><a href="Configuration.html" title="interface in org.gradle.api.artifacts">Configuration</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#defaultDependencies-org.gradle.api.Action-">defaultDependencies</a></span>&#8203;(<a href="../Action.html" title="interface in org.gradle.api">Action</a>&lt;? super <a href="DependencySet.html" title="interface in org.gradle.api.artifacts">DependencySet</a>&gt;&nbsp;action)</code></th>
<td class="colLast">
<div class="block">Execute the given action if the configuration has no defined dependencies when it first participates in
 dependency resolution.</div>
</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code><a href="Configuration.html" title="interface in org.gradle.api.artifacts">Configuration</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#disableConsistentResolution--">disableConsistentResolution</a></span>()</code></th>
<td class="colLast">
<div class="block">Disables consistent resolution for this configuration.</div>
</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code><a href="Configuration.html" title="interface in org.gradle.api.artifacts">Configuration</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#exclude-java.util.Map-">exclude</a></span>&#8203;(java.util.Map&lt;java.lang.String,&#8203;java.lang.String&gt;&nbsp;excludeProperties)</code></th>
<td class="colLast">
<div class="block">Adds an exclude rule to exclude transitive dependencies for all dependencies of this configuration.</div>
</td>
</tr>
<tr id="i9" class="rowColor">
<td class="colFirst"><code><a href="Configuration.html" title="interface in org.gradle.api.artifacts">Configuration</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#extendsFrom-org.gradle.api.artifacts.Configuration...-">extendsFrom</a></span>&#8203;(<a href="Configuration.html" title="interface in org.gradle.api.artifacts">Configuration</a>...&nbsp;superConfigs)</code></th>
<td class="colLast">
<div class="block">Adds the given configurations to the set of configuration which this configuration extends from.</div>
</td>
</tr>
<tr id="i10" class="altColor">
<td class="colFirst"><code><a href="../file/FileCollection.html" title="interface in org.gradle.api.file">FileCollection</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#fileCollection-groovy.lang.Closure-">fileCollection</a></span>&#8203;(<a href="https://docs.groovy-lang.org/docs/groovy-3.0.17/html/gapi/groovy/lang/Closure.html?is-external=true" title="class or interface in groovy.lang" class="externalLink">Closure</a>&nbsp;dependencySpecClosure)</code></th>
<td class="colLast">
<div class="block">Takes a closure which gets coerced into a <a href="../specs/Spec.html" title="interface in org.gradle.api.specs"><code>Spec</code></a>.</div>
</td>
</tr>
<tr id="i11" class="rowColor">
<td class="colFirst"><code><a href="../file/FileCollection.html" title="interface in org.gradle.api.file">FileCollection</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#fileCollection-org.gradle.api.artifacts.Dependency...-">fileCollection</a></span>&#8203;(<a href="Dependency.html" title="interface in org.gradle.api.artifacts">Dependency</a>...&nbsp;dependencies)</code></th>
<td class="colLast">
<div class="block">Resolves this configuration lazily.</div>
</td>
</tr>
<tr id="i12" class="altColor">
<td class="colFirst"><code><a href="../file/FileCollection.html" title="interface in org.gradle.api.file">FileCollection</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#fileCollection-org.gradle.api.specs.Spec-">fileCollection</a></span>&#8203;(<a href="../specs/Spec.html" title="interface in org.gradle.api.specs">Spec</a>&lt;? super <a href="Dependency.html" title="interface in org.gradle.api.artifacts">Dependency</a>&gt;&nbsp;dependencySpec)</code></th>
<td class="colLast">
<div class="block">Resolves this configuration lazily.</div>
</td>
</tr>
<tr id="i13" class="rowColor">
<td class="colFirst"><code>java.util.Set&lt;java.io.File&gt;</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#files-groovy.lang.Closure-">files</a></span>&#8203;(<a href="https://docs.groovy-lang.org/docs/groovy-3.0.17/html/gapi/groovy/lang/Closure.html?is-external=true" title="class or interface in groovy.lang" class="externalLink">Closure</a>&nbsp;dependencySpecClosure)</code></th>
<td class="colLast">
<div class="block">Takes a closure which gets coerced into a <a href="../specs/Spec.html" title="interface in org.gradle.api.specs"><code>Spec</code></a>.</div>
</td>
</tr>
<tr id="i14" class="altColor">
<td class="colFirst"><code>java.util.Set&lt;java.io.File&gt;</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#files-org.gradle.api.artifacts.Dependency...-">files</a></span>&#8203;(<a href="Dependency.html" title="interface in org.gradle.api.artifacts">Dependency</a>...&nbsp;dependencies)</code></th>
<td class="colLast">
<div class="block">Resolves this configuration.</div>
</td>
</tr>
<tr id="i15" class="rowColor">
<td class="colFirst"><code>java.util.Set&lt;java.io.File&gt;</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#files-org.gradle.api.specs.Spec-">files</a></span>&#8203;(<a href="../specs/Spec.html" title="interface in org.gradle.api.specs">Spec</a>&lt;? super <a href="Dependency.html" title="interface in org.gradle.api.artifacts">Dependency</a>&gt;&nbsp;dependencySpec)</code></th>
<td class="colLast">
<div class="block">Resolves this configuration.</div>
</td>
</tr>
<tr id="i16" class="altColor">
<td class="colFirst"><code>java.util.Set&lt;<a href="Configuration.html" title="interface in org.gradle.api.artifacts">Configuration</a>&gt;</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getAll--">getAll</a></span>()</code></th>
<td class="colLast">
<div class="block"><span class="deprecatedLabel">Deprecated.</span></div>
</td>
</tr>
<tr id="i17" class="rowColor">
<td class="colFirst"><code><a href="PublishArtifactSet.html" title="interface in org.gradle.api.artifacts">PublishArtifactSet</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getAllArtifacts--">getAllArtifacts</a></span>()</code></th>
<td class="colLast">
<div class="block">Returns the artifacts of this configuration including the artifacts of extended configurations.</div>
</td>
</tr>
<tr id="i18" class="altColor">
<td class="colFirst"><code><a href="DependencySet.html" title="interface in org.gradle.api.artifacts">DependencySet</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getAllDependencies--">getAllDependencies</a></span>()</code></th>
<td class="colLast">
<div class="block">Gets the complete set of declared dependencies including those contributed by
 superconfigurations.</div>
</td>
</tr>
<tr id="i19" class="rowColor">
<td class="colFirst"><code><a href="DependencyConstraintSet.html" title="interface in org.gradle.api.artifacts">DependencyConstraintSet</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getAllDependencyConstraints--">getAllDependencyConstraints</a></span>()</code></th>
<td class="colLast">
<div class="block">Gets the complete set of dependency constraints including those contributed by
 superconfigurations.</div>
</td>
</tr>
<tr id="i20" class="altColor">
<td class="colFirst"><code><a href="PublishArtifactSet.html" title="interface in org.gradle.api.artifacts">PublishArtifactSet</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getArtifacts--">getArtifacts</a></span>()</code></th>
<td class="colLast">
<div class="block">Returns the artifacts of this configuration excluding the artifacts of extended configurations.</div>
</td>
</tr>
<tr id="i21" class="rowColor">
<td class="colFirst"><code><a href="../tasks/TaskDependency.html" title="interface in org.gradle.api.tasks">TaskDependency</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getBuildDependencies--">getBuildDependencies</a></span>()</code></th>
<td class="colLast">
<div class="block">Returns a <code>TaskDependency</code> object containing all required dependencies to build the local dependencies
 (e.g.</div>
</td>
</tr>
<tr id="i22" class="altColor">
<td class="colFirst"><code><a href="DependencySet.html" title="interface in org.gradle.api.artifacts">DependencySet</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getDependencies--">getDependencies</a></span>()</code></th>
<td class="colLast">
<div class="block">Gets the set of declared dependencies directly contained in this configuration
 (ignoring superconfigurations).</div>
</td>
</tr>
<tr id="i23" class="rowColor">
<td class="colFirst"><code><a href="DependencyConstraintSet.html" title="interface in org.gradle.api.artifacts">DependencyConstraintSet</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getDependencyConstraints--">getDependencyConstraints</a></span>()</code></th>
<td class="colLast">
<div class="block">Gets the set of dependency constraints directly contained in this configuration
 (ignoring superconfigurations).</div>
</td>
</tr>
<tr id="i24" class="altColor">
<td class="colFirst"><code>java.lang.String</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getDescription--">getDescription</a></span>()</code></th>
<td class="colLast">
<div class="block">Returns the description for this configuration.</div>
</td>
</tr>
<tr id="i25" class="rowColor">
<td class="colFirst"><code>java.util.Set&lt;<a href="ExcludeRule.html" title="interface in org.gradle.api.artifacts">ExcludeRule</a>&gt;</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getExcludeRules--">getExcludeRules</a></span>()</code></th>
<td class="colLast">
<div class="block">Returns the exclude rules applied for resolving any dependency of this configuration.</div>
</td>
</tr>
<tr id="i26" class="altColor">
<td class="colFirst"><code>java.util.Set&lt;<a href="Configuration.html" title="interface in org.gradle.api.artifacts">Configuration</a>&gt;</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getExtendsFrom--">getExtendsFrom</a></span>()</code></th>
<td class="colLast">
<div class="block">Returns the names of the configurations which this configuration extends from.</div>
</td>
</tr>
<tr id="i27" class="rowColor">
<td class="colFirst"><code>java.util.Set&lt;<a href="Configuration.html" title="interface in org.gradle.api.artifacts">Configuration</a>&gt;</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getHierarchy--">getHierarchy</a></span>()</code></th>
<td class="colLast">
<div class="block">Gets an ordered set including this configuration and all superconfigurations
 recursively.</div>
</td>
</tr>
<tr id="i28" class="altColor">
<td class="colFirst"><code><a href="ResolvableDependencies.html" title="interface in org.gradle.api.artifacts">ResolvableDependencies</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getIncoming--">getIncoming</a></span>()</code></th>
<td class="colLast">
<div class="block">Returns the incoming dependencies of this configuration.</div>
</td>
</tr>
<tr id="i29" class="rowColor">
<td class="colFirst"><code><a href="ConfigurationPublications.html" title="interface in org.gradle.api.artifacts">ConfigurationPublications</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getOutgoing--">getOutgoing</a></span>()</code></th>
<td class="colLast">
<div class="block">Returns the outgoing <a href="ConfigurationPublications.html" title="interface in org.gradle.api.artifacts"><code>ConfigurationPublications</code></a> instance that advertises and allows configuring the artifacts and variants published by this configuration.</div>
</td>
</tr>
<tr id="i30" class="altColor">
<td class="colFirst"><code><a href="ResolutionStrategy.html" title="interface in org.gradle.api.artifacts">ResolutionStrategy</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getResolutionStrategy--">getResolutionStrategy</a></span>()</code></th>
<td class="colLast">
<div class="block">Returns the resolution strategy used by this configuration.</div>
</td>
</tr>
<tr id="i31" class="rowColor">
<td class="colFirst"><code><a href="ResolvedConfiguration.html" title="interface in org.gradle.api.artifacts">ResolvedConfiguration</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getResolvedConfiguration--">getResolvedConfiguration</a></span>()</code></th>
<td class="colLast">
<div class="block">Resolves this configuration.</div>
</td>
</tr>
<tr id="i32" class="altColor">
<td class="colFirst"><code><a href="Configuration.State.html" title="enum in org.gradle.api.artifacts">Configuration.State</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getState--">getState</a></span>()</code></th>
<td class="colLast">
<div class="block">Returns the state of the configuration.</div>
</td>
</tr>
<tr id="i33" class="rowColor">
<td class="colFirst"><code><a href="../tasks/TaskDependency.html" title="interface in org.gradle.api.tasks">TaskDependency</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getTaskDependencyFromProjectDependency-boolean-java.lang.String-">getTaskDependencyFromProjectDependency</a></span>&#8203;(boolean&nbsp;useDependedOn,
                                      java.lang.String&nbsp;taskName)</code></th>
<td class="colLast">
<div class="block">Returns a TaskDependency object containing dependencies on all tasks with the specified name from project
 dependencies related to this configuration or one of its super configurations.</div>
</td>
</tr>
<tr id="i34" class="altColor">
<td class="colFirst"><code>java.lang.String</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getUploadTaskName--">getUploadTaskName</a></span>()</code></th>
<td class="colLast">
<div class="block"><span class="deprecatedLabel">Deprecated.</span></div>
</td>
</tr>
<tr id="i35" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#isCanBeConsumed--">isCanBeConsumed</a></span>()</code></th>
<td class="colLast">
<div class="block">Returns true if this configuration can be consumed from another project, or published.</div>
</td>
</tr>
<tr id="i36" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#isCanBeDeclared--">isCanBeDeclared</a></span>()</code></th>
<td class="colLast">
<div class="block">Returns true if it is allowed to declare dependencies upon this configuration.</div>
</td>
</tr>
<tr id="i37" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#isCanBeResolved--">isCanBeResolved</a></span>()</code></th>
<td class="colLast">
<div class="block">Returns true if it is allowed to query or resolve this configuration.</div>
</td>
</tr>
<tr id="i38" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#isTransitive--">isTransitive</a></span>()</code></th>
<td class="colLast">
<div class="block">Returns the transitivity of this configuration.</div>
</td>
</tr>
<tr id="i39" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#isVisible--">isVisible</a></span>()</code></th>
<td class="colLast">
<div class="block">Returns true if this is a visible configuration.</div>
</td>
</tr>
<tr id="i40" class="altColor">
<td class="colFirst"><code>void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#outgoing-org.gradle.api.Action-">outgoing</a></span>&#8203;(<a href="../Action.html" title="interface in org.gradle.api">Action</a>&lt;? super <a href="ConfigurationPublications.html" title="interface in org.gradle.api.artifacts">ConfigurationPublications</a>&gt;&nbsp;action)</code></th>
<td class="colLast">
<div class="block">Configures the outgoing <a href="ConfigurationPublications.html" title="interface in org.gradle.api.artifacts"><code>ConfigurationPublications</code></a> instance that advertises and allows configuring the artifacts and variants published by this configuration.</div>
</td>
</tr>
<tr id="i41" class="rowColor">
<td class="colFirst"><code><a href="Configuration.html" title="interface in org.gradle.api.artifacts">Configuration</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#resolutionStrategy-groovy.lang.Closure-">resolutionStrategy</a></span>&#8203;(<a href="https://docs.groovy-lang.org/docs/groovy-3.0.17/html/gapi/groovy/lang/Closure.html?is-external=true" title="class or interface in groovy.lang" class="externalLink">Closure</a>&nbsp;closure)</code></th>
<td class="colLast">
<div class="block">The resolution strategy provides extra details on how to resolve this configuration.</div>
</td>
</tr>
<tr id="i42" class="altColor">
<td class="colFirst"><code><a href="Configuration.html" title="interface in org.gradle.api.artifacts">Configuration</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#resolutionStrategy-org.gradle.api.Action-">resolutionStrategy</a></span>&#8203;(<a href="../Action.html" title="interface in org.gradle.api">Action</a>&lt;? super <a href="ResolutionStrategy.html" title="interface in org.gradle.api.artifacts">ResolutionStrategy</a>&gt;&nbsp;action)</code></th>
<td class="colLast">
<div class="block">The resolution strategy provides extra details on how to resolve this configuration.</div>
</td>
</tr>
<tr id="i43" class="rowColor">
<td class="colFirst"><code>java.util.Set&lt;java.io.File&gt;</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#resolve--">resolve</a></span>()</code></th>
<td class="colLast">
<div class="block">Resolves this configuration.</div>
</td>
</tr>
<tr id="i44" class="altColor">
<td class="colFirst"><code>void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#setCanBeConsumed-boolean-">setCanBeConsumed</a></span>&#8203;(boolean&nbsp;allowed)</code></th>
<td class="colLast">
<div class="block">Configures if a configuration can be consumed.</div>
</td>
</tr>
<tr id="i45" class="rowColor">
<td class="colFirst"><code>void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#setCanBeDeclared-boolean-">setCanBeDeclared</a></span>&#8203;(boolean&nbsp;allowed)</code></th>
<td class="colLast">
<div class="block">Configures if a configuration can have dependencies declared upon it.</div>
</td>
</tr>
<tr id="i46" class="altColor">
<td class="colFirst"><code>void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#setCanBeResolved-boolean-">setCanBeResolved</a></span>&#8203;(boolean&nbsp;allowed)</code></th>
<td class="colLast">
<div class="block">Configures if a configuration can be resolved.</div>
</td>
</tr>
<tr id="i47" class="rowColor">
<td class="colFirst"><code><a href="Configuration.html" title="interface in org.gradle.api.artifacts">Configuration</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#setDescription-java.lang.String-">setDescription</a></span>&#8203;(java.lang.String&nbsp;description)</code></th>
<td class="colLast">
<div class="block">Sets the description for this configuration.</div>
</td>
</tr>
<tr id="i48" class="altColor">
<td class="colFirst"><code><a href="Configuration.html" title="interface in org.gradle.api.artifacts">Configuration</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#setExtendsFrom-java.lang.Iterable-">setExtendsFrom</a></span>&#8203;(java.lang.Iterable&lt;<a href="Configuration.html" title="interface in org.gradle.api.artifacts">Configuration</a>&gt;&nbsp;superConfigs)</code></th>
<td class="colLast">
<div class="block">Sets the configurations which this configuration extends from.</div>
</td>
</tr>
<tr id="i49" class="rowColor">
<td class="colFirst"><code><a href="Configuration.html" title="interface in org.gradle.api.artifacts">Configuration</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#setTransitive-boolean-">setTransitive</a></span>&#8203;(boolean&nbsp;t)</code></th>
<td class="colLast">
<div class="block">Sets the transitivity of this configuration.</div>
</td>
</tr>
<tr id="i50" class="altColor">
<td class="colFirst"><code><a href="Configuration.html" title="interface in org.gradle.api.artifacts">Configuration</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#setVisible-boolean-">setVisible</a></span>&#8203;(boolean&nbsp;visible)</code></th>
<td class="colLast">
<div class="block">Sets the visibility of this configuration.</div>
</td>
</tr>
<tr id="i51" class="rowColor">
<td class="colFirst"><code><a href="Configuration.html" title="interface in org.gradle.api.artifacts">Configuration</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#shouldResolveConsistentlyWith-org.gradle.api.artifacts.Configuration-">shouldResolveConsistentlyWith</a></span>&#8203;(<a href="Configuration.html" title="interface in org.gradle.api.artifacts">Configuration</a>&nbsp;versionsSource)</code></th>
<td class="colLast">
<div class="block">Tells that this configuration, when resolved, should resolve versions consistently
 from the resolution result of another resolvable configuration.</div>
</td>
</tr>
<tr id="i52" class="altColor">
<td class="colFirst"><code><a href="Configuration.html" title="interface in org.gradle.api.artifacts">Configuration</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#withDependencies-org.gradle.api.Action-">withDependencies</a></span>&#8203;(<a href="../Action.html" title="interface in org.gradle.api">Action</a>&lt;? super <a href="DependencySet.html" title="interface in org.gradle.api.artifacts">DependencySet</a>&gt;&nbsp;action)</code></th>
<td class="colLast">
<div class="block">Execute the given action before the configuration first participates in
 dependency resolution.</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.org.gradle.api.file.FileCollection">
<!--   -->
</a>
<h3>Methods inherited from interface&nbsp;org.gradle.api.file.<a href="../file/FileCollection.html" title="interface in org.gradle.api.file">FileCollection</a></h3>
<code><a href="../file/FileCollection.html#addToAntBuilder-java.lang.Object-java.lang.String-">addToAntBuilder</a>, <a href="../file/FileCollection.html#addToAntBuilder-java.lang.Object-java.lang.String-org.gradle.api.file.FileCollection.AntType-">addToAntBuilder</a>, <a href="../file/FileCollection.html#contains-java.io.File-">contains</a>, <a href="../file/FileCollection.html#filter-groovy.lang.Closure-">filter</a>, <a href="../file/FileCollection.html#filter-org.gradle.api.specs.Spec-">filter</a>, <a href="../file/FileCollection.html#getAsFileTree--">getAsFileTree</a>, <a href="../file/FileCollection.html#getAsPath--">getAsPath</a>, <a href="../file/FileCollection.html#getElements--">getElements</a>, <a href="../file/FileCollection.html#getFiles--">getFiles</a>, <a href="../file/FileCollection.html#getSingleFile--">getSingleFile</a>, <a href="../file/FileCollection.html#isEmpty--">isEmpty</a>, <a href="../file/FileCollection.html#minus-org.gradle.api.file.FileCollection-">minus</a>, <a href="../file/FileCollection.html#plus-org.gradle.api.file.FileCollection-">plus</a></code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.org.gradle.api.attributes.HasAttributes">
<!--   -->
</a>
<h3>Methods inherited from interface&nbsp;org.gradle.api.attributes.<a href="../attributes/HasAttributes.html" title="interface in org.gradle.api.attributes">HasAttributes</a></h3>
<code><a href="../attributes/HasAttributes.html#getAttributes--">getAttributes</a></code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.org.gradle.api.attributes.HasConfigurableAttributes">
<!--   -->
</a>
<h3>Methods inherited from interface&nbsp;org.gradle.api.attributes.<a href="../attributes/HasConfigurableAttributes.html" title="interface in org.gradle.api.attributes">HasConfigurableAttributes</a></h3>
<code><a href="../attributes/HasConfigurableAttributes.html#attributes-org.gradle.api.Action-">attributes</a></code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Iterable">
<!--   -->
</a>
<h3>Methods inherited from interface&nbsp;java.lang.Iterable</h3>
<code>forEach, iterator, spliterator</code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.org.gradle.api.Named">
<!--   -->
</a>
<h3>Methods inherited from interface&nbsp;org.gradle.api.<a href="../Named.html" title="interface in org.gradle.api">Named</a></h3>
<code><a href="../Named.html#getName--">getName</a></code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="getResolutionStrategy--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getResolutionStrategy</h4>
<pre class="methodSignature"><a href="ResolutionStrategy.html" title="interface in org.gradle.api.artifacts">ResolutionStrategy</a>&nbsp;getResolutionStrategy()</pre>
<div class="block">Returns the resolution strategy used by this configuration.
 The resolution strategy provides extra details on how to resolve this configuration.
 See docs for <a href="ResolutionStrategy.html" title="interface in org.gradle.api.artifacts"><code>ResolutionStrategy</code></a> for more info and examples.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>resolution strategy</dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>1.0-milestone-6</dd>
<dt><span class="simpleTagLabel">Implementation Requirements:</span></dt>
<dd>Usage: This method should only be called on resolvable configurations, but will not warn if used otherwise.</dd>
</dl>
</li>
</ul>
<a name="resolutionStrategy-groovy.lang.Closure-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>resolutionStrategy</h4>
<pre class="methodSignature"><a href="Configuration.html" title="interface in org.gradle.api.artifacts">Configuration</a>&nbsp;resolutionStrategy&#8203;(<a href="https://docs.groovy-lang.org/docs/groovy-3.0.17/html/gapi/groovy/lang/DelegatesTo.html?is-external=true" title="class or interface in groovy.lang" class="externalLink">@DelegatesTo</a>(<a href="https://docs.groovy-lang.org/docs/groovy-3.0.17/html/gapi/groovy/lang/DelegatesTo.html?is-external=true#value--" title="class or interface in groovy.lang" class="externalLink">value</a>=<a href="ResolutionStrategy.html" title="interface in org.gradle.api.artifacts">ResolutionStrategy.class</a>,<a href="https://docs.groovy-lang.org/docs/groovy-3.0.17/html/gapi/groovy/lang/DelegatesTo.html?is-external=true#strategy--" title="class or interface in groovy.lang" class="externalLink">strategy</a>=1)
                                 <a href="https://docs.groovy-lang.org/docs/groovy-3.0.17/html/gapi/groovy/lang/Closure.html?is-external=true" title="class or interface in groovy.lang" class="externalLink">Closure</a>&nbsp;closure)</pre>
<div class="block">The resolution strategy provides extra details on how to resolve this configuration.
 See docs for <a href="ResolutionStrategy.html" title="interface in org.gradle.api.artifacts"><code>ResolutionStrategy</code></a> for more info and examples.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>closure</code> - closure applied to the <a href="ResolutionStrategy.html" title="interface in org.gradle.api.artifacts"><code>ResolutionStrategy</code></a></dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>this configuration instance</dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>1.0-milestone-6</dd>
<dt><span class="simpleTagLabel">Implementation Requirements:</span></dt>
<dd>Usage: This method should only be called on resolvable configurations, but will not warn if used otherwise.</dd>
</dl>
</li>
</ul>
<a name="resolutionStrategy-org.gradle.api.Action-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>resolutionStrategy</h4>
<pre class="methodSignature"><a href="Configuration.html" title="interface in org.gradle.api.artifacts">Configuration</a>&nbsp;resolutionStrategy&#8203;(<a href="../Action.html" title="interface in org.gradle.api">Action</a>&lt;? super <a href="ResolutionStrategy.html" title="interface in org.gradle.api.artifacts">ResolutionStrategy</a>&gt;&nbsp;action)</pre>
<div class="block">The resolution strategy provides extra details on how to resolve this configuration.
 See docs for <a href="ResolutionStrategy.html" title="interface in org.gradle.api.artifacts"><code>ResolutionStrategy</code></a> for more info and examples.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>action</code> - action applied to the <a href="ResolutionStrategy.html" title="interface in org.gradle.api.artifacts"><code>ResolutionStrategy</code></a></dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>this configuration instance</dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>3.1</dd>
<dt><span class="simpleTagLabel">Implementation Requirements:</span></dt>
<dd>Usage: This method should only be called on resolvable configurations, but will not warn if used otherwise.</dd>
</dl>
</li>
</ul>
<a name="getState--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getState</h4>
<pre class="methodSignature"><a href="Configuration.State.html" title="enum in org.gradle.api.artifacts">Configuration.State</a>&nbsp;getState()</pre>
<div class="block">Returns the state of the configuration.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>The state of the configuration</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="Configuration.State.html" title="enum in org.gradle.api.artifacts"><code>Configuration.State</code></a></dd>
<dt><span class="simpleTagLabel">Implementation Requirements:</span></dt>
<dd>Usage: This method should only be called on resolvable configurations, but will not warn if used otherwise.</dd>
</dl>
</li>
</ul>
<a name="isVisible--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isVisible</h4>
<pre class="methodSignature">boolean&nbsp;isVisible()</pre>
<div class="block">Returns true if this is a visible configuration. A visible configuration is usable outside the project it belongs
 to. The default value is true.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>true if this is a visible configuration.</dd>
</dl>
</li>
</ul>
<a name="setVisible-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setVisible</h4>
<pre class="methodSignature"><a href="Configuration.html" title="interface in org.gradle.api.artifacts">Configuration</a>&nbsp;setVisible&#8203;(boolean&nbsp;visible)</pre>
<div class="block">Sets the visibility of this configuration. When visible is set to true, this configuration is visible outside
 the project it belongs to. The default value is true.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>visible</code> - true if this is a visible configuration</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>this configuration</dd>
<dt><span class="simpleTagLabel">Implementation Requirements:</span></dt>
<dd>Usage: This method should only be called on consumable configurations, but will not warn if used otherwise.</dd>
</dl>
</li>
</ul>
<a name="getExtendsFrom--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getExtendsFrom</h4>
<pre class="methodSignature">java.util.Set&lt;<a href="Configuration.html" title="interface in org.gradle.api.artifacts">Configuration</a>&gt;&nbsp;getExtendsFrom()</pre>
<div class="block">Returns the names of the configurations which this configuration extends from. The artifacts of the super
 configurations are also available in this configuration.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>The super configurations. Returns an empty set when this configuration does not extend any others.</dd>
</dl>
</li>
</ul>
<a name="setExtendsFrom-java.lang.Iterable-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setExtendsFrom</h4>
<pre class="methodSignature"><a href="Configuration.html" title="interface in org.gradle.api.artifacts">Configuration</a>&nbsp;setExtendsFrom&#8203;(java.lang.Iterable&lt;<a href="Configuration.html" title="interface in org.gradle.api.artifacts">Configuration</a>&gt;&nbsp;superConfigs)</pre>
<div class="block">Sets the configurations which this configuration extends from.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>superConfigs</code> - The super configuration. Should not be null.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>this configuration</dd>
</dl>
</li>
</ul>
<a name="extendsFrom-org.gradle.api.artifacts.Configuration...-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>extendsFrom</h4>
<pre class="methodSignature"><a href="Configuration.html" title="interface in org.gradle.api.artifacts">Configuration</a>&nbsp;extendsFrom&#8203;(<a href="Configuration.html" title="interface in org.gradle.api.artifacts">Configuration</a>...&nbsp;superConfigs)</pre>
<div class="block">Adds the given configurations to the set of configuration which this configuration extends from.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>superConfigs</code> - The super configurations.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>this configuration</dd>
</dl>
</li>
</ul>
<a name="isTransitive--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isTransitive</h4>
<pre class="methodSignature">boolean&nbsp;isTransitive()</pre>
<div class="block">Returns the transitivity of this configuration. A transitive configuration contains the transitive closure of its
 direct dependencies, and all their dependencies. An intransitive configuration contains only the direct
 dependencies. The default value is true.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>true if this is a transitive configuration, false otherwise.</dd>
</dl>
</li>
</ul>
<a name="setTransitive-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setTransitive</h4>
<pre class="methodSignature"><a href="Configuration.html" title="interface in org.gradle.api.artifacts">Configuration</a>&nbsp;setTransitive&#8203;(boolean&nbsp;t)</pre>
<div class="block">Sets the transitivity of this configuration. When set to true, this configuration will contain the transitive
 closure of its dependencies and their dependencies. The default value is true.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>t</code> - true if this is a transitive configuration.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>this configuration</dd>
</dl>
</li>
</ul>
<a name="getDescription--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getDescription</h4>
<pre class="methodSignature">@Nullable
java.lang.String&nbsp;getDescription()</pre>
<div class="block">Returns the description for this configuration.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>the description. May be null.</dd>
</dl>
</li>
</ul>
<a name="setDescription-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setDescription</h4>
<pre class="methodSignature"><a href="Configuration.html" title="interface in org.gradle.api.artifacts">Configuration</a>&nbsp;setDescription&#8203;(@Nullable
                             java.lang.String&nbsp;description)</pre>
<div class="block">Sets the description for this configuration.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>description</code> - the description. May be null</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>this configuration</dd>
</dl>
</li>
</ul>
<a name="getHierarchy--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getHierarchy</h4>
<pre class="methodSignature">java.util.Set&lt;<a href="Configuration.html" title="interface in org.gradle.api.artifacts">Configuration</a>&gt;&nbsp;getHierarchy()</pre>
<div class="block">Gets an ordered set including this configuration and all superconfigurations
 recursively.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>the set of all configurations</dd>
</dl>
</li>
</ul>
<a name="resolve--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>resolve</h4>
<pre class="methodSignature">java.util.Set&lt;java.io.File&gt;&nbsp;resolve()</pre>
<div class="block">Resolves this configuration. This locates and downloads the files which make up this configuration, and returns
 the resulting set of files.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>The files of this configuration.</dd>
<dt><span class="simpleTagLabel">Implementation Requirements:</span></dt>
<dd>Usage: This method should only be called on resolvable configurations and will emit a deprecation warning if
 called on a configuration that does not permit this usage, or has allowed this usage but marked it as deprecated.</dd>
</dl>
</li>
</ul>
<a name="files-groovy.lang.Closure-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>files</h4>
<pre class="methodSignature">java.util.Set&lt;java.io.File&gt;&nbsp;files&#8203;(<a href="https://docs.groovy-lang.org/docs/groovy-3.0.17/html/gapi/groovy/lang/Closure.html?is-external=true" title="class or interface in groovy.lang" class="externalLink">Closure</a>&nbsp;dependencySpecClosure)</pre>
<div class="block">Takes a closure which gets coerced into a <a href="../specs/Spec.html" title="interface in org.gradle.api.specs"><code>Spec</code></a>. Behaves otherwise in the same way as
 <a href="#files-org.gradle.api.specs.Spec-"><code>files(org.gradle.api.specs.Spec)</code></a>.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>dependencySpecClosure</code> - The closure describing a filter applied to the all the dependencies of this configuration (including dependencies from extended configurations).</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>The files of a subset of dependencies of this configuration.</dd>
<dt><span class="simpleTagLabel">Implementation Requirements:</span></dt>
<dd>Usage: This method should only be called on resolvable configurations and should fail if
 called on a configuration that does not permit this usage.  It should warn if called on a configuration that has
 allowed this usage but marked it as deprecated.</dd>
</dl>
</li>
</ul>
<a name="files-org.gradle.api.specs.Spec-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>files</h4>
<pre class="methodSignature">java.util.Set&lt;java.io.File&gt;&nbsp;files&#8203;(<a href="../specs/Spec.html" title="interface in org.gradle.api.specs">Spec</a>&lt;? super <a href="Dependency.html" title="interface in org.gradle.api.artifacts">Dependency</a>&gt;&nbsp;dependencySpec)</pre>
<div class="block">Resolves this configuration. This locates and downloads the files which make up this configuration.
 But only the resulting set of files belonging to the subset of dependencies specified by the dependencySpec
 is returned.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>dependencySpec</code> - The spec describing a filter applied to the all the dependencies of this configuration (including dependencies from extended configurations).</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>The files of a subset of dependencies of this configuration.</dd>
<dt><span class="simpleTagLabel">Implementation Requirements:</span></dt>
<dd>Usage: This method should only be called on resolvable configurations and should fail if
 called on a configuration that does not permit this usage.  It should warn if called on a configuration that has
 allowed this usage but marked it as deprecated.</dd>
</dl>
</li>
</ul>
<a name="files-org.gradle.api.artifacts.Dependency...-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>files</h4>
<pre class="methodSignature">java.util.Set&lt;java.io.File&gt;&nbsp;files&#8203;(<a href="Dependency.html" title="interface in org.gradle.api.artifacts">Dependency</a>...&nbsp;dependencies)</pre>
<div class="block">Resolves this configuration. This locates and downloads the files which make up this configuration.
 But only the resulting set of files belonging to the specified dependencies
 is returned.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>dependencies</code> - The dependencies to be resolved</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>The files of a subset of dependencies of this configuration.</dd>
<dt><span class="simpleTagLabel">Implementation Requirements:</span></dt>
<dd>Usage: This method should only be called on resolvable configurations and should fail if
 called on a configuration that does not permit this usage.  It should warn if called on a configuration that has
 allowed this usage but marked it as deprecated.</dd>
</dl>
</li>
</ul>
<a name="fileCollection-org.gradle.api.specs.Spec-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>fileCollection</h4>
<pre class="methodSignature"><a href="../file/FileCollection.html" title="interface in org.gradle.api.file">FileCollection</a>&nbsp;fileCollection&#8203;(<a href="../specs/Spec.html" title="interface in org.gradle.api.specs">Spec</a>&lt;? super <a href="Dependency.html" title="interface in org.gradle.api.artifacts">Dependency</a>&gt;&nbsp;dependencySpec)</pre>
<div class="block">Resolves this configuration lazily. The resolve happens when the elements of the returned <a href="../file/FileCollection.html" title="interface in org.gradle.api.file"><code>FileCollection</code></a> get accessed the first time.
 This locates and downloads the files which make up this configuration. Only the resulting set of files belonging to the subset
 of dependencies specified by the dependencySpec is contained in the FileCollection.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>dependencySpec</code> - The spec describing a filter applied to the all the dependencies of this configuration (including dependencies from extended configurations).</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>The FileCollection with a subset of dependencies of this configuration.</dd>
<dt><span class="simpleTagLabel">Implementation Requirements:</span></dt>
<dd>Usage: This method should only be called on resolvable configurations and should fail if
 called on a configuration that does not permit this usage.  It should warn if called on a configuration that has
 allowed this usage but marked it as deprecated.</dd>
</dl>
</li>
</ul>
<a name="fileCollection-groovy.lang.Closure-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>fileCollection</h4>
<pre class="methodSignature"><a href="../file/FileCollection.html" title="interface in org.gradle.api.file">FileCollection</a>&nbsp;fileCollection&#8203;(<a href="https://docs.groovy-lang.org/docs/groovy-3.0.17/html/gapi/groovy/lang/Closure.html?is-external=true" title="class or interface in groovy.lang" class="externalLink">Closure</a>&nbsp;dependencySpecClosure)</pre>
<div class="block">Takes a closure which gets coerced into a <a href="../specs/Spec.html" title="interface in org.gradle.api.specs"><code>Spec</code></a>. Behaves otherwise in the same way as
 <a href="#fileCollection-org.gradle.api.specs.Spec-"><code>fileCollection(org.gradle.api.specs.Spec)</code></a>.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>dependencySpecClosure</code> - The closure describing a filter applied to the all the dependencies of this configuration (including dependencies from extended configurations).</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>The FileCollection with a subset of dependencies of this configuration.</dd>
<dt><span class="simpleTagLabel">Implementation Requirements:</span></dt>
<dd>Usage: This method should only be called on resolvable configurations and should fail if
 called on a configuration that does not permit this usage.  It should warn if called on a configuration that has
 allowed this usage but marked it as deprecated.</dd>
</dl>
</li>
</ul>
<a name="fileCollection-org.gradle.api.artifacts.Dependency...-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>fileCollection</h4>
<pre class="methodSignature"><a href="../file/FileCollection.html" title="interface in org.gradle.api.file">FileCollection</a>&nbsp;fileCollection&#8203;(<a href="Dependency.html" title="interface in org.gradle.api.artifacts">Dependency</a>...&nbsp;dependencies)</pre>
<div class="block">Resolves this configuration lazily. The resolve happens when the elements of the returned <a href="../file/FileCollection.html" title="interface in org.gradle.api.file"><code>FileCollection</code></a> get accessed the first time.
 This locates and downloads the files which make up this configuration. Only the resulting set of files belonging to specified
 dependencies is contained in the FileCollection.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>dependencies</code> - The dependencies for which the FileCollection should contain the files.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>The FileCollection with a subset of dependencies of this configuration.</dd>
<dt><span class="simpleTagLabel">Implementation Requirements:</span></dt>
<dd>Usage: This method should only be called on resolvable configurations and should fail if
 called on a configuration that does not permit this usage.  It should warn if called on a configuration that has
 allowed this usage but marked it as deprecated.</dd>
</dl>
</li>
</ul>
<a name="getResolvedConfiguration--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getResolvedConfiguration</h4>
<pre class="methodSignature"><a href="ResolvedConfiguration.html" title="interface in org.gradle.api.artifacts">ResolvedConfiguration</a>&nbsp;getResolvedConfiguration()</pre>
<div class="block">Resolves this configuration. This locates and downloads the files which make up this configuration, and returns
 a <a href="ResolvedConfiguration.html" title="interface in org.gradle.api.artifacts"><code>ResolvedConfiguration</code></a> that may be used to determine information about the resolve (including errors).</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>The ResolvedConfiguration object</dd>
<dt><span class="simpleTagLabel">Implementation Requirements:</span></dt>
<dd>Usage: This method should only be called on resolvable configurations and should fail if
 called on a configuration that does not permit this usage.  It should warn if called on a configuration that has
 allowed this usage but marked it as deprecated.</dd>
</dl>
</li>
</ul>
<a name="getUploadTaskName--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getUploadTaskName</h4>
<pre class="methodSignature">@Deprecated
java.lang.String&nbsp;getUploadTaskName()</pre>
<div class="deprecationBlock"><span class="deprecatedLabel">Deprecated.</span></div>
<div class="block">Returns the name of the task that upload the artifacts of this configuration to repositories
 declared by the user.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>The name of the associated upload task</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../tasks/Upload.html" title="class in org.gradle.api.tasks"><code>Upload</code></a></dd>
</dl>
</li>
</ul>
<a name="getBuildDependencies--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getBuildDependencies</h4>
<pre class="methodSignature"><a href="../tasks/TaskDependency.html" title="interface in org.gradle.api.tasks">TaskDependency</a>&nbsp;getBuildDependencies()</pre>
<div class="block">Returns a <code>TaskDependency</code> object containing all required dependencies to build the local dependencies
 (e.g. project dependencies) belonging to this configuration or to one of its super configurations.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../Buildable.html#getBuildDependencies--">getBuildDependencies</a></code>&nbsp;in interface&nbsp;<code><a href="../Buildable.html" title="interface in org.gradle.api">Buildable</a></code></dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>a TaskDependency object</dd>
<dt><span class="simpleTagLabel">Implementation Requirements:</span></dt>
<dd>Usage: This method should only be called on resolvable configurations, but will not warn if used otherwise.</dd>
</dl>
</li>
</ul>
<a name="getTaskDependencyFromProjectDependency-boolean-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getTaskDependencyFromProjectDependency</h4>
<pre class="methodSignature"><a href="../tasks/TaskDependency.html" title="interface in org.gradle.api.tasks">TaskDependency</a>&nbsp;getTaskDependencyFromProjectDependency&#8203;(boolean&nbsp;useDependedOn,
                                                      java.lang.String&nbsp;taskName)</pre>
<div class="block">Returns a TaskDependency object containing dependencies on all tasks with the specified name from project
 dependencies related to this configuration or one of its super configurations.  These other projects may be
 projects this configuration depends on or projects with a similarly named configuration that depend on this one
 based on the useDependOn argument.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>useDependedOn</code> - if true, add tasks from project dependencies in this configuration, otherwise use projects
                      from configurations with the same name that depend on this one.</dd>
<dd><code>taskName</code> - name of task to depend on</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>the populated TaskDependency object</dd>
</dl>
</li>
</ul>
<a name="getDependencies--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getDependencies</h4>
<pre class="methodSignature"><a href="DependencySet.html" title="interface in org.gradle.api.artifacts">DependencySet</a>&nbsp;getDependencies()</pre>
<div class="block">Gets the set of declared dependencies directly contained in this configuration
 (ignoring superconfigurations).
 <p>
 This method does not resolve the configuration. Therefore, the return value does not include
 transitive dependencies.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>the set of dependencies</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="#extendsFrom-org.gradle.api.artifacts.Configuration...-"><code>extendsFrom(Configuration...)</code></a></dd>
<dt><span class="simpleTagLabel">Implementation Requirements:</span></dt>
<dd>Usage: This method should only be called on declarable configurations, but will not warn if used otherwise.</dd>
</dl>
</li>
</ul>
<a name="getAllDependencies--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getAllDependencies</h4>
<pre class="methodSignature"><a href="DependencySet.html" title="interface in org.gradle.api.artifacts">DependencySet</a>&nbsp;getAllDependencies()</pre>
<div class="block">Gets the complete set of declared dependencies including those contributed by
 superconfigurations.
 <p>
 This method does not resolve the configuration. Therefore, the return value does not include
 transitive dependencies.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>the (read-only) set of dependencies</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="#extendsFrom-org.gradle.api.artifacts.Configuration...-"><code>extendsFrom(Configuration...)</code></a></dd>
</dl>
</li>
</ul>
<a name="getDependencyConstraints--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getDependencyConstraints</h4>
<pre class="methodSignature"><a href="DependencyConstraintSet.html" title="interface in org.gradle.api.artifacts">DependencyConstraintSet</a>&nbsp;getDependencyConstraints()</pre>
<div class="block">Gets the set of dependency constraints directly contained in this configuration
 (ignoring superconfigurations).</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>the set of dependency constraints</dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>4.6</dd>
<dt><span class="simpleTagLabel">Implementation Requirements:</span></dt>
<dd>Usage: This method should only be called on declarable configurations, but will not warn if used otherwise.</dd>
</dl>
</li>
</ul>
<a name="getAllDependencyConstraints--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getAllDependencyConstraints</h4>
<pre class="methodSignature"><a href="DependencyConstraintSet.html" title="interface in org.gradle.api.artifacts">DependencyConstraintSet</a>&nbsp;getAllDependencyConstraints()</pre>
<div class="block"><p>Gets the complete set of dependency constraints including those contributed by
 superconfigurations.</p></div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>the (read-only) set of dependency constraints</dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>4.6</dd>
</dl>
</li>
</ul>
<a name="getArtifacts--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getArtifacts</h4>
<pre class="methodSignature"><a href="PublishArtifactSet.html" title="interface in org.gradle.api.artifacts">PublishArtifactSet</a>&nbsp;getArtifacts()</pre>
<div class="block">Returns the artifacts of this configuration excluding the artifacts of extended configurations.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>The set.</dd>
<dt><span class="simpleTagLabel">Implementation Requirements:</span></dt>
<dd>Usage: This method should only be called on consumable configurations, but will not warn if used otherwise.</dd>
</dl>
</li>
</ul>
<a name="getAllArtifacts--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getAllArtifacts</h4>
<pre class="methodSignature"><a href="PublishArtifactSet.html" title="interface in org.gradle.api.artifacts">PublishArtifactSet</a>&nbsp;getAllArtifacts()</pre>
<div class="block">Returns the artifacts of this configuration including the artifacts of extended configurations.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>The (read-only) set.</dd>
</dl>
</li>
</ul>
<a name="getExcludeRules--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getExcludeRules</h4>
<pre class="methodSignature">java.util.Set&lt;<a href="ExcludeRule.html" title="interface in org.gradle.api.artifacts">ExcludeRule</a>&gt;&nbsp;getExcludeRules()</pre>
<div class="block">Returns the exclude rules applied for resolving any dependency of this configuration.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>The exclude rules</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="#exclude-java.util.Map-"><code>exclude(java.util.Map)</code></a></dd>
</dl>
</li>
</ul>
<a name="exclude-java.util.Map-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>exclude</h4>
<pre class="methodSignature"><a href="Configuration.html" title="interface in org.gradle.api.artifacts">Configuration</a>&nbsp;exclude&#8203;(java.util.Map&lt;java.lang.String,&#8203;java.lang.String&gt;&nbsp;excludeProperties)</pre>
<div class="block">Adds an exclude rule to exclude transitive dependencies for all dependencies of this configuration.
 You can also add exclude rules per-dependency. See <a href="ModuleDependency.html#exclude-java.util.Map-"><code>ModuleDependency.exclude(java.util.Map)</code></a>.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>excludeProperties</code> - the properties to define the exclude rule.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>this</dd>
</dl>
</li>
</ul>
<a name="defaultDependencies-org.gradle.api.Action-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>defaultDependencies</h4>
<pre class="methodSignature"><a href="Configuration.html" title="interface in org.gradle.api.artifacts">Configuration</a>&nbsp;defaultDependencies&#8203;(<a href="../Action.html" title="interface in org.gradle.api">Action</a>&lt;? super <a href="DependencySet.html" title="interface in org.gradle.api.artifacts">DependencySet</a>&gt;&nbsp;action)</pre>
<div class="block">Execute the given action if the configuration has no defined dependencies when it first participates in
 dependency resolution. A <code>Configuration</code> will participate in dependency resolution
 when:
 <ul>
     <li>The <a href="Configuration.html" title="interface in org.gradle.api.artifacts"><code>Configuration</code></a> itself is resolved</li>
     <li>Another <a href="Configuration.html" title="interface in org.gradle.api.artifacts"><code>Configuration</code></a> that extends this one is resolved</li>
     <li>Another <a href="Configuration.html" title="interface in org.gradle.api.artifacts"><code>Configuration</code></a> that references this one as a project dependency is resolved</li>
 </ul>

 This method is useful for specifying default dependencies for a configuration:
 <pre class='autoTested'>
 configurations { conf }
 configurations['conf'].defaultDependencies { dependencies -&gt;
      dependencies.add(owner.project.dependencies.create("org.gradle:my-util:1.0"))
 }
 </pre>
 <p>
 A <code>Configuration</code> is considered empty even if it extends another, non-empty <code>Configuration</code>.
 <p>
 If multiple actions are supplied, each action will be executed until the set of dependencies is no longer empty.
 Remaining actions will be ignored.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>action</code> - the action to execute when the configuration has no defined dependencies.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>this</dd>
<dt><span class="simpleTagLabel">Implementation Requirements:</span></dt>
<dd>Usage: This method should only be called on resolvable configurations and will emit a deprecation warning if
 called on a configuration that does not permit this usage, or has allowed this usage but marked it as deprecated.</dd>
</dl>
</li>
</ul>
<a name="withDependencies-org.gradle.api.Action-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>withDependencies</h4>
<pre class="methodSignature"><a href="Configuration.html" title="interface in org.gradle.api.artifacts">Configuration</a>&nbsp;withDependencies&#8203;(<a href="../Action.html" title="interface in org.gradle.api">Action</a>&lt;? super <a href="DependencySet.html" title="interface in org.gradle.api.artifacts">DependencySet</a>&gt;&nbsp;action)</pre>
<div class="block">Execute the given action before the configuration first participates in
 dependency resolution. A <code>Configuration</code> will participate in dependency resolution
 when:
 <ul>
     <li>The <a href="Configuration.html" title="interface in org.gradle.api.artifacts"><code>Configuration</code></a> itself is resolved</li>
     <li>Another <a href="Configuration.html" title="interface in org.gradle.api.artifacts"><code>Configuration</code></a> that extends this one is resolved</li>
     <li>Another <a href="Configuration.html" title="interface in org.gradle.api.artifacts"><code>Configuration</code></a> that references this one as a project dependency is resolved</li>
 </ul>

 This method is useful for mutating the dependencies for a configuration:
 <pre class='autoTested'>
 configurations { conf }
 configurations['conf'].withDependencies { dependencies -&gt;
      dependencies.each { dependency -&gt;
          if (dependency.version == null) {
              dependency.version { require '1.0' }
          }
      }
 }
 </pre>

 Actions will be executed in the order provided.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>action</code> - a dependency action to execute before the configuration is used.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>this</dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>4.4</dd>
</dl>
</li>
</ul>
<a name="getAll--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getAll</h4>
<pre class="methodSignature">@Deprecated
java.util.Set&lt;<a href="Configuration.html" title="interface in org.gradle.api.artifacts">Configuration</a>&gt;&nbsp;getAll()</pre>
<div class="deprecationBlock"><span class="deprecatedLabel">Deprecated.</span></div>
<div class="block">Returns all the configurations belonging to the same configuration container as this
 configuration (including this configuration).</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>All the configurations belonging to the configuration container that this set belongs to itself.</dd>
</dl>
</li>
</ul>
<a name="getIncoming--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getIncoming</h4>
<pre class="methodSignature"><a href="ResolvableDependencies.html" title="interface in org.gradle.api.artifacts">ResolvableDependencies</a>&nbsp;getIncoming()</pre>
<div class="block">Returns the incoming dependencies of this configuration.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>The incoming dependencies of this configuration. Never <code>null</code>.</dd>
<dt><span class="simpleTagLabel">Implementation Requirements:</span></dt>
<dd>Usage: This method should only be called on consumable and resolvable configurations, but will not warn if used otherwise.</dd>
</dl>
</li>
</ul>
<a name="getOutgoing--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getOutgoing</h4>
<pre class="methodSignature"><a href="ConfigurationPublications.html" title="interface in org.gradle.api.artifacts">ConfigurationPublications</a>&nbsp;getOutgoing()</pre>
<div class="block">Returns the outgoing <a href="ConfigurationPublications.html" title="interface in org.gradle.api.artifacts"><code>ConfigurationPublications</code></a> instance that advertises and allows configuring the artifacts and variants published by this configuration.
 <p>
 This allows adding additional artifacts and accessing and configuring variants to publish.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>The outgoing publications object containing artifacts and variants published by this configuration.</dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>3.4</dd>
<dt><span class="simpleTagLabel">Implementation Requirements:</span></dt>
<dd>Usage: This method should only be called on consumable configurations, but will not warn if used otherwise.</dd>
</dl>
</li>
</ul>
<a name="outgoing-org.gradle.api.Action-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>outgoing</h4>
<pre class="methodSignature">void&nbsp;outgoing&#8203;(<a href="../Action.html" title="interface in org.gradle.api">Action</a>&lt;? super <a href="ConfigurationPublications.html" title="interface in org.gradle.api.artifacts">ConfigurationPublications</a>&gt;&nbsp;action)</pre>
<div class="block">Configures the outgoing <a href="ConfigurationPublications.html" title="interface in org.gradle.api.artifacts"><code>ConfigurationPublications</code></a> instance that advertises and allows configuring the artifacts and variants published by this configuration.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>action</code> - The action to perform the configuration.</dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>3.4</dd>
<dt><span class="simpleTagLabel">Implementation Requirements:</span></dt>
<dd>Usage: This method should only be called on consumable configurations, but will not warn if used otherwise.</dd>
</dl>
</li>
</ul>
<a name="copy--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>copy</h4>
<pre class="methodSignature"><a href="Configuration.html" title="interface in org.gradle.api.artifacts">Configuration</a>&nbsp;copy()</pre>
<div class="block">Creates a copy of this configuration that only contains the dependencies directly in this configuration
 (without contributions from superconfigurations).  The new configuration will be in the
 UNRESOLVED state, but will retain all other attributes of this configuration except superconfigurations.
 <a href="#getHierarchy--"><code>getHierarchy()</code></a> for the copy will not include any superconfigurations.
 <p>
 This method is only intended for use for specific situations involving resolvable configuration, it is
 <strong>NOT</strong> intended as a general-purpose copying mechanism.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>copy of this configuration</dd>
<dt><span class="simpleTagLabel">Implementation Requirements:</span></dt>
<dd>Usage: This method should only be called on resolvable configurations and will emit a deprecation warning if
 called on a configuration that does not permit this usage, or has allowed this usage but marked it as deprecated.</dd>
</dl>
</li>
</ul>
<a name="copyRecursive--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>copyRecursive</h4>
<pre class="methodSignature"><a href="Configuration.html" title="interface in org.gradle.api.artifacts">Configuration</a>&nbsp;copyRecursive()</pre>
<div class="block">Creates a copy of this configuration that contains the dependencies directly in this configuration
 and those derived from superconfigurations.  The new configuration will be in the
 UNRESOLVED state, but will retain all other attributes of this configuration except superconfigurations.
 <a href="#getHierarchy--"><code>getHierarchy()</code></a> for the copy will not include any superconfigurations.
 <p>
 This method is only intended for use for specific situations involving resolvable configuration, it is
 <strong>NOT</strong> intended as a general-purpose copying mechanism.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>copy of this configuration</dd>
<dt><span class="simpleTagLabel">Implementation Requirements:</span></dt>
<dd>Usage: This method should only be called on resolvable configurations and will emit a deprecation warning if
 called on a configuration that does not permit this usage, or has allowed this usage but marked it as deprecated.</dd>
</dl>
</li>
</ul>
<a name="copy-org.gradle.api.specs.Spec-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>copy</h4>
<pre class="methodSignature"><a href="Configuration.html" title="interface in org.gradle.api.artifacts">Configuration</a>&nbsp;copy&#8203;(<a href="../specs/Spec.html" title="interface in org.gradle.api.specs">Spec</a>&lt;? super <a href="Dependency.html" title="interface in org.gradle.api.artifacts">Dependency</a>&gt;&nbsp;dependencySpec)</pre>
<div class="block">Creates a copy of this configuration ignoring superconfigurations (see <a href="#copy--"><code>copy()</code></a> but filtering
 the dependencies using the specified dependency spec.
 <p>
 This method is only intended for use for specific situations involving resolvable configuration, it is
 <strong>NOT</strong> intended as a general-purpose copying mechanism.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>dependencySpec</code> - filtering requirements</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>copy of this configuration</dd>
<dt><span class="simpleTagLabel">Implementation Requirements:</span></dt>
<dd>Usage: This method should only be called on resolvable configurations and will emit a deprecation warning if
 called on a configuration that does not permit this usage, or has allowed this usage but marked it as deprecated.</dd>
</dl>
</li>
</ul>
<a name="copyRecursive-org.gradle.api.specs.Spec-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>copyRecursive</h4>
<pre class="methodSignature"><a href="Configuration.html" title="interface in org.gradle.api.artifacts">Configuration</a>&nbsp;copyRecursive&#8203;(<a href="../specs/Spec.html" title="interface in org.gradle.api.specs">Spec</a>&lt;? super <a href="Dependency.html" title="interface in org.gradle.api.artifacts">Dependency</a>&gt;&nbsp;dependencySpec)</pre>
<div class="block">Creates a copy of this configuration with dependencies from superconfigurations (see <a href="#copyRecursive--"><code>copyRecursive()</code></a>)
 but filtering the dependencies using the dependencySpec.
 <p>
 This method is only intended for use for specific situations involving resolvable configuration, it is
 <strong>NOT</strong> intended as a general-purpose copying mechanism.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>dependencySpec</code> - filtering requirements</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>copy of this configuration</dd>
<dt><span class="simpleTagLabel">Implementation Requirements:</span></dt>
<dd>Usage: This method should only be called on resolvable configurations and will emit a deprecation warning if
 called on a configuration that does not permit this usage, or has allowed this usage but marked it as deprecated.</dd>
</dl>
</li>
</ul>
<a name="copy-groovy.lang.Closure-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>copy</h4>
<pre class="methodSignature"><a href="Configuration.html" title="interface in org.gradle.api.artifacts">Configuration</a>&nbsp;copy&#8203;(<a href="https://docs.groovy-lang.org/docs/groovy-3.0.17/html/gapi/groovy/lang/Closure.html?is-external=true" title="class or interface in groovy.lang" class="externalLink">Closure</a>&nbsp;dependencySpec)</pre>
<div class="block">Takes a closure which gets coerced into a <a href="../specs/Spec.html" title="interface in org.gradle.api.specs"><code>Spec</code></a>. Behaves otherwise in the same way as <a href="#copy-org.gradle.api.specs.Spec-"><code>copy(org.gradle.api.specs.Spec)</code></a>
  <p>
 This method is only intended for use for specific situations involving resolvable configuration, it is
 <strong>NOT</strong> intended as a general-purpose copying mechanism.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>dependencySpec</code> - filtering requirements</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>copy of this configuration</dd>
<dt><span class="simpleTagLabel">Implementation Requirements:</span></dt>
<dd>Usage: This method should only be called on resolvable configurations and will emit a deprecation warning if
 called on a configuration that does not permit this usage, or has allowed this usage but marked it as deprecated.</dd>
</dl>
</li>
</ul>
<a name="copyRecursive-groovy.lang.Closure-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>copyRecursive</h4>
<pre class="methodSignature"><a href="Configuration.html" title="interface in org.gradle.api.artifacts">Configuration</a>&nbsp;copyRecursive&#8203;(<a href="https://docs.groovy-lang.org/docs/groovy-3.0.17/html/gapi/groovy/lang/Closure.html?is-external=true" title="class or interface in groovy.lang" class="externalLink">Closure</a>&nbsp;dependencySpec)</pre>
<div class="block">Takes a closure which gets coerced into a <a href="../specs/Spec.html" title="interface in org.gradle.api.specs"><code>Spec</code></a>. Behaves otherwise in the same way as <a href="#copyRecursive-org.gradle.api.specs.Spec-"><code>copyRecursive(org.gradle.api.specs.Spec)</code></a>
 <p>
 This method is only intended for use for specific situations involving resolvable configuration, it is
 <strong>NOT</strong> intended as a general-purpose copying mechanism.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>dependencySpec</code> - filtering requirements</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>copy of this configuration</dd>
<dt><span class="simpleTagLabel">Implementation Requirements:</span></dt>
<dd>Usage: This method should only be called on resolvable configurations and will emit a deprecation warning if
 called on a configuration that does not permit this usage, or has allowed this usage but marked it as deprecated.</dd>
</dl>
</li>
</ul>
<a name="setCanBeConsumed-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setCanBeConsumed</h4>
<pre class="methodSignature">void&nbsp;setCanBeConsumed&#8203;(boolean&nbsp;allowed)</pre>
<div class="block">Configures if a configuration can be consumed.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>3.3</dd>
</dl>
</li>
</ul>
<a name="isCanBeConsumed--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isCanBeConsumed</h4>
<pre class="methodSignature">boolean&nbsp;isCanBeConsumed()</pre>
<div class="block">Returns true if this configuration can be consumed from another project, or published. Defaults to true.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>true if this configuration can be consumed or published.</dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>3.3</dd>
</dl>
</li>
</ul>
<a name="setCanBeResolved-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setCanBeResolved</h4>
<pre class="methodSignature">void&nbsp;setCanBeResolved&#8203;(boolean&nbsp;allowed)</pre>
<div class="block">Configures if a configuration can be resolved.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>3.3</dd>
</dl>
</li>
</ul>
<a name="isCanBeResolved--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isCanBeResolved</h4>
<pre class="methodSignature">boolean&nbsp;isCanBeResolved()</pre>
<div class="block">Returns true if it is allowed to query or resolve this configuration. Defaults to true.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>true if this configuration can be queried or resolved.</dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>3.3</dd>
</dl>
</li>
</ul>
<a name="setCanBeDeclared-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setCanBeDeclared</h4>
<pre class="methodSignature"><a href="../Incubating.html" title="annotation in org.gradle.api">@Incubating</a>
void&nbsp;setCanBeDeclared&#8203;(boolean&nbsp;allowed)</pre>
<div class="block">Configures if a configuration can have dependencies declared upon it.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>8.2</dd>
</dl>
</li>
</ul>
<a name="isCanBeDeclared--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isCanBeDeclared</h4>
<pre class="methodSignature"><a href="../Incubating.html" title="annotation in org.gradle.api">@Incubating</a>
boolean&nbsp;isCanBeDeclared()</pre>
<div class="block">Returns true if it is allowed to declare dependencies upon this configuration.
 Defaults to true.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>true if this configuration can have dependencies declared</dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>8.2</dd>
</dl>
</li>
</ul>
<a name="shouldResolveConsistentlyWith-org.gradle.api.artifacts.Configuration-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>shouldResolveConsistentlyWith</h4>
<pre class="methodSignature"><a href="../Incubating.html" title="annotation in org.gradle.api">@Incubating</a>
<a href="Configuration.html" title="interface in org.gradle.api.artifacts">Configuration</a>&nbsp;shouldResolveConsistentlyWith&#8203;(<a href="Configuration.html" title="interface in org.gradle.api.artifacts">Configuration</a>&nbsp;versionsSource)</pre>
<div class="block">Tells that this configuration, when resolved, should resolve versions consistently
 from the resolution result of another resolvable configuration. For example, it's
 expected that the versions of the runtime classpath are the same as the versions
 from the compile classpath.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>versionsSource</code> - another resolvable configuration to use as reference for versions</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>this configuration</dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>6.8</dd>
<dt><span class="simpleTagLabel">Implementation Requirements:</span></dt>
<dd>Usage: This method should only be called on resolvable configurations and will emit a deprecation warning if
 called on a configuration that does not permit this usage, or has had allowed this usage but marked it as deprecated.</dd>
</dl>
</li>
</ul>
<a name="disableConsistentResolution--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>disableConsistentResolution</h4>
<pre class="methodSignature"><a href="../Incubating.html" title="annotation in org.gradle.api">@Incubating</a>
<a href="Configuration.html" title="interface in org.gradle.api.artifacts">Configuration</a>&nbsp;disableConsistentResolution()</pre>
<div class="block">Disables consistent resolution for this configuration.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>6.8</dd>
<dt><span class="simpleTagLabel">Implementation Requirements:</span></dt>
<dd>Usage: This method should only be called on resolvable configurations and will emit a deprecation warning if
 called on a configuration that does not permit this usage, or has had allowed this usage but marked it as deprecated.</dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../allclasses.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li><a href="#nested.class.summary">Nested</a>&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
