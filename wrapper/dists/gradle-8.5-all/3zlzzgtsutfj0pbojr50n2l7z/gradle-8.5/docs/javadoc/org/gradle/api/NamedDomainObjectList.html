<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc -->
<title>NamedDomainObjectList (Gradle API 8.5)</title>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<link rel="stylesheet" type="text/css" href="../../../javadoc.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../jquery/jquery-ui.min.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../jquery-ui.overrides.css" title="Style">
<script type="text/javascript" src="../../../script.js"></script>
<script type="text/javascript" src="../../../jquery/jszip/dist/jszip.min.js"></script>
<script type="text/javascript" src="../../../jquery/jszip-utils/dist/jszip-utils.min.js"></script>
<!--[if IE]>
<script type="text/javascript" src="../../../jquery/jszip-utils/dist/jszip-utils-ie.min.js"></script>
<![endif]-->
<script type="text/javascript" src="../../../jquery/jquery-3.6.1.min.js"></script>
<script type="text/javascript" src="../../../jquery/jquery-ui.min.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="NamedDomainObjectList (Gradle API 8.5)";
        }
    }
    catch(err) {
    }
//-->
var data = {"i0":6,"i1":6,"i2":6,"i3":6};
var tabs = {65535:["t0","All Methods"],2:["t2","Instance Methods"],4:["t3","Abstract Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
var pathtoroot = "../../../";
var useModuleDirectories = false;
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<div class="fixedNav">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../index-all.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../allclasses.html">All&nbsp;Classes</a></li>
</ul>
<ul class="navListSearch">
<li><label for="search">SEARCH:</label>
<input type="text" id="search" value="search" disabled="disabled">
<input type="reset" id="reset" value="reset" disabled="disabled">
</li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
</div>
<div class="navPadding">&nbsp;</div>
<script type="text/javascript"><!--
$('.navPadding').css('padding-top', $('.fixedNav').css("height"));
//-->
</script>
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle"><span class="packageLabelInType">Package</span>&nbsp;<a href="package-summary.html">org.gradle.api</a></div>
<h2 title="Interface NamedDomainObjectList" class="title">Interface NamedDomainObjectList&lt;T&gt;</h2>
</div>
<div class="contentContainer">
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt><span class="paramLabel">Type Parameters:</span></dt>
<dd><code>T</code> - The type of objects in the list</dd>
</dl>
<dl>
<dt>All Superinterfaces:</dt>
<dd><code>java.util.Collection&lt;T&gt;</code>, <code><a href="DomainObjectCollection.html" title="interface in org.gradle.api">DomainObjectCollection</a>&lt;T&gt;</code>, <code>java.lang.Iterable&lt;T&gt;</code>, <code>java.util.List&lt;T&gt;</code>, <code><a href="NamedDomainObjectCollection.html" title="interface in org.gradle.api">NamedDomainObjectCollection</a>&lt;T&gt;</code></dd>
</dl>
<dl>
<dt>All Known Subinterfaces:</dt>
<dd><code><a href="artifacts/ArtifactRepositoryContainer.html" title="interface in org.gradle.api.artifacts">ArtifactRepositoryContainer</a></code>, <code><a href="artifacts/dsl/RepositoryHandler.html" title="interface in org.gradle.api.artifacts.dsl">RepositoryHandler</a></code></dd>
</dl>
<hr>
<pre>public interface <span class="typeNameLabel">NamedDomainObjectList&lt;T&gt;</span>
extends <a href="NamedDomainObjectCollection.html" title="interface in org.gradle.api">NamedDomainObjectCollection</a>&lt;T&gt;, java.util.List&lt;T&gt;</pre>
<div class="block"><p>A specialization of <a href="NamedDomainObjectCollection.html" title="interface in org.gradle.api"><code>NamedDomainObjectCollection</code></a> that also implements <code>List</code>.</p>

 <p>All object equality is determined in terms of object names. That is, calling <code>remove()</code> with an object that is NOT equal to
 an existing object in terms of <code>equals</code>, but IS in terms of name equality will result in the existing collection item with
 the equal name being removed.</p>

 <p>You can create an instance of this type using the factory method <a href="model/ObjectFactory.html#namedDomainObjectList-java.lang.Class-"><code>ObjectFactory.namedDomainObjectList(Class)</code></a>.</p></div>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t3" class="tableTab"><span><a href="javascript:show(4);">Abstract Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colSecond" scope="col">Method</th>
<th class="colLast" scope="col">Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>java.util.List&lt;<a href="NamedDomainObjectList.html" title="type parameter in NamedDomainObjectList">T</a>&gt;</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#findAll-groovy.lang.Closure-">findAll</a></span>&#8203;(<a href="https://docs.groovy-lang.org/docs/groovy-3.0.17/html/gapi/groovy/lang/Closure.html?is-external=true" title="class or interface in groovy.lang" class="externalLink">Closure</a>&nbsp;spec)</code></th>
<td class="colLast">
<div class="block">Returns a collection which contains the objects in this collection which meet the given closure specification.</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code><a href="NamedDomainObjectList.html" title="interface in org.gradle.api">NamedDomainObjectList</a>&lt;<a href="NamedDomainObjectList.html" title="type parameter in NamedDomainObjectList">T</a>&gt;</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#matching-groovy.lang.Closure-">matching</a></span>&#8203;(<a href="https://docs.groovy-lang.org/docs/groovy-3.0.17/html/gapi/groovy/lang/Closure.html?is-external=true" title="class or interface in groovy.lang" class="externalLink">Closure</a>&nbsp;spec)</code></th>
<td class="colLast">
<div class="block">Returns a collection which contains the objects in this collection which meet the given closure specification.</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code><a href="NamedDomainObjectList.html" title="interface in org.gradle.api">NamedDomainObjectList</a>&lt;<a href="NamedDomainObjectList.html" title="type parameter in NamedDomainObjectList">T</a>&gt;</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#matching-org.gradle.api.specs.Spec-">matching</a></span>&#8203;(<a href="specs/Spec.html" title="interface in org.gradle.api.specs">Spec</a>&lt;? super <a href="NamedDomainObjectList.html" title="type parameter in NamedDomainObjectList">T</a>&gt;&nbsp;spec)</code></th>
<td class="colLast">
<div class="block">Returns a collection which contains the objects in this collection which meet the given specification.</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>&lt;S extends <a href="NamedDomainObjectList.html" title="type parameter in NamedDomainObjectList">T</a>&gt;<br><a href="NamedDomainObjectList.html" title="interface in org.gradle.api">NamedDomainObjectList</a>&lt;S&gt;</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#withType-java.lang.Class-">withType</a></span>&#8203;(java.lang.Class&lt;S&gt;&nbsp;type)</code></th>
<td class="colLast">
<div class="block">Returns a collection containing the objects in this collection of the given type.</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.util.Collection">
<!--   -->
</a>
<h3>Methods inherited from interface&nbsp;java.util.Collection</h3>
<code>parallelStream, removeIf, stream, toArray</code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.org.gradle.api.DomainObjectCollection">
<!--   -->
</a>
<h3>Methods inherited from interface&nbsp;org.gradle.api.<a href="DomainObjectCollection.html" title="interface in org.gradle.api">DomainObjectCollection</a></h3>
<code><a href="DomainObjectCollection.html#addAllLater-org.gradle.api.provider.Provider-">addAllLater</a>, <a href="DomainObjectCollection.html#addLater-org.gradle.api.provider.Provider-">addLater</a>, <a href="DomainObjectCollection.html#all-groovy.lang.Closure-">all</a>, <a href="DomainObjectCollection.html#all-org.gradle.api.Action-">all</a>, <a href="DomainObjectCollection.html#configureEach-org.gradle.api.Action-">configureEach</a>, <a href="DomainObjectCollection.html#whenObjectAdded-groovy.lang.Closure-">whenObjectAdded</a>, <a href="DomainObjectCollection.html#whenObjectAdded-org.gradle.api.Action-">whenObjectAdded</a>, <a href="DomainObjectCollection.html#whenObjectRemoved-groovy.lang.Closure-">whenObjectRemoved</a>, <a href="DomainObjectCollection.html#whenObjectRemoved-org.gradle.api.Action-">whenObjectRemoved</a>, <a href="DomainObjectCollection.html#withType-java.lang.Class-groovy.lang.Closure-">withType</a>, <a href="DomainObjectCollection.html#withType-java.lang.Class-org.gradle.api.Action-">withType</a></code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Iterable">
<!--   -->
</a>
<h3>Methods inherited from interface&nbsp;java.lang.Iterable</h3>
<code>forEach</code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.util.List">
<!--   -->
</a>
<h3>Methods inherited from interface&nbsp;java.util.List</h3>
<code>add, add, addAll, addAll, clear, contains, containsAll, equals, get, hashCode, indexOf, isEmpty, iterator, lastIndexOf, listIterator, listIterator, remove, remove, removeAll, replaceAll, retainAll, set, size, sort, spliterator, subList, toArray, toArray</code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.org.gradle.api.NamedDomainObjectCollection">
<!--   -->
</a>
<h3>Methods inherited from interface&nbsp;org.gradle.api.<a href="NamedDomainObjectCollection.html" title="interface in org.gradle.api">NamedDomainObjectCollection</a></h3>
<code><a href="NamedDomainObjectCollection.html#add-T-">add</a>, <a href="NamedDomainObjectCollection.html#addAll-java.util.Collection-">addAll</a>, <a href="NamedDomainObjectCollection.html#addRule-java.lang.String-groovy.lang.Closure-">addRule</a>, <a href="NamedDomainObjectCollection.html#addRule-java.lang.String-org.gradle.api.Action-">addRule</a>, <a href="NamedDomainObjectCollection.html#addRule-org.gradle.api.Rule-">addRule</a>, <a href="NamedDomainObjectCollection.html#findByName-java.lang.String-">findByName</a>, <a href="NamedDomainObjectCollection.html#getAsMap--">getAsMap</a>, <a href="NamedDomainObjectCollection.html#getAt-java.lang.String-">getAt</a>, <a href="NamedDomainObjectCollection.html#getByName-java.lang.String-">getByName</a>, <a href="NamedDomainObjectCollection.html#getByName-java.lang.String-groovy.lang.Closure-">getByName</a>, <a href="NamedDomainObjectCollection.html#getByName-java.lang.String-org.gradle.api.Action-">getByName</a>, <a href="NamedDomainObjectCollection.html#getCollectionSchema--">getCollectionSchema</a>, <a href="NamedDomainObjectCollection.html#getNamer--">getNamer</a>, <a href="NamedDomainObjectCollection.html#getNames--">getNames</a>, <a href="NamedDomainObjectCollection.html#getRules--">getRules</a>, <a href="NamedDomainObjectCollection.html#named-java.lang.String-">named</a>, <a href="NamedDomainObjectCollection.html#named-java.lang.String-java.lang.Class-">named</a>, <a href="NamedDomainObjectCollection.html#named-java.lang.String-java.lang.Class-org.gradle.api.Action-">named</a>, <a href="NamedDomainObjectCollection.html#named-java.lang.String-org.gradle.api.Action-">named</a></code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="withType-java.lang.Class-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>withType</h4>
<pre class="methodSignature">&lt;S extends <a href="NamedDomainObjectList.html" title="type parameter in NamedDomainObjectList">T</a>&gt;&nbsp;<a href="NamedDomainObjectList.html" title="interface in org.gradle.api">NamedDomainObjectList</a>&lt;S&gt;&nbsp;withType&#8203;(java.lang.Class&lt;S&gt;&nbsp;type)</pre>
<div class="block">Returns a collection containing the objects in this collection of the given type.  The returned collection is
 live, so that when matching objects are later added to this collection, they are also visible in the filtered
 collection.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="DomainObjectCollection.html#withType-java.lang.Class-">withType</a></code>&nbsp;in interface&nbsp;<code><a href="DomainObjectCollection.html" title="interface in org.gradle.api">DomainObjectCollection</a>&lt;<a href="NamedDomainObjectList.html" title="type parameter in NamedDomainObjectList">T</a>&gt;</code></dd>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="NamedDomainObjectCollection.html#withType-java.lang.Class-">withType</a></code>&nbsp;in interface&nbsp;<code><a href="NamedDomainObjectCollection.html" title="interface in org.gradle.api">NamedDomainObjectCollection</a>&lt;<a href="NamedDomainObjectList.html" title="type parameter in NamedDomainObjectList">T</a>&gt;</code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>type</code> - The type of objects to find.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>The matching objects. Returns an empty collection if there are no such objects in this collection.</dd>
</dl>
</li>
</ul>
<a name="matching-org.gradle.api.specs.Spec-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>matching</h4>
<pre class="methodSignature"><a href="NamedDomainObjectList.html" title="interface in org.gradle.api">NamedDomainObjectList</a>&lt;<a href="NamedDomainObjectList.html" title="type parameter in NamedDomainObjectList">T</a>&gt;&nbsp;matching&#8203;(<a href="specs/Spec.html" title="interface in org.gradle.api.specs">Spec</a>&lt;? super <a href="NamedDomainObjectList.html" title="type parameter in NamedDomainObjectList">T</a>&gt;&nbsp;spec)</pre>
<div class="block">Returns a collection which contains the objects in this collection which meet the given specification. The
 returned collection is live, so that when matching objects are added to this collection, they are also visible in
 the filtered collection.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="DomainObjectCollection.html#matching-org.gradle.api.specs.Spec-">matching</a></code>&nbsp;in interface&nbsp;<code><a href="DomainObjectCollection.html" title="interface in org.gradle.api">DomainObjectCollection</a>&lt;<a href="NamedDomainObjectList.html" title="type parameter in NamedDomainObjectList">T</a>&gt;</code></dd>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="NamedDomainObjectCollection.html#matching-org.gradle.api.specs.Spec-">matching</a></code>&nbsp;in interface&nbsp;<code><a href="NamedDomainObjectCollection.html" title="interface in org.gradle.api">NamedDomainObjectCollection</a>&lt;<a href="NamedDomainObjectList.html" title="type parameter in NamedDomainObjectList">T</a>&gt;</code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>spec</code> - The specification to use.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>The collection of matching objects. Returns an empty collection if there are no such objects in this
         collection.</dd>
</dl>
</li>
</ul>
<a name="matching-groovy.lang.Closure-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>matching</h4>
<pre class="methodSignature"><a href="NamedDomainObjectList.html" title="interface in org.gradle.api">NamedDomainObjectList</a>&lt;<a href="NamedDomainObjectList.html" title="type parameter in NamedDomainObjectList">T</a>&gt;&nbsp;matching&#8203;(<a href="https://docs.groovy-lang.org/docs/groovy-3.0.17/html/gapi/groovy/lang/Closure.html?is-external=true" title="class or interface in groovy.lang" class="externalLink">Closure</a>&nbsp;spec)</pre>
<div class="block">Returns a collection which contains the objects in this collection which meet the given closure specification. The
 returned collection is live, so that when matching objects are added to this collection, they are also visible in
 the filtered collection.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="DomainObjectCollection.html#matching-groovy.lang.Closure-">matching</a></code>&nbsp;in interface&nbsp;<code><a href="DomainObjectCollection.html" title="interface in org.gradle.api">DomainObjectCollection</a>&lt;<a href="NamedDomainObjectList.html" title="type parameter in NamedDomainObjectList">T</a>&gt;</code></dd>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="NamedDomainObjectCollection.html#matching-groovy.lang.Closure-">matching</a></code>&nbsp;in interface&nbsp;<code><a href="NamedDomainObjectCollection.html" title="interface in org.gradle.api">NamedDomainObjectCollection</a>&lt;<a href="NamedDomainObjectList.html" title="type parameter in NamedDomainObjectList">T</a>&gt;</code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>spec</code> - The specification to use. The closure gets a collection element as an argument.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>The collection of matching objects. Returns an empty collection if there are no such objects in this
         collection.</dd>
</dl>
</li>
</ul>
<a name="findAll-groovy.lang.Closure-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>findAll</h4>
<pre class="methodSignature">java.util.List&lt;<a href="NamedDomainObjectList.html" title="type parameter in NamedDomainObjectList">T</a>&gt;&nbsp;findAll&#8203;(<a href="https://docs.groovy-lang.org/docs/groovy-3.0.17/html/gapi/groovy/lang/Closure.html?is-external=true" title="class or interface in groovy.lang" class="externalLink">Closure</a>&nbsp;spec)</pre>
<div class="block">Returns a collection which contains the objects in this collection which meet the given closure specification.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="DomainObjectCollection.html#findAll-groovy.lang.Closure-">findAll</a></code>&nbsp;in interface&nbsp;<code><a href="DomainObjectCollection.html" title="interface in org.gradle.api">DomainObjectCollection</a>&lt;<a href="NamedDomainObjectList.html" title="type parameter in NamedDomainObjectList">T</a>&gt;</code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>spec</code> - The specification to use. The closure gets a collection element as an argument.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>The collection of matching objects. Returns an empty collection if there are no such objects in this
         collection.</dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../index-all.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../allclasses.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
