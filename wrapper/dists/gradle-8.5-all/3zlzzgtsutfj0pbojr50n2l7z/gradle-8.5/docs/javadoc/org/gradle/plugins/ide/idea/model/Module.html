<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc -->
<title>Mo<PERSON><PERSON> (Gradle API 8.5)</title>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<link rel="stylesheet" type="text/css" href="../../../../../../javadoc.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../../../jquery/jquery-ui.min.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../../../jquery-ui.overrides.css" title="Style">
<script type="text/javascript" src="../../../../../../script.js"></script>
<script type="text/javascript" src="../../../../../../jquery/jszip/dist/jszip.min.js"></script>
<script type="text/javascript" src="../../../../../../jquery/jszip-utils/dist/jszip-utils.min.js"></script>
<!--[if IE]>
<script type="text/javascript" src="../../../../../../jquery/jszip-utils/dist/jszip-utils-ie.min.js"></script>
<![endif]-->
<script type="text/javascript" src="../../../../../../jquery/jquery-3.6.1.min.js"></script>
<script type="text/javascript" src="../../../../../../jquery/jquery-ui.min.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="Module (Gradle API 8.5)";
        }
    }
    catch(err) {
    }
//-->
var data = {"i0":10,"i1":10,"i2":10,"i3":10,"i4":10,"i5":10,"i6":10,"i7":10,"i8":10,"i9":10,"i10":10,"i11":10,"i12":10,"i13":10,"i14":10,"i15":10,"i16":10,"i17":10,"i18":10,"i19":10,"i20":10,"i21":10,"i22":10,"i23":10,"i24":10,"i25":10,"i26":10,"i27":10,"i28":10,"i29":10,"i30":10,"i31":10};
var tabs = {65535:["t0","All Methods"],2:["t2","Instance Methods"],8:["t4","Concrete Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
var pathtoroot = "../../../../../../";
var useModuleDirectories = false;
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<div class="fixedNav">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../../index-all.html">Index</a></li>
<li><a href="../../../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../../../allclasses.html">All&nbsp;Classes</a></li>
</ul>
<ul class="navListSearch">
<li><label for="search">SEARCH:</label>
<input type="text" id="search" value="search" disabled="disabled">
<input type="reset" id="reset" value="reset" disabled="disabled">
</li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li><a href="#field.summary">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li><a href="#field.detail">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
</div>
<div class="navPadding">&nbsp;</div>
<script type="text/javascript"><!--
$('.navPadding').css('padding-top', $('.fixedNav').css("height"));
//-->
</script>
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle"><span class="packageLabelInType">Package</span>&nbsp;<a href="package-summary.html">org.gradle.plugins.ide.idea.model</a></div>
<h2 title="Class Module" class="title">Class Module</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li>org.gradle.plugins.ide.internal.generator.AbstractPersistableConfigurationObject</li>
<li>
<ul class="inheritance">
<li>org.gradle.plugins.ide.internal.generator.XmlPersistableConfigurationObject</li>
<li>
<ul class="inheritance">
<li>org.gradle.plugins.ide.idea.model.Module</li>
</ul>
</li>
</ul>
</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt>All Implemented Interfaces:</dt>
<dd><code>org.gradle.plugins.ide.internal.generator.generator.PersistableConfigurationObject</code></dd>
</dl>
<hr>
<pre>public class <span class="typeNameLabel">Module</span>
extends org.gradle.plugins.ide.internal.generator.XmlPersistableConfigurationObject</pre>
<div class="block">Represents the customizable elements of an iml (via XML hooks everything of the iml is customizable).</div>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- =========== FIELD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="field.summary">
<!--   -->
</a>
<h3>Field Summary</h3>
<table class="memberSummary" summary="Field Summary table, listing fields, and an explanation">
<caption><span>Fields</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colSecond" scope="col">Field</th>
<th class="colLast" scope="col">Description</th>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#INHERITED">INHERITED</a></span></code></th>
<td class="colLast">&nbsp;</td>
</tr>
</table>
</li>
</ul>
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.summary">
<!--   -->
</a>
<h3>Constructor Summary</h3>
<table class="memberSummary" summary="Constructor Summary table, listing constructors, and an explanation">
<caption><span>Constructors</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Constructor</th>
<th class="colLast" scope="col">Description</th>
</tr>
<tr class="altColor">
<th class="colConstructorName" scope="row"><code><span class="memberNameLink"><a href="#Module-org.gradle.internal.xml.XmlTransformer-org.gradle.plugins.ide.idea.model.PathFactory-">Module</a></span>&#8203;(org.gradle.internal.xml.XmlTransformer&nbsp;withXmlActions,
      <a href="PathFactory.html" title="class in org.gradle.plugins.ide.idea.model">PathFactory</a>&nbsp;pathFactory)</code></th>
<td class="colLast">&nbsp;</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colSecond" scope="col">Method</th>
<th class="colLast" scope="col">Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>protected java.lang.Object</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#configure-org.gradle.plugins.ide.idea.model.Path-java.util.Set-java.util.Set-java.util.Set-java.util.Set-java.util.Set-java.util.Set-java.lang.Boolean-org.gradle.plugins.ide.idea.model.Path-org.gradle.plugins.ide.idea.model.Path-java.util.Set-java.lang.String-java.lang.String-">configure</a></span>&#8203;(<a href="Path.html" title="class in org.gradle.plugins.ide.idea.model">Path</a>&nbsp;contentPath,
         java.util.Set&lt;<a href="Path.html" title="class in org.gradle.plugins.ide.idea.model">Path</a>&gt;&nbsp;sourceFolders,
         java.util.Set&lt;<a href="Path.html" title="class in org.gradle.plugins.ide.idea.model">Path</a>&gt;&nbsp;testSourceFolders,
         java.util.Set&lt;<a href="Path.html" title="class in org.gradle.plugins.ide.idea.model">Path</a>&gt;&nbsp;resourceFolders,
         java.util.Set&lt;<a href="Path.html" title="class in org.gradle.plugins.ide.idea.model">Path</a>&gt;&nbsp;testResourceFolders,
         java.util.Set&lt;<a href="Path.html" title="class in org.gradle.plugins.ide.idea.model">Path</a>&gt;&nbsp;generatedSourceFolders,
         java.util.Set&lt;<a href="Path.html" title="class in org.gradle.plugins.ide.idea.model">Path</a>&gt;&nbsp;excludeFolders,
         java.lang.Boolean&nbsp;inheritOutputDirs,
         <a href="Path.html" title="class in org.gradle.plugins.ide.idea.model">Path</a>&nbsp;outputDir,
         <a href="Path.html" title="class in org.gradle.plugins.ide.idea.model">Path</a>&nbsp;testOutputDir,
         java.util.Set&lt;<a href="Dependency.html" title="interface in org.gradle.plugins.ide.idea.model">Dependency</a>&gt;&nbsp;dependencies,
         java.lang.String&nbsp;jdkName,
         java.lang.String&nbsp;languageLevel)</code></th>
<td class="colLast">&nbsp;</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#equals-java.lang.Object-">equals</a></span>&#8203;(java.lang.Object&nbsp;o)</code></th>
<td class="colLast">&nbsp;</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code><a href="Path.html" title="class in org.gradle.plugins.ide.idea.model">Path</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getContentPath--">getContentPath</a></span>()</code></th>
<td class="colLast">
<div class="block">The directory for the content root of the module.</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>protected java.lang.String</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getDefaultResourceName--">getDefaultResourceName</a></span>()</code></th>
<td class="colLast">&nbsp;</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code>java.util.Set&lt;<a href="Dependency.html" title="interface in org.gradle.plugins.ide.idea.model">Dependency</a>&gt;</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getDependencies--">getDependencies</a></span>()</code></th>
<td class="colLast">
<div class="block">The dependencies of this module.</div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code>java.util.Set&lt;<a href="Path.html" title="class in org.gradle.plugins.ide.idea.model">Path</a>&gt;</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getExcludeFolders--">getExcludeFolders</a></span>()</code></th>
<td class="colLast">
<div class="block">The directories to be excluded.</div>
</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code>java.util.Set&lt;<a href="Path.html" title="class in org.gradle.plugins.ide.idea.model">Path</a>&gt;</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getGeneratedSourceFolders--">getGeneratedSourceFolders</a></span>()</code></th>
<td class="colLast">
<div class="block">The directories containing generated the production sources.</div>
</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code>java.lang.String</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getJdkName--">getJdkName</a></span>()</code></th>
<td class="colLast">&nbsp;</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code><a href="Path.html" title="class in org.gradle.plugins.ide.idea.model">Path</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getOutputDir--">getOutputDir</a></span>()</code></th>
<td class="colLast">
<div class="block">The output directory for production classes.</div>
</td>
</tr>
<tr id="i9" class="rowColor">
<td class="colFirst"><code>java.util.Set&lt;<a href="Path.html" title="class in org.gradle.plugins.ide.idea.model">Path</a>&gt;</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getResourceFolders--">getResourceFolders</a></span>()</code></th>
<td class="colLast">
<div class="block">The directories containing resources.</div>
</td>
</tr>
<tr id="i10" class="altColor">
<td class="colFirst"><code>java.util.Set&lt;<a href="Path.html" title="class in org.gradle.plugins.ide.idea.model">Path</a>&gt;</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getSourceFolders--">getSourceFolders</a></span>()</code></th>
<td class="colLast">
<div class="block">The directories containing the production sources.</div>
</td>
</tr>
<tr id="i11" class="rowColor">
<td class="colFirst"><code><a href="Path.html" title="class in org.gradle.plugins.ide.idea.model">Path</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getTestOutputDir--">getTestOutputDir</a></span>()</code></th>
<td class="colLast">
<div class="block">The output directory for test classes.</div>
</td>
</tr>
<tr id="i12" class="altColor">
<td class="colFirst"><code>java.util.Set&lt;<a href="Path.html" title="class in org.gradle.plugins.ide.idea.model">Path</a>&gt;</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getTestResourceFolders--">getTestResourceFolders</a></span>()</code></th>
<td class="colLast">
<div class="block">The directories containing test resources.</div>
</td>
</tr>
<tr id="i13" class="rowColor">
<td class="colFirst"><code>java.util.Set&lt;<a href="Path.html" title="class in org.gradle.plugins.ide.idea.model">Path</a>&gt;</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getTestSourceFolders--">getTestSourceFolders</a></span>()</code></th>
<td class="colLast">
<div class="block">The directories containing the test sources.</div>
</td>
</tr>
<tr id="i14" class="altColor">
<td class="colFirst"><code>int</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#hashCode--">hashCode</a></span>()</code></th>
<td class="colLast">&nbsp;</td>
</tr>
<tr id="i15" class="rowColor">
<td class="colFirst"><code>protected boolean</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#isDependencyOrderEntry-java.lang.Object-">isDependencyOrderEntry</a></span>&#8203;(java.lang.Object&nbsp;orderEntry)</code></th>
<td class="colLast">&nbsp;</td>
</tr>
<tr id="i16" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#isInheritOutputDirs--">isInheritOutputDirs</a></span>()</code></th>
<td class="colLast">
<div class="block">If true, output directories for this module will be located below the output directory for the project;
 otherwise, <a href="#outputDir"><code>outputDir</code></a> and <a href="#testOutputDir"><code>testOutputDir</code></a> will take effect.</div>
</td>
</tr>
<tr id="i17" class="rowColor">
<td class="colFirst"><code>protected void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#load-groovy.util.Node-">load</a></span>&#8203;(<a href="https://docs.groovy-lang.org/docs/groovy-3.0.17/html/gapi/groovy/util/Node.html?is-external=true" title="class or interface in groovy.util" class="externalLink">Node</a>&nbsp;xml)</code></th>
<td class="colLast">&nbsp;</td>
</tr>
<tr id="i18" class="altColor">
<td class="colFirst"><code>void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#setContentPath-org.gradle.plugins.ide.idea.model.Path-">setContentPath</a></span>&#8203;(<a href="Path.html" title="class in org.gradle.plugins.ide.idea.model">Path</a>&nbsp;contentPath)</code></th>
<td class="colLast">&nbsp;</td>
</tr>
<tr id="i19" class="rowColor">
<td class="colFirst"><code>void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#setDependencies-java.util.Set-">setDependencies</a></span>&#8203;(java.util.Set&lt;<a href="Dependency.html" title="interface in org.gradle.plugins.ide.idea.model">Dependency</a>&gt;&nbsp;dependencies)</code></th>
<td class="colLast">&nbsp;</td>
</tr>
<tr id="i20" class="altColor">
<td class="colFirst"><code>void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#setExcludeFolders-java.util.Set-">setExcludeFolders</a></span>&#8203;(java.util.Set&lt;<a href="Path.html" title="class in org.gradle.plugins.ide.idea.model">Path</a>&gt;&nbsp;excludeFolders)</code></th>
<td class="colLast">&nbsp;</td>
</tr>
<tr id="i21" class="rowColor">
<td class="colFirst"><code>void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#setGeneratedSourceFolders-java.util.Set-">setGeneratedSourceFolders</a></span>&#8203;(java.util.Set&lt;<a href="Path.html" title="class in org.gradle.plugins.ide.idea.model">Path</a>&gt;&nbsp;generatedSourceFolders)</code></th>
<td class="colLast">&nbsp;</td>
</tr>
<tr id="i22" class="altColor">
<td class="colFirst"><code>void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#setInheritOutputDirs-boolean-">setInheritOutputDirs</a></span>&#8203;(boolean&nbsp;inheritOutputDirs)</code></th>
<td class="colLast">&nbsp;</td>
</tr>
<tr id="i23" class="rowColor">
<td class="colFirst"><code>void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#setJdkName-java.lang.String-">setJdkName</a></span>&#8203;(java.lang.String&nbsp;jdkName)</code></th>
<td class="colLast">&nbsp;</td>
</tr>
<tr id="i24" class="altColor">
<td class="colFirst"><code>void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#setOutputDir-org.gradle.plugins.ide.idea.model.Path-">setOutputDir</a></span>&#8203;(<a href="Path.html" title="class in org.gradle.plugins.ide.idea.model">Path</a>&nbsp;outputDir)</code></th>
<td class="colLast">&nbsp;</td>
</tr>
<tr id="i25" class="rowColor">
<td class="colFirst"><code>void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#setResourceFolders-java.util.Set-">setResourceFolders</a></span>&#8203;(java.util.Set&lt;<a href="Path.html" title="class in org.gradle.plugins.ide.idea.model">Path</a>&gt;&nbsp;resourceFolders)</code></th>
<td class="colLast">
<div class="block">Sets the directories containing resources.</div>
</td>
</tr>
<tr id="i26" class="altColor">
<td class="colFirst"><code>void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#setSourceFolders-java.util.Set-">setSourceFolders</a></span>&#8203;(java.util.Set&lt;<a href="Path.html" title="class in org.gradle.plugins.ide.idea.model">Path</a>&gt;&nbsp;sourceFolders)</code></th>
<td class="colLast">&nbsp;</td>
</tr>
<tr id="i27" class="rowColor">
<td class="colFirst"><code>void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#setTestOutputDir-org.gradle.plugins.ide.idea.model.Path-">setTestOutputDir</a></span>&#8203;(<a href="Path.html" title="class in org.gradle.plugins.ide.idea.model">Path</a>&nbsp;testOutputDir)</code></th>
<td class="colLast">&nbsp;</td>
</tr>
<tr id="i28" class="altColor">
<td class="colFirst"><code>void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#setTestResourceFolders-java.util.Set-">setTestResourceFolders</a></span>&#8203;(java.util.Set&lt;<a href="Path.html" title="class in org.gradle.plugins.ide.idea.model">Path</a>&gt;&nbsp;testResourceFolders)</code></th>
<td class="colLast">
<div class="block">Sets the directories containing test resources.</div>
</td>
</tr>
<tr id="i29" class="rowColor">
<td class="colFirst"><code>void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#setTestSourceFolders-java.util.Set-">setTestSourceFolders</a></span>&#8203;(java.util.Set&lt;<a href="Path.html" title="class in org.gradle.plugins.ide.idea.model">Path</a>&gt;&nbsp;testSourceFolders)</code></th>
<td class="colLast">&nbsp;</td>
</tr>
<tr id="i30" class="altColor">
<td class="colFirst"><code>protected void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#store-groovy.util.Node-">store</a></span>&#8203;(<a href="https://docs.groovy-lang.org/docs/groovy-3.0.17/html/gapi/groovy/util/Node.html?is-external=true" title="class or interface in groovy.util" class="externalLink">Node</a>&nbsp;xml)</code></th>
<td class="colLast">&nbsp;</td>
</tr>
<tr id="i31" class="rowColor">
<td class="colFirst"><code>java.lang.String</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#toString--">toString</a></span>()</code></th>
<td class="colLast">&nbsp;</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.org.gradle.plugins.ide.internal.generator.XmlPersistableConfigurationObject">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;org.gradle.plugins.ide.internal.generator.XmlPersistableConfigurationObject</h3>
<code>findFirstChildNamed, findFirstChildWithAttributeValue, findFirstWithAttributeValue, findOrCreateFirstChildNamed, findOrCreateFirstChildWithAttributeValue, getChildren, getXml, load, store, transformAction, transformAction</code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.org.gradle.plugins.ide.internal.generator.AbstractPersistableConfigurationObject">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;org.gradle.plugins.ide.internal.generator.AbstractPersistableConfigurationObject</h3>
<code>load, loadDefaults, store</code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.Object</h3>
<code>clone, finalize, getClass, notify, notifyAll, wait, wait, wait</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ FIELD DETAIL =========== -->
<ul class="blockList">
<li class="blockList"><a name="field.detail">
<!--   -->
</a>
<h3>Field Detail</h3>
<a name="INHERITED">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>INHERITED</h4>
<pre>public static final&nbsp;java.lang.String INHERITED</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../../../../constant-values.html#org.gradle.plugins.ide.idea.model.Module.INHERITED">Constant Field Values</a></dd>
</dl>
</li>
</ul>
</li>
</ul>
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.detail">
<!--   -->
</a>
<h3>Constructor Detail</h3>
<a name="Module-org.gradle.internal.xml.XmlTransformer-org.gradle.plugins.ide.idea.model.PathFactory-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>Module</h4>
<pre>public&nbsp;Module&#8203;(org.gradle.internal.xml.XmlTransformer&nbsp;withXmlActions,
              <a href="PathFactory.html" title="class in org.gradle.plugins.ide.idea.model">PathFactory</a>&nbsp;pathFactory)</pre>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="getContentPath--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getContentPath</h4>
<pre class="methodSignature">public&nbsp;<a href="Path.html" title="class in org.gradle.plugins.ide.idea.model">Path</a>&nbsp;getContentPath()</pre>
<div class="block">The directory for the content root of the module.
 Defaults to the project directory.
 If null, the directory containing the output file will be used.</div>
</li>
</ul>
<a name="setContentPath-org.gradle.plugins.ide.idea.model.Path-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setContentPath</h4>
<pre class="methodSignature">public&nbsp;void&nbsp;setContentPath&#8203;(<a href="Path.html" title="class in org.gradle.plugins.ide.idea.model">Path</a>&nbsp;contentPath)</pre>
</li>
</ul>
<a name="getSourceFolders--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getSourceFolders</h4>
<pre class="methodSignature">public&nbsp;java.util.Set&lt;<a href="Path.html" title="class in org.gradle.plugins.ide.idea.model">Path</a>&gt;&nbsp;getSourceFolders()</pre>
<div class="block">The directories containing the production sources.
 Must not be null.</div>
</li>
</ul>
<a name="setSourceFolders-java.util.Set-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setSourceFolders</h4>
<pre class="methodSignature">public&nbsp;void&nbsp;setSourceFolders&#8203;(java.util.Set&lt;<a href="Path.html" title="class in org.gradle.plugins.ide.idea.model">Path</a>&gt;&nbsp;sourceFolders)</pre>
</li>
</ul>
<a name="getTestSourceFolders--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getTestSourceFolders</h4>
<pre class="methodSignature">public&nbsp;java.util.Set&lt;<a href="Path.html" title="class in org.gradle.plugins.ide.idea.model">Path</a>&gt;&nbsp;getTestSourceFolders()</pre>
<div class="block">The directories containing the test sources.
 Must not be null.</div>
</li>
</ul>
<a name="setTestSourceFolders-java.util.Set-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setTestSourceFolders</h4>
<pre class="methodSignature">public&nbsp;void&nbsp;setTestSourceFolders&#8203;(java.util.Set&lt;<a href="Path.html" title="class in org.gradle.plugins.ide.idea.model">Path</a>&gt;&nbsp;testSourceFolders)</pre>
</li>
</ul>
<a name="getResourceFolders--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getResourceFolders</h4>
<pre class="methodSignature">public&nbsp;java.util.Set&lt;<a href="Path.html" title="class in org.gradle.plugins.ide.idea.model">Path</a>&gt;&nbsp;getResourceFolders()</pre>
<div class="block">The directories containing resources.
 Must not be null.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>4.7</dd>
</dl>
</li>
</ul>
<a name="setResourceFolders-java.util.Set-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setResourceFolders</h4>
<pre class="methodSignature">public&nbsp;void&nbsp;setResourceFolders&#8203;(java.util.Set&lt;<a href="Path.html" title="class in org.gradle.plugins.ide.idea.model">Path</a>&gt;&nbsp;resourceFolders)</pre>
<div class="block">Sets the directories containing resources.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>4.7</dd>
</dl>
</li>
</ul>
<a name="getTestResourceFolders--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getTestResourceFolders</h4>
<pre class="methodSignature">public&nbsp;java.util.Set&lt;<a href="Path.html" title="class in org.gradle.plugins.ide.idea.model">Path</a>&gt;&nbsp;getTestResourceFolders()</pre>
<div class="block">The directories containing test resources.
 Must not be null.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>4.7</dd>
</dl>
</li>
</ul>
<a name="setTestResourceFolders-java.util.Set-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setTestResourceFolders</h4>
<pre class="methodSignature">public&nbsp;void&nbsp;setTestResourceFolders&#8203;(java.util.Set&lt;<a href="Path.html" title="class in org.gradle.plugins.ide.idea.model">Path</a>&gt;&nbsp;testResourceFolders)</pre>
<div class="block">Sets the directories containing test resources.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>4.7</dd>
</dl>
</li>
</ul>
<a name="getGeneratedSourceFolders--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getGeneratedSourceFolders</h4>
<pre class="methodSignature">public&nbsp;java.util.Set&lt;<a href="Path.html" title="class in org.gradle.plugins.ide.idea.model">Path</a>&gt;&nbsp;getGeneratedSourceFolders()</pre>
<div class="block">The directories containing generated the production sources.
 Must not be null.</div>
</li>
</ul>
<a name="setGeneratedSourceFolders-java.util.Set-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setGeneratedSourceFolders</h4>
<pre class="methodSignature">public&nbsp;void&nbsp;setGeneratedSourceFolders&#8203;(java.util.Set&lt;<a href="Path.html" title="class in org.gradle.plugins.ide.idea.model">Path</a>&gt;&nbsp;generatedSourceFolders)</pre>
</li>
</ul>
<a name="getExcludeFolders--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getExcludeFolders</h4>
<pre class="methodSignature">public&nbsp;java.util.Set&lt;<a href="Path.html" title="class in org.gradle.plugins.ide.idea.model">Path</a>&gt;&nbsp;getExcludeFolders()</pre>
<div class="block">The directories to be excluded.
 Must not be null.</div>
</li>
</ul>
<a name="setExcludeFolders-java.util.Set-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setExcludeFolders</h4>
<pre class="methodSignature">public&nbsp;void&nbsp;setExcludeFolders&#8203;(java.util.Set&lt;<a href="Path.html" title="class in org.gradle.plugins.ide.idea.model">Path</a>&gt;&nbsp;excludeFolders)</pre>
</li>
</ul>
<a name="isInheritOutputDirs--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isInheritOutputDirs</h4>
<pre class="methodSignature">public&nbsp;boolean&nbsp;isInheritOutputDirs()</pre>
<div class="block">If true, output directories for this module will be located below the output directory for the project;
 otherwise, <a href="#outputDir"><code>outputDir</code></a> and <a href="#testOutputDir"><code>testOutputDir</code></a> will take effect.</div>
</li>
</ul>
<a name="setInheritOutputDirs-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setInheritOutputDirs</h4>
<pre class="methodSignature">public&nbsp;void&nbsp;setInheritOutputDirs&#8203;(boolean&nbsp;inheritOutputDirs)</pre>
</li>
</ul>
<a name="getOutputDir--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getOutputDir</h4>
<pre class="methodSignature">public&nbsp;<a href="Path.html" title="class in org.gradle.plugins.ide.idea.model">Path</a>&nbsp;getOutputDir()</pre>
<div class="block">The output directory for production classes.
 If <code>null</code>, no entry will be created.</div>
</li>
</ul>
<a name="setOutputDir-org.gradle.plugins.ide.idea.model.Path-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setOutputDir</h4>
<pre class="methodSignature">public&nbsp;void&nbsp;setOutputDir&#8203;(<a href="Path.html" title="class in org.gradle.plugins.ide.idea.model">Path</a>&nbsp;outputDir)</pre>
</li>
</ul>
<a name="getTestOutputDir--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getTestOutputDir</h4>
<pre class="methodSignature">public&nbsp;<a href="Path.html" title="class in org.gradle.plugins.ide.idea.model">Path</a>&nbsp;getTestOutputDir()</pre>
<div class="block">The output directory for test classes.
 If <code>null</code>, no entry will be created.</div>
</li>
</ul>
<a name="setTestOutputDir-org.gradle.plugins.ide.idea.model.Path-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setTestOutputDir</h4>
<pre class="methodSignature">public&nbsp;void&nbsp;setTestOutputDir&#8203;(<a href="Path.html" title="class in org.gradle.plugins.ide.idea.model">Path</a>&nbsp;testOutputDir)</pre>
</li>
</ul>
<a name="getDependencies--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getDependencies</h4>
<pre class="methodSignature">public&nbsp;java.util.Set&lt;<a href="Dependency.html" title="interface in org.gradle.plugins.ide.idea.model">Dependency</a>&gt;&nbsp;getDependencies()</pre>
<div class="block">The dependencies of this module.
 Must not be null.</div>
</li>
</ul>
<a name="setDependencies-java.util.Set-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setDependencies</h4>
<pre class="methodSignature">public&nbsp;void&nbsp;setDependencies&#8203;(java.util.Set&lt;<a href="Dependency.html" title="interface in org.gradle.plugins.ide.idea.model">Dependency</a>&gt;&nbsp;dependencies)</pre>
</li>
</ul>
<a name="getJdkName--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getJdkName</h4>
<pre class="methodSignature">public&nbsp;java.lang.String&nbsp;getJdkName()</pre>
</li>
</ul>
<a name="setJdkName-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setJdkName</h4>
<pre class="methodSignature">public&nbsp;void&nbsp;setJdkName&#8203;(java.lang.String&nbsp;jdkName)</pre>
</li>
</ul>
<a name="getDefaultResourceName--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getDefaultResourceName</h4>
<pre class="methodSignature">protected&nbsp;java.lang.String&nbsp;getDefaultResourceName()</pre>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code>getDefaultResourceName</code>&nbsp;in class&nbsp;<code>org.gradle.plugins.ide.internal.generator.AbstractPersistableConfigurationObject</code></dd>
</dl>
</li>
</ul>
<a name="configure-org.gradle.plugins.ide.idea.model.Path-java.util.Set-java.util.Set-java.util.Set-java.util.Set-java.util.Set-java.util.Set-java.lang.Boolean-org.gradle.plugins.ide.idea.model.Path-org.gradle.plugins.ide.idea.model.Path-java.util.Set-java.lang.String-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>configure</h4>
<pre class="methodSignature">protected&nbsp;java.lang.Object&nbsp;configure&#8203;(<a href="Path.html" title="class in org.gradle.plugins.ide.idea.model">Path</a>&nbsp;contentPath,
                                     java.util.Set&lt;<a href="Path.html" title="class in org.gradle.plugins.ide.idea.model">Path</a>&gt;&nbsp;sourceFolders,
                                     java.util.Set&lt;<a href="Path.html" title="class in org.gradle.plugins.ide.idea.model">Path</a>&gt;&nbsp;testSourceFolders,
                                     java.util.Set&lt;<a href="Path.html" title="class in org.gradle.plugins.ide.idea.model">Path</a>&gt;&nbsp;resourceFolders,
                                     java.util.Set&lt;<a href="Path.html" title="class in org.gradle.plugins.ide.idea.model">Path</a>&gt;&nbsp;testResourceFolders,
                                     java.util.Set&lt;<a href="Path.html" title="class in org.gradle.plugins.ide.idea.model">Path</a>&gt;&nbsp;generatedSourceFolders,
                                     java.util.Set&lt;<a href="Path.html" title="class in org.gradle.plugins.ide.idea.model">Path</a>&gt;&nbsp;excludeFolders,
                                     java.lang.Boolean&nbsp;inheritOutputDirs,
                                     <a href="Path.html" title="class in org.gradle.plugins.ide.idea.model">Path</a>&nbsp;outputDir,
                                     <a href="Path.html" title="class in org.gradle.plugins.ide.idea.model">Path</a>&nbsp;testOutputDir,
                                     java.util.Set&lt;<a href="Dependency.html" title="interface in org.gradle.plugins.ide.idea.model">Dependency</a>&gt;&nbsp;dependencies,
                                     java.lang.String&nbsp;jdkName,
                                     java.lang.String&nbsp;languageLevel)</pre>
</li>
</ul>
<a name="load-groovy.util.Node-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>load</h4>
<pre class="methodSignature">protected&nbsp;void&nbsp;load&#8203;(<a href="https://docs.groovy-lang.org/docs/groovy-3.0.17/html/gapi/groovy/util/Node.html?is-external=true" title="class or interface in groovy.util" class="externalLink">Node</a>&nbsp;xml)</pre>
<dl>
<dt><span class="overrideSpecifyLabel">Overrides:</span></dt>
<dd><code>load</code>&nbsp;in class&nbsp;<code>org.gradle.plugins.ide.internal.generator.XmlPersistableConfigurationObject</code></dd>
</dl>
</li>
</ul>
<a name="store-groovy.util.Node-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>store</h4>
<pre class="methodSignature">protected&nbsp;void&nbsp;store&#8203;(<a href="https://docs.groovy-lang.org/docs/groovy-3.0.17/html/gapi/groovy/util/Node.html?is-external=true" title="class or interface in groovy.util" class="externalLink">Node</a>&nbsp;xml)</pre>
<dl>
<dt><span class="overrideSpecifyLabel">Overrides:</span></dt>
<dd><code>store</code>&nbsp;in class&nbsp;<code>org.gradle.plugins.ide.internal.generator.XmlPersistableConfigurationObject</code></dd>
</dl>
</li>
</ul>
<a name="isDependencyOrderEntry-java.lang.Object-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isDependencyOrderEntry</h4>
<pre class="methodSignature">protected&nbsp;boolean&nbsp;isDependencyOrderEntry&#8203;(java.lang.Object&nbsp;orderEntry)</pre>
</li>
</ul>
<a name="toString--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>toString</h4>
<pre class="methodSignature">public&nbsp;java.lang.String&nbsp;toString()</pre>
<dl>
<dt><span class="overrideSpecifyLabel">Overrides:</span></dt>
<dd><code>toString</code>&nbsp;in class&nbsp;<code>java.lang.Object</code></dd>
</dl>
</li>
</ul>
<a name="equals-java.lang.Object-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>equals</h4>
<pre class="methodSignature">public&nbsp;boolean&nbsp;equals&#8203;(java.lang.Object&nbsp;o)</pre>
<dl>
<dt><span class="overrideSpecifyLabel">Overrides:</span></dt>
<dd><code>equals</code>&nbsp;in class&nbsp;<code>java.lang.Object</code></dd>
</dl>
</li>
</ul>
<a name="hashCode--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>hashCode</h4>
<pre class="methodSignature">public&nbsp;int&nbsp;hashCode()</pre>
<dl>
<dt><span class="overrideSpecifyLabel">Overrides:</span></dt>
<dd><code>hashCode</code>&nbsp;in class&nbsp;<code>java.lang.Object</code></dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../../index-all.html">Index</a></li>
<li><a href="../../../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../../../allclasses.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li><a href="#field.summary">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li><a href="#field.detail">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
