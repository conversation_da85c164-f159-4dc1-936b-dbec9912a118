<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc -->
<title>LenientConfiguration (Gradle API 8.5)</title>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<link rel="stylesheet" type="text/css" href="../../../../javadoc.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../jquery/jquery-ui.min.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../jquery-ui.overrides.css" title="Style">
<script type="text/javascript" src="../../../../script.js"></script>
<script type="text/javascript" src="../../../../jquery/jszip/dist/jszip.min.js"></script>
<script type="text/javascript" src="../../../../jquery/jszip-utils/dist/jszip-utils.min.js"></script>
<!--[if IE]>
<script type="text/javascript" src="../../../../jquery/jszip-utils/dist/jszip-utils-ie.min.js"></script>
<![endif]-->
<script type="text/javascript" src="../../../../jquery/jquery-3.6.1.min.js"></script>
<script type="text/javascript" src="../../../../jquery/jquery-ui.min.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="LenientConfiguration (Gradle API 8.5)";
        }
    }
    catch(err) {
    }
//-->
var data = {"i0":6,"i1":6,"i2":6,"i3":6,"i4":6,"i5":6,"i6":6,"i7":6};
var tabs = {65535:["t0","All Methods"],2:["t2","Instance Methods"],4:["t3","Abstract Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
var pathtoroot = "../../../../";
var useModuleDirectories = false;
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<div class="fixedNav">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../allclasses.html">All&nbsp;Classes</a></li>
</ul>
<ul class="navListSearch">
<li><label for="search">SEARCH:</label>
<input type="text" id="search" value="search" disabled="disabled">
<input type="reset" id="reset" value="reset" disabled="disabled">
</li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
</div>
<div class="navPadding">&nbsp;</div>
<script type="text/javascript"><!--
$('.navPadding').css('padding-top', $('.fixedNav').css("height"));
//-->
</script>
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle"><span class="packageLabelInType">Package</span>&nbsp;<a href="package-summary.html">org.gradle.api.artifacts</a></div>
<h2 title="Interface LenientConfiguration" class="title">Interface LenientConfiguration</h2>
</div>
<div class="contentContainer">
<div class="description">
<ul class="blockList">
<li class="blockList">
<hr>
<pre>public interface <span class="typeNameLabel">LenientConfiguration</span></pre>
<div class="block">Resolved configuration that does not fail eagerly when some dependencies are not resolved, or some artifacts do not exist.</div>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t3" class="tableTab"><span><a href="javascript:show(4);">Abstract Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colSecond" scope="col">Method</th>
<th class="colLast" scope="col">Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>java.util.Set&lt;<a href="ResolvedDependency.html" title="interface in org.gradle.api.artifacts">ResolvedDependency</a>&gt;</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getAllModuleDependencies--">getAllModuleDependencies</a></span>()</code></th>
<td class="colLast">
<div class="block">Returns all successfully resolved dependencies including transitive dependencies.</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>java.util.Set&lt;<a href="ResolvedArtifact.html" title="interface in org.gradle.api.artifacts">ResolvedArtifact</a>&gt;</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getArtifacts--">getArtifacts</a></span>()</code></th>
<td class="colLast">
<div class="block">Gets successfully resolved artifacts.</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>java.util.Set&lt;<a href="ResolvedArtifact.html" title="interface in org.gradle.api.artifacts">ResolvedArtifact</a>&gt;</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getArtifacts-org.gradle.api.specs.Spec-">getArtifacts</a></span>&#8203;(<a href="../specs/Spec.html" title="interface in org.gradle.api.specs">Spec</a>&lt;? super <a href="Dependency.html" title="interface in org.gradle.api.artifacts">Dependency</a>&gt;&nbsp;dependencySpec)</code></th>
<td class="colLast">
<div class="block">Gets successfully resolved artifacts.</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>java.util.Set&lt;java.io.File&gt;</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getFiles--">getFiles</a></span>()</code></th>
<td class="colLast">
<div class="block">Returns successfully resolved files.</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code>java.util.Set&lt;java.io.File&gt;</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getFiles-org.gradle.api.specs.Spec-">getFiles</a></span>&#8203;(<a href="../specs/Spec.html" title="interface in org.gradle.api.specs">Spec</a>&lt;? super <a href="Dependency.html" title="interface in org.gradle.api.artifacts">Dependency</a>&gt;&nbsp;dependencySpec)</code></th>
<td class="colLast">
<div class="block">Returns successfully resolved files.</div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code>java.util.Set&lt;<a href="ResolvedDependency.html" title="interface in org.gradle.api.artifacts">ResolvedDependency</a>&gt;</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getFirstLevelModuleDependencies--">getFirstLevelModuleDependencies</a></span>()</code></th>
<td class="colLast">
<div class="block">Returns successfully resolved direct dependencies.</div>
</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code>java.util.Set&lt;<a href="ResolvedDependency.html" title="interface in org.gradle.api.artifacts">ResolvedDependency</a>&gt;</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getFirstLevelModuleDependencies-org.gradle.api.specs.Spec-">getFirstLevelModuleDependencies</a></span>&#8203;(<a href="../specs/Spec.html" title="interface in org.gradle.api.specs">Spec</a>&lt;? super <a href="Dependency.html" title="interface in org.gradle.api.artifacts">Dependency</a>&gt;&nbsp;dependencySpec)</code></th>
<td class="colLast">
<div class="block">Returns successfully resolved dependencies that match the given spec.</div>
</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code>java.util.Set&lt;<a href="UnresolvedDependency.html" title="interface in org.gradle.api.artifacts">UnresolvedDependency</a>&gt;</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getUnresolvedModuleDependencies--">getUnresolvedModuleDependencies</a></span>()</code></th>
<td class="colLast">
<div class="block">returns dependencies that were attempted to resolve but failed.</div>
</td>
</tr>
</table>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="getFirstLevelModuleDependencies--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getFirstLevelModuleDependencies</h4>
<pre class="methodSignature">java.util.Set&lt;<a href="ResolvedDependency.html" title="interface in org.gradle.api.artifacts">ResolvedDependency</a>&gt;&nbsp;getFirstLevelModuleDependencies()</pre>
<div class="block">Returns successfully resolved direct dependencies.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>only resolved dependencies</dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>3.3</dd>
</dl>
</li>
</ul>
<a name="getFirstLevelModuleDependencies-org.gradle.api.specs.Spec-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getFirstLevelModuleDependencies</h4>
<pre class="methodSignature">java.util.Set&lt;<a href="ResolvedDependency.html" title="interface in org.gradle.api.artifacts">ResolvedDependency</a>&gt;&nbsp;getFirstLevelModuleDependencies&#8203;(<a href="../specs/Spec.html" title="interface in org.gradle.api.specs">Spec</a>&lt;? super <a href="Dependency.html" title="interface in org.gradle.api.artifacts">Dependency</a>&gt;&nbsp;dependencySpec)</pre>
<div class="block">Returns successfully resolved dependencies that match the given spec.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>dependencySpec</code> - dependency spec</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>only resolved dependencies</dd>
</dl>
</li>
</ul>
<a name="getAllModuleDependencies--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getAllModuleDependencies</h4>
<pre class="methodSignature">java.util.Set&lt;<a href="ResolvedDependency.html" title="interface in org.gradle.api.artifacts">ResolvedDependency</a>&gt;&nbsp;getAllModuleDependencies()</pre>
<div class="block">Returns all successfully resolved dependencies including transitive dependencies.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>all resolved dependencies</dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>3.1</dd>
</dl>
</li>
</ul>
<a name="getUnresolvedModuleDependencies--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getUnresolvedModuleDependencies</h4>
<pre class="methodSignature">java.util.Set&lt;<a href="UnresolvedDependency.html" title="interface in org.gradle.api.artifacts">UnresolvedDependency</a>&gt;&nbsp;getUnresolvedModuleDependencies()</pre>
<div class="block">returns dependencies that were attempted to resolve but failed.
 If empty then all dependencies are neatly resolved.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>only unresolved dependencies</dd>
</dl>
</li>
</ul>
<a name="getFiles--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getFiles</h4>
<pre class="methodSignature">java.util.Set&lt;java.io.File&gt;&nbsp;getFiles()</pre>
<div class="block">Returns successfully resolved files. Ignores dependencies or files that cannot be resolved.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>resolved dependencies files</dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>3.3</dd>
</dl>
</li>
</ul>
<a name="getFiles-org.gradle.api.specs.Spec-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getFiles</h4>
<pre class="methodSignature">java.util.Set&lt;java.io.File&gt;&nbsp;getFiles&#8203;(<a href="../specs/Spec.html" title="interface in org.gradle.api.specs">Spec</a>&lt;? super <a href="Dependency.html" title="interface in org.gradle.api.artifacts">Dependency</a>&gt;&nbsp;dependencySpec)</pre>
<div class="block">Returns successfully resolved files. Ignores dependencies or files that cannot be resolved.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>dependencySpec</code> - dependency spec</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>resolved dependencies files</dd>
</dl>
</li>
</ul>
<a name="getArtifacts--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getArtifacts</h4>
<pre class="methodSignature">java.util.Set&lt;<a href="ResolvedArtifact.html" title="interface in org.gradle.api.artifacts">ResolvedArtifact</a>&gt;&nbsp;getArtifacts()</pre>
<div class="block">Gets successfully resolved artifacts. Ignores dependencies or files that cannot be resolved.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>successfully resolved artifacts</dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>3.3</dd>
</dl>
</li>
</ul>
<a name="getArtifacts-org.gradle.api.specs.Spec-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>getArtifacts</h4>
<pre class="methodSignature">java.util.Set&lt;<a href="ResolvedArtifact.html" title="interface in org.gradle.api.artifacts">ResolvedArtifact</a>&gt;&nbsp;getArtifacts&#8203;(<a href="../specs/Spec.html" title="interface in org.gradle.api.specs">Spec</a>&lt;? super <a href="Dependency.html" title="interface in org.gradle.api.artifacts">Dependency</a>&gt;&nbsp;dependencySpec)</pre>
<div class="block">Gets successfully resolved artifacts. Ignores dependencies or files that cannot be resolved.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>dependencySpec</code> - dependency spec</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>successfully resolved artifacts for dependencies that match given dependency spec</dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../allclasses.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
