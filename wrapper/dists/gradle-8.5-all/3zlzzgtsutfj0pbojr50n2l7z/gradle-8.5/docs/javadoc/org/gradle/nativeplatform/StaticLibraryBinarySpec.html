<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc -->
<title>StaticLibraryBinarySpec (Gradle API 8.5)</title>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<link rel="stylesheet" type="text/css" href="../../../javadoc.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../jquery/jquery-ui.min.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../jquery-ui.overrides.css" title="Style">
<script type="text/javascript" src="../../../script.js"></script>
<script type="text/javascript" src="../../../jquery/jszip/dist/jszip.min.js"></script>
<script type="text/javascript" src="../../../jquery/jszip-utils/dist/jszip-utils.min.js"></script>
<!--[if IE]>
<script type="text/javascript" src="../../../jquery/jszip-utils/dist/jszip-utils-ie.min.js"></script>
<![endif]-->
<script type="text/javascript" src="../../../jquery/jquery-3.6.1.min.js"></script>
<script type="text/javascript" src="../../../jquery/jquery-ui.min.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="StaticLibraryBinarySpec (Gradle API 8.5)";
        }
    }
    catch(err) {
    }
//-->
var data = {"i0":6,"i1":6,"i2":6,"i3":6};
var tabs = {65535:["t0","All Methods"],2:["t2","Instance Methods"],4:["t3","Abstract Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
var pathtoroot = "../../../";
var useModuleDirectories = false;
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<div class="fixedNav">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../index-all.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../allclasses.html">All&nbsp;Classes</a></li>
</ul>
<ul class="navListSearch">
<li><label for="search">SEARCH:</label>
<input type="text" id="search" value="search" disabled="disabled">
<input type="reset" id="reset" value="reset" disabled="disabled">
</li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li><a href="#nested.class.summary">Nested</a>&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
</div>
<div class="navPadding">&nbsp;</div>
<script type="text/javascript"><!--
$('.navPadding').css('padding-top', $('.fixedNav').css("height"));
//-->
</script>
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle"><span class="packageLabelInType">Package</span>&nbsp;<a href="package-summary.html">org.gradle.nativeplatform</a></div>
<h2 title="Interface StaticLibraryBinarySpec" class="title">Interface StaticLibraryBinarySpec</h2>
</div>
<div class="contentContainer">
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt>All Superinterfaces:</dt>
<dd><code><a href="../platform/base/Binary.html" title="interface in org.gradle.platform.base">Binary</a></code>, <code><a href="../platform/base/BinarySpec.html" title="interface in org.gradle.platform.base">BinarySpec</a></code>, <code><a href="../api/Buildable.html" title="interface in org.gradle.api">Buildable</a></code>, <code><a href="../api/BuildableComponentSpec.html" title="interface in org.gradle.api">BuildableComponentSpec</a></code>, <code><a href="../api/CheckableComponentSpec.html" title="interface in org.gradle.api">CheckableComponentSpec</a></code>, <code><a href="../platform/base/ComponentSpec.html" title="interface in org.gradle.platform.base">ComponentSpec</a></code>, <code><a href="../platform/base/LibraryBinarySpec.html" title="interface in org.gradle.platform.base">LibraryBinarySpec</a></code>, <code><a href="../model/ModelElement.html" title="interface in org.gradle.model">ModelElement</a></code>, <code><a href="../api/Named.html" title="interface in org.gradle.api">Named</a></code>, <code><a href="NativeBinarySpec.html" title="interface in org.gradle.nativeplatform">NativeBinarySpec</a></code>, <code><a href="NativeLibraryBinarySpec.html" title="interface in org.gradle.nativeplatform">NativeLibraryBinarySpec</a></code></dd>
</dl>
<hr>
<pre><a href="../api/Incubating.html" title="annotation in org.gradle.api">@Incubating</a>
public interface <span class="typeNameLabel">StaticLibraryBinarySpec</span>
extends <a href="NativeLibraryBinarySpec.html" title="interface in org.gradle.nativeplatform">NativeLibraryBinarySpec</a></pre>
<div class="block">A static library binary built by Gradle for a native library.</div>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ======== NESTED CLASS SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="nested.class.summary">
<!--   -->
</a>
<h3>Nested Class Summary</h3>
<table class="memberSummary" summary="Nested Class Summary table, listing nested classes, and an explanation">
<caption><span>Nested Classes</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colSecond" scope="col">Interface</th>
<th class="colLast" scope="col">Description</th>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static interface&nbsp;</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="StaticLibraryBinarySpec.TasksCollection.html" title="interface in org.gradle.nativeplatform">StaticLibraryBinarySpec.TasksCollection</a></span></code></th>
<td class="colLast">
<div class="block">Provides access to key tasks used for building the binary.</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="nested.classes.inherited.from.class.org.gradle.api.Named">
<!--   -->
</a>
<h3>Nested classes/interfaces inherited from interface&nbsp;org.gradle.api.<a href="../api/Named.html" title="interface in org.gradle.api">Named</a></h3>
<code><a href="../api/Named.Namer.html" title="class in org.gradle.api">Named.Namer</a></code></li>
</ul>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t3" class="tableTab"><span><a href="javascript:show(4);">Abstract Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colSecond" scope="col">Method</th>
<th class="colLast" scope="col">Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#additionalLinkFiles-org.gradle.api.file.FileCollection-">additionalLinkFiles</a></span>&#8203;(<a href="../api/file/FileCollection.html" title="interface in org.gradle.api.file">FileCollection</a>&nbsp;files)</code></th>
<td class="colLast">
<div class="block">Add some additional files required by consumers of this library at link time.</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>java.io.File</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getStaticLibraryFile--">getStaticLibraryFile</a></span>()</code></th>
<td class="colLast">
<div class="block">The static library file.</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code><a href="StaticLibraryBinarySpec.TasksCollection.html" title="interface in org.gradle.nativeplatform">StaticLibraryBinarySpec.TasksCollection</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getTasks--">getTasks</a></span>()</code></th>
<td class="colLast">
<div class="block">The set of tasks associated with this binary.</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#setStaticLibraryFile-java.io.File-">setStaticLibraryFile</a></span>&#8203;(java.io.File&nbsp;staticLibraryFile)</code></th>
<td class="colLast">
<div class="block">The static library binary file.</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.org.gradle.platform.base.Binary">
<!--   -->
</a>
<h3>Methods inherited from interface&nbsp;org.gradle.platform.base.<a href="../platform/base/Binary.html" title="interface in org.gradle.platform.base">Binary</a></h3>
<code><a href="../platform/base/Binary.html#getDisplayName--">getDisplayName</a></code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.org.gradle.platform.base.BinarySpec">
<!--   -->
</a>
<h3>Methods inherited from interface&nbsp;org.gradle.platform.base.<a href="../platform/base/BinarySpec.html" title="interface in org.gradle.platform.base">BinarySpec</a></h3>
<code><a href="../platform/base/BinarySpec.html#getInputs--">getInputs</a>, <a href="../platform/base/BinarySpec.html#getSources--">getSources</a>, <a href="../platform/base/BinarySpec.html#isBuildable--">isBuildable</a></code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.org.gradle.api.Buildable">
<!--   -->
</a>
<h3>Methods inherited from interface&nbsp;org.gradle.api.<a href="../api/Buildable.html" title="interface in org.gradle.api">Buildable</a></h3>
<code><a href="../api/Buildable.html#getBuildDependencies--">getBuildDependencies</a></code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.org.gradle.api.BuildableComponentSpec">
<!--   -->
</a>
<h3>Methods inherited from interface&nbsp;org.gradle.api.<a href="../api/BuildableComponentSpec.html" title="interface in org.gradle.api">BuildableComponentSpec</a></h3>
<code><a href="../api/BuildableComponentSpec.html#builtBy-java.lang.Object...-">builtBy</a>, <a href="../api/BuildableComponentSpec.html#getBuildTask--">getBuildTask</a>, <a href="../api/BuildableComponentSpec.html#hasBuildDependencies--">hasBuildDependencies</a>, <a href="../api/BuildableComponentSpec.html#setBuildTask-org.gradle.api.Task-">setBuildTask</a></code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.org.gradle.api.CheckableComponentSpec">
<!--   -->
</a>
<h3>Methods inherited from interface&nbsp;org.gradle.api.<a href="../api/CheckableComponentSpec.html" title="interface in org.gradle.api">CheckableComponentSpec</a></h3>
<code><a href="../api/CheckableComponentSpec.html#checkedBy-java.lang.Object...-">checkedBy</a>, <a href="../api/CheckableComponentSpec.html#getCheckTask--">getCheckTask</a>, <a href="../api/CheckableComponentSpec.html#setCheckTask-org.gradle.api.Task-">setCheckTask</a></code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.org.gradle.platform.base.ComponentSpec">
<!--   -->
</a>
<h3>Methods inherited from interface&nbsp;org.gradle.platform.base.<a href="../platform/base/ComponentSpec.html" title="interface in org.gradle.platform.base">ComponentSpec</a></h3>
<code><a href="../platform/base/ComponentSpec.html#getProjectPath--">getProjectPath</a></code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.org.gradle.model.ModelElement">
<!--   -->
</a>
<h3>Methods inherited from interface&nbsp;org.gradle.model.<a href="../model/ModelElement.html" title="interface in org.gradle.model">ModelElement</a></h3>
<code><a href="../model/ModelElement.html#getDisplayName--">getDisplayName</a>, <a href="../model/ModelElement.html#getName--">getName</a></code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.org.gradle.nativeplatform.NativeBinarySpec">
<!--   -->
</a>
<h3>Methods inherited from interface&nbsp;org.gradle.nativeplatform.<a href="NativeBinarySpec.html" title="interface in org.gradle.nativeplatform">NativeBinarySpec</a></h3>
<code><a href="NativeBinarySpec.html#getAssembler--">getAssembler</a>, <a href="NativeBinarySpec.html#getBuildType--">getBuildType</a>, <a href="NativeBinarySpec.html#getcCompiler--">getcCompiler</a>, <a href="NativeBinarySpec.html#getCppCompiler--">getCppCompiler</a>, <a href="NativeBinarySpec.html#getFlavor--">getFlavor</a>, <a href="NativeBinarySpec.html#getLibs--">getLibs</a>, <a href="NativeBinarySpec.html#getLinker--">getLinker</a>, <a href="NativeBinarySpec.html#getObjcCompiler--">getObjcCompiler</a>, <a href="NativeBinarySpec.html#getObjcppCompiler--">getObjcppCompiler</a>, <a href="NativeBinarySpec.html#getRcCompiler--">getRcCompiler</a>, <a href="NativeBinarySpec.html#getStaticLibArchiver--">getStaticLibArchiver</a>, <a href="NativeBinarySpec.html#getTargetPlatform--">getTargetPlatform</a>, <a href="NativeBinarySpec.html#getToolChain--">getToolChain</a>, <a href="NativeBinarySpec.html#lib-java.lang.Object-">lib</a></code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.org.gradle.nativeplatform.NativeLibraryBinarySpec">
<!--   -->
</a>
<h3>Methods inherited from interface&nbsp;org.gradle.nativeplatform.<a href="NativeLibraryBinarySpec.html" title="interface in org.gradle.nativeplatform">NativeLibraryBinarySpec</a></h3>
<code><a href="NativeLibraryBinarySpec.html#getComponent--">getComponent</a>, <a href="NativeLibraryBinarySpec.html#getLibrary--">getLibrary</a></code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="getStaticLibraryFile--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getStaticLibraryFile</h4>
<pre class="methodSignature">java.io.File&nbsp;getStaticLibraryFile()</pre>
<div class="block">The static library file.</div>
</li>
</ul>
<a name="setStaticLibraryFile-java.io.File-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setStaticLibraryFile</h4>
<pre class="methodSignature">void&nbsp;setStaticLibraryFile&#8203;(java.io.File&nbsp;staticLibraryFile)</pre>
<div class="block">The static library binary file.</div>
</li>
</ul>
<a name="additionalLinkFiles-org.gradle.api.file.FileCollection-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>additionalLinkFiles</h4>
<pre class="methodSignature">void&nbsp;additionalLinkFiles&#8203;(<a href="../api/file/FileCollection.html" title="interface in org.gradle.api.file">FileCollection</a>&nbsp;files)</pre>
<div class="block">Add some additional files required by consumers of this library at link time.</div>
</li>
</ul>
<a name="getTasks--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>getTasks</h4>
<pre class="methodSignature"><a href="StaticLibraryBinarySpec.TasksCollection.html" title="interface in org.gradle.nativeplatform">StaticLibraryBinarySpec.TasksCollection</a>&nbsp;getTasks()</pre>
<div class="block">The set of tasks associated with this binary.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../platform/base/BinarySpec.html#getTasks--">getTasks</a></code>&nbsp;in interface&nbsp;<code><a href="../platform/base/BinarySpec.html" title="interface in org.gradle.platform.base">BinarySpec</a></code></dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../index-all.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../allclasses.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li><a href="#nested.class.summary">Nested</a>&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
