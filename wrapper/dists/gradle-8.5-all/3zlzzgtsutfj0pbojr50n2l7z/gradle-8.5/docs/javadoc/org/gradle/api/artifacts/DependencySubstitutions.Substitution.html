<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc -->
<title>DependencySubstitutions.Substitution (Gradle API 8.5)</title>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<link rel="stylesheet" type="text/css" href="../../../../javadoc.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../jquery/jquery-ui.min.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../jquery-ui.overrides.css" title="Style">
<script type="text/javascript" src="../../../../script.js"></script>
<script type="text/javascript" src="../../../../jquery/jszip/dist/jszip.min.js"></script>
<script type="text/javascript" src="../../../../jquery/jszip-utils/dist/jszip-utils.min.js"></script>
<!--[if IE]>
<script type="text/javascript" src="../../../../jquery/jszip-utils/dist/jszip-utils-ie.min.js"></script>
<![endif]-->
<script type="text/javascript" src="../../../../jquery/jquery-3.6.1.min.js"></script>
<script type="text/javascript" src="../../../../jquery/jquery-ui.min.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="DependencySubstitutions.Substitution (Gradle API 8.5)";
        }
    }
    catch(err) {
    }
//-->
var data = {"i0":6,"i1":6,"i2":6,"i3":6,"i4":6};
var tabs = {65535:["t0","All Methods"],2:["t2","Instance Methods"],4:["t3","Abstract Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
var pathtoroot = "../../../../";
var useModuleDirectories = false;
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<div class="fixedNav">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../allclasses.html">All&nbsp;Classes</a></li>
</ul>
<ul class="navListSearch">
<li><label for="search">SEARCH:</label>
<input type="text" id="search" value="search" disabled="disabled">
<input type="reset" id="reset" value="reset" disabled="disabled">
</li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
</div>
<div class="navPadding">&nbsp;</div>
<script type="text/javascript"><!--
$('.navPadding').css('padding-top', $('.fixedNav').css("height"));
//-->
</script>
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle"><span class="packageLabelInType">Package</span>&nbsp;<a href="package-summary.html">org.gradle.api.artifacts</a></div>
<h2 title="Interface DependencySubstitutions.Substitution" class="title">Interface DependencySubstitutions.Substitution</h2>
</div>
<div class="contentContainer">
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt>Enclosing interface:</dt>
<dd><a href="DependencySubstitutions.html" title="interface in org.gradle.api.artifacts">DependencySubstitutions</a></dd>
</dl>
<hr>
<pre>public static interface <span class="typeNameLabel">DependencySubstitutions.Substitution</span></pre>
<div class="block">Provides a DSL-friendly mechanism for specifying the target of a substitution.</div>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t3" class="tableTab"><span><a href="javascript:show(4);">Abstract Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colSecond" scope="col">Method</th>
<th class="colLast" scope="col">Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code><a href="DependencySubstitutions.Substitution.html" title="interface in org.gradle.api.artifacts">DependencySubstitutions.Substitution</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#because-java.lang.String-">because</a></span>&#8203;(java.lang.String&nbsp;reason)</code></th>
<td class="colLast">
<div class="block">Specify a reason for the substitution.</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code><a href="DependencySubstitutions.Substitution.html" title="interface in org.gradle.api.artifacts">DependencySubstitutions.Substitution</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#using-org.gradle.api.artifacts.component.ComponentSelector-">using</a></span>&#8203;(<a href="component/ComponentSelector.html" title="interface in org.gradle.api.artifacts.component">ComponentSelector</a>&nbsp;notation)</code></th>
<td class="colLast">
<div class="block">Specify the target of the substitution.</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code><a href="DependencySubstitutions.Substitution.html" title="interface in org.gradle.api.artifacts">DependencySubstitutions.Substitution</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#withClassifier-java.lang.String-">withClassifier</a></span>&#8203;(java.lang.String&nbsp;classifier)</code></th>
<td class="colLast">
<div class="block">Specifies that the substituted target dependency should use the specified classifier.</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code><a href="DependencySubstitutions.Substitution.html" title="interface in org.gradle.api.artifacts">DependencySubstitutions.Substitution</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#withoutArtifactSelectors--">withoutArtifactSelectors</a></span>()</code></th>
<td class="colLast">
<div class="block">Specifies that substituted dependencies must not carry any artifact selector.</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code><a href="DependencySubstitutions.Substitution.html" title="interface in org.gradle.api.artifacts">DependencySubstitutions.Substitution</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#withoutClassifier--">withoutClassifier</a></span>()</code></th>
<td class="colLast">
<div class="block">Specifies that the substituted dependency mustn't have any classifier.</div>
</td>
</tr>
</table>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="because-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>because</h4>
<pre class="methodSignature"><a href="DependencySubstitutions.Substitution.html" title="interface in org.gradle.api.artifacts">DependencySubstitutions.Substitution</a>&nbsp;because&#8203;(java.lang.String&nbsp;reason)</pre>
<div class="block">Specify a reason for the substitution. This is optional</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>reason</code> - the reason for the selection</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>the substitution</dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>4.5</dd>
</dl>
</li>
</ul>
<a name="withClassifier-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>withClassifier</h4>
<pre class="methodSignature"><a href="DependencySubstitutions.Substitution.html" title="interface in org.gradle.api.artifacts">DependencySubstitutions.Substitution</a>&nbsp;withClassifier&#8203;(java.lang.String&nbsp;classifier)</pre>
<div class="block">Specifies that the substituted target dependency should use the specified classifier.

 This method assumes that the target dependency is a jar (type jar, extension jar).</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>6.6</dd>
</dl>
</li>
</ul>
<a name="withoutClassifier--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>withoutClassifier</h4>
<pre class="methodSignature"><a href="DependencySubstitutions.Substitution.html" title="interface in org.gradle.api.artifacts">DependencySubstitutions.Substitution</a>&nbsp;withoutClassifier()</pre>
<div class="block">Specifies that the substituted dependency mustn't have any classifier.
 It can be used whenever you need to substitute a dependency which uses a classifier into
 a dependency which doesn't.

 This method assumes that the target dependency is a jar (type jar, extension jar).</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>6.6</dd>
</dl>
</li>
</ul>
<a name="withoutArtifactSelectors--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>withoutArtifactSelectors</h4>
<pre class="methodSignature"><a href="DependencySubstitutions.Substitution.html" title="interface in org.gradle.api.artifacts">DependencySubstitutions.Substitution</a>&nbsp;withoutArtifactSelectors()</pre>
<div class="block">Specifies that substituted dependencies must not carry any artifact selector.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>6.6</dd>
</dl>
</li>
</ul>
<a name="using-org.gradle.api.artifacts.component.ComponentSelector-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>using</h4>
<pre class="methodSignature"><a href="DependencySubstitutions.Substitution.html" title="interface in org.gradle.api.artifacts">DependencySubstitutions.Substitution</a>&nbsp;using&#8203;(<a href="component/ComponentSelector.html" title="interface in org.gradle.api.artifacts.component">ComponentSelector</a>&nbsp;notation)</pre>
<div class="block">Specify the target of the substitution. This is a replacement for the prior <code>#with(ComponentSelector)</code>
 method which supports chaining.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>6.6</dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../allclasses.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
