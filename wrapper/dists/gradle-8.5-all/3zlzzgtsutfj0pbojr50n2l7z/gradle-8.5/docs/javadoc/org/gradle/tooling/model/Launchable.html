<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc -->
<title>Launchable (Gradle API 8.5)</title>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<link rel="stylesheet" type="text/css" href="../../../../javadoc.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../jquery/jquery-ui.min.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../jquery-ui.overrides.css" title="Style">
<script type="text/javascript" src="../../../../script.js"></script>
<script type="text/javascript" src="../../../../jquery/jszip/dist/jszip.min.js"></script>
<script type="text/javascript" src="../../../../jquery/jszip-utils/dist/jszip-utils.min.js"></script>
<!--[if IE]>
<script type="text/javascript" src="../../../../jquery/jszip-utils/dist/jszip-utils-ie.min.js"></script>
<![endif]-->
<script type="text/javascript" src="../../../../jquery/jquery-3.6.1.min.js"></script>
<script type="text/javascript" src="../../../../jquery/jquery-ui.min.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="Launchable (Gradle API 8.5)";
        }
    }
    catch(err) {
    }
//-->
var data = {"i0":6,"i1":6,"i2":6,"i3":6};
var tabs = {65535:["t0","All Methods"],2:["t2","Instance Methods"],4:["t3","Abstract Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
var pathtoroot = "../../../../";
var useModuleDirectories = false;
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<div class="fixedNav">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../allclasses.html">All&nbsp;Classes</a></li>
</ul>
<ul class="navListSearch">
<li><label for="search">SEARCH:</label>
<input type="text" id="search" value="search" disabled="disabled">
<input type="reset" id="reset" value="reset" disabled="disabled">
</li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
</div>
<div class="navPadding">&nbsp;</div>
<script type="text/javascript"><!--
$('.navPadding').css('padding-top', $('.fixedNav').css("height"));
//-->
</script>
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle"><span class="packageLabelInType">Package</span>&nbsp;<a href="package-summary.html">org.gradle.tooling.model</a></div>
<h2 title="Interface Launchable" class="title">Interface Launchable</h2>
</div>
<div class="contentContainer">
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt>All Superinterfaces:</dt>
<dd><code><a href="ProjectModel.html" title="interface in org.gradle.tooling.model">ProjectModel</a></code></dd>
</dl>
<dl>
<dt>All Known Subinterfaces:</dt>
<dd><code><a href="GradleTask.html" title="interface in org.gradle.tooling.model">GradleTask</a></code>, <code><a href="Task.html" title="interface in org.gradle.tooling.model">Task</a></code>, <code><a href="TaskSelector.html" title="interface in org.gradle.tooling.model">TaskSelector</a></code></dd>
</dl>
<hr>
<pre>public interface <span class="typeNameLabel">Launchable</span>
extends <a href="ProjectModel.html" title="interface in org.gradle.tooling.model">ProjectModel</a></pre>
<div class="block">Represents an object that can be used to launch a Gradle build, such as a task.

 <p>To launch a build, you pass one or more <a href="Launchable.html" title="interface in org.gradle.tooling.model"><code>Launchable</code></a> instances
 to either <a href="../BuildLauncher.html#forTasks-java.lang.Iterable-"><code>BuildLauncher.forTasks(Iterable)</code></a> or <a href="../BuildLauncher.html#forLaunchables-java.lang.Iterable-"><code>BuildLauncher.forLaunchables(Iterable)</code></a>.</p></div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>1.12</dd>
</dl>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t3" class="tableTab"><span><a href="javascript:show(4);">Abstract Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colSecond" scope="col">Method</th>
<th class="colLast" scope="col">Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>java.lang.String</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getDescription--">getDescription</a></span>()</code></th>
<td class="colLast">
<div class="block">Returns the description of this launchable, or <code>null</code> if it has no description.</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>java.lang.String</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getDisplayName--">getDisplayName</a></span>()</code></th>
<td class="colLast">
<div class="block">Returns a human-consumable display name for this launchable.</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code><a href="ProjectIdentifier.html" title="interface in org.gradle.tooling.model">ProjectIdentifier</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getProjectIdentifier--">getProjectIdentifier</a></span>()</code></th>
<td class="colLast">
<div class="block">Returns the identifier for the Gradle project that this model originated from.</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#isPublic--">isPublic</a></span>()</code></th>
<td class="colLast">
<div class="block">Returns whether launchable is public or not.</div>
</td>
</tr>
</table>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="getProjectIdentifier--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getProjectIdentifier</h4>
<pre class="methodSignature"><a href="ProjectIdentifier.html" title="interface in org.gradle.tooling.model">ProjectIdentifier</a>&nbsp;getProjectIdentifier()</pre>
<div class="block">Returns the identifier for the Gradle project that this model originated from.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="ProjectModel.html#getProjectIdentifier--">getProjectIdentifier</a></code>&nbsp;in interface&nbsp;<code><a href="ProjectModel.html" title="interface in org.gradle.tooling.model">ProjectModel</a></code></dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>2.13</dd>
</dl>
</li>
</ul>
<a name="getDisplayName--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getDisplayName</h4>
<pre class="methodSignature">java.lang.String&nbsp;getDisplayName()</pre>
<div class="block">Returns a human-consumable display name for this launchable.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>Display name of this launchable.</dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>1.12</dd>
</dl>
</li>
</ul>
<a name="getDescription--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getDescription</h4>
<pre class="methodSignature">@Nullable
java.lang.String&nbsp;getDescription()</pre>
<div class="block">Returns the description of this launchable, or <code>null</code> if it has no description.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>The description of this launchable, or <code>null</code> if it has no description.</dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>1.12</dd>
</dl>
</li>
</ul>
<a name="isPublic--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>isPublic</h4>
<pre class="methodSignature">boolean&nbsp;isPublic()</pre>
<div class="block">Returns whether launchable is public or not. A public launchable is one that is considered a public 'entry point' to the build, that is interesting for
 an end user of the build to run.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>Public property.</dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>2.1</dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../allclasses.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
