<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc -->
<title>org.gradle.api.publish Class Hierarchy (Gradle API 8.5)</title>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<link rel="stylesheet" type="text/css" href="../../../../javadoc.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../jquery/jquery-ui.min.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../jquery-ui.overrides.css" title="Style">
<script type="text/javascript" src="../../../../script.js"></script>
<script type="text/javascript" src="../../../../jquery/jszip/dist/jszip.min.js"></script>
<script type="text/javascript" src="../../../../jquery/jszip-utils/dist/jszip-utils.min.js"></script>
<!--[if IE]>
<script type="text/javascript" src="../../../../jquery/jszip-utils/dist/jszip-utils-ie.min.js"></script>
<![endif]-->
<script type="text/javascript" src="../../../../jquery/jquery-3.6.1.min.js"></script>
<script type="text/javascript" src="../../../../jquery/jquery-ui.min.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="org.gradle.api.publish Class Hierarchy (Gradle API 8.5)";
        }
    }
    catch(err) {
    }
//-->
var pathtoroot = "../../../../";
var useModuleDirectories = false;
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<div class="fixedNav">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li>Class</li>
<li class="navBarCell1Rev">Tree</li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../allclasses.html">All&nbsp;Classes</a></li>
</ul>
<ul class="navListSearch">
<li><label for="search">SEARCH:</label>
<input type="text" id="search" value="search" disabled="disabled">
<input type="reset" id="reset" value="reset" disabled="disabled">
</li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
</div>
<div class="navPadding">&nbsp;</div>
<script type="text/javascript"><!--
$('.navPadding').css('padding-top', $('.fixedNav').css("height"));
//-->
</script>
<div class="header">
<h1 class="title">Hierarchy For Package org.gradle.api.publish</h1>
<span class="packageHierarchyLabel">Package Hierarchies:</span>
<ul class="horizontal">
<li><a href="../../../../overview-tree.html">All Packages</a></li>
</ul>
</div>
<div class="contentContainer">
<h2 title="Interface Hierarchy">Interface Hierarchy</h2>
<ul>
<li class="circle">org.gradle.api.<a href="../Buildable.html" title="interface in org.gradle.api"><span class="typeNameLink">Buildable</span></a>
<ul>
<li class="circle">org.gradle.api.publish.<a href="PublicationArtifact.html" title="interface in org.gradle.api.publish"><span class="typeNameLink">PublicationArtifact</span></a></li>
</ul>
</li>
<li class="circle">org.gradle.util.<a href="../../util/Configurable.html" title="interface in org.gradle.util"><span class="typeNameLink">Configurable</span></a>&lt;T&gt;
<ul>
<li class="circle">org.gradle.api.<a href="../NamedDomainObjectContainer.html" title="interface in org.gradle.api"><span class="typeNameLink">NamedDomainObjectContainer</span></a>&lt;T&gt; (also extends org.gradle.api.<a href="../NamedDomainObjectSet.html" title="interface in org.gradle.api">NamedDomainObjectSet</a>&lt;T&gt;)
<ul>
<li class="circle">org.gradle.api.<a href="../PolymorphicDomainObjectContainer.html" title="interface in org.gradle.api"><span class="typeNameLink">PolymorphicDomainObjectContainer</span></a>&lt;T&gt;
<ul>
<li class="circle">org.gradle.api.<a href="../ExtensiblePolymorphicDomainObjectContainer.html" title="interface in org.gradle.api"><span class="typeNameLink">ExtensiblePolymorphicDomainObjectContainer</span></a>&lt;T&gt; (also extends org.gradle.api.internal.rules.NamedDomainObjectFactoryRegistry&lt;T&gt;)
<ul>
<li class="circle">org.gradle.api.publish.<a href="PublicationContainer.html" title="interface in org.gradle.api.publish"><span class="typeNameLink">PublicationContainer</span></a></li>
</ul>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</li>
<li class="circle">java.lang.Iterable&lt;T&gt;
<ul>
<li class="circle">java.util.Collection&lt;E&gt;
<ul>
<li class="circle">org.gradle.api.<a href="../DomainObjectCollection.html" title="interface in org.gradle.api"><span class="typeNameLink">DomainObjectCollection</span></a>&lt;T&gt;
<ul>
<li class="circle">org.gradle.api.<a href="../DomainObjectSet.html" title="interface in org.gradle.api"><span class="typeNameLink">DomainObjectSet</span></a>&lt;T&gt; (also extends java.util.Set&lt;E&gt;)
<ul>
<li class="circle">org.gradle.api.<a href="../NamedDomainObjectSet.html" title="interface in org.gradle.api"><span class="typeNameLink">NamedDomainObjectSet</span></a>&lt;T&gt; (also extends org.gradle.api.<a href="../NamedDomainObjectCollection.html" title="interface in org.gradle.api">NamedDomainObjectCollection</a>&lt;T&gt;)
<ul>
<li class="circle">org.gradle.api.<a href="../NamedDomainObjectContainer.html" title="interface in org.gradle.api"><span class="typeNameLink">NamedDomainObjectContainer</span></a>&lt;T&gt; (also extends org.gradle.util.<a href="../../util/Configurable.html" title="interface in org.gradle.util">Configurable</a>&lt;T&gt;)
<ul>
<li class="circle">org.gradle.api.<a href="../PolymorphicDomainObjectContainer.html" title="interface in org.gradle.api"><span class="typeNameLink">PolymorphicDomainObjectContainer</span></a>&lt;T&gt;
<ul>
<li class="circle">org.gradle.api.<a href="../ExtensiblePolymorphicDomainObjectContainer.html" title="interface in org.gradle.api"><span class="typeNameLink">ExtensiblePolymorphicDomainObjectContainer</span></a>&lt;T&gt; (also extends org.gradle.api.internal.rules.NamedDomainObjectFactoryRegistry&lt;T&gt;)
<ul>
<li class="circle">org.gradle.api.publish.<a href="PublicationContainer.html" title="interface in org.gradle.api.publish"><span class="typeNameLink">PublicationContainer</span></a></li>
</ul>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</li>
<li class="circle">org.gradle.api.<a href="../NamedDomainObjectCollection.html" title="interface in org.gradle.api"><span class="typeNameLink">NamedDomainObjectCollection</span></a>&lt;T&gt;
<ul>
<li class="circle">org.gradle.api.<a href="../NamedDomainObjectSet.html" title="interface in org.gradle.api"><span class="typeNameLink">NamedDomainObjectSet</span></a>&lt;T&gt; (also extends org.gradle.api.<a href="../DomainObjectSet.html" title="interface in org.gradle.api">DomainObjectSet</a>&lt;T&gt;)
<ul>
<li class="circle">org.gradle.api.<a href="../NamedDomainObjectContainer.html" title="interface in org.gradle.api"><span class="typeNameLink">NamedDomainObjectContainer</span></a>&lt;T&gt; (also extends org.gradle.util.<a href="../../util/Configurable.html" title="interface in org.gradle.util">Configurable</a>&lt;T&gt;)
<ul>
<li class="circle">org.gradle.api.<a href="../PolymorphicDomainObjectContainer.html" title="interface in org.gradle.api"><span class="typeNameLink">PolymorphicDomainObjectContainer</span></a>&lt;T&gt;
<ul>
<li class="circle">org.gradle.api.<a href="../ExtensiblePolymorphicDomainObjectContainer.html" title="interface in org.gradle.api"><span class="typeNameLink">ExtensiblePolymorphicDomainObjectContainer</span></a>&lt;T&gt; (also extends org.gradle.api.internal.rules.NamedDomainObjectFactoryRegistry&lt;T&gt;)
<ul>
<li class="circle">org.gradle.api.publish.<a href="PublicationContainer.html" title="interface in org.gradle.api.publish"><span class="typeNameLink">PublicationContainer</span></a></li>
</ul>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</li>
<li class="circle">java.util.Set&lt;E&gt;
<ul>
<li class="circle">org.gradle.api.<a href="../DomainObjectSet.html" title="interface in org.gradle.api"><span class="typeNameLink">DomainObjectSet</span></a>&lt;T&gt; (also extends org.gradle.api.<a href="../DomainObjectCollection.html" title="interface in org.gradle.api">DomainObjectCollection</a>&lt;T&gt;)
<ul>
<li class="circle">org.gradle.api.<a href="../NamedDomainObjectSet.html" title="interface in org.gradle.api"><span class="typeNameLink">NamedDomainObjectSet</span></a>&lt;T&gt; (also extends org.gradle.api.<a href="../NamedDomainObjectCollection.html" title="interface in org.gradle.api">NamedDomainObjectCollection</a>&lt;T&gt;)
<ul>
<li class="circle">org.gradle.api.<a href="../NamedDomainObjectContainer.html" title="interface in org.gradle.api"><span class="typeNameLink">NamedDomainObjectContainer</span></a>&lt;T&gt; (also extends org.gradle.util.<a href="../../util/Configurable.html" title="interface in org.gradle.util">Configurable</a>&lt;T&gt;)
<ul>
<li class="circle">org.gradle.api.<a href="../PolymorphicDomainObjectContainer.html" title="interface in org.gradle.api"><span class="typeNameLink">PolymorphicDomainObjectContainer</span></a>&lt;T&gt;
<ul>
<li class="circle">org.gradle.api.<a href="../ExtensiblePolymorphicDomainObjectContainer.html" title="interface in org.gradle.api"><span class="typeNameLink">ExtensiblePolymorphicDomainObjectContainer</span></a>&lt;T&gt; (also extends org.gradle.api.internal.rules.NamedDomainObjectFactoryRegistry&lt;T&gt;)
<ul>
<li class="circle">org.gradle.api.publish.<a href="PublicationContainer.html" title="interface in org.gradle.api.publish"><span class="typeNameLink">PublicationContainer</span></a></li>
</ul>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</li>
<li class="circle">org.gradle.api.<a href="../Named.html" title="interface in org.gradle.api"><span class="typeNameLink">Named</span></a>
<ul>
<li class="circle">org.gradle.api.publish.<a href="Publication.html" title="interface in org.gradle.api.publish"><span class="typeNameLink">Publication</span></a></li>
</ul>
</li>
<li class="circle">org.gradle.api.internal.rules.NamedDomainObjectFactoryRegistry&lt;T&gt;
<ul>
<li class="circle">org.gradle.api.<a href="../ExtensiblePolymorphicDomainObjectContainer.html" title="interface in org.gradle.api"><span class="typeNameLink">ExtensiblePolymorphicDomainObjectContainer</span></a>&lt;T&gt; (also extends org.gradle.api.<a href="../PolymorphicDomainObjectContainer.html" title="interface in org.gradle.api">PolymorphicDomainObjectContainer</a>&lt;T&gt;)
<ul>
<li class="circle">org.gradle.api.publish.<a href="PublicationContainer.html" title="interface in org.gradle.api.publish"><span class="typeNameLink">PublicationContainer</span></a></li>
</ul>
</li>
</ul>
</li>
<li class="circle">org.gradle.api.publish.<a href="PublishingExtension.html" title="interface in org.gradle.api.publish"><span class="typeNameLink">PublishingExtension</span></a></li>
<li class="circle">org.gradle.api.publish.<a href="VariantVersionMappingStrategy.html" title="interface in org.gradle.api.publish"><span class="typeNameLink">VariantVersionMappingStrategy</span></a></li>
<li class="circle">org.gradle.api.publish.<a href="VersionMappingStrategy.html" title="interface in org.gradle.api.publish"><span class="typeNameLink">VersionMappingStrategy</span></a></li>
</ul>
</div>
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li>Class</li>
<li class="navBarCell1Rev">Tree</li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../allclasses.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
