<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc -->
<title>ExternalModuleDependency (Gradle API 8.5)</title>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<link rel="stylesheet" type="text/css" href="../../../../javadoc.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../jquery/jquery-ui.min.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../jquery-ui.overrides.css" title="Style">
<script type="text/javascript" src="../../../../script.js"></script>
<script type="text/javascript" src="../../../../jquery/jszip/dist/jszip.min.js"></script>
<script type="text/javascript" src="../../../../jquery/jszip-utils/dist/jszip-utils.min.js"></script>
<!--[if IE]>
<script type="text/javascript" src="../../../../jquery/jszip-utils/dist/jszip-utils-ie.min.js"></script>
<![endif]-->
<script type="text/javascript" src="../../../../jquery/jquery-3.6.1.min.js"></script>
<script type="text/javascript" src="../../../../jquery/jquery-ui.min.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="ExternalModuleDependency (Gradle API 8.5)";
        }
    }
    catch(err) {
    }
//-->
var data = {"i0":6,"i1":6,"i2":6};
var tabs = {65535:["t0","All Methods"],2:["t2","Instance Methods"],4:["t3","Abstract Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
var pathtoroot = "../../../../";
var useModuleDirectories = false;
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<div class="fixedNav">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../allclasses.html">All&nbsp;Classes</a></li>
</ul>
<ul class="navListSearch">
<li><label for="search">SEARCH:</label>
<input type="text" id="search" value="search" disabled="disabled">
<input type="reset" id="reset" value="reset" disabled="disabled">
</li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li><a href="#field.summary">Field</a>&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
</div>
<div class="navPadding">&nbsp;</div>
<script type="text/javascript"><!--
$('.navPadding').css('padding-top', $('.fixedNav').css("height"));
//-->
</script>
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle"><span class="packageLabelInType">Package</span>&nbsp;<a href="package-summary.html">org.gradle.api.artifacts</a></div>
<h2 title="Interface ExternalModuleDependency" class="title">Interface ExternalModuleDependency</h2>
</div>
<div class="contentContainer">
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt>All Superinterfaces:</dt>
<dd><code><a href="Dependency.html" title="interface in org.gradle.api.artifacts">Dependency</a></code>, <code><a href="ExternalDependency.html" title="interface in org.gradle.api.artifacts">ExternalDependency</a></code>, <code><a href="../attributes/HasAttributes.html" title="interface in org.gradle.api.attributes">HasAttributes</a></code>, <code><a href="../attributes/HasConfigurableAttributes.html" title="interface in org.gradle.api.attributes">HasConfigurableAttributes</a>&lt;<a href="ModuleDependency.html" title="interface in org.gradle.api.artifacts">ModuleDependency</a>&gt;</code>, <code><a href="ModuleDependency.html" title="interface in org.gradle.api.artifacts">ModuleDependency</a></code>, <code><a href="ModuleVersionSelector.html" title="interface in org.gradle.api.artifacts">ModuleVersionSelector</a></code></dd>
</dl>
<dl>
<dt>All Known Subinterfaces:</dt>
<dd><code><a href="ClientModule.html" title="interface in org.gradle.api.artifacts">ClientModule</a></code>, <code><a href="MinimalExternalModuleDependency.html" title="interface in org.gradle.api.artifacts">MinimalExternalModuleDependency</a></code></dd>
</dl>
<hr>
<pre>public interface <span class="typeNameLabel">ExternalModuleDependency</span>
extends <a href="ExternalDependency.html" title="interface in org.gradle.api.artifacts">ExternalDependency</a></pre>
<div class="block"><p>A <code>ModuleDependency</code> is a <a href="Dependency.html" title="interface in org.gradle.api.artifacts"><code>Dependency</code></a> on a module outside the current project hierarchy.</p></div>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- =========== FIELD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="field.summary">
<!--   -->
</a>
<h3>Field Summary</h3>
<ul class="blockList">
<li class="blockList"><a name="fields.inherited.from.class.org.gradle.api.artifacts.Dependency">
<!--   -->
</a>
<h3>Fields inherited from interface&nbsp;org.gradle.api.artifacts.<a href="Dependency.html" title="interface in org.gradle.api.artifacts">Dependency</a></h3>
<code><a href="Dependency.html#ARCHIVES_CONFIGURATION">ARCHIVES_CONFIGURATION</a>, <a href="Dependency.html#CLASSIFIER">CLASSIFIER</a>, <a href="Dependency.html#DEFAULT_CONFIGURATION">DEFAULT_CONFIGURATION</a></code></li>
</ul>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t3" class="tableTab"><span><a href="javascript:show(4);">Abstract Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colSecond" scope="col">Method</th>
<th class="colLast" scope="col">Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code><a href="ExternalModuleDependency.html" title="interface in org.gradle.api.artifacts">ExternalModuleDependency</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#copy--">copy</a></span>()</code></th>
<td class="colLast">
<div class="block">Creates and returns a new dependency with the property values of this one.</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#isChanging--">isChanging</a></span>()</code></th>
<td class="colLast">
<div class="block">Returns whether or not Gradle should always check for a change in the remote repository.</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code><a href="ExternalModuleDependency.html" title="interface in org.gradle.api.artifacts">ExternalModuleDependency</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#setChanging-boolean-">setChanging</a></span>&#8203;(boolean&nbsp;changing)</code></th>
<td class="colLast">
<div class="block">Sets whether or not Gradle should always check for a change in the remote repository.</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.org.gradle.api.artifacts.Dependency">
<!--   -->
</a>
<h3>Methods inherited from interface&nbsp;org.gradle.api.artifacts.<a href="Dependency.html" title="interface in org.gradle.api.artifacts">Dependency</a></h3>
<code><a href="Dependency.html#because-java.lang.String-">because</a>, <a href="Dependency.html#contentEquals-org.gradle.api.artifacts.Dependency-">contentEquals</a>, <a href="Dependency.html#getGroup--">getGroup</a>, <a href="Dependency.html#getName--">getName</a>, <a href="Dependency.html#getReason--">getReason</a>, <a href="Dependency.html#getVersion--">getVersion</a></code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.org.gradle.api.artifacts.ExternalDependency">
<!--   -->
</a>
<h3>Methods inherited from interface&nbsp;org.gradle.api.artifacts.<a href="ExternalDependency.html" title="interface in org.gradle.api.artifacts">ExternalDependency</a></h3>
<code><a href="ExternalDependency.html#getVersionConstraint--">getVersionConstraint</a>, <a href="ExternalDependency.html#isForce--">isForce</a>, <a href="ExternalDependency.html#version-org.gradle.api.Action-">version</a></code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.org.gradle.api.artifacts.ModuleDependency">
<!--   -->
</a>
<h3>Methods inherited from interface&nbsp;org.gradle.api.artifacts.<a href="ModuleDependency.html" title="interface in org.gradle.api.artifacts">ModuleDependency</a></h3>
<code><a href="ModuleDependency.html#addArtifact-org.gradle.api.artifacts.DependencyArtifact-">addArtifact</a>, <a href="ModuleDependency.html#artifact-groovy.lang.Closure-">artifact</a>, <a href="ModuleDependency.html#artifact-org.gradle.api.Action-">artifact</a>, <a href="ModuleDependency.html#attributes-org.gradle.api.Action-">attributes</a>, <a href="ModuleDependency.html#capabilities-org.gradle.api.Action-">capabilities</a>, <a href="ModuleDependency.html#doNotEndorseStrictVersions--">doNotEndorseStrictVersions</a>, <a href="ModuleDependency.html#endorseStrictVersions--">endorseStrictVersions</a>, <a href="ModuleDependency.html#exclude-java.util.Map-">exclude</a>, <a href="ModuleDependency.html#getArtifacts--">getArtifacts</a>, <a href="ModuleDependency.html#getAttributes--">getAttributes</a>, <a href="ModuleDependency.html#getExcludeRules--">getExcludeRules</a>, <a href="ModuleDependency.html#getRequestedCapabilities--">getRequestedCapabilities</a>, <a href="ModuleDependency.html#getTargetConfiguration--">getTargetConfiguration</a>, <a href="ModuleDependency.html#isEndorsingStrictVersions--">isEndorsingStrictVersions</a>, <a href="ModuleDependency.html#isTransitive--">isTransitive</a>, <a href="ModuleDependency.html#setTargetConfiguration-java.lang.String-">setTargetConfiguration</a>, <a href="ModuleDependency.html#setTransitive-boolean-">setTransitive</a></code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.org.gradle.api.artifacts.ModuleVersionSelector">
<!--   -->
</a>
<h3>Methods inherited from interface&nbsp;org.gradle.api.artifacts.<a href="ModuleVersionSelector.html" title="interface in org.gradle.api.artifacts">ModuleVersionSelector</a></h3>
<code><a href="ModuleVersionSelector.html#getGroup--">getGroup</a>, <a href="ModuleVersionSelector.html#getModule--">getModule</a>, <a href="ModuleVersionSelector.html#getName--">getName</a>, <a href="ModuleVersionSelector.html#getVersion--">getVersion</a>, <a href="ModuleVersionSelector.html#matchesStrictly-org.gradle.api.artifacts.ModuleVersionIdentifier-">matchesStrictly</a></code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="isChanging--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isChanging</h4>
<pre class="methodSignature">boolean&nbsp;isChanging()</pre>
<div class="block">Returns whether or not Gradle should always check for a change in the remote repository.</div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="#setChanging-boolean-"><code>setChanging(boolean)</code></a></dd>
</dl>
</li>
</ul>
<a name="setChanging-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setChanging</h4>
<pre class="methodSignature"><a href="ExternalModuleDependency.html" title="interface in org.gradle.api.artifacts">ExternalModuleDependency</a>&nbsp;setChanging&#8203;(boolean&nbsp;changing)</pre>
<div class="block">Sets whether or not Gradle should always check for a change in the remote repository. If set to true, Gradle will
 check the remote repository even if a dependency with the same version is already in the local cache. Defaults to
 false.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>changing</code> - Whether or not Gradle should always check for a change in the remote repository</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>this</dd>
</dl>
</li>
</ul>
<a name="copy--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>copy</h4>
<pre class="methodSignature"><a href="ExternalModuleDependency.html" title="interface in org.gradle.api.artifacts">ExternalModuleDependency</a>&nbsp;copy()</pre>
<div class="block">Creates and returns a new dependency with the property values of this one.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="Dependency.html#copy--">copy</a></code>&nbsp;in interface&nbsp;<code><a href="Dependency.html" title="interface in org.gradle.api.artifacts">Dependency</a></code></dd>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="ExternalDependency.html#copy--">copy</a></code>&nbsp;in interface&nbsp;<code><a href="ExternalDependency.html" title="interface in org.gradle.api.artifacts">ExternalDependency</a></code></dd>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="ModuleDependency.html#copy--">copy</a></code>&nbsp;in interface&nbsp;<code><a href="ModuleDependency.html" title="interface in org.gradle.api.artifacts">ModuleDependency</a></code></dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>The copy. Never returns null.</dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../allclasses.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li><a href="#field.summary">Field</a>&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
