<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc -->
<title>NativeBinary (Gradle API 8.5)</title>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<link rel="stylesheet" type="text/css" href="../../../javadoc.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../jquery/jquery-ui.min.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../jquery-ui.overrides.css" title="Style">
<script type="text/javascript" src="../../../script.js"></script>
<script type="text/javascript" src="../../../jquery/jszip/dist/jszip.min.js"></script>
<script type="text/javascript" src="../../../jquery/jszip-utils/dist/jszip-utils.min.js"></script>
<!--[if IE]>
<script type="text/javascript" src="../../../jquery/jszip-utils/dist/jszip-utils-ie.min.js"></script>
<![endif]-->
<script type="text/javascript" src="../../../jquery/jquery-3.6.1.min.js"></script>
<script type="text/javascript" src="../../../jquery/jquery-ui.min.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="NativeBinary (Gradle API 8.5)";
        }
    }
    catch(err) {
    }
//-->
var data = {"i0":6,"i1":6,"i2":6};
var tabs = {65535:["t0","All Methods"],2:["t2","Instance Methods"],4:["t3","Abstract Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
var pathtoroot = "../../../";
var useModuleDirectories = false;
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<div class="fixedNav">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../index-all.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../allclasses.html">All&nbsp;Classes</a></li>
</ul>
<ul class="navListSearch">
<li><label for="search">SEARCH:</label>
<input type="text" id="search" value="search" disabled="disabled">
<input type="reset" id="reset" value="reset" disabled="disabled">
</li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
</div>
<div class="navPadding">&nbsp;</div>
<script type="text/javascript"><!--
$('.navPadding').css('padding-top', $('.fixedNav').css("height"));
//-->
</script>
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle"><span class="packageLabelInType">Package</span>&nbsp;<a href="package-summary.html">org.gradle.nativeplatform</a></div>
<h2 title="Interface NativeBinary" class="title">Interface NativeBinary</h2>
</div>
<div class="contentContainer">
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt>All Superinterfaces:</dt>
<dd><code><a href="../platform/base/Binary.html" title="interface in org.gradle.platform.base">Binary</a></code></dd>
</dl>
<dl>
<dt>All Known Subinterfaces:</dt>
<dd><code><a href="NativeExecutableBinary.html" title="interface in org.gradle.nativeplatform">NativeExecutableBinary</a></code>, <code><a href="NativeLibraryBinary.html" title="interface in org.gradle.nativeplatform">NativeLibraryBinary</a></code>, <code><a href="PrebuiltSharedLibraryBinary.html" title="interface in org.gradle.nativeplatform">PrebuiltSharedLibraryBinary</a></code>, <code><a href="PrebuiltStaticLibraryBinary.html" title="interface in org.gradle.nativeplatform">PrebuiltStaticLibraryBinary</a></code>, <code><a href="SharedLibraryBinary.html" title="interface in org.gradle.nativeplatform">SharedLibraryBinary</a></code>, <code><a href="StaticLibraryBinary.html" title="interface in org.gradle.nativeplatform">StaticLibraryBinary</a></code></dd>
</dl>
<hr>
<pre><a href="../api/Incubating.html" title="annotation in org.gradle.api">@Incubating</a>
public interface <span class="typeNameLabel">NativeBinary</span>
extends <a href="../platform/base/Binary.html" title="interface in org.gradle.platform.base">Binary</a></pre>
<div class="block">Represents a particular binary artifact.</div>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t3" class="tableTab"><span><a href="javascript:show(4);">Abstract Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colSecond" scope="col">Method</th>
<th class="colLast" scope="col">Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code><a href="BuildType.html" title="interface in org.gradle.nativeplatform">BuildType</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getBuildType--">getBuildType</a></span>()</code></th>
<td class="colLast">
<div class="block">Returns the <a href="BuildType.html" title="interface in org.gradle.nativeplatform"><code>BuildType</code></a> used to construct this binary.</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code><a href="Flavor.html" title="interface in org.gradle.nativeplatform">Flavor</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getFlavor--">getFlavor</a></span>()</code></th>
<td class="colLast">
<div class="block">The <a href="Flavor.html" title="interface in org.gradle.nativeplatform"><code>Flavor</code></a> that this binary was built with.</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code><a href="platform/NativePlatform.html" title="interface in org.gradle.nativeplatform.platform">NativePlatform</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getTargetPlatform--">getTargetPlatform</a></span>()</code></th>
<td class="colLast">
<div class="block">Returns the <a href="platform/NativePlatform.html" title="interface in org.gradle.nativeplatform.platform"><code>NativePlatform</code></a> that this binary is targeted to run on.</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.org.gradle.platform.base.Binary">
<!--   -->
</a>
<h3>Methods inherited from interface&nbsp;org.gradle.platform.base.<a href="../platform/base/Binary.html" title="interface in org.gradle.platform.base">Binary</a></h3>
<code><a href="../platform/base/Binary.html#getDisplayName--">getDisplayName</a></code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="getFlavor--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getFlavor</h4>
<pre class="methodSignature"><a href="Flavor.html" title="interface in org.gradle.nativeplatform">Flavor</a>&nbsp;getFlavor()</pre>
<div class="block">The <a href="Flavor.html" title="interface in org.gradle.nativeplatform"><code>Flavor</code></a> that this binary was built with.</div>
</li>
</ul>
<a name="getTargetPlatform--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getTargetPlatform</h4>
<pre class="methodSignature"><a href="platform/NativePlatform.html" title="interface in org.gradle.nativeplatform.platform">NativePlatform</a>&nbsp;getTargetPlatform()</pre>
<div class="block">Returns the <a href="platform/NativePlatform.html" title="interface in org.gradle.nativeplatform.platform"><code>NativePlatform</code></a> that this binary is targeted to run on.</div>
</li>
</ul>
<a name="getBuildType--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>getBuildType</h4>
<pre class="methodSignature"><a href="BuildType.html" title="interface in org.gradle.nativeplatform">BuildType</a>&nbsp;getBuildType()</pre>
<div class="block">Returns the <a href="BuildType.html" title="interface in org.gradle.nativeplatform"><code>BuildType</code></a> used to construct this binary.</div>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../index-all.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../allclasses.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
