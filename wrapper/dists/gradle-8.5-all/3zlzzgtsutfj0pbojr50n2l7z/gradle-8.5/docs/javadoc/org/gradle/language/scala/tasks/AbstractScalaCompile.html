<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc -->
<title>AbstractScalaCompile (Gradle API 8.5)</title>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<link rel="stylesheet" type="text/css" href="../../../../../javadoc.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../../jquery/jquery-ui.min.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../../jquery-ui.overrides.css" title="Style">
<script type="text/javascript" src="../../../../../script.js"></script>
<script type="text/javascript" src="../../../../../jquery/jszip/dist/jszip.min.js"></script>
<script type="text/javascript" src="../../../../../jquery/jszip-utils/dist/jszip-utils.min.js"></script>
<!--[if IE]>
<script type="text/javascript" src="../../../../../jquery/jszip-utils/dist/jszip-utils-ie.min.js"></script>
<![endif]-->
<script type="text/javascript" src="../../../../../jquery/jquery-3.6.1.min.js"></script>
<script type="text/javascript" src="../../../../../jquery/jquery-ui.min.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="AbstractScalaCompile (Gradle API 8.5)";
        }
    }
    catch(err) {
    }
//-->
var data = {"i0":10,"i1":10,"i2":10,"i3":10,"i4":6,"i5":6,"i6":6,"i7":10,"i8":6,"i9":10,"i10":6,"i11":10,"i12":6,"i13":10,"i14":10,"i15":10};
var tabs = {65535:["t0","All Methods"],2:["t2","Instance Methods"],4:["t3","Abstract Methods"],8:["t4","Concrete Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
var pathtoroot = "../../../../../";
var useModuleDirectories = false;
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<div class="fixedNav">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../index-all.html">Index</a></li>
<li><a href="../../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../../allclasses.html">All&nbsp;Classes</a></li>
</ul>
<ul class="navListSearch">
<li><label for="search">SEARCH:</label>
<input type="text" id="search" value="search" disabled="disabled">
<input type="reset" id="reset" value="reset" disabled="disabled">
</li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li><a href="#nested.class.summary">Nested</a>&nbsp;|&nbsp;</li>
<li><a href="#field.summary">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li><a href="#field.detail">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
</div>
<div class="navPadding">&nbsp;</div>
<script type="text/javascript"><!--
$('.navPadding').css('padding-top', $('.fixedNav').css("height"));
//-->
</script>
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle"><span class="packageLabelInType">Package</span>&nbsp;<a href="package-summary.html">org.gradle.language.scala.tasks</a></div>
<h2 title="Class AbstractScalaCompile" class="title">Class AbstractScalaCompile</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li>org.gradle.api.internal.AbstractTask</li>
<li>
<ul class="inheritance">
<li><a href="../../../api/DefaultTask.html" title="class in org.gradle.api">org.gradle.api.DefaultTask</a></li>
<li>
<ul class="inheritance">
<li>org.gradle.api.internal.ConventionTask</li>
<li>
<ul class="inheritance">
<li><a href="../../../api/tasks/SourceTask.html" title="class in org.gradle.api.tasks">org.gradle.api.tasks.SourceTask</a></li>
<li>
<ul class="inheritance">
<li><a href="../../../api/tasks/compile/AbstractCompile.html" title="class in org.gradle.api.tasks.compile">org.gradle.api.tasks.compile.AbstractCompile</a></li>
<li>
<ul class="inheritance">
<li>org.gradle.language.scala.tasks.AbstractScalaCompile</li>
</ul>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt>All Implemented Interfaces:</dt>
<dd><code>java.lang.Comparable&lt;<a href="../../../api/Task.html" title="interface in org.gradle.api">Task</a>&gt;</code>, <code>org.gradle.api.internal.DynamicObjectAware</code>, <code>org.gradle.api.internal.IConventionAware</code>, <code>org.gradle.api.internal.TaskInternal</code>, <code>org.gradle.api.internal.tasks.compile.HasCompileOptions</code>, <code><a href="../../../api/plugins/ExtensionAware.html" title="interface in org.gradle.api.plugins">ExtensionAware</a></code>, <code><a href="../../../api/Task.html" title="interface in org.gradle.api">Task</a></code>, <code><a href="../../../api/tasks/util/PatternFilterable.html" title="interface in org.gradle.api.tasks.util">PatternFilterable</a></code>, <code><a href="../../../util/Configurable.html" title="interface in org.gradle.util">Configurable</a>&lt;<a href="../../../api/Task.html" title="interface in org.gradle.api">Task</a>&gt;</code></dd>
</dl>
<dl>
<dt>Direct Known Subclasses:</dt>
<dd><code><a href="../../../api/tasks/scala/ScalaCompile.html" title="class in org.gradle.api.tasks.scala">ScalaCompile</a></code></dd>
</dl>
<hr>
<pre><a href="../../../work/DisableCachingByDefault.html" title="annotation in org.gradle.work">@DisableCachingByDefault</a>(<a href="../../../work/DisableCachingByDefault.html#because--">because</a>="Abstract super-class, not to be instantiated directly")
public abstract class <span class="typeNameLabel">AbstractScalaCompile</span>
extends <a href="../../../api/tasks/compile/AbstractCompile.html" title="class in org.gradle.api.tasks.compile">AbstractCompile</a>
implements org.gradle.api.internal.tasks.compile.HasCompileOptions</pre>
<div class="block">An abstract Scala compile task sharing common functionality for compiling scala.</div>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ======== NESTED CLASS SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="nested.class.summary">
<!--   -->
</a>
<h3>Nested Class Summary</h3>
<ul class="blockList">
<li class="blockList"><a name="nested.classes.inherited.from.class.org.gradle.api.Task">
<!--   -->
</a>
<h3>Nested classes/interfaces inherited from interface&nbsp;org.gradle.api.<a href="../../../api/Task.html" title="interface in org.gradle.api">Task</a></h3>
<code><a href="../../../api/Task.Namer.html" title="class in org.gradle.api">Task.Namer</a></code></li>
</ul>
</li>
</ul>
<!-- =========== FIELD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="field.summary">
<!--   -->
</a>
<h3>Field Summary</h3>
<table class="memberSummary" summary="Field Summary table, listing fields, and an explanation">
<caption><span>Fields</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colSecond" scope="col">Field</th>
<th class="colLast" scope="col">Description</th>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected static <a href="../../../api/logging/Logger.html" title="interface in org.gradle.api.logging">Logger</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#LOGGER">LOGGER</a></span></code></th>
<td class="colLast">&nbsp;</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="fields.inherited.from.class.org.gradle.api.Task">
<!--   -->
</a>
<h3>Fields inherited from interface&nbsp;org.gradle.api.<a href="../../../api/Task.html" title="interface in org.gradle.api">Task</a></h3>
<code><a href="../../../api/Task.html#TASK_ACTION">TASK_ACTION</a>, <a href="../../../api/Task.html#TASK_CONSTRUCTOR_ARGS">TASK_CONSTRUCTOR_ARGS</a>, <a href="../../../api/Task.html#TASK_DEPENDS_ON">TASK_DEPENDS_ON</a>, <a href="../../../api/Task.html#TASK_DESCRIPTION">TASK_DESCRIPTION</a>, <a href="../../../api/Task.html#TASK_GROUP">TASK_GROUP</a>, <a href="../../../api/Task.html#TASK_NAME">TASK_NAME</a>, <a href="../../../api/Task.html#TASK_OVERWRITE">TASK_OVERWRITE</a>, <a href="../../../api/Task.html#TASK_TYPE">TASK_TYPE</a></code></li>
</ul>
</li>
</ul>
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.summary">
<!--   -->
</a>
<h3>Constructor Summary</h3>
<table class="memberSummary" summary="Constructor Summary table, listing constructors, and an explanation">
<caption><span>Constructors</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier</th>
<th class="colSecond" scope="col">Constructor</th>
<th class="colLast" scope="col">Description</th>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected </code></td>
<th class="colConstructorName" scope="row"><code><span class="memberNameLink"><a href="#AbstractScalaCompile--">AbstractScalaCompile</a></span>()</code></th>
<td class="colLast">
<div class="block">Constructor.</div>
</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t3" class="tableTab"><span><a href="javascript:show(4);">Abstract Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colSecond" scope="col">Method</th>
<th class="colLast" scope="col">Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#compile--">compile</a></span>()</code></th>
<td class="colLast">&nbsp;</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>protected org.gradle.api.internal.tasks.scala.ScalaJavaJointCompileSpec</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#createSpec--">createSpec</a></span>()</code></th>
<td class="colLast">&nbsp;</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code><a href="../../../api/file/ConfigurableFileCollection.html" title="interface in org.gradle.api.file">ConfigurableFileCollection</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getAnalysisFiles--">getAnalysisFiles</a></span>()</code></th>
<td class="colLast">
<div class="block">Source of analysis mapping files for incremental Scala compilation.</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code><a href="../../../api/file/RegularFileProperty.html" title="interface in org.gradle.api.file">RegularFileProperty</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getAnalysisMappingFile--">getAnalysisMappingFile</a></span>()</code></th>
<td class="colLast">
<div class="block">Analysis mapping file.</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code>protected abstract org.gradle.internal.classpath.CachedClasspathTransformer</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getCachedClasspathTransformer--">getCachedClasspathTransformer</a></span>()</code></th>
<td class="colLast">&nbsp;</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code>protected abstract org.gradle.language.base.internal.compile.Compiler&lt;org.gradle.api.internal.tasks.scala.ScalaJavaJointCompileSpec&gt;</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getCompiler-org.gradle.api.internal.tasks.scala.ScalaJavaJointCompileSpec-">getCompiler</a></span>&#8203;(org.gradle.api.internal.tasks.scala.ScalaJavaJointCompileSpec&nbsp;spec)</code></th>
<td class="colLast">&nbsp;</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code>protected abstract org.gradle.internal.file.Deleter</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getDeleter--">getDeleter</a></span>()</code></th>
<td class="colLast">&nbsp;</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code><a href="../../../api/provider/Property.html" title="interface in org.gradle.api.provider">Property</a>&lt;<a href="../../../jvm/toolchain/JavaLauncher.html" title="interface in org.gradle.jvm.toolchain">JavaLauncher</a>&gt;</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getJavaLauncher--">getJavaLauncher</a></span>()</code></th>
<td class="colLast">
<div class="block">The toolchain <a href="../../../jvm/toolchain/JavaLauncher.html" title="interface in org.gradle.jvm.toolchain"><code>JavaLauncher</code></a> to use for executing the Scala compiler.</div>
</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code>protected abstract <a href="../../../jvm/toolchain/JavaToolchainService.html" title="interface in org.gradle.jvm.toolchain">JavaToolchainService</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getJavaToolchainService--">getJavaToolchainService</a></span>()</code></th>
<td class="colLast">&nbsp;</td>
</tr>
<tr id="i9" class="rowColor">
<td class="colFirst"><code>protected java.lang.String</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getJvmVersion--">getJvmVersion</a></span>()</code></th>
<td class="colLast">
<div class="block">The Java major version of the JVM the Scala compiler is running on.</div>
</td>
</tr>
<tr id="i10" class="altColor">
<td class="colFirst"><code>protected abstract <a href="../../../api/model/ObjectFactory.html" title="interface in org.gradle.api.model">ObjectFactory</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getObjectFactory--">getObjectFactory</a></span>()</code></th>
<td class="colLast">&nbsp;</td>
</tr>
<tr id="i11" class="rowColor">
<td class="colFirst"><code><a href="../../../api/tasks/compile/CompileOptions.html" title="class in org.gradle.api.tasks.compile">CompileOptions</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getOptions--">getOptions</a></span>()</code></th>
<td class="colLast">
<div class="block">Returns the Java compilation options.</div>
</td>
</tr>
<tr id="i12" class="altColor">
<td class="colFirst"><code>protected abstract <a href="../../../api/file/ProjectLayout.html" title="interface in org.gradle.api.file">ProjectLayout</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getProjectLayout--">getProjectLayout</a></span>()</code></th>
<td class="colLast">&nbsp;</td>
</tr>
<tr id="i13" class="rowColor">
<td class="colFirst"><code><a href="BaseScalaCompileOptions.html" title="class in org.gradle.language.scala.tasks">BaseScalaCompileOptions</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getScalaCompileOptions--">getScalaCompileOptions</a></span>()</code></th>
<td class="colLast">
<div class="block">Returns the Scala compilation options.</div>
</td>
</tr>
<tr id="i14" class="altColor">
<td class="colFirst"><code><a href="../../../api/file/FileTree.html" title="interface in org.gradle.api.file">FileTree</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getSource--">getSource</a></span>()</code></th>
<td class="colLast">
<div class="block">Returns the source for this task, after the include and exclude patterns have been applied.</div>
</td>
</tr>
<tr id="i15" class="rowColor">
<td class="colFirst"><code>protected <a href="../../../jvm/toolchain/JavaInstallationMetadata.html" title="interface in org.gradle.jvm.toolchain">JavaInstallationMetadata</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getToolchain--">getToolchain</a></span>()</code></th>
<td class="colLast">&nbsp;</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.org.gradle.api.tasks.compile.AbstractCompile">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;org.gradle.api.tasks.compile.<a href="../../../api/tasks/compile/AbstractCompile.html" title="class in org.gradle.api.tasks.compile">AbstractCompile</a></h3>
<code><a href="../../../api/tasks/compile/AbstractCompile.html#getClasspath--">getClasspath</a>, <a href="../../../api/tasks/compile/AbstractCompile.html#getDestinationDir--">getDestinationDir</a>, <a href="../../../api/tasks/compile/AbstractCompile.html#getDestinationDirectory--">getDestinationDirectory</a>, <a href="../../../api/tasks/compile/AbstractCompile.html#getSourceCompatibility--">getSourceCompatibility</a>, <a href="../../../api/tasks/compile/AbstractCompile.html#getTargetCompatibility--">getTargetCompatibility</a>, <a href="../../../api/tasks/compile/AbstractCompile.html#setClasspath-org.gradle.api.file.FileCollection-">setClasspath</a>, <a href="../../../api/tasks/compile/AbstractCompile.html#setDestinationDir-java.io.File-">setDestinationDir</a>, <a href="../../../api/tasks/compile/AbstractCompile.html#setDestinationDir-org.gradle.api.provider.Provider-">setDestinationDir</a>, <a href="../../../api/tasks/compile/AbstractCompile.html#setSourceCompatibility-java.lang.String-">setSourceCompatibility</a>, <a href="../../../api/tasks/compile/AbstractCompile.html#setTargetCompatibility-java.lang.String-">setTargetCompatibility</a></code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.org.gradle.api.tasks.SourceTask">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;org.gradle.api.tasks.<a href="../../../api/tasks/SourceTask.html" title="class in org.gradle.api.tasks">SourceTask</a></h3>
<code><a href="../../../api/tasks/SourceTask.html#exclude-groovy.lang.Closure-">exclude</a>, <a href="../../../api/tasks/SourceTask.html#exclude-java.lang.Iterable-">exclude</a>, <a href="../../../api/tasks/SourceTask.html#exclude-java.lang.String...-">exclude</a>, <a href="../../../api/tasks/SourceTask.html#exclude-org.gradle.api.specs.Spec-">exclude</a>, <a href="../../../api/tasks/SourceTask.html#getExcludes--">getExcludes</a>, <a href="../../../api/tasks/SourceTask.html#getIncludes--">getIncludes</a>, <a href="../../../api/tasks/SourceTask.html#getPatternSet--">getPatternSet</a>, <a href="../../../api/tasks/SourceTask.html#getPatternSetFactory--">getPatternSetFactory</a>, <a href="../../../api/tasks/SourceTask.html#include-groovy.lang.Closure-">include</a>, <a href="../../../api/tasks/SourceTask.html#include-java.lang.Iterable-">include</a>, <a href="../../../api/tasks/SourceTask.html#include-java.lang.String...-">include</a>, <a href="../../../api/tasks/SourceTask.html#include-org.gradle.api.specs.Spec-">include</a>, <a href="../../../api/tasks/SourceTask.html#setExcludes-java.lang.Iterable-">setExcludes</a>, <a href="../../../api/tasks/SourceTask.html#setIncludes-java.lang.Iterable-">setIncludes</a>, <a href="../../../api/tasks/SourceTask.html#setSource-java.lang.Object-">setSource</a>, <a href="../../../api/tasks/SourceTask.html#setSource-org.gradle.api.file.FileTree-">setSource</a>, <a href="../../../api/tasks/SourceTask.html#source-java.lang.Object...-">source</a></code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.org.gradle.api.internal.ConventionTask">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;org.gradle.api.internal.ConventionTask</h3>
<code>conventionMapping, conventionMapping, getConventionMapping</code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.org.gradle.api.DefaultTask">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;org.gradle.api.<a href="../../../api/DefaultTask.html" title="class in org.gradle.api">DefaultTask</a></h3>
<code><a href="../../../api/DefaultTask.html#compareTo-org.gradle.api.Task-">compareTo</a>, <a href="../../../api/DefaultTask.html#configure-groovy.lang.Closure-">configure</a>, <a href="../../../api/DefaultTask.html#dependsOn-java.lang.Object...-">dependsOn</a>, <a href="../../../api/DefaultTask.html#doFirst-groovy.lang.Closure-">doFirst</a>, <a href="../../../api/DefaultTask.html#doFirst-java.lang.String-org.gradle.api.Action-">doFirst</a>, <a href="../../../api/DefaultTask.html#doFirst-org.gradle.api.Action-">doFirst</a>, <a href="../../../api/DefaultTask.html#doLast-groovy.lang.Closure-">doLast</a>, <a href="../../../api/DefaultTask.html#doLast-java.lang.String-org.gradle.api.Action-">doLast</a>, <a href="../../../api/DefaultTask.html#doLast-org.gradle.api.Action-">doLast</a>, <a href="../../../api/DefaultTask.html#finalizedBy-java.lang.Object...-">finalizedBy</a>, <a href="../../../api/DefaultTask.html#getActions--">getActions</a>, <a href="../../../api/DefaultTask.html#getAnt--">getAnt</a>, <a href="../../../api/DefaultTask.html#getDependsOn--">getDependsOn</a>, <a href="../../../api/DefaultTask.html#getDescription--">getDescription</a>, <a href="../../../api/DefaultTask.html#getDestroyables--">getDestroyables</a>, <a href="../../../api/DefaultTask.html#getDidWork--">getDidWork</a>, <a href="../../../api/DefaultTask.html#getEnabled--">getEnabled</a>, <a href="../../../api/DefaultTask.html#getExtensions--">getExtensions</a>, <a href="../../../api/DefaultTask.html#getFinalizedBy--">getFinalizedBy</a>, <a href="../../../api/DefaultTask.html#getGroup--">getGroup</a>, <a href="../../../api/DefaultTask.html#getInputs--">getInputs</a>, <a href="../../../api/DefaultTask.html#getLocalState--">getLocalState</a>, <a href="../../../api/DefaultTask.html#getLogger--">getLogger</a>, <a href="../../../api/DefaultTask.html#getLogging--">getLogging</a>, <a href="../../../api/DefaultTask.html#getMustRunAfter--">getMustRunAfter</a>, <a href="../../../api/DefaultTask.html#getName--">getName</a>, <a href="../../../api/DefaultTask.html#getOutputs--">getOutputs</a>, <a href="../../../api/DefaultTask.html#getPath--">getPath</a>, <a href="../../../api/DefaultTask.html#getProject--">getProject</a>, <a href="../../../api/DefaultTask.html#getShouldRunAfter--">getShouldRunAfter</a>, <a href="../../../api/DefaultTask.html#getState--">getState</a>, <a href="../../../api/DefaultTask.html#getTaskDependencies--">getTaskDependencies</a>, <a href="../../../api/DefaultTask.html#getTemporaryDir--">getTemporaryDir</a>, <a href="../../../api/DefaultTask.html#getTimeout--">getTimeout</a>, <a href="../../../api/DefaultTask.html#hasProperty-java.lang.String-">hasProperty</a>, <a href="../../../api/DefaultTask.html#mustRunAfter-java.lang.Object...-">mustRunAfter</a>, <a href="../../../api/DefaultTask.html#onlyIf-groovy.lang.Closure-">onlyIf</a>, <a href="../../../api/DefaultTask.html#onlyIf-java.lang.String-org.gradle.api.specs.Spec-">onlyIf</a>, <a href="../../../api/DefaultTask.html#onlyIf-org.gradle.api.specs.Spec-">onlyIf</a>, <a href="../../../api/DefaultTask.html#property-java.lang.String-">property</a>, <a href="../../../api/DefaultTask.html#setActions-java.util.List-">setActions</a>, <a href="../../../api/DefaultTask.html#setDependsOn-java.lang.Iterable-">setDependsOn</a>, <a href="../../../api/DefaultTask.html#setDescription-java.lang.String-">setDescription</a>, <a href="../../../api/DefaultTask.html#setDidWork-boolean-">setDidWork</a>, <a href="../../../api/DefaultTask.html#setEnabled-boolean-">setEnabled</a>, <a href="../../../api/DefaultTask.html#setFinalizedBy-java.lang.Iterable-">setFinalizedBy</a>, <a href="../../../api/DefaultTask.html#setGroup-java.lang.String-">setGroup</a>, <a href="../../../api/DefaultTask.html#setMustRunAfter-java.lang.Iterable-">setMustRunAfter</a>, <a href="../../../api/DefaultTask.html#setOnlyIf-groovy.lang.Closure-">setOnlyIf</a>, <a href="../../../api/DefaultTask.html#setOnlyIf-java.lang.String-org.gradle.api.specs.Spec-">setOnlyIf</a>, <a href="../../../api/DefaultTask.html#setOnlyIf-org.gradle.api.specs.Spec-">setOnlyIf</a>, <a href="../../../api/DefaultTask.html#setProperty-java.lang.String-java.lang.Object-">setProperty</a>, <a href="../../../api/DefaultTask.html#setShouldRunAfter-java.lang.Iterable-">setShouldRunAfter</a>, <a href="../../../api/DefaultTask.html#shouldRunAfter-java.lang.Object...-">shouldRunAfter</a>, <a href="../../../api/DefaultTask.html#usesService-org.gradle.api.provider.Provider-">usesService</a></code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.org.gradle.api.internal.AbstractTask">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;org.gradle.api.internal.AbstractTask</h3>
<code>acceptServiceReferences, appendParallelSafeAction, doNotTrackState, getAsDynamicObject, getConvention, getIdentityPath, getImpliesSubProjects, getLifecycleDependencies, getOnlyIf, getReasonNotToTrackState, getReasonTaskIsIncompatibleWithConfigurationCache, getRequiredServices, getServices, getSharedResources, getStandardOutputCapture, getTaskActions, getTaskIdentity, getTemporaryDirFactory, hasTaskActions, injectIntoNewInstance, isCompatibleWithConfigurationCache, isEnabled, isHasCustomActions, notCompatibleWithConfigurationCache, prependParallelSafeAction, setImpliesSubProjects</code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.Object</h3>
<code>clone, equals, finalize, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.org.gradle.api.Task">
<!--   -->
</a>
<h3>Methods inherited from interface&nbsp;org.gradle.api.<a href="../../../api/Task.html" title="interface in org.gradle.api">Task</a></h3>
<code><a href="../../../api/Task.html#doNotTrackState-java.lang.String-">doNotTrackState</a>, <a href="../../../api/Task.html#getConvention--">getConvention</a>, <a href="../../../api/Task.html#notCompatibleWithConfigurationCache-java.lang.String-">notCompatibleWithConfigurationCache</a></code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ FIELD DETAIL =========== -->
<ul class="blockList">
<li class="blockList"><a name="field.detail">
<!--   -->
</a>
<h3>Field Detail</h3>
<a name="LOGGER">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>LOGGER</h4>
<pre>protected static final&nbsp;<a href="../../../api/logging/Logger.html" title="interface in org.gradle.api.logging">Logger</a> LOGGER</pre>
</li>
</ul>
</li>
</ul>
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.detail">
<!--   -->
</a>
<h3>Constructor Detail</h3>
<a name="AbstractScalaCompile--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>AbstractScalaCompile</h4>
<pre><a href="../../../api/Incubating.html" title="annotation in org.gradle.api">@Incubating</a>
protected&nbsp;AbstractScalaCompile()</pre>
<div class="block">Constructor.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>7.6</dd>
</dl>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="getScalaCompileOptions--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getScalaCompileOptions</h4>
<pre class="methodSignature">public&nbsp;<a href="BaseScalaCompileOptions.html" title="class in org.gradle.language.scala.tasks">BaseScalaCompileOptions</a>&nbsp;getScalaCompileOptions()</pre>
<div class="block">Returns the Scala compilation options.</div>
</li>
</ul>
<a name="getOptions--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getOptions</h4>
<pre class="methodSignature">public&nbsp;<a href="../../../api/tasks/compile/CompileOptions.html" title="class in org.gradle.api.tasks.compile">CompileOptions</a>&nbsp;getOptions()</pre>
<div class="block">Returns the Java compilation options.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code>getOptions</code>&nbsp;in interface&nbsp;<code>org.gradle.api.internal.tasks.compile.HasCompileOptions</code></dd>
</dl>
</li>
</ul>
<a name="getCompiler-org.gradle.api.internal.tasks.scala.ScalaJavaJointCompileSpec-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getCompiler</h4>
<pre class="methodSignature">protected abstract&nbsp;org.gradle.language.base.internal.compile.Compiler&lt;org.gradle.api.internal.tasks.scala.ScalaJavaJointCompileSpec&gt;&nbsp;getCompiler&#8203;(org.gradle.api.internal.tasks.scala.ScalaJavaJointCompileSpec&nbsp;spec)</pre>
</li>
</ul>
<a name="compile--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>compile</h4>
<pre class="methodSignature">public&nbsp;void&nbsp;compile()</pre>
</li>
</ul>
<a name="getJavaLauncher--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getJavaLauncher</h4>
<pre class="methodSignature">public&nbsp;<a href="../../../api/provider/Property.html" title="interface in org.gradle.api.provider">Property</a>&lt;<a href="../../../jvm/toolchain/JavaLauncher.html" title="interface in org.gradle.jvm.toolchain">JavaLauncher</a>&gt;&nbsp;getJavaLauncher()</pre>
<div class="block">The toolchain <a href="../../../jvm/toolchain/JavaLauncher.html" title="interface in org.gradle.jvm.toolchain"><code>JavaLauncher</code></a> to use for executing the Scala compiler.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>the java launcher property</dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>7.2</dd>
</dl>
</li>
</ul>
<a name="getToolchain--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getToolchain</h4>
<pre class="methodSignature"><a href="../../../api/tasks/Internal.html" title="annotation in org.gradle.api.tasks">@Internal</a>
protected&nbsp;<a href="../../../jvm/toolchain/JavaInstallationMetadata.html" title="interface in org.gradle.jvm.toolchain">JavaInstallationMetadata</a>&nbsp;getToolchain()</pre>
</li>
</ul>
<a name="createSpec--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createSpec</h4>
<pre class="methodSignature">protected&nbsp;org.gradle.api.internal.tasks.scala.ScalaJavaJointCompileSpec&nbsp;createSpec()</pre>
</li>
</ul>
<a name="getSource--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getSource</h4>
<pre class="methodSignature"><a href="../../../api/tasks/PathSensitive.html" title="annotation in org.gradle.api.tasks">@PathSensitive</a>(<a href="../../../api/tasks/PathSensitivity.html#RELATIVE">RELATIVE</a>)
public&nbsp;<a href="../../../api/file/FileTree.html" title="interface in org.gradle.api.file">FileTree</a>&nbsp;getSource()</pre>
<div class="block">Returns the source for this task, after the include and exclude patterns have been applied. Ignores source files which do not exist.

 <p>
 The <a href="../../../api/tasks/PathSensitivity.html" title="enum in org.gradle.api.tasks"><code>PathSensitivity</code></a> for the sources is configured to be <a href="../../../api/tasks/PathSensitivity.html#ABSOLUTE"><code>PathSensitivity.ABSOLUTE</code></a>.
 If your sources are less strict, please change it accordingly by overriding this method in your subclass.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Overrides:</span></dt>
<dd><code><a href="../../../api/tasks/SourceTask.html#getSource--">getSource</a></code>&nbsp;in class&nbsp;<code><a href="../../../api/tasks/SourceTask.html" title="class in org.gradle.api.tasks">SourceTask</a></code></dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>The source.</dd>
</dl>
</li>
</ul>
<a name="getJvmVersion--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getJvmVersion</h4>
<pre class="methodSignature"><a href="../../../api/tasks/Input.html" title="annotation in org.gradle.api.tasks">@Input</a>
protected&nbsp;java.lang.String&nbsp;getJvmVersion()</pre>
<div class="block">The Java major version of the JVM the Scala compiler is running on.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>4.6</dd>
</dl>
</li>
</ul>
<a name="getAnalysisFiles--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getAnalysisFiles</h4>
<pre class="methodSignature"><a href="../../../api/tasks/Internal.html" title="annotation in org.gradle.api.tasks">@Internal</a>
public&nbsp;<a href="../../../api/file/ConfigurableFileCollection.html" title="interface in org.gradle.api.file">ConfigurableFileCollection</a>&nbsp;getAnalysisFiles()</pre>
<div class="block">Source of analysis mapping files for incremental Scala compilation.
 <p>
     An analysis mapping file is produced by each <code>AbstractScalaCompile</code> task. This file contains paths to the jar containing
     compiled Scala classes and the Scala compiler analysis file for that jar. The Scala compiler uses this information to perform
     incremental compilation of Scala sources.
 </p></div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>collection of analysis mapping files.</dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>4.10.1</dd>
</dl>
</li>
</ul>
<a name="getAnalysisMappingFile--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getAnalysisMappingFile</h4>
<pre class="methodSignature"><a href="../../../api/tasks/LocalState.html" title="annotation in org.gradle.api.tasks">@LocalState</a>
public&nbsp;<a href="../../../api/file/RegularFileProperty.html" title="interface in org.gradle.api.file">RegularFileProperty</a>&nbsp;getAnalysisMappingFile()</pre>
<div class="block">Analysis mapping file.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>4.10.1</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="#getAnalysisFiles--"><code>getAnalysisFiles()</code></a></dd>
</dl>
</li>
</ul>
<a name="getDeleter--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getDeleter</h4>
<pre class="methodSignature">@Inject
protected abstract&nbsp;org.gradle.internal.file.Deleter&nbsp;getDeleter()</pre>
</li>
</ul>
<a name="getProjectLayout--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getProjectLayout</h4>
<pre class="methodSignature">@Inject
protected abstract&nbsp;<a href="../../../api/file/ProjectLayout.html" title="interface in org.gradle.api.file">ProjectLayout</a>&nbsp;getProjectLayout()</pre>
</li>
</ul>
<a name="getObjectFactory--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getObjectFactory</h4>
<pre class="methodSignature">@Inject
protected abstract&nbsp;<a href="../../../api/model/ObjectFactory.html" title="interface in org.gradle.api.model">ObjectFactory</a>&nbsp;getObjectFactory()</pre>
</li>
</ul>
<a name="getJavaToolchainService--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getJavaToolchainService</h4>
<pre class="methodSignature">@Inject
protected abstract&nbsp;<a href="../../../jvm/toolchain/JavaToolchainService.html" title="interface in org.gradle.jvm.toolchain">JavaToolchainService</a>&nbsp;getJavaToolchainService()</pre>
</li>
</ul>
<a name="getCachedClasspathTransformer--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>getCachedClasspathTransformer</h4>
<pre class="methodSignature">@Inject
protected abstract&nbsp;org.gradle.internal.classpath.CachedClasspathTransformer&nbsp;getCachedClasspathTransformer()</pre>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../index-all.html">Index</a></li>
<li><a href="../../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../../allclasses.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li><a href="#nested.class.summary">Nested</a>&nbsp;|&nbsp;</li>
<li><a href="#field.summary">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li><a href="#field.detail">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
