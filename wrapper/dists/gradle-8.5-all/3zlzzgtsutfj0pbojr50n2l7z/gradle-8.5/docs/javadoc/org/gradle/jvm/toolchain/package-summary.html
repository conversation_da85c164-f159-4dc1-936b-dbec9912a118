<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc -->
<title>org.gradle.jvm.toolchain (Gradle API 8.5)</title>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<link rel="stylesheet" type="text/css" href="../../../../javadoc.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../jquery/jquery-ui.min.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../jquery-ui.overrides.css" title="Style">
<script type="text/javascript" src="../../../../script.js"></script>
<script type="text/javascript" src="../../../../jquery/jszip/dist/jszip.min.js"></script>
<script type="text/javascript" src="../../../../jquery/jszip-utils/dist/jszip-utils.min.js"></script>
<!--[if IE]>
<script type="text/javascript" src="../../../../jquery/jszip-utils/dist/jszip-utils-ie.min.js"></script>
<![endif]-->
<script type="text/javascript" src="../../../../jquery/jquery-3.6.1.min.js"></script>
<script type="text/javascript" src="../../../../jquery/jquery-ui.min.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="org.gradle.jvm.toolchain (Gradle API 8.5)";
        }
    }
    catch(err) {
    }
//-->
var pathtoroot = "../../../../";
var useModuleDirectories = false;
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<div class="fixedNav">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../index.html">Overview</a></li>
<li class="navBarCell1Rev">Package</li>
<li>Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../allclasses.html">All&nbsp;Classes</a></li>
</ul>
<ul class="navListSearch">
<li><label for="search">SEARCH:</label>
<input type="text" id="search" value="search" disabled="disabled">
<input type="reset" id="reset" value="reset" disabled="disabled">
</li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
</div>
<div class="navPadding">&nbsp;</div>
<script type="text/javascript"><!--
$('.navPadding').css('padding-top', $('.fixedNav').css("height"));
//-->
</script>
<div class="header">
<p><a href="../../api/NonNullApi.html" title="annotation in org.gradle.api">@NonNullApi</a>
</p>
<h1 title="Package" class="title">Package&nbsp;org.gradle.jvm.toolchain</h1>
</div>
<div class="contentContainer"><a name="package.description">
<!--   -->
</a>
<div class="block">Defines tools that can build things that run on the JVM.</div>
<ul class="blockList">
<li class="blockList">
<table class="typeSummary" summary="Interface Summary table, listing interfaces, and an explanation">
<caption><span>Interface Summary</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Interface</th>
<th class="colLast" scope="col">Description</th>
</tr>
<tbody>
<tr class="altColor">
<th class="colFirst" scope="row"><a href="JavaCompiler.html" title="interface in org.gradle.jvm.toolchain">JavaCompiler</a></th>
<td class="colLast">
<div class="block">A java compiler used by compile tasks.</div>
</td>
</tr>
<tr class="rowColor">
<th class="colFirst" scope="row"><a href="JavadocTool.html" title="interface in org.gradle.jvm.toolchain">JavadocTool</a></th>
<td class="colLast">
<div class="block">Generates HTML API documentation for Java classes.</div>
</td>
</tr>
<tr class="altColor">
<th class="colFirst" scope="row"><a href="JavaInstallationMetadata.html" title="interface in org.gradle.jvm.toolchain">JavaInstallationMetadata</a></th>
<td class="colLast">
<div class="block">Metadata about a Java tool obtained from a toolchain.</div>
</td>
</tr>
<tr class="rowColor">
<th class="colFirst" scope="row"><a href="JavaLanguageVersion.html" title="interface in org.gradle.jvm.toolchain">JavaLanguageVersion</a></th>
<td class="colLast">
<div class="block">Represents a Java Language version</div>
</td>
</tr>
<tr class="altColor">
<th class="colFirst" scope="row"><a href="JavaLauncher.html" title="interface in org.gradle.jvm.toolchain">JavaLauncher</a></th>
<td class="colLast">
<div class="block">A java executable used to execute applications or run tests.</div>
</td>
</tr>
<tr class="rowColor">
<th class="colFirst" scope="row"><a href="JavaToolchainDownload.html" title="interface in org.gradle.jvm.toolchain">JavaToolchainDownload</a></th>
<td class="colLast">
<div class="block">The response provided by a <a href="JavaToolchainResolver.html" title="interface in org.gradle.jvm.toolchain"><code>JavaToolchainResolver</code></a> to a specific
 <a href="JavaToolchainRequest.html" title="interface in org.gradle.jvm.toolchain"><code>JavaToolchainRequest</code></a>.</div>
</td>
</tr>
<tr class="altColor">
<th class="colFirst" scope="row"><a href="JavaToolchainRepository.html" title="interface in org.gradle.jvm.toolchain">JavaToolchainRepository</a></th>
<td class="colLast">
<div class="block">Named configuration of <a href="JavaToolchainResolver.html" title="interface in org.gradle.jvm.toolchain"><code>JavaToolchainResolver</code></a> implementations,
 identified by their implementation class.</div>
</td>
</tr>
<tr class="rowColor">
<th class="colFirst" scope="row"><a href="JavaToolchainRepositoryHandler.html" title="interface in org.gradle.jvm.toolchain">JavaToolchainRepositoryHandler</a></th>
<td class="colLast">
<div class="block"><a href="../../api/NamedDomainObjectList.html" title="interface in org.gradle.api"><code>NamedDomainObjectList</code></a> based handler for configuring an
 ordered collection of <code>JavaToolchainRepository</code> implementations.</div>
</td>
</tr>
<tr class="altColor">
<th class="colFirst" scope="row"><a href="JavaToolchainRequest.html" title="interface in org.gradle.jvm.toolchain">JavaToolchainRequest</a></th>
<td class="colLast">
<div class="block">Describes Java toolchains that need to be auto-provisioned, including
 their properties (see <a href="JavaToolchainSpec.html" title="interface in org.gradle.jvm.toolchain"><code>JavaToolchainSpec</code></a>} and the build
 environment they are needed in.</div>
</td>
</tr>
<tr class="rowColor">
<th class="colFirst" scope="row"><a href="JavaToolchainResolver.html" title="interface in org.gradle.jvm.toolchain">JavaToolchainResolver</a></th>
<td class="colLast">
<div class="block">Interface that needs to be implemented by Java toolchain provisioning plugins.</div>
</td>
</tr>
<tr class="altColor">
<th class="colFirst" scope="row"><a href="JavaToolchainResolverRegistry.html" title="interface in org.gradle.jvm.toolchain">JavaToolchainResolverRegistry</a></th>
<td class="colLast">
<div class="block">The build level object/service provided by Gradle which Java toolchain provisioning plugins can access
 to register their <code>JavaToolchainResolver</code> implementations/build services into.</div>
</td>
</tr>
<tr class="rowColor">
<th class="colFirst" scope="row"><a href="JavaToolchainService.html" title="interface in org.gradle.jvm.toolchain">JavaToolchainService</a></th>
<td class="colLast">
<div class="block">Allows to query for toolchain managed tools, like <a href="JavaCompiler.html" title="interface in org.gradle.jvm.toolchain"><code>JavaCompiler</code></a>, <a href="JavaLauncher.html" title="interface in org.gradle.jvm.toolchain"><code>JavaLauncher</code></a> and <a href="JavadocTool.html" title="interface in org.gradle.jvm.toolchain"><code>JavadocTool</code></a>.</div>
</td>
</tr>
<tr class="altColor">
<th class="colFirst" scope="row"><a href="JavaToolchainSpec.html" title="interface in org.gradle.jvm.toolchain">JavaToolchainSpec</a></th>
<td class="colLast">
<div class="block">Requirements for selecting a Java toolchain.</div>
</td>
</tr>
<tr class="rowColor">
<th class="colFirst" scope="row"><a href="JvmToolchainManagement.html" title="interface in org.gradle.jvm.toolchain">JvmToolchainManagement</a></th>
<td class="colLast">
<div class="block">Dynamic extension added to <code>ToolchainManagement</code> at runtime, by the
 <code>jvm-toolchain-management</code> plugin.</div>
</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList">
<table class="typeSummary" summary="Class Summary table, listing classes, and an explanation">
<caption><span>Class Summary</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Class</th>
<th class="colLast" scope="col">Description</th>
</tr>
<tbody>
<tr class="altColor">
<th class="colFirst" scope="row"><a href="JvmImplementation.html" title="class in org.gradle.jvm.toolchain">JvmImplementation</a></th>
<td class="colLast">
<div class="block">Represents a filter for a implementation of a Java Virtual Machine.</div>
</td>
</tr>
<tr class="rowColor">
<th class="colFirst" scope="row"><a href="JvmVendorSpec.html" title="class in org.gradle.jvm.toolchain">JvmVendorSpec</a></th>
<td class="colLast">
<div class="block">Represents a filter for a vendor of a Java Virtual Machine implementation.</div>
</td>
</tr>
</tbody>
</table>
</li>
</ul>
</div>
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../index.html">Overview</a></li>
<li class="navBarCell1Rev">Package</li>
<li>Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../allclasses.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
