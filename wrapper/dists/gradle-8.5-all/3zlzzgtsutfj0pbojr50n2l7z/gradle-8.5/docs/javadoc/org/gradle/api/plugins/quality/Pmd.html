<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc -->
<title>Pmd (Gradle API 8.5)</title>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<link rel="stylesheet" type="text/css" href="../../../../../javadoc.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../../jquery/jquery-ui.min.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../../jquery-ui.overrides.css" title="Style">
<script type="text/javascript" src="../../../../../script.js"></script>
<script type="text/javascript" src="../../../../../jquery/jszip/dist/jszip.min.js"></script>
<script type="text/javascript" src="../../../../../jquery/jszip-utils/dist/jszip-utils.min.js"></script>
<!--[if IE]>
<script type="text/javascript" src="../../../../../jquery/jszip-utils/dist/jszip-utils-ie.min.js"></script>
<![endif]-->
<script type="text/javascript" src="../../../../../jquery/jquery-3.6.1.min.js"></script>
<script type="text/javascript" src="../../../../../jquery/jquery-ui.min.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="Pmd (Gradle API 8.5)";
        }
    }
    catch(err) {
    }
//-->
var data = {"i0":10,"i1":10,"i2":10,"i3":10,"i4":10,"i5":10,"i6":10,"i7":10,"i8":10,"i9":10,"i10":10,"i11":10,"i12":10,"i13":10,"i14":10,"i15":10,"i16":10,"i17":10,"i18":10,"i19":10,"i20":10,"i21":10,"i22":10,"i23":10,"i24":10,"i25":9};
var tabs = {65535:["t0","All Methods"],1:["t1","Static Methods"],2:["t2","Instance Methods"],8:["t4","Concrete Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
var pathtoroot = "../../../../../";
var useModuleDirectories = false;
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<div class="fixedNav">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../index-all.html">Index</a></li>
<li><a href="../../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../../allclasses.html">All&nbsp;Classes</a></li>
</ul>
<ul class="navListSearch">
<li><label for="search">SEARCH:</label>
<input type="text" id="search" value="search" disabled="disabled">
<input type="reset" id="reset" value="reset" disabled="disabled">
</li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li><a href="#nested.class.summary">Nested</a>&nbsp;|&nbsp;</li>
<li><a href="#field.summary">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
</div>
<div class="navPadding">&nbsp;</div>
<script type="text/javascript"><!--
$('.navPadding').css('padding-top', $('.fixedNav').css("height"));
//-->
</script>
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle"><span class="packageLabelInType">Package</span>&nbsp;<a href="package-summary.html">org.gradle.api.plugins.quality</a></div>
<h2 title="Class Pmd" class="title">Class Pmd</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li>org.gradle.api.internal.AbstractTask</li>
<li>
<ul class="inheritance">
<li><a href="../../DefaultTask.html" title="class in org.gradle.api">org.gradle.api.DefaultTask</a></li>
<li>
<ul class="inheritance">
<li>org.gradle.api.internal.ConventionTask</li>
<li>
<ul class="inheritance">
<li><a href="../../tasks/SourceTask.html" title="class in org.gradle.api.tasks">org.gradle.api.tasks.SourceTask</a></li>
<li>
<ul class="inheritance">
<li><a href="AbstractCodeQualityTask.html" title="class in org.gradle.api.plugins.quality">org.gradle.api.plugins.quality.AbstractCodeQualityTask</a></li>
<li>
<ul class="inheritance">
<li>org.gradle.api.plugins.quality.Pmd</li>
</ul>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt>All Implemented Interfaces:</dt>
<dd><code>java.lang.Comparable&lt;<a href="../../Task.html" title="interface in org.gradle.api">Task</a>&gt;</code>, <code>org.gradle.api.internal.DynamicObjectAware</code>, <code>org.gradle.api.internal.IConventionAware</code>, <code>org.gradle.api.internal.TaskInternal</code>, <code><a href="../ExtensionAware.html" title="interface in org.gradle.api.plugins">ExtensionAware</a></code>, <code><a href="../../reporting/Reporting.html" title="interface in org.gradle.api.reporting">Reporting</a>&lt;<a href="PmdReports.html" title="interface in org.gradle.api.plugins.quality">PmdReports</a>&gt;</code>, <code><a href="../../Task.html" title="interface in org.gradle.api">Task</a></code>, <code><a href="../../tasks/util/PatternFilterable.html" title="interface in org.gradle.api.tasks.util">PatternFilterable</a></code>, <code><a href="../../tasks/VerificationTask.html" title="interface in org.gradle.api.tasks">VerificationTask</a></code>, <code><a href="../../../util/Configurable.html" title="interface in org.gradle.util">Configurable</a>&lt;<a href="../../Task.html" title="interface in org.gradle.api">Task</a>&gt;</code></dd>
</dl>
<hr>
<pre><a href="../../tasks/CacheableTask.html" title="annotation in org.gradle.api.tasks">@CacheableTask</a>
public abstract class <span class="typeNameLabel">Pmd</span>
extends <a href="AbstractCodeQualityTask.html" title="class in org.gradle.api.plugins.quality">AbstractCodeQualityTask</a>
implements <a href="../../reporting/Reporting.html" title="interface in org.gradle.api.reporting">Reporting</a>&lt;<a href="PmdReports.html" title="interface in org.gradle.api.plugins.quality">PmdReports</a>&gt;</pre>
<div class="block">Runs a set of static code analysis rules on Java source code files and generates a report of problems found.</div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="PmdPlugin.html" title="class in org.gradle.api.plugins.quality"><code>PmdPlugin</code></a>, 
<a href="PmdExtension.html" title="class in org.gradle.api.plugins.quality"><code>PmdExtension</code></a></dd>
</dl>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ======== NESTED CLASS SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="nested.class.summary">
<!--   -->
</a>
<h3>Nested Class Summary</h3>
<ul class="blockList">
<li class="blockList"><a name="nested.classes.inherited.from.class.org.gradle.api.Task">
<!--   -->
</a>
<h3>Nested classes/interfaces inherited from interface&nbsp;org.gradle.api.<a href="../../Task.html" title="interface in org.gradle.api">Task</a></h3>
<code><a href="../../Task.Namer.html" title="class in org.gradle.api">Task.Namer</a></code></li>
</ul>
</li>
</ul>
<!-- =========== FIELD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="field.summary">
<!--   -->
</a>
<h3>Field Summary</h3>
<ul class="blockList">
<li class="blockList"><a name="fields.inherited.from.class.org.gradle.api.Task">
<!--   -->
</a>
<h3>Fields inherited from interface&nbsp;org.gradle.api.<a href="../../Task.html" title="interface in org.gradle.api">Task</a></h3>
<code><a href="../../Task.html#TASK_ACTION">TASK_ACTION</a>, <a href="../../Task.html#TASK_CONSTRUCTOR_ARGS">TASK_CONSTRUCTOR_ARGS</a>, <a href="../../Task.html#TASK_DEPENDS_ON">TASK_DEPENDS_ON</a>, <a href="../../Task.html#TASK_DESCRIPTION">TASK_DESCRIPTION</a>, <a href="../../Task.html#TASK_GROUP">TASK_GROUP</a>, <a href="../../Task.html#TASK_NAME">TASK_NAME</a>, <a href="../../Task.html#TASK_OVERWRITE">TASK_OVERWRITE</a>, <a href="../../Task.html#TASK_TYPE">TASK_TYPE</a></code></li>
</ul>
</li>
</ul>
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.summary">
<!--   -->
</a>
<h3>Constructor Summary</h3>
<table class="memberSummary" summary="Constructor Summary table, listing constructors, and an explanation">
<caption><span>Constructors</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Constructor</th>
<th class="colLast" scope="col">Description</th>
</tr>
<tr class="altColor">
<th class="colConstructorName" scope="row"><code><span class="memberNameLink"><a href="#Pmd--">Pmd</a></span>()</code></th>
<td class="colLast">&nbsp;</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t1" class="tableTab"><span><a href="javascript:show(1);">Static Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colSecond" scope="col">Method</th>
<th class="colLast" scope="col">Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code><a href="../../file/FileCollection.html" title="interface in org.gradle.api.file">FileCollection</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getClasspath--">getClasspath</a></span>()</code></th>
<td class="colLast">
<div class="block">Compile class path for the classes to be analyzed.</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code><a href="../../provider/Property.html" title="interface in org.gradle.api.provider">Property</a>&lt;java.lang.Boolean&gt;</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getIncrementalAnalysis--">getIncrementalAnalysis</a></span>()</code></th>
<td class="colLast">
<div class="block">Controls whether to use incremental analysis or not.</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>java.io.File</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getIncrementalCacheFile--">getIncrementalCacheFile</a></span>()</code></th>
<td class="colLast">
<div class="block">Path to the incremental cache file, if incremental analysis is used.</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code><a href="../../provider/Property.html" title="interface in org.gradle.api.provider">Property</a>&lt;java.lang.Integer&gt;</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getMaxFailures--">getMaxFailures</a></span>()</code></th>
<td class="colLast">
<div class="block">The maximum number of failures to allow before stopping the build.</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code><a href="../../file/FileCollection.html" title="interface in org.gradle.api.file">FileCollection</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getPmdClasspath--">getPmdClasspath</a></span>()</code></th>
<td class="colLast">
<div class="block">The class path containing the PMD library to be used.</div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code><a href="PmdReports.html" title="interface in org.gradle.api.plugins.quality">PmdReports</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getReports--">getReports</a></span>()</code></th>
<td class="colLast">
<div class="block">The reports to be generated by this task.</div>
</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code><a href="../../resources/TextResource.html" title="interface in org.gradle.api.resources">TextResource</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getRuleSetConfig--">getRuleSetConfig</a></span>()</code></th>
<td class="colLast">
<div class="block">The custom rule set to be used (if any).</div>
</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code><a href="../../file/FileCollection.html" title="interface in org.gradle.api.file">FileCollection</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getRuleSetFiles--">getRuleSetFiles</a></span>()</code></th>
<td class="colLast">
<div class="block">The custom rule set files to be used.</div>
</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code>java.util.List&lt;java.lang.String&gt;</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getRuleSets--">getRuleSets</a></span>()</code></th>
<td class="colLast">
<div class="block">The built-in rule sets to be used.</div>
</td>
</tr>
<tr id="i9" class="rowColor">
<td class="colFirst"><code><a href="../../provider/Property.html" title="interface in org.gradle.api.provider">Property</a>&lt;java.lang.Integer&gt;</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getRulesMinimumPriority--">getRulesMinimumPriority</a></span>()</code></th>
<td class="colLast">
<div class="block">Specifies the rule priority threshold.</div>
</td>
</tr>
<tr id="i10" class="altColor">
<td class="colFirst"><code><a href="../../file/FileTree.html" title="interface in org.gradle.api.file">FileTree</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getSource--">getSource</a></span>()</code></th>
<td class="colLast">
<div class="block">Returns the source for this task, after the include and exclude patterns have been applied.</div>
</td>
</tr>
<tr id="i11" class="rowColor">
<td class="colFirst"><code><a href="TargetJdk.html" title="enum in org.gradle.api.plugins.quality">TargetJdk</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getTargetJdk--">getTargetJdk</a></span>()</code></th>
<td class="colLast">
<div class="block">The target JDK to use with PMD.</div>
</td>
</tr>
<tr id="i12" class="altColor">
<td class="colFirst"><code><a href="../../provider/Property.html" title="interface in org.gradle.api.provider">Property</a>&lt;java.lang.Integer&gt;</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getThreads--">getThreads</a></span>()</code></th>
<td class="colLast">
<div class="block">Specifies the number of threads used by PMD.</div>
</td>
</tr>
<tr id="i13" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#isConsoleOutput--">isConsoleOutput</a></span>()</code></th>
<td class="colLast">
<div class="block">Whether or not to write PMD results to <code>System.out</code>.</div>
</td>
</tr>
<tr id="i14" class="altColor">
<td class="colFirst"><code><a href="PmdReports.html" title="interface in org.gradle.api.plugins.quality">PmdReports</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#reports-groovy.lang.Closure-">reports</a></span>&#8203;(<a href="https://docs.groovy-lang.org/docs/groovy-3.0.17/html/gapi/groovy/lang/Closure.html?is-external=true" title="class or interface in groovy.lang" class="externalLink">Closure</a>&nbsp;closure)</code></th>
<td class="colLast">
<div class="block">Configures the reports to be generated by this task.</div>
</td>
</tr>
<tr id="i15" class="rowColor">
<td class="colFirst"><code><a href="PmdReports.html" title="interface in org.gradle.api.plugins.quality">PmdReports</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#reports-org.gradle.api.Action-">reports</a></span>&#8203;(<a href="../../Action.html" title="interface in org.gradle.api">Action</a>&lt;? super <a href="PmdReports.html" title="interface in org.gradle.api.plugins.quality">PmdReports</a>&gt;&nbsp;configureAction)</code></th>
<td class="colLast">
<div class="block">Configures the reports to be generated by this task.</div>
</td>
</tr>
<tr id="i16" class="altColor">
<td class="colFirst"><code>void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#run--">run</a></span>()</code></th>
<td class="colLast">&nbsp;</td>
</tr>
<tr id="i17" class="rowColor">
<td class="colFirst"><code>void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#setClasspath-org.gradle.api.file.FileCollection-">setClasspath</a></span>&#8203;(<a href="../../file/FileCollection.html" title="interface in org.gradle.api.file">FileCollection</a>&nbsp;classpath)</code></th>
<td class="colLast">
<div class="block">Compile class path for the classes to be analyzed.</div>
</td>
</tr>
<tr id="i18" class="altColor">
<td class="colFirst"><code>void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#setConsoleOutput-boolean-">setConsoleOutput</a></span>&#8203;(boolean&nbsp;consoleOutput)</code></th>
<td class="colLast">
<div class="block">Whether or not to write PMD results to <code>System.out</code>.</div>
</td>
</tr>
<tr id="i19" class="rowColor">
<td class="colFirst"><code>void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#setPmdClasspath-org.gradle.api.file.FileCollection-">setPmdClasspath</a></span>&#8203;(<a href="../../file/FileCollection.html" title="interface in org.gradle.api.file">FileCollection</a>&nbsp;pmdClasspath)</code></th>
<td class="colLast">
<div class="block">The class path containing the PMD library to be used.</div>
</td>
</tr>
<tr id="i20" class="altColor">
<td class="colFirst"><code>void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#setRuleSetConfig-org.gradle.api.resources.TextResource-">setRuleSetConfig</a></span>&#8203;(<a href="../../resources/TextResource.html" title="interface in org.gradle.api.resources">TextResource</a>&nbsp;ruleSetConfig)</code></th>
<td class="colLast">
<div class="block">The custom rule set to be used (if any).</div>
</td>
</tr>
<tr id="i21" class="rowColor">
<td class="colFirst"><code>void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#setRuleSetFiles-org.gradle.api.file.FileCollection-">setRuleSetFiles</a></span>&#8203;(<a href="../../file/FileCollection.html" title="interface in org.gradle.api.file">FileCollection</a>&nbsp;ruleSetFiles)</code></th>
<td class="colLast">
<div class="block">The custom rule set files to be used.</div>
</td>
</tr>
<tr id="i22" class="altColor">
<td class="colFirst"><code>void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#setRuleSets-java.util.List-">setRuleSets</a></span>&#8203;(java.util.List&lt;java.lang.String&gt;&nbsp;ruleSets)</code></th>
<td class="colLast">
<div class="block">The built-in rule sets to be used.</div>
</td>
</tr>
<tr id="i23" class="rowColor">
<td class="colFirst"><code>void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#setTargetJdk-org.gradle.api.plugins.quality.TargetJdk-">setTargetJdk</a></span>&#8203;(<a href="TargetJdk.html" title="enum in org.gradle.api.plugins.quality">TargetJdk</a>&nbsp;targetJdk)</code></th>
<td class="colLast">
<div class="block">The target JDK to use with PMD.</div>
</td>
</tr>
<tr id="i24" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#stdOutIsAttachedToTerminal--">stdOutIsAttachedToTerminal</a></span>()</code></th>
<td class="colLast">&nbsp;</td>
</tr>
<tr id="i25" class="rowColor">
<td class="colFirst"><code>static void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#validate-int-">validate</a></span>&#8203;(int&nbsp;value)</code></th>
<td class="colLast">
<div class="block">Validates the value is a valid PMD rules minimum priority (1-5)</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.org.gradle.api.plugins.quality.AbstractCodeQualityTask">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;org.gradle.api.plugins.quality.<a href="AbstractCodeQualityTask.html" title="class in org.gradle.api.plugins.quality">AbstractCodeQualityTask</a></h3>
<code><a href="AbstractCodeQualityTask.html#configureForkOptions-org.gradle.process.JavaForkOptions-">configureForkOptions</a>, <a href="AbstractCodeQualityTask.html#getIgnoreFailures--">getIgnoreFailures</a>, <a href="AbstractCodeQualityTask.html#getIgnoreFailuresProperty--">getIgnoreFailuresProperty</a>, <a href="AbstractCodeQualityTask.html#getJavaLauncher--">getJavaLauncher</a>, <a href="AbstractCodeQualityTask.html#getMaxHeapSize--">getMaxHeapSize</a>, <a href="AbstractCodeQualityTask.html#getMinHeapSize--">getMinHeapSize</a>, <a href="AbstractCodeQualityTask.html#getObjectFactory--">getObjectFactory</a>, <a href="AbstractCodeQualityTask.html#getToolchainService--">getToolchainService</a>, <a href="AbstractCodeQualityTask.html#getWorkerExecutor--">getWorkerExecutor</a>, <a href="AbstractCodeQualityTask.html#setIgnoreFailures-boolean-">setIgnoreFailures</a></code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.org.gradle.api.tasks.SourceTask">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;org.gradle.api.tasks.<a href="../../tasks/SourceTask.html" title="class in org.gradle.api.tasks">SourceTask</a></h3>
<code><a href="../../tasks/SourceTask.html#exclude-groovy.lang.Closure-">exclude</a>, <a href="../../tasks/SourceTask.html#exclude-java.lang.Iterable-">exclude</a>, <a href="../../tasks/SourceTask.html#exclude-java.lang.String...-">exclude</a>, <a href="../../tasks/SourceTask.html#exclude-org.gradle.api.specs.Spec-">exclude</a>, <a href="../../tasks/SourceTask.html#getExcludes--">getExcludes</a>, <a href="../../tasks/SourceTask.html#getIncludes--">getIncludes</a>, <a href="../../tasks/SourceTask.html#getPatternSet--">getPatternSet</a>, <a href="../../tasks/SourceTask.html#getPatternSetFactory--">getPatternSetFactory</a>, <a href="../../tasks/SourceTask.html#include-groovy.lang.Closure-">include</a>, <a href="../../tasks/SourceTask.html#include-java.lang.Iterable-">include</a>, <a href="../../tasks/SourceTask.html#include-java.lang.String...-">include</a>, <a href="../../tasks/SourceTask.html#include-org.gradle.api.specs.Spec-">include</a>, <a href="../../tasks/SourceTask.html#setExcludes-java.lang.Iterable-">setExcludes</a>, <a href="../../tasks/SourceTask.html#setIncludes-java.lang.Iterable-">setIncludes</a>, <a href="../../tasks/SourceTask.html#setSource-java.lang.Object-">setSource</a>, <a href="../../tasks/SourceTask.html#setSource-org.gradle.api.file.FileTree-">setSource</a>, <a href="../../tasks/SourceTask.html#source-java.lang.Object...-">source</a></code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.org.gradle.api.internal.ConventionTask">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;org.gradle.api.internal.ConventionTask</h3>
<code>conventionMapping, conventionMapping, getConventionMapping</code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.org.gradle.api.DefaultTask">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;org.gradle.api.<a href="../../DefaultTask.html" title="class in org.gradle.api">DefaultTask</a></h3>
<code><a href="../../DefaultTask.html#compareTo-org.gradle.api.Task-">compareTo</a>, <a href="../../DefaultTask.html#configure-groovy.lang.Closure-">configure</a>, <a href="../../DefaultTask.html#dependsOn-java.lang.Object...-">dependsOn</a>, <a href="../../DefaultTask.html#doFirst-groovy.lang.Closure-">doFirst</a>, <a href="../../DefaultTask.html#doFirst-java.lang.String-org.gradle.api.Action-">doFirst</a>, <a href="../../DefaultTask.html#doFirst-org.gradle.api.Action-">doFirst</a>, <a href="../../DefaultTask.html#doLast-groovy.lang.Closure-">doLast</a>, <a href="../../DefaultTask.html#doLast-java.lang.String-org.gradle.api.Action-">doLast</a>, <a href="../../DefaultTask.html#doLast-org.gradle.api.Action-">doLast</a>, <a href="../../DefaultTask.html#finalizedBy-java.lang.Object...-">finalizedBy</a>, <a href="../../DefaultTask.html#getActions--">getActions</a>, <a href="../../DefaultTask.html#getAnt--">getAnt</a>, <a href="../../DefaultTask.html#getDependsOn--">getDependsOn</a>, <a href="../../DefaultTask.html#getDescription--">getDescription</a>, <a href="../../DefaultTask.html#getDestroyables--">getDestroyables</a>, <a href="../../DefaultTask.html#getDidWork--">getDidWork</a>, <a href="../../DefaultTask.html#getEnabled--">getEnabled</a>, <a href="../../DefaultTask.html#getExtensions--">getExtensions</a>, <a href="../../DefaultTask.html#getFinalizedBy--">getFinalizedBy</a>, <a href="../../DefaultTask.html#getGroup--">getGroup</a>, <a href="../../DefaultTask.html#getInputs--">getInputs</a>, <a href="../../DefaultTask.html#getLocalState--">getLocalState</a>, <a href="../../DefaultTask.html#getLogger--">getLogger</a>, <a href="../../DefaultTask.html#getLogging--">getLogging</a>, <a href="../../DefaultTask.html#getMustRunAfter--">getMustRunAfter</a>, <a href="../../DefaultTask.html#getName--">getName</a>, <a href="../../DefaultTask.html#getOutputs--">getOutputs</a>, <a href="../../DefaultTask.html#getPath--">getPath</a>, <a href="../../DefaultTask.html#getProject--">getProject</a>, <a href="../../DefaultTask.html#getShouldRunAfter--">getShouldRunAfter</a>, <a href="../../DefaultTask.html#getState--">getState</a>, <a href="../../DefaultTask.html#getTaskDependencies--">getTaskDependencies</a>, <a href="../../DefaultTask.html#getTemporaryDir--">getTemporaryDir</a>, <a href="../../DefaultTask.html#getTimeout--">getTimeout</a>, <a href="../../DefaultTask.html#hasProperty-java.lang.String-">hasProperty</a>, <a href="../../DefaultTask.html#mustRunAfter-java.lang.Object...-">mustRunAfter</a>, <a href="../../DefaultTask.html#onlyIf-groovy.lang.Closure-">onlyIf</a>, <a href="../../DefaultTask.html#onlyIf-java.lang.String-org.gradle.api.specs.Spec-">onlyIf</a>, <a href="../../DefaultTask.html#onlyIf-org.gradle.api.specs.Spec-">onlyIf</a>, <a href="../../DefaultTask.html#property-java.lang.String-">property</a>, <a href="../../DefaultTask.html#setActions-java.util.List-">setActions</a>, <a href="../../DefaultTask.html#setDependsOn-java.lang.Iterable-">setDependsOn</a>, <a href="../../DefaultTask.html#setDescription-java.lang.String-">setDescription</a>, <a href="../../DefaultTask.html#setDidWork-boolean-">setDidWork</a>, <a href="../../DefaultTask.html#setEnabled-boolean-">setEnabled</a>, <a href="../../DefaultTask.html#setFinalizedBy-java.lang.Iterable-">setFinalizedBy</a>, <a href="../../DefaultTask.html#setGroup-java.lang.String-">setGroup</a>, <a href="../../DefaultTask.html#setMustRunAfter-java.lang.Iterable-">setMustRunAfter</a>, <a href="../../DefaultTask.html#setOnlyIf-groovy.lang.Closure-">setOnlyIf</a>, <a href="../../DefaultTask.html#setOnlyIf-java.lang.String-org.gradle.api.specs.Spec-">setOnlyIf</a>, <a href="../../DefaultTask.html#setOnlyIf-org.gradle.api.specs.Spec-">setOnlyIf</a>, <a href="../../DefaultTask.html#setProperty-java.lang.String-java.lang.Object-">setProperty</a>, <a href="../../DefaultTask.html#setShouldRunAfter-java.lang.Iterable-">setShouldRunAfter</a>, <a href="../../DefaultTask.html#shouldRunAfter-java.lang.Object...-">shouldRunAfter</a>, <a href="../../DefaultTask.html#usesService-org.gradle.api.provider.Provider-">usesService</a></code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.org.gradle.api.internal.AbstractTask">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;org.gradle.api.internal.AbstractTask</h3>
<code>acceptServiceReferences, appendParallelSafeAction, doNotTrackState, getAsDynamicObject, getConvention, getIdentityPath, getImpliesSubProjects, getLifecycleDependencies, getOnlyIf, getReasonNotToTrackState, getReasonTaskIsIncompatibleWithConfigurationCache, getRequiredServices, getServices, getSharedResources, getStandardOutputCapture, getTaskActions, getTaskIdentity, getTemporaryDirFactory, hasTaskActions, injectIntoNewInstance, isCompatibleWithConfigurationCache, isEnabled, isHasCustomActions, notCompatibleWithConfigurationCache, prependParallelSafeAction, setImpliesSubProjects</code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.Object</h3>
<code>clone, equals, finalize, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.org.gradle.api.Task">
<!--   -->
</a>
<h3>Methods inherited from interface&nbsp;org.gradle.api.<a href="../../Task.html" title="interface in org.gradle.api">Task</a></h3>
<code><a href="../../Task.html#doNotTrackState-java.lang.String-">doNotTrackState</a>, <a href="../../Task.html#getConvention--">getConvention</a>, <a href="../../Task.html#notCompatibleWithConfigurationCache-java.lang.String-">notCompatibleWithConfigurationCache</a></code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.detail">
<!--   -->
</a>
<h3>Constructor Detail</h3>
<a name="Pmd--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>Pmd</h4>
<pre>public&nbsp;Pmd()</pre>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="run--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>run</h4>
<pre class="methodSignature">public&nbsp;void&nbsp;run()</pre>
</li>
</ul>
<a name="stdOutIsAttachedToTerminal--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>stdOutIsAttachedToTerminal</h4>
<pre class="methodSignature">public&nbsp;boolean&nbsp;stdOutIsAttachedToTerminal()</pre>
</li>
</ul>
<a name="reports-groovy.lang.Closure-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>reports</h4>
<pre class="methodSignature">public&nbsp;<a href="PmdReports.html" title="interface in org.gradle.api.plugins.quality">PmdReports</a>&nbsp;reports&#8203;(<a href="https://docs.groovy-lang.org/docs/groovy-3.0.17/html/gapi/groovy/lang/DelegatesTo.html?is-external=true" title="class or interface in groovy.lang" class="externalLink">@DelegatesTo</a>(<a href="https://docs.groovy-lang.org/docs/groovy-3.0.17/html/gapi/groovy/lang/DelegatesTo.html?is-external=true#value--" title="class or interface in groovy.lang" class="externalLink">value</a>=<a href="PmdReports.html" title="interface in org.gradle.api.plugins.quality">PmdReports.class</a>,<a href="https://docs.groovy-lang.org/docs/groovy-3.0.17/html/gapi/groovy/lang/DelegatesTo.html?is-external=true#strategy--" title="class or interface in groovy.lang" class="externalLink">strategy</a>=1)
                          <a href="https://docs.groovy-lang.org/docs/groovy-3.0.17/html/gapi/groovy/lang/Closure.html?is-external=true" title="class or interface in groovy.lang" class="externalLink">Closure</a>&nbsp;closure)</pre>
<div class="block">Configures the reports to be generated by this task.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../reporting/Reporting.html#reports-groovy.lang.Closure-">reports</a></code>&nbsp;in interface&nbsp;<code><a href="../../reporting/Reporting.html" title="interface in org.gradle.api.reporting">Reporting</a>&lt;<a href="PmdReports.html" title="interface in org.gradle.api.plugins.quality">PmdReports</a>&gt;</code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>closure</code> - The configuration</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>The report container</dd>
</dl>
</li>
</ul>
<a name="reports-org.gradle.api.Action-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>reports</h4>
<pre class="methodSignature">public&nbsp;<a href="PmdReports.html" title="interface in org.gradle.api.plugins.quality">PmdReports</a>&nbsp;reports&#8203;(<a href="../../Action.html" title="interface in org.gradle.api">Action</a>&lt;? super <a href="PmdReports.html" title="interface in org.gradle.api.plugins.quality">PmdReports</a>&gt;&nbsp;configureAction)</pre>
<div class="block">Configures the reports to be generated by this task.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../reporting/Reporting.html#reports-org.gradle.api.Action-">reports</a></code>&nbsp;in interface&nbsp;<code><a href="../../reporting/Reporting.html" title="interface in org.gradle.api.reporting">Reporting</a>&lt;<a href="PmdReports.html" title="interface in org.gradle.api.plugins.quality">PmdReports</a>&gt;</code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>configureAction</code> - The configuration</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>The report container</dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>3.0</dd>
</dl>
</li>
</ul>
<a name="validate-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>validate</h4>
<pre class="methodSignature">public static&nbsp;void&nbsp;validate&#8203;(int&nbsp;value)</pre>
<div class="block">Validates the value is a valid PMD rules minimum priority (1-5)</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - rules minimum priority threshold</dd>
</dl>
</li>
</ul>
<a name="getSource--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getSource</h4>
<pre class="methodSignature"><a href="../../tasks/PathSensitive.html" title="annotation in org.gradle.api.tasks">@PathSensitive</a>(<a href="../../tasks/PathSensitivity.html#RELATIVE">RELATIVE</a>)
public&nbsp;<a href="../../file/FileTree.html" title="interface in org.gradle.api.file">FileTree</a>&nbsp;getSource()</pre>
<div class="block">Returns the source for this task, after the include and exclude patterns have been applied. Ignores source files which do not exist.

 <p>
 The <a href="../../tasks/PathSensitivity.html" title="enum in org.gradle.api.tasks"><code>PathSensitivity</code></a> for the sources is configured to be <a href="../../tasks/PathSensitivity.html#ABSOLUTE"><code>PathSensitivity.ABSOLUTE</code></a>.
 If your sources are less strict, please change it accordingly by overriding this method in your subclass.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Overrides:</span></dt>
<dd><code><a href="../../tasks/SourceTask.html#getSource--">getSource</a></code>&nbsp;in class&nbsp;<code><a href="../../tasks/SourceTask.html" title="class in org.gradle.api.tasks">SourceTask</a></code></dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>The source.</dd>
</dl>
</li>
</ul>
<a name="getPmdClasspath--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getPmdClasspath</h4>
<pre class="methodSignature"><a href="../../tasks/Classpath.html" title="annotation in org.gradle.api.tasks">@Classpath</a>
public&nbsp;<a href="../../file/FileCollection.html" title="interface in org.gradle.api.file">FileCollection</a>&nbsp;getPmdClasspath()</pre>
<div class="block">The class path containing the PMD library to be used.</div>
</li>
</ul>
<a name="setPmdClasspath-org.gradle.api.file.FileCollection-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setPmdClasspath</h4>
<pre class="methodSignature">public&nbsp;void&nbsp;setPmdClasspath&#8203;(<a href="../../file/FileCollection.html" title="interface in org.gradle.api.file">FileCollection</a>&nbsp;pmdClasspath)</pre>
<div class="block">The class path containing the PMD library to be used.</div>
</li>
</ul>
<a name="getRuleSets--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getRuleSets</h4>
<pre class="methodSignature"><a href="../../tasks/Input.html" title="annotation in org.gradle.api.tasks">@Input</a>
public&nbsp;java.util.List&lt;java.lang.String&gt;&nbsp;getRuleSets()</pre>
<div class="block">The built-in rule sets to be used. See the <a href="https://docs.pmd-code.org/pmd-doc-6.55.0/pmd_rules_java.html">official list</a> of built-in rule sets.

 <pre>
     ruleSets = ["basic", "braces"]
 </pre></div>
</li>
</ul>
<a name="setRuleSets-java.util.List-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setRuleSets</h4>
<pre class="methodSignature">public&nbsp;void&nbsp;setRuleSets&#8203;(java.util.List&lt;java.lang.String&gt;&nbsp;ruleSets)</pre>
<div class="block">The built-in rule sets to be used. See the <a href="https://docs.pmd-code.org/pmd-doc-6.55.0/pmd_rules_java.html">official list</a> of built-in rule sets.

 <pre>
     ruleSets = ["basic", "braces"]
 </pre></div>
</li>
</ul>
<a name="getTargetJdk--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getTargetJdk</h4>
<pre class="methodSignature"><a href="../../tasks/Input.html" title="annotation in org.gradle.api.tasks">@Input</a>
public&nbsp;<a href="TargetJdk.html" title="enum in org.gradle.api.plugins.quality">TargetJdk</a>&nbsp;getTargetJdk()</pre>
<div class="block">The target JDK to use with PMD.</div>
</li>
</ul>
<a name="setTargetJdk-org.gradle.api.plugins.quality.TargetJdk-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setTargetJdk</h4>
<pre class="methodSignature">public&nbsp;void&nbsp;setTargetJdk&#8203;(<a href="TargetJdk.html" title="enum in org.gradle.api.plugins.quality">TargetJdk</a>&nbsp;targetJdk)</pre>
<div class="block">The target JDK to use with PMD.</div>
</li>
</ul>
<a name="getRuleSetConfig--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getRuleSetConfig</h4>
<pre class="methodSignature">@Nullable
<a href="../../tasks/Optional.html" title="annotation in org.gradle.api.tasks">@Optional</a>
public&nbsp;<a href="../../resources/TextResource.html" title="interface in org.gradle.api.resources">TextResource</a>&nbsp;getRuleSetConfig()</pre>
<div class="block">The custom rule set to be used (if any). Replaces <code>ruleSetFiles</code>, except that it does not currently support multiple rule sets.

 See the <a href="https://docs.pmd-code.org/pmd-doc-6.55.0/pmd_userdocs_making_rulesets.html">official documentation</a> for how to author a rule set.

 <pre>
     ruleSetConfig = resources.text.fromFile(resources.file("config/pmd/myRuleSets.xml"))
 </pre></div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>2.2</dd>
</dl>
</li>
</ul>
<a name="setRuleSetConfig-org.gradle.api.resources.TextResource-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setRuleSetConfig</h4>
<pre class="methodSignature">public&nbsp;void&nbsp;setRuleSetConfig&#8203;(@Nullable
                             <a href="../../resources/TextResource.html" title="interface in org.gradle.api.resources">TextResource</a>&nbsp;ruleSetConfig)</pre>
<div class="block">The custom rule set to be used (if any). Replaces <code>ruleSetFiles</code>, except that it does not currently support multiple rule sets.

 See the <a href="https://docs.pmd-code.org/pmd-doc-6.55.0/pmd_userdocs_making_rulesets.html">official documentation</a> for how to author a rule set.

 <pre>
     ruleSetConfig = resources.text.fromFile(resources.file("config/pmd/myRuleSets.xml"))
 </pre></div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>2.2</dd>
</dl>
</li>
</ul>
<a name="getRuleSetFiles--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getRuleSetFiles</h4>
<pre class="methodSignature"><a href="../../tasks/InputFiles.html" title="annotation in org.gradle.api.tasks">@InputFiles</a>
<a href="../../tasks/PathSensitive.html" title="annotation in org.gradle.api.tasks">@PathSensitive</a>(<a href="../../tasks/PathSensitivity.html#NONE">NONE</a>)
public&nbsp;<a href="../../file/FileCollection.html" title="interface in org.gradle.api.file">FileCollection</a>&nbsp;getRuleSetFiles()</pre>
<div class="block">The custom rule set files to be used. See the <a href="https://docs.pmd-code.org/pmd-doc-6.55.0/pmd_userdocs_making_rulesets.html">official documentation</a> for how to author a rule set file.
 If you want to only use custom rule sets, you must clear <code>ruleSets</code>.

 <pre>
     ruleSetFiles = files("config/pmd/myRuleSet.xml")
 </pre></div>
</li>
</ul>
<a name="setRuleSetFiles-org.gradle.api.file.FileCollection-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setRuleSetFiles</h4>
<pre class="methodSignature">public&nbsp;void&nbsp;setRuleSetFiles&#8203;(<a href="../../file/FileCollection.html" title="interface in org.gradle.api.file">FileCollection</a>&nbsp;ruleSetFiles)</pre>
<div class="block">The custom rule set files to be used. See the <a href="https://docs.pmd-code.org/pmd-doc-6.55.0/pmd_userdocs_making_rulesets.html">official documentation</a> for how to author a rule set file.
 This adds to the default rule sets defined by <a href="#getRuleSets--"><code>getRuleSets()</code></a>.

 <pre>
     ruleSetFiles = files("config/pmd/myRuleSets.xml")
 </pre></div>
</li>
</ul>
<a name="getReports--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getReports</h4>
<pre class="methodSignature">public final&nbsp;<a href="PmdReports.html" title="interface in org.gradle.api.plugins.quality">PmdReports</a>&nbsp;getReports()</pre>
<div class="block">The reports to be generated by this task.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../reporting/Reporting.html#getReports--">getReports</a></code>&nbsp;in interface&nbsp;<code><a href="../../reporting/Reporting.html" title="interface in org.gradle.api.reporting">Reporting</a>&lt;<a href="PmdReports.html" title="interface in org.gradle.api.plugins.quality">PmdReports</a>&gt;</code></dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>The report container</dd>
</dl>
</li>
</ul>
<a name="getMaxFailures--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getMaxFailures</h4>
<pre class="methodSignature"><a href="../../tasks/Input.html" title="annotation in org.gradle.api.tasks">@Input</a>
public&nbsp;<a href="../../provider/Property.html" title="interface in org.gradle.api.provider">Property</a>&lt;java.lang.Integer&gt;&nbsp;getMaxFailures()</pre>
<div class="block">The maximum number of failures to allow before stopping the build.

 Defaults to 0, which will stop the build on any failure.  Values 0 and
 above are valid.  If <pre>ignoreFailures</pre> is set, this is ignored
 and the build will continue (infinite failures allowed).</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>6.4</dd>
</dl>
</li>
</ul>
<a name="getRulesMinimumPriority--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getRulesMinimumPriority</h4>
<pre class="methodSignature"><a href="../../tasks/Input.html" title="annotation in org.gradle.api.tasks">@Input</a>
public&nbsp;<a href="../../provider/Property.html" title="interface in org.gradle.api.provider">Property</a>&lt;java.lang.Integer&gt;&nbsp;getRulesMinimumPriority()</pre>
<div class="block">Specifies the rule priority threshold.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>6.8</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="PmdExtension.html#getRulesMinimumPriority--"><code>PmdExtension.getRulesMinimumPriority()</code></a></dd>
</dl>
</li>
</ul>
<a name="isConsoleOutput--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isConsoleOutput</h4>
<pre class="methodSignature"><a href="../../tasks/Input.html" title="annotation in org.gradle.api.tasks">@Input</a>
public&nbsp;boolean&nbsp;isConsoleOutput()</pre>
<div class="block">Whether or not to write PMD results to <code>System.out</code>.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>2.1</dd>
</dl>
</li>
</ul>
<a name="setConsoleOutput-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setConsoleOutput</h4>
<pre class="methodSignature">public&nbsp;void&nbsp;setConsoleOutput&#8203;(boolean&nbsp;consoleOutput)</pre>
<div class="block">Whether or not to write PMD results to <code>System.out</code>.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>2.1</dd>
</dl>
</li>
</ul>
<a name="getClasspath--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getClasspath</h4>
<pre class="methodSignature">@Nullable
<a href="../../tasks/Optional.html" title="annotation in org.gradle.api.tasks">@Optional</a>
<a href="../../tasks/Classpath.html" title="annotation in org.gradle.api.tasks">@Classpath</a>
public&nbsp;<a href="../../file/FileCollection.html" title="interface in org.gradle.api.file">FileCollection</a>&nbsp;getClasspath()</pre>
<div class="block">Compile class path for the classes to be analyzed.

 The classes on this class path are used during analysis but aren't analyzed themselves.

 This is only well supported for PMD 5.2.1 or better.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>2.8</dd>
</dl>
</li>
</ul>
<a name="setClasspath-org.gradle.api.file.FileCollection-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setClasspath</h4>
<pre class="methodSignature">public&nbsp;void&nbsp;setClasspath&#8203;(@Nullable
                         <a href="../../file/FileCollection.html" title="interface in org.gradle.api.file">FileCollection</a>&nbsp;classpath)</pre>
<div class="block">Compile class path for the classes to be analyzed.

 The classes on this class path are used during analysis but aren't analyzed themselves.

 This is only well supported for PMD 5.2.1 or better.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>2.8</dd>
</dl>
</li>
</ul>
<a name="getIncrementalAnalysis--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getIncrementalAnalysis</h4>
<pre class="methodSignature"><a href="../../tasks/Internal.html" title="annotation in org.gradle.api.tasks">@Internal</a>
public&nbsp;<a href="../../provider/Property.html" title="interface in org.gradle.api.provider">Property</a>&lt;java.lang.Boolean&gt;&nbsp;getIncrementalAnalysis()</pre>
<div class="block">Controls whether to use incremental analysis or not.

 This is only supported for PMD 6.0.0 or better. See <a href="https://docs.pmd-code.org/pmd-doc-6.55.0/pmd_userdocs_incremental_analysis.html"></a> for more details.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>5.6</dd>
</dl>
</li>
</ul>
<a name="getIncrementalCacheFile--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getIncrementalCacheFile</h4>
<pre class="methodSignature"><a href="../../tasks/LocalState.html" title="annotation in org.gradle.api.tasks">@LocalState</a>
public&nbsp;java.io.File&nbsp;getIncrementalCacheFile()</pre>
<div class="block">Path to the incremental cache file, if incremental analysis is used.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>5.6</dd>
</dl>
</li>
</ul>
<a name="getThreads--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>getThreads</h4>
<pre class="methodSignature"><a href="../../tasks/Input.html" title="annotation in org.gradle.api.tasks">@Input</a>
public&nbsp;<a href="../../provider/Property.html" title="interface in org.gradle.api.provider">Property</a>&lt;java.lang.Integer&gt;&nbsp;getThreads()</pre>
<div class="block">Specifies the number of threads used by PMD.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>7.5</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="PmdExtension.html#getThreads--"><code>PmdExtension.getThreads()</code></a></dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../index-all.html">Index</a></li>
<li><a href="../../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../../allclasses.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li><a href="#nested.class.summary">Nested</a>&nbsp;|&nbsp;</li>
<li><a href="#field.summary">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
