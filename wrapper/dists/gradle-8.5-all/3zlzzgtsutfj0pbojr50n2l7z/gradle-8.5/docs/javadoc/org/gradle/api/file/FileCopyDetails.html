<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc -->
<title>FileCopyDetails (Gradle API 8.5)</title>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<link rel="stylesheet" type="text/css" href="../../../../javadoc.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../jquery/jquery-ui.min.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../jquery-ui.overrides.css" title="Style">
<script type="text/javascript" src="../../../../script.js"></script>
<script type="text/javascript" src="../../../../jquery/jszip/dist/jszip.min.js"></script>
<script type="text/javascript" src="../../../../jquery/jszip-utils/dist/jszip-utils.min.js"></script>
<!--[if IE]>
<script type="text/javascript" src="../../../../jquery/jszip-utils/dist/jszip-utils-ie.min.js"></script>
<![endif]-->
<script type="text/javascript" src="../../../../jquery/jquery-3.6.1.min.js"></script>
<script type="text/javascript" src="../../../../jquery/jquery-ui.min.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="FileCopyDetails (Gradle API 8.5)";
        }
    }
    catch(err) {
    }
//-->
var data = {"i0":6,"i1":6,"i2":6,"i3":6,"i4":6,"i5":6,"i6":6,"i7":6,"i8":6,"i9":6,"i10":6,"i11":6,"i12":6,"i13":6,"i14":6};
var tabs = {65535:["t0","All Methods"],2:["t2","Instance Methods"],4:["t3","Abstract Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
var pathtoroot = "../../../../";
var useModuleDirectories = false;
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<div class="fixedNav">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../allclasses.html">All&nbsp;Classes</a></li>
</ul>
<ul class="navListSearch">
<li><label for="search">SEARCH:</label>
<input type="text" id="search" value="search" disabled="disabled">
<input type="reset" id="reset" value="reset" disabled="disabled">
</li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
</div>
<div class="navPadding">&nbsp;</div>
<script type="text/javascript"><!--
$('.navPadding').css('padding-top', $('.fixedNav').css("height"));
//-->
</script>
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle"><span class="packageLabelInType">Package</span>&nbsp;<a href="package-summary.html">org.gradle.api.file</a></div>
<h2 title="Interface FileCopyDetails" class="title">Interface FileCopyDetails</h2>
</div>
<div class="contentContainer">
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt>All Superinterfaces:</dt>
<dd><code><a href="ContentFilterable.html" title="interface in org.gradle.api.file">ContentFilterable</a></code>, <code><a href="FileTreeElement.html" title="interface in org.gradle.api.file">FileTreeElement</a></code></dd>
</dl>
<hr>
<pre><a href="../NonExtensible.html" title="annotation in org.gradle.api">@NonExtensible</a>
public interface <span class="typeNameLabel">FileCopyDetails</span>
extends <a href="FileTreeElement.html" title="interface in org.gradle.api.file">FileTreeElement</a>, <a href="ContentFilterable.html" title="interface in org.gradle.api.file">ContentFilterable</a></pre>
<div class="block"><p>Provides details about a file or directory about to be copied, and allows some aspects of the destination file to
 be modified.</p>

 <p>Using this interface, you can change the destination path of the file, filter the content of the file, or exclude
 the file from the result entirely.</p>

 <p>Access to the source file itself after any filters have been added is not a supported operation.
 </p></div>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t3" class="tableTab"><span><a href="javascript:show(4);">Abstract Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colSecond" scope="col">Method</th>
<th class="colLast" scope="col">Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#exclude--">exclude</a></span>()</code></th>
<td class="colLast">
<div class="block">Excludes this file from the copy.</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code><a href="DuplicatesStrategy.html" title="enum in org.gradle.api.file">DuplicatesStrategy</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getDuplicatesStrategy--">getDuplicatesStrategy</a></span>()</code></th>
<td class="colLast">
<div class="block">The strategy to use if there is already a file at this file's destination.</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>java.lang.String</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getName--">getName</a></span>()</code></th>
<td class="colLast">
<div class="block">Returns the base name of this file at the copy destination.</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>java.lang.String</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getPath--">getPath</a></span>()</code></th>
<td class="colLast">
<div class="block">Returns the path of this file, relative to the root of the copy destination.</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code><a href="RelativePath.html" title="class in org.gradle.api.file">RelativePath</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getRelativePath--">getRelativePath</a></span>()</code></th>
<td class="colLast">
<div class="block">Returns the path of this file, relative to the root of the copy destination.</div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code><a href="RelativePath.html" title="class in org.gradle.api.file">RelativePath</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getRelativeSourcePath--">getRelativeSourcePath</a></span>()</code></th>
<td class="colLast">
<div class="block">Returns the path of this file, relative to the root of the containing file tree.</div>
</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code>java.lang.String</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getSourceName--">getSourceName</a></span>()</code></th>
<td class="colLast">
<div class="block">Returns the base name of this file at the copy source.</div>
</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code>java.lang.String</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getSourcePath--">getSourcePath</a></span>()</code></th>
<td class="colLast">
<div class="block">Returns the path of this file, relative to the root of the containing file tree.</div>
</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code>void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#permissions-org.gradle.api.Action-">permissions</a></span>&#8203;(<a href="../Action.html" title="interface in org.gradle.api">Action</a>&lt;? super <a href="ConfigurableFilePermissions.html" title="interface in org.gradle.api.file">ConfigurableFilePermissions</a>&gt;&nbsp;configureAction)</code></th>
<td class="colLast">
<div class="block">Configuration action for specifying file and directory access permissions.</div>
</td>
</tr>
<tr id="i9" class="rowColor">
<td class="colFirst"><code>void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#setDuplicatesStrategy-org.gradle.api.file.DuplicatesStrategy-">setDuplicatesStrategy</a></span>&#8203;(<a href="DuplicatesStrategy.html" title="enum in org.gradle.api.file">DuplicatesStrategy</a>&nbsp;strategy)</code></th>
<td class="colLast">
<div class="block">The strategy to use if there is already a file at this file's destination.</div>
</td>
</tr>
<tr id="i10" class="altColor">
<td class="colFirst"><code>void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#setMode-int-">setMode</a></span>&#8203;(int&nbsp;mode)</code></th>
<td class="colLast">
<div class="block">Sets the Unix permissions of this file.</div>
</td>
</tr>
<tr id="i11" class="rowColor">
<td class="colFirst"><code>void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#setName-java.lang.String-">setName</a></span>&#8203;(java.lang.String&nbsp;name)</code></th>
<td class="colLast">
<div class="block">Sets the destination name of this file.</div>
</td>
</tr>
<tr id="i12" class="altColor">
<td class="colFirst"><code>void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#setPath-java.lang.String-">setPath</a></span>&#8203;(java.lang.String&nbsp;path)</code></th>
<td class="colLast">
<div class="block">Sets the destination path of this file.</div>
</td>
</tr>
<tr id="i13" class="rowColor">
<td class="colFirst"><code>void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#setPermissions-org.gradle.api.file.FilePermissions-">setPermissions</a></span>&#8203;(<a href="FilePermissions.html" title="interface in org.gradle.api.file">FilePermissions</a>&nbsp;permissions)</code></th>
<td class="colLast">
<div class="block">Set file and directory access permissions based on an externally
 provided permission instance.</div>
</td>
</tr>
<tr id="i14" class="altColor">
<td class="colFirst"><code>void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#setRelativePath-org.gradle.api.file.RelativePath-">setRelativePath</a></span>&#8203;(<a href="RelativePath.html" title="class in org.gradle.api.file">RelativePath</a>&nbsp;path)</code></th>
<td class="colLast">
<div class="block">Sets the destination path of this file.</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.org.gradle.api.file.ContentFilterable">
<!--   -->
</a>
<h3>Methods inherited from interface&nbsp;org.gradle.api.file.<a href="ContentFilterable.html" title="interface in org.gradle.api.file">ContentFilterable</a></h3>
<code><a href="ContentFilterable.html#expand-java.util.Map-">expand</a>, <a href="ContentFilterable.html#expand-java.util.Map-org.gradle.api.Action-">expand</a>, <a href="ContentFilterable.html#filter-groovy.lang.Closure-">filter</a>, <a href="ContentFilterable.html#filter-java.lang.Class-">filter</a>, <a href="ContentFilterable.html#filter-java.util.Map-java.lang.Class-">filter</a>, <a href="ContentFilterable.html#filter-org.gradle.api.Transformer-">filter</a></code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.org.gradle.api.file.FileTreeElement">
<!--   -->
</a>
<h3>Methods inherited from interface&nbsp;org.gradle.api.file.<a href="FileTreeElement.html" title="interface in org.gradle.api.file">FileTreeElement</a></h3>
<code><a href="FileTreeElement.html#copyTo-java.io.File-">copyTo</a>, <a href="FileTreeElement.html#copyTo-java.io.OutputStream-">copyTo</a>, <a href="FileTreeElement.html#getFile--">getFile</a>, <a href="FileTreeElement.html#getLastModified--">getLastModified</a>, <a href="FileTreeElement.html#getMode--">getMode</a>, <a href="FileTreeElement.html#getPermissions--">getPermissions</a>, <a href="FileTreeElement.html#getSize--">getSize</a>, <a href="FileTreeElement.html#isDirectory--">isDirectory</a>, <a href="FileTreeElement.html#open--">open</a></code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="exclude--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>exclude</h4>
<pre class="methodSignature">void&nbsp;exclude()</pre>
<div class="block">Excludes this file from the copy.</div>
</li>
</ul>
<a name="setName-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setName</h4>
<pre class="methodSignature">void&nbsp;setName&#8203;(java.lang.String&nbsp;name)</pre>
<div class="block">Sets the destination name of this file.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>name</code> - The destination name of this file.</dd>
</dl>
</li>
</ul>
<a name="setPath-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setPath</h4>
<pre class="methodSignature">void&nbsp;setPath&#8203;(java.lang.String&nbsp;path)</pre>
<div class="block">Sets the destination path of this file.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>path</code> - The path of this file.</dd>
</dl>
</li>
</ul>
<a name="setRelativePath-org.gradle.api.file.RelativePath-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setRelativePath</h4>
<pre class="methodSignature">void&nbsp;setRelativePath&#8203;(<a href="RelativePath.html" title="class in org.gradle.api.file">RelativePath</a>&nbsp;path)</pre>
<div class="block">Sets the destination path of this file.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>path</code> - the new path for this file.</dd>
</dl>
</li>
</ul>
<a name="setMode-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setMode</h4>
<pre class="methodSignature">void&nbsp;setMode&#8203;(int&nbsp;mode)</pre>
<div class="block">Sets the Unix permissions of this file.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>mode</code> - the Unix permissions, e.g. <code>0644</code>.</dd>
<dt><span class="simpleTagLabel">API Note:</span></dt>
<dd>Consider using <a href="#permissions-org.gradle.api.Action-"><code>permissions(Action)</code></a> instead.</dd>
</dl>
</li>
</ul>
<a name="permissions-org.gradle.api.Action-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>permissions</h4>
<pre class="methodSignature"><a href="../Incubating.html" title="annotation in org.gradle.api">@Incubating</a>
void&nbsp;permissions&#8203;(<a href="../Action.html" title="interface in org.gradle.api">Action</a>&lt;? super <a href="ConfigurableFilePermissions.html" title="interface in org.gradle.api.file">ConfigurableFilePermissions</a>&gt;&nbsp;configureAction)</pre>
<div class="block">Configuration action for specifying file and directory access permissions.
 For details see <a href="ConfigurableFilePermissions.html" title="interface in org.gradle.api.file"><code>ConfigurableFilePermissions</code></a>.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>8.3</dd>
</dl>
</li>
</ul>
<a name="setPermissions-org.gradle.api.file.FilePermissions-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setPermissions</h4>
<pre class="methodSignature"><a href="../Incubating.html" title="annotation in org.gradle.api">@Incubating</a>
void&nbsp;setPermissions&#8203;(<a href="FilePermissions.html" title="interface in org.gradle.api.file">FilePermissions</a>&nbsp;permissions)</pre>
<div class="block">Set file and directory access permissions based on an externally
 provided permission instance.
 For details see <a href="ConfigurableFilePermissions.html" title="interface in org.gradle.api.file"><code>ConfigurableFilePermissions</code></a>.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>8.3</dd>
</dl>
</li>
</ul>
<a name="setDuplicatesStrategy-org.gradle.api.file.DuplicatesStrategy-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setDuplicatesStrategy</h4>
<pre class="methodSignature">void&nbsp;setDuplicatesStrategy&#8203;(<a href="DuplicatesStrategy.html" title="enum in org.gradle.api.file">DuplicatesStrategy</a>&nbsp;strategy)</pre>
<div class="block">The strategy to use if there is already a file at this file's destination.</div>
</li>
</ul>
<a name="getDuplicatesStrategy--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getDuplicatesStrategy</h4>
<pre class="methodSignature"><a href="DuplicatesStrategy.html" title="enum in org.gradle.api.file">DuplicatesStrategy</a>&nbsp;getDuplicatesStrategy()</pre>
<div class="block">The strategy to use if there is already a file at this file's destination.
 <p>
 The value can be set with a case-insensitive string of the enum value (e.g. <code>'exclude'</code> for <a href="DuplicatesStrategy.html#EXCLUDE"><code>DuplicatesStrategy.EXCLUDE</code></a>).</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>the strategy to use for this file.</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="DuplicatesStrategy.html" title="enum in org.gradle.api.file"><code>DuplicatesStrategy</code></a></dd>
</dl>
</li>
</ul>
<a name="getName--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getName</h4>
<pre class="methodSignature">java.lang.String&nbsp;getName()</pre>
<div class="block">Returns the base name of this file at the copy destination.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="FileTreeElement.html#getName--">getName</a></code>&nbsp;in interface&nbsp;<code><a href="FileTreeElement.html" title="interface in org.gradle.api.file">FileTreeElement</a></code></dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>The destination name. Never returns null.</dd>
</dl>
</li>
</ul>
<a name="getPath--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getPath</h4>
<pre class="methodSignature">java.lang.String&nbsp;getPath()</pre>
<div class="block">Returns the path of this file, relative to the root of the copy destination.
 <p>
 Always uses '/' as the hierarchy separator, regardless of platform file separator.
 Same as calling <code>getRelativePath().getPathString()</code>.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="FileTreeElement.html#getPath--">getPath</a></code>&nbsp;in interface&nbsp;<code><a href="FileTreeElement.html" title="interface in org.gradle.api.file">FileTreeElement</a></code></dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>The path, relative to the root of the copy destination. Never returns null.</dd>
</dl>
</li>
</ul>
<a name="getRelativePath--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getRelativePath</h4>
<pre class="methodSignature"><a href="RelativePath.html" title="class in org.gradle.api.file">RelativePath</a>&nbsp;getRelativePath()</pre>
<div class="block">Returns the path of this file, relative to the root of the copy destination.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="FileTreeElement.html#getRelativePath--">getRelativePath</a></code>&nbsp;in interface&nbsp;<code><a href="FileTreeElement.html" title="interface in org.gradle.api.file">FileTreeElement</a></code></dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>The path, relative to the root of the copy destination. Never returns null.</dd>
</dl>
</li>
</ul>
<a name="getSourceName--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getSourceName</h4>
<pre class="methodSignature">java.lang.String&nbsp;getSourceName()</pre>
<div class="block">Returns the base name of this file at the copy source.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>The source name. Never returns null.</dd>
</dl>
</li>
</ul>
<a name="getSourcePath--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getSourcePath</h4>
<pre class="methodSignature">java.lang.String&nbsp;getSourcePath()</pre>
<div class="block">Returns the path of this file, relative to the root of the containing file tree.
 <p>
 Always uses '/' as the hierarchy separator, regardless of platform file separator.
 Same as calling <code>getRelativeSourcePath().getPathString()</code>.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>The path, relative to the root of the containing file tree. Never returns null.</dd>
</dl>
</li>
</ul>
<a name="getRelativeSourcePath--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>getRelativeSourcePath</h4>
<pre class="methodSignature"><a href="RelativePath.html" title="class in org.gradle.api.file">RelativePath</a>&nbsp;getRelativeSourcePath()</pre>
<div class="block">Returns the path of this file, relative to the root of the containing file tree.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>The path, relative to the root of the containing file tree. Never returns null.</dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../allclasses.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
