<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc -->
<title>ModelSet (Gradle API 8.5)</title>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<link rel="stylesheet" type="text/css" href="../../../javadoc.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../jquery/jquery-ui.min.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../jquery-ui.overrides.css" title="Style">
<script type="text/javascript" src="../../../script.js"></script>
<script type="text/javascript" src="../../../jquery/jszip/dist/jszip.min.js"></script>
<script type="text/javascript" src="../../../jquery/jszip-utils/dist/jszip-utils.min.js"></script>
<!--[if IE]>
<script type="text/javascript" src="../../../jquery/jszip-utils/dist/jszip-utils-ie.min.js"></script>
<![endif]-->
<script type="text/javascript" src="../../../jquery/jquery-3.6.1.min.js"></script>
<script type="text/javascript" src="../../../jquery/jquery-ui.min.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="ModelSet (Gradle API 8.5)";
        }
    }
    catch(err) {
    }
//-->
var data = {"i0":6,"i1":6,"i2":6};
var tabs = {65535:["t0","All Methods"],2:["t2","Instance Methods"],4:["t3","Abstract Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
var pathtoroot = "../../../";
var useModuleDirectories = false;
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<div class="fixedNav">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../index-all.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../allclasses.html">All&nbsp;Classes</a></li>
</ul>
<ul class="navListSearch">
<li><label for="search">SEARCH:</label>
<input type="text" id="search" value="search" disabled="disabled">
<input type="reset" id="reset" value="reset" disabled="disabled">
</li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li><a href="#nested.class.summary">Nested</a>&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
</div>
<div class="navPadding">&nbsp;</div>
<script type="text/javascript"><!--
$('.navPadding').css('padding-top', $('.fixedNav').css("height"));
//-->
</script>
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle"><span class="packageLabelInType">Package</span>&nbsp;<a href="package-summary.html">org.gradle.model</a></div>
<h2 title="Interface ModelSet" class="title">Interface ModelSet&lt;T&gt;</h2>
</div>
<div class="contentContainer">
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt><span class="paramLabel">Type Parameters:</span></dt>
<dd><code>T</code> - the type of model object</dd>
</dl>
<dl>
<dt>All Superinterfaces:</dt>
<dd><code>java.util.Collection&lt;T&gt;</code>, <code>java.lang.Iterable&lt;T&gt;</code>, <code><a href="ModelElement.html" title="interface in org.gradle.model">ModelElement</a></code>, <code><a href="../api/Named.html" title="interface in org.gradle.api">Named</a></code>, <code>java.util.Set&lt;T&gt;</code></dd>
</dl>
<hr>
<pre><a href="../api/Incubating.html" title="annotation in org.gradle.api">@Incubating</a>
public interface <span class="typeNameLabel">ModelSet&lt;T&gt;</span>
extends java.util.Set&lt;T&gt;, <a href="ModelElement.html" title="interface in org.gradle.model">ModelElement</a></pre>
<div class="block">A set of managed model objects.
 <p>
 <a href="Managed.html" title="annotation in org.gradle.model"><code>Managed</code></a> types may declare managed set properties.
 Managed sets can only contain managed types.
 <p>
 Managed set objects cannot be mutated via the mutative methods of the <code>Set</code> interface (e.g. <code>Set.add(Object)</code>, <code>Set.clear()</code>).
 To add elements to the set, the <a href="#create-org.gradle.api.Action-"><code>create(Action)</code></a> method can be used.</div>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ======== NESTED CLASS SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="nested.class.summary">
<!--   -->
</a>
<h3>Nested Class Summary</h3>
<ul class="blockList">
<li class="blockList"><a name="nested.classes.inherited.from.class.org.gradle.api.Named">
<!--   -->
</a>
<h3>Nested classes/interfaces inherited from interface&nbsp;org.gradle.api.<a href="../api/Named.html" title="interface in org.gradle.api">Named</a></h3>
<code><a href="../api/Named.Namer.html" title="class in org.gradle.api">Named.Namer</a></code></li>
</ul>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t3" class="tableTab"><span><a href="javascript:show(4);">Abstract Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colSecond" scope="col">Method</th>
<th class="colLast" scope="col">Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#afterEach-org.gradle.api.Action-">afterEach</a></span>&#8203;(<a href="../api/Action.html" title="interface in org.gradle.api">Action</a>&lt;? super <a href="ModelSet.html" title="type parameter in ModelSet">T</a>&gt;&nbsp;configAction)</code></th>
<td class="colLast">
<div class="block">Apply the given action to each set element just before it is considered to be realised.</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#beforeEach-org.gradle.api.Action-">beforeEach</a></span>&#8203;(<a href="../api/Action.html" title="interface in org.gradle.api">Action</a>&lt;? super <a href="ModelSet.html" title="type parameter in ModelSet">T</a>&gt;&nbsp;configAction)</code></th>
<td class="colLast">
<div class="block">Apply the given action to each set element just after it is created.</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#create-org.gradle.api.Action-">create</a></span>&#8203;(<a href="../api/Action.html" title="interface in org.gradle.api">Action</a>&lt;? super <a href="ModelSet.html" title="type parameter in ModelSet">T</a>&gt;&nbsp;action)</code></th>
<td class="colLast">
<div class="block">Declares a new set element, configured by the given action.</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.util.Collection">
<!--   -->
</a>
<h3>Methods inherited from interface&nbsp;java.util.Collection</h3>
<code>parallelStream, removeIf, stream, toArray</code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Iterable">
<!--   -->
</a>
<h3>Methods inherited from interface&nbsp;java.lang.Iterable</h3>
<code>forEach</code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.org.gradle.model.ModelElement">
<!--   -->
</a>
<h3>Methods inherited from interface&nbsp;org.gradle.model.<a href="ModelElement.html" title="interface in org.gradle.model">ModelElement</a></h3>
<code><a href="ModelElement.html#getDisplayName--">getDisplayName</a>, <a href="ModelElement.html#getName--">getName</a></code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.util.Set">
<!--   -->
</a>
<h3>Methods inherited from interface&nbsp;java.util.Set</h3>
<code>add, addAll, clear, contains, containsAll, equals, hashCode, isEmpty, iterator, remove, removeAll, retainAll, size, spliterator, toArray, toArray</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="create-org.gradle.api.Action-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>create</h4>
<pre class="methodSignature">void&nbsp;create&#8203;(<a href="../api/Action.html" title="interface in org.gradle.api">Action</a>&lt;? super <a href="ModelSet.html" title="type parameter in ModelSet">T</a>&gt;&nbsp;action)</pre>
<div class="block">Declares a new set element, configured by the given action.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>action</code> - the object configuration</dd>
</dl>
</li>
</ul>
<a name="beforeEach-org.gradle.api.Action-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>beforeEach</h4>
<pre class="methodSignature">void&nbsp;beforeEach&#8203;(<a href="../api/Action.html" title="interface in org.gradle.api">Action</a>&lt;? super <a href="ModelSet.html" title="type parameter in ModelSet">T</a>&gt;&nbsp;configAction)</pre>
<div class="block">Apply the given action to each set element just after it is created.
 <p>
 The configuration action is equivalent in terms of lifecycle to <a href="Defaults.html" title="annotation in org.gradle.model"><code>Defaults</code></a> rule methods.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>configAction</code> - the object configuration</dd>
</dl>
</li>
</ul>
<a name="afterEach-org.gradle.api.Action-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>afterEach</h4>
<pre class="methodSignature">void&nbsp;afterEach&#8203;(<a href="../api/Action.html" title="interface in org.gradle.api">Action</a>&lt;? super <a href="ModelSet.html" title="type parameter in ModelSet">T</a>&gt;&nbsp;configAction)</pre>
<div class="block">Apply the given action to each set element just before it is considered to be realised.
 <p>
 The configuration action is equivalent in terms of lifecycle to <a href="Finalize.html" title="annotation in org.gradle.model"><code>Finalize</code></a> rule methods.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>configAction</code> - the object configuration</dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../index-all.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../allclasses.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li><a href="#nested.class.summary">Nested</a>&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
