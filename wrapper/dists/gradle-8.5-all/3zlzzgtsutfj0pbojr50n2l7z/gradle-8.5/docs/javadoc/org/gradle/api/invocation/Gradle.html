<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc -->
<title>Gradle (Gradle API 8.5)</title>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<link rel="stylesheet" type="text/css" href="../../../../javadoc.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../jquery/jquery-ui.min.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../jquery-ui.overrides.css" title="Style">
<script type="text/javascript" src="../../../../script.js"></script>
<script type="text/javascript" src="../../../../jquery/jszip/dist/jszip.min.js"></script>
<script type="text/javascript" src="../../../../jquery/jszip-utils/dist/jszip-utils.min.js"></script>
<!--[if IE]>
<script type="text/javascript" src="../../../../jquery/jszip-utils/dist/jszip-utils-ie.min.js"></script>
<![endif]-->
<script type="text/javascript" src="../../../../jquery/jquery-3.6.1.min.js"></script>
<script type="text/javascript" src="../../../../jquery/jquery-ui.min.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="Gradle (Gradle API 8.5)";
        }
    }
    catch(err) {
    }
//-->
var data = {"i0":6,"i1":6,"i2":6,"i3":6,"i4":6,"i5":6,"i6":6,"i7":6,"i8":6,"i9":6,"i10":38,"i11":38,"i12":6,"i13":6,"i14":6,"i15":6,"i16":6,"i17":6,"i18":6,"i19":6,"i20":6,"i21":6,"i22":6,"i23":6,"i24":6,"i25":6,"i26":6,"i27":6,"i28":6,"i29":6,"i30":6,"i31":6,"i32":6};
var tabs = {65535:["t0","All Methods"],2:["t2","Instance Methods"],4:["t3","Abstract Methods"],32:["t6","Deprecated Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
var pathtoroot = "../../../../";
var useModuleDirectories = false;
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<div class="fixedNav">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../allclasses.html">All&nbsp;Classes</a></li>
</ul>
<ul class="navListSearch">
<li><label for="search">SEARCH:</label>
<input type="text" id="search" value="search" disabled="disabled">
<input type="reset" id="reset" value="reset" disabled="disabled">
</li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
</div>
<div class="navPadding">&nbsp;</div>
<script type="text/javascript"><!--
$('.navPadding').css('padding-top', $('.fixedNav').css("height"));
//-->
</script>
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle"><span class="packageLabelInType">Package</span>&nbsp;<a href="package-summary.html">org.gradle.api.invocation</a></div>
<h2 title="Interface Gradle" class="title">Interface Gradle</h2>
</div>
<div class="contentContainer">
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt>All Superinterfaces:</dt>
<dd><code><a href="../plugins/ExtensionAware.html" title="interface in org.gradle.api.plugins">ExtensionAware</a></code>, <code><a href="../plugins/PluginAware.html" title="interface in org.gradle.api.plugins">PluginAware</a></code></dd>
</dl>
<hr>
<pre>public interface <span class="typeNameLabel">Gradle</span>
extends <a href="../plugins/PluginAware.html" title="interface in org.gradle.api.plugins">PluginAware</a>, <a href="../plugins/ExtensionAware.html" title="interface in org.gradle.api.plugins">ExtensionAware</a></pre>
<div class="block">Represents an invocation of Gradle.

 <p>You can obtain a <code>Gradle</code> instance by calling <a href="../Project.html#getGradle--"><code>Project.getGradle()</code></a>.</p></div>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t3" class="tableTab"><span><a href="javascript:show(4);">Abstract Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t6" class="tableTab"><span><a href="javascript:show(32);">Deprecated Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colSecond" scope="col">Method</th>
<th class="colLast" scope="col">Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#addBuildListener-org.gradle.BuildListener-">addBuildListener</a></span>&#8203;(<a href="../../BuildListener.html" title="interface in org.gradle">BuildListener</a>&nbsp;buildListener)</code></th>
<td class="colLast">
<div class="block">Adds a <a href="../../BuildListener.html" title="interface in org.gradle"><code>BuildListener</code></a> to this Build instance.</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#addListener-java.lang.Object-">addListener</a></span>&#8203;(java.lang.Object&nbsp;listener)</code></th>
<td class="colLast">
<div class="block">Adds the given listener to this build.</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code><a href="../ProjectEvaluationListener.html" title="interface in org.gradle.api">ProjectEvaluationListener</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#addProjectEvaluationListener-org.gradle.api.ProjectEvaluationListener-">addProjectEvaluationListener</a></span>&#8203;(<a href="../ProjectEvaluationListener.html" title="interface in org.gradle.api">ProjectEvaluationListener</a>&nbsp;listener)</code></th>
<td class="colLast">
<div class="block">Adds a listener to this build, to receive notifications as projects are evaluated.</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#afterProject-groovy.lang.Closure-">afterProject</a></span>&#8203;(<a href="https://docs.groovy-lang.org/docs/groovy-3.0.17/html/gapi/groovy/lang/Closure.html?is-external=true" title="class or interface in groovy.lang" class="externalLink">Closure</a>&nbsp;closure)</code></th>
<td class="colLast">
<div class="block">Adds a closure to be called immediately after a project is evaluated.</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code>void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#afterProject-org.gradle.api.Action-">afterProject</a></span>&#8203;(<a href="../Action.html" title="interface in org.gradle.api">Action</a>&lt;? super <a href="../Project.html" title="interface in org.gradle.api">Project</a>&gt;&nbsp;action)</code></th>
<td class="colLast">
<div class="block">Adds an action to be called immediately after a project is evaluated.</div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code>void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#allprojects-org.gradle.api.Action-">allprojects</a></span>&#8203;(<a href="../Action.html" title="interface in org.gradle.api">Action</a>&lt;? super <a href="../Project.html" title="interface in org.gradle.api">Project</a>&gt;&nbsp;action)</code></th>
<td class="colLast">
<div class="block">Adds an action to execute against all projects of this build.</div>
</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code>void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#beforeProject-groovy.lang.Closure-">beforeProject</a></span>&#8203;(<a href="https://docs.groovy-lang.org/docs/groovy-3.0.17/html/gapi/groovy/lang/Closure.html?is-external=true" title="class or interface in groovy.lang" class="externalLink">Closure</a>&nbsp;closure)</code></th>
<td class="colLast">
<div class="block">Adds a closure to be called immediately before a project is evaluated.</div>
</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code>void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#beforeProject-org.gradle.api.Action-">beforeProject</a></span>&#8203;(<a href="../Action.html" title="interface in org.gradle.api">Action</a>&lt;? super <a href="../Project.html" title="interface in org.gradle.api">Project</a>&gt;&nbsp;action)</code></th>
<td class="colLast">
<div class="block">Adds an action to be called immediately before a project is evaluated.</div>
</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code>void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#beforeSettings-groovy.lang.Closure-">beforeSettings</a></span>&#8203;(<a href="https://docs.groovy-lang.org/docs/groovy-3.0.17/html/gapi/groovy/lang/Closure.html?is-external=true" title="class or interface in groovy.lang" class="externalLink">Closure</a>&lt;?&gt;&nbsp;closure)</code></th>
<td class="colLast">
<div class="block">Adds an action to be called before the build settings have been loaded and evaluated.</div>
</td>
</tr>
<tr id="i9" class="rowColor">
<td class="colFirst"><code>void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#beforeSettings-org.gradle.api.Action-">beforeSettings</a></span>&#8203;(<a href="../Action.html" title="interface in org.gradle.api">Action</a>&lt;? super <a href="../initialization/Settings.html" title="interface in org.gradle.api.initialization">Settings</a>&gt;&nbsp;action)</code></th>
<td class="colLast">
<div class="block">Adds an action to be called before the build settings have been loaded and evaluated.</div>
</td>
</tr>
<tr id="i10" class="altColor">
<td class="colFirst"><code>void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#buildFinished-groovy.lang.Closure-">buildFinished</a></span>&#8203;(<a href="https://docs.groovy-lang.org/docs/groovy-3.0.17/html/gapi/groovy/lang/Closure.html?is-external=true" title="class or interface in groovy.lang" class="externalLink">Closure</a>&nbsp;closure)</code></th>
<td class="colLast">
<div class="block"><span class="deprecatedLabel">Deprecated.</span>
<div class="deprecationComment">This method is not supported when configuration caching is enabled.</div>
</div>
</td>
</tr>
<tr id="i11" class="rowColor">
<td class="colFirst"><code>void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#buildFinished-org.gradle.api.Action-">buildFinished</a></span>&#8203;(<a href="../Action.html" title="interface in org.gradle.api">Action</a>&lt;? super <a href="../../BuildResult.html" title="class in org.gradle">BuildResult</a>&gt;&nbsp;action)</code></th>
<td class="colLast">
<div class="block"><span class="deprecatedLabel">Deprecated.</span>
<div class="deprecationComment">This method is not supported when configuration caching is enabled.</div>
</div>
</td>
</tr>
<tr id="i12" class="altColor">
<td class="colFirst"><code><a href="Gradle.html" title="interface in org.gradle.api.invocation">Gradle</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getGradle--">getGradle</a></span>()</code></th>
<td class="colLast">
<div class="block">Returns this <code>Gradle</code> instance.</div>
</td>
</tr>
<tr id="i13" class="rowColor">
<td class="colFirst"><code>java.io.File</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getGradleHomeDir--">getGradleHomeDir</a></span>()</code></th>
<td class="colLast">
<div class="block">Returns the Gradle home directory, if any.</div>
</td>
</tr>
<tr id="i14" class="altColor">
<td class="colFirst"><code>java.io.File</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getGradleUserHomeDir--">getGradleUserHomeDir</a></span>()</code></th>
<td class="colLast">
<div class="block">Returns the Gradle user home directory.</div>
</td>
</tr>
<tr id="i15" class="rowColor">
<td class="colFirst"><code>java.lang.String</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getGradleVersion--">getGradleVersion</a></span>()</code></th>
<td class="colLast">
<div class="block">Returns the current Gradle version.</div>
</td>
</tr>
<tr id="i16" class="altColor">
<td class="colFirst"><code>java.util.Collection&lt;<a href="../initialization/IncludedBuild.html" title="interface in org.gradle.api.initialization">IncludedBuild</a>&gt;</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getIncludedBuilds--">getIncludedBuilds</a></span>()</code></th>
<td class="colLast">
<div class="block">Returns the included builds for this build.</div>
</td>
</tr>
<tr id="i17" class="rowColor">
<td class="colFirst"><code><a href="Gradle.html" title="interface in org.gradle.api.invocation">Gradle</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getParent--">getParent</a></span>()</code></th>
<td class="colLast">
<div class="block">Returns the parent build of this build, if any.</div>
</td>
</tr>
<tr id="i18" class="altColor">
<td class="colFirst"><code><a href="../Project.html" title="interface in org.gradle.api">Project</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getRootProject--">getRootProject</a></span>()</code></th>
<td class="colLast">
<div class="block">Returns the root project of this build.</div>
</td>
</tr>
<tr id="i19" class="rowColor">
<td class="colFirst"><code><a href="../services/BuildServiceRegistry.html" title="interface in org.gradle.api.services">BuildServiceRegistry</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getSharedServices--">getSharedServices</a></span>()</code></th>
<td class="colLast">
<div class="block">Returns the build services that are shared by all projects of this build.</div>
</td>
</tr>
<tr id="i20" class="altColor">
<td class="colFirst"><code><a href="../../StartParameter.html" title="class in org.gradle">StartParameter</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getStartParameter--">getStartParameter</a></span>()</code></th>
<td class="colLast">
<div class="block">Returns the <a href="../../StartParameter.html" title="class in org.gradle"><code>StartParameter</code></a> used to start this build.</div>
</td>
</tr>
<tr id="i21" class="rowColor">
<td class="colFirst"><code><a href="../execution/TaskExecutionGraph.html" title="interface in org.gradle.api.execution">TaskExecutionGraph</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getTaskGraph--">getTaskGraph</a></span>()</code></th>
<td class="colLast">
<div class="block">Returns the <a href="../execution/TaskExecutionGraph.html" title="interface in org.gradle.api.execution"><code>TaskExecutionGraph</code></a> for this build.</div>
</td>
</tr>
<tr id="i22" class="altColor">
<td class="colFirst"><code><a href="../initialization/IncludedBuild.html" title="interface in org.gradle.api.initialization">IncludedBuild</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#includedBuild-java.lang.String-">includedBuild</a></span>&#8203;(java.lang.String&nbsp;name)</code></th>
<td class="colLast">
<div class="block">Returns the included build with the specified name for this build.</div>
</td>
</tr>
<tr id="i23" class="rowColor">
<td class="colFirst"><code>void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#projectsEvaluated-groovy.lang.Closure-">projectsEvaluated</a></span>&#8203;(<a href="https://docs.groovy-lang.org/docs/groovy-3.0.17/html/gapi/groovy/lang/Closure.html?is-external=true" title="class or interface in groovy.lang" class="externalLink">Closure</a>&nbsp;closure)</code></th>
<td class="colLast">
<div class="block">Adds a closure to be called when all projects for the build have been evaluated.</div>
</td>
</tr>
<tr id="i24" class="altColor">
<td class="colFirst"><code>void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#projectsEvaluated-org.gradle.api.Action-">projectsEvaluated</a></span>&#8203;(<a href="../Action.html" title="interface in org.gradle.api">Action</a>&lt;? super <a href="Gradle.html" title="interface in org.gradle.api.invocation">Gradle</a>&gt;&nbsp;action)</code></th>
<td class="colLast">
<div class="block">Adds an action to be called when all projects for the build have been evaluated.</div>
</td>
</tr>
<tr id="i25" class="rowColor">
<td class="colFirst"><code>void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#projectsLoaded-groovy.lang.Closure-">projectsLoaded</a></span>&#8203;(<a href="https://docs.groovy-lang.org/docs/groovy-3.0.17/html/gapi/groovy/lang/Closure.html?is-external=true" title="class or interface in groovy.lang" class="externalLink">Closure</a>&nbsp;closure)</code></th>
<td class="colLast">
<div class="block">Adds a closure to be called when the projects for the build have been created from the settings.</div>
</td>
</tr>
<tr id="i26" class="altColor">
<td class="colFirst"><code>void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#projectsLoaded-org.gradle.api.Action-">projectsLoaded</a></span>&#8203;(<a href="../Action.html" title="interface in org.gradle.api">Action</a>&lt;? super <a href="Gradle.html" title="interface in org.gradle.api.invocation">Gradle</a>&gt;&nbsp;action)</code></th>
<td class="colLast">
<div class="block">Adds an action to be called when the projects for the build have been created from the settings.</div>
</td>
</tr>
<tr id="i27" class="rowColor">
<td class="colFirst"><code>void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#removeListener-java.lang.Object-">removeListener</a></span>&#8203;(java.lang.Object&nbsp;listener)</code></th>
<td class="colLast">
<div class="block">Removes the given listener from this build.</div>
</td>
</tr>
<tr id="i28" class="altColor">
<td class="colFirst"><code>void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#removeProjectEvaluationListener-org.gradle.api.ProjectEvaluationListener-">removeProjectEvaluationListener</a></span>&#8203;(<a href="../ProjectEvaluationListener.html" title="interface in org.gradle.api">ProjectEvaluationListener</a>&nbsp;listener)</code></th>
<td class="colLast">
<div class="block">Removes the given listener from this build.</div>
</td>
</tr>
<tr id="i29" class="rowColor">
<td class="colFirst"><code>void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#rootProject-org.gradle.api.Action-">rootProject</a></span>&#8203;(<a href="../Action.html" title="interface in org.gradle.api">Action</a>&lt;? super <a href="../Project.html" title="interface in org.gradle.api">Project</a>&gt;&nbsp;action)</code></th>
<td class="colLast">
<div class="block">Adds an action to execute against the root project of this build.</div>
</td>
</tr>
<tr id="i30" class="altColor">
<td class="colFirst"><code>void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#settingsEvaluated-groovy.lang.Closure-">settingsEvaluated</a></span>&#8203;(<a href="https://docs.groovy-lang.org/docs/groovy-3.0.17/html/gapi/groovy/lang/Closure.html?is-external=true" title="class or interface in groovy.lang" class="externalLink">Closure</a>&nbsp;closure)</code></th>
<td class="colLast">
<div class="block">Adds a closure to be called when the build settings have been loaded and evaluated.</div>
</td>
</tr>
<tr id="i31" class="rowColor">
<td class="colFirst"><code>void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#settingsEvaluated-org.gradle.api.Action-">settingsEvaluated</a></span>&#8203;(<a href="../Action.html" title="interface in org.gradle.api">Action</a>&lt;? super <a href="../initialization/Settings.html" title="interface in org.gradle.api.initialization">Settings</a>&gt;&nbsp;action)</code></th>
<td class="colLast">
<div class="block">Adds an action to be called when the build settings have been loaded and evaluated.</div>
</td>
</tr>
<tr id="i32" class="altColor">
<td class="colFirst"><code>void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#useLogger-java.lang.Object-">useLogger</a></span>&#8203;(java.lang.Object&nbsp;logger)</code></th>
<td class="colLast">
<div class="block">Uses the given object as a logger.</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.org.gradle.api.plugins.ExtensionAware">
<!--   -->
</a>
<h3>Methods inherited from interface&nbsp;org.gradle.api.plugins.<a href="../plugins/ExtensionAware.html" title="interface in org.gradle.api.plugins">ExtensionAware</a></h3>
<code><a href="../plugins/ExtensionAware.html#getExtensions--">getExtensions</a></code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.org.gradle.api.plugins.PluginAware">
<!--   -->
</a>
<h3>Methods inherited from interface&nbsp;org.gradle.api.plugins.<a href="../plugins/PluginAware.html" title="interface in org.gradle.api.plugins">PluginAware</a></h3>
<code><a href="../plugins/PluginAware.html#apply-groovy.lang.Closure-">apply</a>, <a href="../plugins/PluginAware.html#apply-java.util.Map-">apply</a>, <a href="../plugins/PluginAware.html#apply-org.gradle.api.Action-">apply</a>, <a href="../plugins/PluginAware.html#getPluginManager--">getPluginManager</a>, <a href="../plugins/PluginAware.html#getPlugins--">getPlugins</a></code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="getGradleVersion--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getGradleVersion</h4>
<pre class="methodSignature">java.lang.String&nbsp;getGradleVersion()</pre>
<div class="block">Returns the current Gradle version.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>The Gradle version. Never returns null.</dd>
</dl>
</li>
</ul>
<a name="getGradleUserHomeDir--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getGradleUserHomeDir</h4>
<pre class="methodSignature">java.io.File&nbsp;getGradleUserHomeDir()</pre>
<div class="block">Returns the Gradle user home directory.

 This directory is used to cache downloaded resources, compiled build scripts and so on.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>The user home directory. Never returns null.</dd>
</dl>
</li>
</ul>
<a name="getGradleHomeDir--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getGradleHomeDir</h4>
<pre class="methodSignature">@Nullable
java.io.File&nbsp;getGradleHomeDir()</pre>
<div class="block">Returns the Gradle home directory, if any.

 This directory is the directory containing the Gradle distribution executing this build.
 <p>
 When using the “Gradle Daemon”, this may not be the same Gradle distribution that the build was started with.
 If an existing daemon process is running that is deemed compatible (e.g. has the desired JVM characteristics)
 then this daemon may be used instead of starting a new process and it may have been started from a different “gradle home”.
 However, it is guaranteed to be the same version of Gradle. For more information on the Gradle Daemon, please consult the
 <a href="https://docs.gradle.org/current/userguide/gradle_daemon.html" target="_top">User Manual</a>.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>The home directory. May return null.</dd>
</dl>
</li>
</ul>
<a name="getParent--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getParent</h4>
<pre class="methodSignature">@Nullable
<a href="Gradle.html" title="interface in org.gradle.api.invocation">Gradle</a>&nbsp;getParent()</pre>
<div class="block">Returns the parent build of this build, if any.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>The parent build. May return null.</dd>
</dl>
</li>
</ul>
<a name="getRootProject--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getRootProject</h4>
<pre class="methodSignature"><a href="../Project.html" title="interface in org.gradle.api">Project</a>&nbsp;getRootProject()
                throws java.lang.IllegalStateException</pre>
<div class="block">Returns the root project of this build.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>The root project. Never returns null.</dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code>java.lang.IllegalStateException</code> - When called before the root project is available.</dd>
</dl>
</li>
</ul>
<a name="rootProject-org.gradle.api.Action-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>rootProject</h4>
<pre class="methodSignature">void&nbsp;rootProject&#8203;(<a href="../Action.html" title="interface in org.gradle.api">Action</a>&lt;? super <a href="../Project.html" title="interface in org.gradle.api">Project</a>&gt;&nbsp;action)</pre>
<div class="block">Adds an action to execute against the root project of this build.

 If the root project is already available, the action
 is executed immediately. Otherwise, the action is executed when the root project becomes available.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>action</code> - The action to execute.</dd>
</dl>
</li>
</ul>
<a name="allprojects-org.gradle.api.Action-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>allprojects</h4>
<pre class="methodSignature">void&nbsp;allprojects&#8203;(<a href="../Action.html" title="interface in org.gradle.api">Action</a>&lt;? super <a href="../Project.html" title="interface in org.gradle.api">Project</a>&gt;&nbsp;action)</pre>
<div class="block">Adds an action to execute against all projects of this build.

 The action is executed immediately against all projects which are
 already available. It is also executed as subsequent projects are added to this build.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>action</code> - The action to execute.</dd>
</dl>
</li>
</ul>
<a name="getTaskGraph--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getTaskGraph</h4>
<pre class="methodSignature"><a href="../execution/TaskExecutionGraph.html" title="interface in org.gradle.api.execution">TaskExecutionGraph</a>&nbsp;getTaskGraph()</pre>
<div class="block">Returns the <a href="../execution/TaskExecutionGraph.html" title="interface in org.gradle.api.execution"><code>TaskExecutionGraph</code></a> for this build.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>The task graph. Never returns null.</dd>
</dl>
</li>
</ul>
<a name="getStartParameter--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getStartParameter</h4>
<pre class="methodSignature"><a href="../../StartParameter.html" title="class in org.gradle">StartParameter</a>&nbsp;getStartParameter()</pre>
<div class="block">Returns the <a href="../../StartParameter.html" title="class in org.gradle"><code>StartParameter</code></a> used to start this build.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>The start parameter. Never returns null.</dd>
</dl>
</li>
</ul>
<a name="addProjectEvaluationListener-org.gradle.api.ProjectEvaluationListener-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>addProjectEvaluationListener</h4>
<pre class="methodSignature"><a href="../ProjectEvaluationListener.html" title="interface in org.gradle.api">ProjectEvaluationListener</a>&nbsp;addProjectEvaluationListener&#8203;(<a href="../ProjectEvaluationListener.html" title="interface in org.gradle.api">ProjectEvaluationListener</a>&nbsp;listener)</pre>
<div class="block">Adds a listener to this build, to receive notifications as projects are evaluated.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>listener</code> - The listener to add. Does nothing if this listener has already been added.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>The added listener.</dd>
</dl>
</li>
</ul>
<a name="removeProjectEvaluationListener-org.gradle.api.ProjectEvaluationListener-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>removeProjectEvaluationListener</h4>
<pre class="methodSignature">void&nbsp;removeProjectEvaluationListener&#8203;(<a href="../ProjectEvaluationListener.html" title="interface in org.gradle.api">ProjectEvaluationListener</a>&nbsp;listener)</pre>
<div class="block">Removes the given listener from this build.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>listener</code> - The listener to remove. Does nothing if this listener has not been added.</dd>
</dl>
</li>
</ul>
<a name="beforeProject-groovy.lang.Closure-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>beforeProject</h4>
<pre class="methodSignature">void&nbsp;beforeProject&#8203;(<a href="https://docs.groovy-lang.org/docs/groovy-3.0.17/html/gapi/groovy/lang/Closure.html?is-external=true" title="class or interface in groovy.lang" class="externalLink">Closure</a>&nbsp;closure)</pre>
<div class="block">Adds a closure to be called immediately before a project is evaluated. The project is passed to the closure as a
 parameter.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>closure</code> - The closure to execute.</dd>
</dl>
</li>
</ul>
<a name="beforeProject-org.gradle.api.Action-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>beforeProject</h4>
<pre class="methodSignature">void&nbsp;beforeProject&#8203;(<a href="../Action.html" title="interface in org.gradle.api">Action</a>&lt;? super <a href="../Project.html" title="interface in org.gradle.api">Project</a>&gt;&nbsp;action)</pre>
<div class="block">Adds an action to be called immediately before a project is evaluated.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>action</code> - The action to execute.</dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>3.4</dd>
</dl>
</li>
</ul>
<a name="afterProject-groovy.lang.Closure-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>afterProject</h4>
<pre class="methodSignature">void&nbsp;afterProject&#8203;(<a href="https://docs.groovy-lang.org/docs/groovy-3.0.17/html/gapi/groovy/lang/Closure.html?is-external=true" title="class or interface in groovy.lang" class="externalLink">Closure</a>&nbsp;closure)</pre>
<div class="block">Adds a closure to be called immediately after a project is evaluated.

 The project is passed to the closure as the first parameter. The project evaluation failure, if any,
 is passed as the second parameter. Both parameters are optional.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>closure</code> - The closure to execute.</dd>
</dl>
</li>
</ul>
<a name="afterProject-org.gradle.api.Action-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>afterProject</h4>
<pre class="methodSignature">void&nbsp;afterProject&#8203;(<a href="../Action.html" title="interface in org.gradle.api">Action</a>&lt;? super <a href="../Project.html" title="interface in org.gradle.api">Project</a>&gt;&nbsp;action)</pre>
<div class="block">Adds an action to be called immediately after a project is evaluated.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>action</code> - The action to execute.</dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>3.4</dd>
</dl>
</li>
</ul>
<a name="beforeSettings-groovy.lang.Closure-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>beforeSettings</h4>
<pre class="methodSignature">void&nbsp;beforeSettings&#8203;(<a href="https://docs.groovy-lang.org/docs/groovy-3.0.17/html/gapi/groovy/lang/DelegatesTo.html?is-external=true" title="class or interface in groovy.lang" class="externalLink">@DelegatesTo</a>(<a href="../initialization/Settings.html" title="interface in org.gradle.api.initialization">Settings.class</a>)
                    <a href="https://docs.groovy-lang.org/docs/groovy-3.0.17/html/gapi/groovy/lang/Closure.html?is-external=true" title="class or interface in groovy.lang" class="externalLink">Closure</a>&lt;?&gt;&nbsp;closure)</pre>
<div class="block">Adds an action to be called before the build settings have been loaded and evaluated.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>closure</code> - The action to execute.</dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>6.0</dd>
</dl>
</li>
</ul>
<a name="beforeSettings-org.gradle.api.Action-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>beforeSettings</h4>
<pre class="methodSignature">void&nbsp;beforeSettings&#8203;(<a href="../Action.html" title="interface in org.gradle.api">Action</a>&lt;? super <a href="../initialization/Settings.html" title="interface in org.gradle.api.initialization">Settings</a>&gt;&nbsp;action)</pre>
<div class="block">Adds an action to be called before the build settings have been loaded and evaluated.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>action</code> - The action to execute.</dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>6.0</dd>
</dl>
</li>
</ul>
<a name="settingsEvaluated-groovy.lang.Closure-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>settingsEvaluated</h4>
<pre class="methodSignature">void&nbsp;settingsEvaluated&#8203;(<a href="https://docs.groovy-lang.org/docs/groovy-3.0.17/html/gapi/groovy/lang/Closure.html?is-external=true" title="class or interface in groovy.lang" class="externalLink">Closure</a>&nbsp;closure)</pre>
<div class="block">Adds a closure to be called when the build settings have been loaded and evaluated.

 The settings object is fully configured and is ready to use to load the build projects. The
 <a href="../initialization/Settings.html" title="interface in org.gradle.api.initialization"><code>Settings</code></a> object is passed to the closure as a parameter.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>closure</code> - The closure to execute.</dd>
</dl>
</li>
</ul>
<a name="settingsEvaluated-org.gradle.api.Action-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>settingsEvaluated</h4>
<pre class="methodSignature">void&nbsp;settingsEvaluated&#8203;(<a href="../Action.html" title="interface in org.gradle.api">Action</a>&lt;? super <a href="../initialization/Settings.html" title="interface in org.gradle.api.initialization">Settings</a>&gt;&nbsp;action)</pre>
<div class="block">Adds an action to be called when the build settings have been loaded and evaluated.

 The settings object is fully configured and is ready to use to load the build projects.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>action</code> - The action to execute.</dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>3.4</dd>
</dl>
</li>
</ul>
<a name="projectsLoaded-groovy.lang.Closure-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>projectsLoaded</h4>
<pre class="methodSignature">void&nbsp;projectsLoaded&#8203;(<a href="https://docs.groovy-lang.org/docs/groovy-3.0.17/html/gapi/groovy/lang/Closure.html?is-external=true" title="class or interface in groovy.lang" class="externalLink">Closure</a>&nbsp;closure)</pre>
<div class="block">Adds a closure to be called when the projects for the build have been created from the settings.

 None of the projects have been evaluated. This <code>Gradle</code> instance is passed to the closure as a parameter.
 <p>
 An example of hooking into the projectsLoaded to configure buildscript classpath from the init script.
 <pre class='autoTested'>
 //init.gradle
 gradle.projectsLoaded {
   rootProject.buildscript {
     repositories {
       //...
     }
     dependencies {
       //...
     }
   }
 }
 </pre></div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>closure</code> - The closure to execute.</dd>
</dl>
</li>
</ul>
<a name="projectsLoaded-org.gradle.api.Action-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>projectsLoaded</h4>
<pre class="methodSignature">void&nbsp;projectsLoaded&#8203;(<a href="../Action.html" title="interface in org.gradle.api">Action</a>&lt;? super <a href="Gradle.html" title="interface in org.gradle.api.invocation">Gradle</a>&gt;&nbsp;action)</pre>
<div class="block">Adds an action to be called when the projects for the build have been created from the settings.

 None of the projects have been evaluated.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>action</code> - The action to execute.</dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>3.4</dd>
</dl>
</li>
</ul>
<a name="projectsEvaluated-groovy.lang.Closure-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>projectsEvaluated</h4>
<pre class="methodSignature">void&nbsp;projectsEvaluated&#8203;(<a href="https://docs.groovy-lang.org/docs/groovy-3.0.17/html/gapi/groovy/lang/Closure.html?is-external=true" title="class or interface in groovy.lang" class="externalLink">Closure</a>&nbsp;closure)</pre>
<div class="block">Adds a closure to be called when all projects for the build have been evaluated.

 The project objects are fully configured and are ready to use to populate the task graph.
 This <code>Gradle</code> instance is passed to the closure as a parameter.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>closure</code> - The closure to execute.</dd>
</dl>
</li>
</ul>
<a name="projectsEvaluated-org.gradle.api.Action-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>projectsEvaluated</h4>
<pre class="methodSignature">void&nbsp;projectsEvaluated&#8203;(<a href="../Action.html" title="interface in org.gradle.api">Action</a>&lt;? super <a href="Gradle.html" title="interface in org.gradle.api.invocation">Gradle</a>&gt;&nbsp;action)</pre>
<div class="block">Adds an action to be called when all projects for the build have been evaluated.

 The project objects are fully configured and are ready to use to populate the task graph.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>action</code> - The action to execute.</dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>3.4</dd>
</dl>
</li>
</ul>
<a name="buildFinished-groovy.lang.Closure-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>buildFinished</h4>
<pre class="methodSignature">@Deprecated
void&nbsp;buildFinished&#8203;(<a href="https://docs.groovy-lang.org/docs/groovy-3.0.17/html/gapi/groovy/lang/Closure.html?is-external=true" title="class or interface in groovy.lang" class="externalLink">Closure</a>&nbsp;closure)</pre>
<div class="deprecationBlock"><span class="deprecatedLabel">Deprecated.</span>
<div class="deprecationComment">This method is not supported when configuration caching is enabled.</div>
</div>
<div class="block">Adds a closure to be called when the build is completed.

 All selected tasks have been executed.
 A <a href="../../BuildResult.html" title="class in org.gradle"><code>BuildResult</code></a> instance is passed to the closure as a parameter.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>closure</code> - The closure to execute.</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../flow/FlowProviders.html#getBuildWorkResult--"><code>FlowProviders.getBuildWorkResult()</code></a></dd>
</dl>
</li>
</ul>
<a name="buildFinished-org.gradle.api.Action-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>buildFinished</h4>
<pre class="methodSignature">@Deprecated
void&nbsp;buildFinished&#8203;(<a href="../Action.html" title="interface in org.gradle.api">Action</a>&lt;? super <a href="../../BuildResult.html" title="class in org.gradle">BuildResult</a>&gt;&nbsp;action)</pre>
<div class="deprecationBlock"><span class="deprecatedLabel">Deprecated.</span>
<div class="deprecationComment">This method is not supported when configuration caching is enabled.</div>
</div>
<div class="block">Adds an action to be called when the build is completed.

 All selected tasks have been executed.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>action</code> - The action to execute.</dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>3.4</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../flow/FlowProviders.html#getBuildWorkResult--"><code>FlowProviders.getBuildWorkResult()</code></a></dd>
</dl>
</li>
</ul>
<a name="addBuildListener-org.gradle.BuildListener-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>addBuildListener</h4>
<pre class="methodSignature">void&nbsp;addBuildListener&#8203;(<a href="../../BuildListener.html" title="interface in org.gradle">BuildListener</a>&nbsp;buildListener)</pre>
<div class="block">Adds a <a href="../../BuildListener.html" title="interface in org.gradle"><code>BuildListener</code></a> to this Build instance.

 The listener is notified of events which occur during the execution of the build.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>buildListener</code> - The listener to add.</dd>
</dl>
</li>
</ul>
<a name="addListener-java.lang.Object-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>addListener</h4>
<pre class="methodSignature">void&nbsp;addListener&#8203;(java.lang.Object&nbsp;listener)</pre>
<div class="block">Adds the given listener to this build. The listener may implement any of the given listener interfaces:

 <ul>
 <li><a href="../execution/TaskExecutionGraphListener.html" title="interface in org.gradle.api.execution"><code>TaskExecutionGraphListener</code></a>
 <li><a href="../ProjectEvaluationListener.html" title="interface in org.gradle.api"><code>ProjectEvaluationListener</code></a>
 <li><a href="../artifacts/DependencyResolutionListener.html" title="interface in org.gradle.api.artifacts"><code>DependencyResolutionListener</code></a>
 </ul>

 <p>The following listener types can be used, but are not supported when configuration caching is enabled.
 Their usage is deprecated and adding a listener of these types become an error in a future Gradle version:</p>

 <ul>
 <li><a href="../../BuildListener.html" title="interface in org.gradle"><code>BuildListener</code></a>
 <li><a href="../execution/TaskExecutionListener.html" title="interface in org.gradle.api.execution"><code>TaskExecutionListener</code></a>
 <li><a href="../execution/TaskActionListener.html" title="interface in org.gradle.api.execution"><code>TaskActionListener</code></a>
 <li><a href="../tasks/testing/TestListener.html" title="interface in org.gradle.api.tasks.testing"><code>TestListener</code></a>
 <li><a href="../tasks/testing/TestOutputListener.html" title="interface in org.gradle.api.tasks.testing"><code>TestOutputListener</code></a>
 </ul></div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>listener</code> - The listener to add. Does nothing if this listener has already been added.</dd>
</dl>
</li>
</ul>
<a name="removeListener-java.lang.Object-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>removeListener</h4>
<pre class="methodSignature">void&nbsp;removeListener&#8203;(java.lang.Object&nbsp;listener)</pre>
<div class="block">Removes the given listener from this build.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>listener</code> - The listener to remove. Does nothing if this listener has not been added.</dd>
</dl>
</li>
</ul>
<a name="useLogger-java.lang.Object-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>useLogger</h4>
<pre class="methodSignature">void&nbsp;useLogger&#8203;(java.lang.Object&nbsp;logger)</pre>
<div class="block">Uses the given object as a logger.

 The logger object may implement any of the listener interfaces supported by
 <a href="#addListener-java.lang.Object-"><code>addListener(Object)</code></a>.
 <p>
 Each listener interface has exactly one associated logger. When you call this
 method with a logger of a given listener type, the new logger will replace whichever logger is currently
 associated with the listener type. This allows you to selectively replace the standard logging which Gradle
 provides with your own implementation, for certain types of events.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>logger</code> - The logger to use.</dd>
</dl>
</li>
</ul>
<a name="getGradle--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getGradle</h4>
<pre class="methodSignature"><a href="Gradle.html" title="interface in org.gradle.api.invocation">Gradle</a>&nbsp;getGradle()</pre>
<div class="block">Returns this <code>Gradle</code> instance.

 This method is useful in init scripts to explicitly access Gradle
 properties and methods. For example, using <code>gradle.parent</code> can express your intent better than using
 <code>parent</code>. This property also allows you to access Gradle properties from a scope where the property
 may be hidden, such as, for example, from a method or closure.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>this. Never returns null.</dd>
</dl>
</li>
</ul>
<a name="getSharedServices--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getSharedServices</h4>
<pre class="methodSignature"><a href="../services/BuildServiceRegistry.html" title="interface in org.gradle.api.services">BuildServiceRegistry</a>&nbsp;getSharedServices()</pre>
<div class="block">Returns the build services that are shared by all projects of this build.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>6.1</dd>
</dl>
</li>
</ul>
<a name="getIncludedBuilds--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getIncludedBuilds</h4>
<pre class="methodSignature">java.util.Collection&lt;<a href="../initialization/IncludedBuild.html" title="interface in org.gradle.api.initialization">IncludedBuild</a>&gt;&nbsp;getIncludedBuilds()</pre>
<div class="block">Returns the included builds for this build.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>3.1</dd>
</dl>
</li>
</ul>
<a name="includedBuild-java.lang.String-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>includedBuild</h4>
<pre class="methodSignature"><a href="../initialization/IncludedBuild.html" title="interface in org.gradle.api.initialization">IncludedBuild</a>&nbsp;includedBuild&#8203;(java.lang.String&nbsp;name)
                     throws <a href="../UnknownDomainObjectException.html" title="class in org.gradle.api">UnknownDomainObjectException</a></pre>
<div class="block">Returns the included build with the specified name for this build.</div>
<dl>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code><a href="../UnknownDomainObjectException.html" title="class in org.gradle.api">UnknownDomainObjectException</a></code> - when there is no build with the given name</dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>3.1</dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../allclasses.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
