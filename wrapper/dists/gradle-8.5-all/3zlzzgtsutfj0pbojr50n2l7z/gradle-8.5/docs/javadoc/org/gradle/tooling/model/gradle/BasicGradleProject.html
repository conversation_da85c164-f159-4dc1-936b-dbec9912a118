<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc -->
<title>BasicGradleProject (Gradle API 8.5)</title>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<link rel="stylesheet" type="text/css" href="../../../../../javadoc.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../../jquery/jquery-ui.min.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../../jquery-ui.overrides.css" title="Style">
<script type="text/javascript" src="../../../../../script.js"></script>
<script type="text/javascript" src="../../../../../jquery/jszip/dist/jszip.min.js"></script>
<script type="text/javascript" src="../../../../../jquery/jszip-utils/dist/jszip-utils.min.js"></script>
<!--[if IE]>
<script type="text/javascript" src="../../../../../jquery/jszip-utils/dist/jszip-utils-ie.min.js"></script>
<![endif]-->
<script type="text/javascript" src="../../../../../jquery/jquery-3.6.1.min.js"></script>
<script type="text/javascript" src="../../../../../jquery/jquery-ui.min.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="BasicGradleProject (Gradle API 8.5)";
        }
    }
    catch(err) {
    }
//-->
var data = {"i0":6,"i1":6,"i2":6,"i3":6,"i4":6,"i5":6,"i6":6};
var tabs = {65535:["t0","All Methods"],2:["t2","Instance Methods"],4:["t3","Abstract Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
var pathtoroot = "../../../../../";
var useModuleDirectories = false;
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<div class="fixedNav">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../index-all.html">Index</a></li>
<li><a href="../../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../../allclasses.html">All&nbsp;Classes</a></li>
</ul>
<ul class="navListSearch">
<li><label for="search">SEARCH:</label>
<input type="text" id="search" value="search" disabled="disabled">
<input type="reset" id="reset" value="reset" disabled="disabled">
</li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
</div>
<div class="navPadding">&nbsp;</div>
<script type="text/javascript"><!--
$('.navPadding').css('padding-top', $('.fixedNav').css("height"));
//-->
</script>
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle"><span class="packageLabelInType">Package</span>&nbsp;<a href="package-summary.html">org.gradle.tooling.model.gradle</a></div>
<h2 title="Interface BasicGradleProject" class="title">Interface BasicGradleProject</h2>
</div>
<div class="contentContainer">
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt>All Superinterfaces:</dt>
<dd><code><a href="../Model.html" title="interface in org.gradle.tooling.model">Model</a></code>, <code><a href="../ProjectModel.html" title="interface in org.gradle.tooling.model">ProjectModel</a></code></dd>
</dl>
<hr>
<pre>public interface <span class="typeNameLabel">BasicGradleProject</span>
extends <a href="../Model.html" title="interface in org.gradle.tooling.model">Model</a>, <a href="../ProjectModel.html" title="interface in org.gradle.tooling.model">ProjectModel</a></pre>
<div class="block">Provides some basic details about a Gradle project.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>1.8</dd>
</dl>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t3" class="tableTab"><span><a href="javascript:show(4);">Abstract Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colSecond" scope="col">Method</th>
<th class="colLast" scope="col">Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>java.lang.String</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getBuildTreePath--">getBuildTreePath</a></span>()</code></th>
<td class="colLast">
<div class="block">Returns a path to the project for the full build tree</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code><a href="../DomainObjectSet.html" title="interface in org.gradle.tooling.model">DomainObjectSet</a>&lt;? extends <a href="BasicGradleProject.html" title="interface in org.gradle.tooling.model.gradle">BasicGradleProject</a>&gt;</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getChildren--">getChildren</a></span>()</code></th>
<td class="colLast">
<div class="block">Returns the child projects of this project, or the empty set if there are no child projects.</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>java.lang.String</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getName--">getName</a></span>()</code></th>
<td class="colLast">
<div class="block">Returns the name of this project.</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code><a href="BasicGradleProject.html" title="interface in org.gradle.tooling.model.gradle">BasicGradleProject</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getParent--">getParent</a></span>()</code></th>
<td class="colLast">
<div class="block">Returns the parent of this project, or <code>null</code> if this is the root project.</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code>java.lang.String</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getPath--">getPath</a></span>()</code></th>
<td class="colLast">
<div class="block">Returns the path of this project.</div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code>java.io.File</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getProjectDirectory--">getProjectDirectory</a></span>()</code></th>
<td class="colLast">
<div class="block">Returns the project directory for this project.</div>
</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code><a href="../ProjectIdentifier.html" title="interface in org.gradle.tooling.model">ProjectIdentifier</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getProjectIdentifier--">getProjectIdentifier</a></span>()</code></th>
<td class="colLast">
<div class="block">Returns the identifier for this Gradle project.</div>
</td>
</tr>
</table>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="getProjectIdentifier--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getProjectIdentifier</h4>
<pre class="methodSignature"><a href="../ProjectIdentifier.html" title="interface in org.gradle.tooling.model">ProjectIdentifier</a>&nbsp;getProjectIdentifier()</pre>
<div class="block">Returns the identifier for this Gradle project.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../ProjectModel.html#getProjectIdentifier--">getProjectIdentifier</a></code>&nbsp;in interface&nbsp;<code><a href="../ProjectModel.html" title="interface in org.gradle.tooling.model">ProjectModel</a></code></dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>2.13</dd>
</dl>
</li>
</ul>
<a name="getName--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getName</h4>
<pre class="methodSignature">java.lang.String&nbsp;getName()</pre>
<div class="block">Returns the name of this project. Note that the name is not a unique identifier for the project.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>The name of this project.</dd>
</dl>
</li>
</ul>
<a name="getPath--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getPath</h4>
<pre class="methodSignature">java.lang.String&nbsp;getPath()</pre>
<div class="block">Returns the path of this project. The path can be used as a unique identifier for the project within a given build.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>The path of this project.</dd>
</dl>
</li>
</ul>
<a name="getProjectDirectory--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getProjectDirectory</h4>
<pre class="methodSignature">java.io.File&nbsp;getProjectDirectory()</pre>
<div class="block">Returns the project directory for this project.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>The project directory.</dd>
</dl>
</li>
</ul>
<a name="getParent--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getParent</h4>
<pre class="methodSignature">@Nullable
<a href="BasicGradleProject.html" title="interface in org.gradle.tooling.model.gradle">BasicGradleProject</a>&nbsp;getParent()</pre>
<div class="block">Returns the parent of this project, or <code>null</code> if this is the root project.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>The parent of this project, or <code>null</code> if this is the root project.</dd>
</dl>
</li>
</ul>
<a name="getChildren--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getChildren</h4>
<pre class="methodSignature"><a href="../DomainObjectSet.html" title="interface in org.gradle.tooling.model">DomainObjectSet</a>&lt;? extends <a href="BasicGradleProject.html" title="interface in org.gradle.tooling.model.gradle">BasicGradleProject</a>&gt;&nbsp;getChildren()</pre>
<div class="block">Returns the child projects of this project, or the empty set if there are no child projects.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>The child projects of this project, or the empty set if there are no child projects.</dd>
</dl>
</li>
</ul>
<a name="getBuildTreePath--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>getBuildTreePath</h4>
<pre class="methodSignature"><a href="../../../api/Incubating.html" title="annotation in org.gradle.api">@Incubating</a>
java.lang.String&nbsp;getBuildTreePath()</pre>
<div class="block">Returns a path to the project for the full build tree</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>a path to the project for the full build tree</dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code><a href="../UnsupportedMethodException.html" title="class in org.gradle.tooling.model">UnsupportedMethodException</a></code> - When the target Gradle version does not support this method.</dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>8.2</dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../index-all.html">Index</a></li>
<li><a href="../../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../../allclasses.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
