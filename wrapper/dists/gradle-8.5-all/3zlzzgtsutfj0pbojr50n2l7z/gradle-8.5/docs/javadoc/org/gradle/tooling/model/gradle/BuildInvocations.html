<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc -->
<title>BuildInvocations (Gradle API 8.5)</title>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<link rel="stylesheet" type="text/css" href="../../../../../javadoc.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../../jquery/jquery-ui.min.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../../jquery-ui.overrides.css" title="Style">
<script type="text/javascript" src="../../../../../script.js"></script>
<script type="text/javascript" src="../../../../../jquery/jszip/dist/jszip.min.js"></script>
<script type="text/javascript" src="../../../../../jquery/jszip-utils/dist/jszip-utils.min.js"></script>
<!--[if IE]>
<script type="text/javascript" src="../../../../../jquery/jszip-utils/dist/jszip-utils-ie.min.js"></script>
<![endif]-->
<script type="text/javascript" src="../../../../../jquery/jquery-3.6.1.min.js"></script>
<script type="text/javascript" src="../../../../../jquery/jquery-ui.min.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="BuildInvocations (Gradle API 8.5)";
        }
    }
    catch(err) {
    }
//-->
var data = {"i0":6,"i1":6,"i2":6};
var tabs = {65535:["t0","All Methods"],2:["t2","Instance Methods"],4:["t3","Abstract Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
var pathtoroot = "../../../../../";
var useModuleDirectories = false;
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<div class="fixedNav">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../index-all.html">Index</a></li>
<li><a href="../../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../../allclasses.html">All&nbsp;Classes</a></li>
</ul>
<ul class="navListSearch">
<li><label for="search">SEARCH:</label>
<input type="text" id="search" value="search" disabled="disabled">
<input type="reset" id="reset" value="reset" disabled="disabled">
</li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
</div>
<div class="navPadding">&nbsp;</div>
<script type="text/javascript"><!--
$('.navPadding').css('padding-top', $('.fixedNav').css("height"));
//-->
</script>
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle"><span class="packageLabelInType">Package</span>&nbsp;<a href="package-summary.html">org.gradle.tooling.model.gradle</a></div>
<h2 title="Interface BuildInvocations" class="title">Interface BuildInvocations</h2>
</div>
<div class="contentContainer">
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt>All Superinterfaces:</dt>
<dd><code><a href="../Model.html" title="interface in org.gradle.tooling.model">Model</a></code>, <code><a href="../ProjectModel.html" title="interface in org.gradle.tooling.model">ProjectModel</a></code></dd>
</dl>
<hr>
<pre>public interface <span class="typeNameLabel">BuildInvocations</span>
extends <a href="../Model.html" title="interface in org.gradle.tooling.model">Model</a>, <a href="../ProjectModel.html" title="interface in org.gradle.tooling.model">ProjectModel</a></pre>
<div class="block">A model providing access to <a href="../Launchable.html" title="interface in org.gradle.tooling.model"><code>Launchable</code></a> instances that can be used
 to initiate Gradle build.

 <p>To launch a build, you pass one or more <a href="../Launchable.html" title="interface in org.gradle.tooling.model"><code>Launchable</code></a> instances
 to either <a href="../../BuildLauncher.html#forTasks-java.lang.Iterable-"><code>BuildLauncher.forTasks(Iterable)</code></a> or <a href="../../BuildLauncher.html#forLaunchables-java.lang.Iterable-"><code>BuildLauncher.forLaunchables(Iterable)</code></a>.</p></div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>1.12</dd>
</dl>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t3" class="tableTab"><span><a href="javascript:show(4);">Abstract Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colSecond" scope="col">Method</th>
<th class="colLast" scope="col">Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code><a href="../ProjectIdentifier.html" title="interface in org.gradle.tooling.model">ProjectIdentifier</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getProjectIdentifier--">getProjectIdentifier</a></span>()</code></th>
<td class="colLast">
<div class="block">Returns the identifier for the Gradle project that these invocations originate from.</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code><a href="../DomainObjectSet.html" title="interface in org.gradle.tooling.model">DomainObjectSet</a>&lt;? extends <a href="../Task.html" title="interface in org.gradle.tooling.model">Task</a>&gt;</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getTasks--">getTasks</a></span>()</code></th>
<td class="colLast">
<div class="block">Returns the tasks that can be used to execute a build.</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code><a href="../DomainObjectSet.html" title="interface in org.gradle.tooling.model">DomainObjectSet</a>&lt;? extends <a href="../TaskSelector.html" title="interface in org.gradle.tooling.model">TaskSelector</a>&gt;</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getTaskSelectors--">getTaskSelectors</a></span>()</code></th>
<td class="colLast">
<div class="block">Returns tasks selectors that can be used to execute a build.</div>
</td>
</tr>
</table>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="getProjectIdentifier--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getProjectIdentifier</h4>
<pre class="methodSignature"><a href="../ProjectIdentifier.html" title="interface in org.gradle.tooling.model">ProjectIdentifier</a>&nbsp;getProjectIdentifier()</pre>
<div class="block">Returns the identifier for the Gradle project that these invocations originate from.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../ProjectModel.html#getProjectIdentifier--">getProjectIdentifier</a></code>&nbsp;in interface&nbsp;<code><a href="../ProjectModel.html" title="interface in org.gradle.tooling.model">ProjectModel</a></code></dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>2.13</dd>
</dl>
</li>
</ul>
<a name="getTaskSelectors--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getTaskSelectors</h4>
<pre class="methodSignature"><a href="../DomainObjectSet.html" title="interface in org.gradle.tooling.model">DomainObjectSet</a>&lt;? extends <a href="../TaskSelector.html" title="interface in org.gradle.tooling.model">TaskSelector</a>&gt;&nbsp;getTaskSelectors()</pre>
<div class="block">Returns tasks selectors that can be used to execute a build.

 Selector is a <a href="../Launchable.html" title="interface in org.gradle.tooling.model"><code>Launchable</code></a> that requests to build all tasks with a given name in context of some project and all its subprojects.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>The task selectors.</dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>1.12</dd>
</dl>
</li>
</ul>
<a name="getTasks--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>getTasks</h4>
<pre class="methodSignature"><a href="../DomainObjectSet.html" title="interface in org.gradle.tooling.model">DomainObjectSet</a>&lt;? extends <a href="../Task.html" title="interface in org.gradle.tooling.model">Task</a>&gt;&nbsp;getTasks()</pre>
<div class="block">Returns the tasks that can be used to execute a build.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>The tasks.</dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>1.12</dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../index-all.html">Index</a></li>
<li><a href="../../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../../allclasses.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
