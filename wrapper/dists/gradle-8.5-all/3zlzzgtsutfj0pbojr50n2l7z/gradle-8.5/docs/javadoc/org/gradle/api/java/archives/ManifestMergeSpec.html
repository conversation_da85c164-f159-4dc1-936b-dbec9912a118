<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc -->
<title>ManifestMergeSpec (Gradle API 8.5)</title>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<link rel="stylesheet" type="text/css" href="../../../../../javadoc.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../../jquery/jquery-ui.min.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../../jquery-ui.overrides.css" title="Style">
<script type="text/javascript" src="../../../../../script.js"></script>
<script type="text/javascript" src="../../../../../jquery/jszip/dist/jszip.min.js"></script>
<script type="text/javascript" src="../../../../../jquery/jszip-utils/dist/jszip-utils.min.js"></script>
<!--[if IE]>
<script type="text/javascript" src="../../../../../jquery/jszip-utils/dist/jszip-utils-ie.min.js"></script>
<![endif]-->
<script type="text/javascript" src="../../../../../jquery/jquery-3.6.1.min.js"></script>
<script type="text/javascript" src="../../../../../jquery/jquery-ui.min.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="ManifestMergeSpec (Gradle API 8.5)";
        }
    }
    catch(err) {
    }
//-->
var data = {"i0":6,"i1":6,"i2":6,"i3":6,"i4":6};
var tabs = {65535:["t0","All Methods"],2:["t2","Instance Methods"],4:["t3","Abstract Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
var pathtoroot = "../../../../../";
var useModuleDirectories = false;
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<div class="fixedNav">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../index-all.html">Index</a></li>
<li><a href="../../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../../allclasses.html">All&nbsp;Classes</a></li>
</ul>
<ul class="navListSearch">
<li><label for="search">SEARCH:</label>
<input type="text" id="search" value="search" disabled="disabled">
<input type="reset" id="reset" value="reset" disabled="disabled">
</li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
</div>
<div class="navPadding">&nbsp;</div>
<script type="text/javascript"><!--
$('.navPadding').css('padding-top', $('.fixedNav').css("height"));
//-->
</script>
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle"><span class="packageLabelInType">Package</span>&nbsp;<a href="package-summary.html">org.gradle.api.java.archives</a></div>
<h2 title="Interface ManifestMergeSpec" class="title">Interface ManifestMergeSpec</h2>
</div>
<div class="contentContainer">
<div class="description">
<ul class="blockList">
<li class="blockList">
<hr>
<pre>public interface <span class="typeNameLabel">ManifestMergeSpec</span></pre>
<div class="block">Specifies how the entries of multiple manifests should be merged together.</div>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t3" class="tableTab"><span><a href="javascript:show(4);">Abstract Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colSecond" scope="col">Method</th>
<th class="colLast" scope="col">Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code><a href="ManifestMergeSpec.html" title="interface in org.gradle.api.java.archives">ManifestMergeSpec</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#eachEntry-groovy.lang.Closure-">eachEntry</a></span>&#8203;(<a href="https://docs.groovy-lang.org/docs/groovy-3.0.17/html/gapi/groovy/lang/Closure.html?is-external=true" title="class or interface in groovy.lang" class="externalLink">Closure</a>&lt;?&gt;&nbsp;mergeAction)</code></th>
<td class="colLast">
<div class="block">Adds an action to be applied to each key-value tuple in a merge operation.</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code><a href="ManifestMergeSpec.html" title="interface in org.gradle.api.java.archives">ManifestMergeSpec</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#eachEntry-org.gradle.api.Action-">eachEntry</a></span>&#8203;(<a href="../../Action.html" title="interface in org.gradle.api">Action</a>&lt;? super <a href="ManifestMergeDetails.html" title="interface in org.gradle.api.java.archives">ManifestMergeDetails</a>&gt;&nbsp;mergeAction)</code></th>
<td class="colLast">
<div class="block">Adds an action to be applied to each key-value tuple in a merge operation.</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code><a href="ManifestMergeSpec.html" title="interface in org.gradle.api.java.archives">ManifestMergeSpec</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#from-java.lang.Object...-">from</a></span>&#8203;(java.lang.Object...&nbsp;mergePaths)</code></th>
<td class="colLast">
<div class="block">Adds a merge path to a manifest that should be merged into the base manifest.</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>java.lang.String</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getContentCharset--">getContentCharset</a></span>()</code></th>
<td class="colLast">
<div class="block">The character set used to decode the merged manifest content.</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code>void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#setContentCharset-java.lang.String-">setContentCharset</a></span>&#8203;(java.lang.String&nbsp;contentCharset)</code></th>
<td class="colLast">
<div class="block">The character set used to decode the merged manifest content.</div>
</td>
</tr>
</table>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="getContentCharset--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getContentCharset</h4>
<pre class="methodSignature">java.lang.String&nbsp;getContentCharset()</pre>
<div class="block">The character set used to decode the merged manifest content.
 Defaults to UTF-8.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>the character set used to decode the merged manifest content</dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>2.14</dd>
</dl>
</li>
</ul>
<a name="setContentCharset-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setContentCharset</h4>
<pre class="methodSignature">void&nbsp;setContentCharset&#8203;(java.lang.String&nbsp;contentCharset)</pre>
<div class="block">The character set used to decode the merged manifest content.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>contentCharset</code> - the character set used to decode the merged manifest content</dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>2.14</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="#getContentCharset--"><code>getContentCharset()</code></a></dd>
</dl>
</li>
</ul>
<a name="from-java.lang.Object...-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>from</h4>
<pre class="methodSignature"><a href="ManifestMergeSpec.html" title="interface in org.gradle.api.java.archives">ManifestMergeSpec</a>&nbsp;from&#8203;(java.lang.Object...&nbsp;mergePaths)</pre>
<div class="block">Adds a merge path to a manifest that should be merged into the base manifest. A merge path can be either another
 <a href="Manifest.html" title="interface in org.gradle.api.java.archives"><code>Manifest</code></a> or a path that is evaluated as per
 <a href="../../Project.html#file-java.lang.Object-"><code>Project.file(Object)</code></a> . If multiple merge paths are specified, the manifest are merged
 in the order in which they are added.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>mergePaths</code> - The paths of manifests to be merged</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>this</dd>
</dl>
</li>
</ul>
<a name="eachEntry-org.gradle.api.Action-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>eachEntry</h4>
<pre class="methodSignature"><a href="ManifestMergeSpec.html" title="interface in org.gradle.api.java.archives">ManifestMergeSpec</a>&nbsp;eachEntry&#8203;(<a href="../../Action.html" title="interface in org.gradle.api">Action</a>&lt;? super <a href="ManifestMergeDetails.html" title="interface in org.gradle.api.java.archives">ManifestMergeDetails</a>&gt;&nbsp;mergeAction)</pre>
<div class="block">Adds an action to be applied to each key-value tuple in a merge operation. If multiple merge paths are specified,
 the action is called for each key-value tuple of each merge operation. The given action is called with a
 <a href="ManifestMergeDetails.html" title="interface in org.gradle.api.java.archives"><code>ManifestMergeDetails</code></a> as its parameter. Actions are executed
 in the order added.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>mergeAction</code> - A merge action to be executed.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>this</dd>
</dl>
</li>
</ul>
<a name="eachEntry-groovy.lang.Closure-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>eachEntry</h4>
<pre class="methodSignature"><a href="ManifestMergeSpec.html" title="interface in org.gradle.api.java.archives">ManifestMergeSpec</a>&nbsp;eachEntry&#8203;(<a href="https://docs.groovy-lang.org/docs/groovy-3.0.17/html/gapi/groovy/lang/DelegatesTo.html?is-external=true" title="class or interface in groovy.lang" class="externalLink">@DelegatesTo</a>(<a href="ManifestMergeDetails.html" title="interface in org.gradle.api.java.archives">ManifestMergeDetails.class</a>)
                            <a href="https://docs.groovy-lang.org/docs/groovy-3.0.17/html/gapi/groovy/lang/Closure.html?is-external=true" title="class or interface in groovy.lang" class="externalLink">Closure</a>&lt;?&gt;&nbsp;mergeAction)</pre>
<div class="block">Adds an action to be applied to each key-value tuple in a merge operation. If multiple merge paths are specified,
 the action is called for each key-value tuple of each merge operation. The given closure is called with a
 <a href="ManifestMergeDetails.html" title="interface in org.gradle.api.java.archives"><code>ManifestMergeDetails</code></a> as its parameter. Actions are executed
 in the order added.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>mergeAction</code> - The action to execute.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>this</dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../index-all.html">Index</a></li>
<li><a href="../../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../../allclasses.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
