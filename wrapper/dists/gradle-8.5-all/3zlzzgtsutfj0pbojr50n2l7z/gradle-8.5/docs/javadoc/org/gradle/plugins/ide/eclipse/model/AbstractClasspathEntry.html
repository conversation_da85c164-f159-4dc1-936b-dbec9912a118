<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc -->
<title>AbstractClasspathEntry (Gradle API 8.5)</title>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<link rel="stylesheet" type="text/css" href="../../../../../../javadoc.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../../../jquery/jquery-ui.min.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../../../jquery-ui.overrides.css" title="Style">
<script type="text/javascript" src="../../../../../../script.js"></script>
<script type="text/javascript" src="../../../../../../jquery/jszip/dist/jszip.min.js"></script>
<script type="text/javascript" src="../../../../../../jquery/jszip-utils/dist/jszip-utils.min.js"></script>
<!--[if IE]>
<script type="text/javascript" src="../../../../../../jquery/jszip-utils/dist/jszip-utils-ie.min.js"></script>
<![endif]-->
<script type="text/javascript" src="../../../../../../jquery/jquery-3.6.1.min.js"></script>
<script type="text/javascript" src="../../../../../../jquery/jquery-ui.min.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="AbstractClasspathEntry (Gradle API 8.5)";
        }
    }
    catch(err) {
    }
//-->
var data = {"i0":10,"i1":10,"i2":10,"i3":10,"i4":10,"i5":10,"i6":10,"i7":10,"i8":10,"i9":10,"i10":10,"i11":10,"i12":10,"i13":10,"i14":10,"i15":10};
var tabs = {65535:["t0","All Methods"],2:["t2","Instance Methods"],8:["t4","Concrete Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
var pathtoroot = "../../../../../../";
var useModuleDirectories = false;
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<div class="fixedNav">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../../index-all.html">Index</a></li>
<li><a href="../../../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../../../allclasses.html">All&nbsp;Classes</a></li>
</ul>
<ul class="navListSearch">
<li><label for="search">SEARCH:</label>
<input type="text" id="search" value="search" disabled="disabled">
<input type="reset" id="reset" value="reset" disabled="disabled">
</li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li><a href="#field.summary">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li><a href="#field.detail">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
</div>
<div class="navPadding">&nbsp;</div>
<script type="text/javascript"><!--
$('.navPadding').css('padding-top', $('.fixedNav').css("height"));
//-->
</script>
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle"><span class="packageLabelInType">Package</span>&nbsp;<a href="package-summary.html">org.gradle.plugins.ide.eclipse.model</a></div>
<h2 title="Class AbstractClasspathEntry" class="title">Class AbstractClasspathEntry</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li>org.gradle.plugins.ide.eclipse.model.AbstractClasspathEntry</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt>All Implemented Interfaces:</dt>
<dd><code><a href="ClasspathEntry.html" title="interface in org.gradle.plugins.ide.eclipse.model">ClasspathEntry</a></code></dd>
</dl>
<dl>
<dt>Direct Known Subclasses:</dt>
<dd><code><a href="AbstractLibrary.html" title="class in org.gradle.plugins.ide.eclipse.model">AbstractLibrary</a></code>, <code><a href="Container.html" title="class in org.gradle.plugins.ide.eclipse.model">Container</a></code>, <code><a href="ProjectDependency.html" title="class in org.gradle.plugins.ide.eclipse.model">ProjectDependency</a></code>, <code><a href="SourceFolder.html" title="class in org.gradle.plugins.ide.eclipse.model">SourceFolder</a></code></dd>
</dl>
<hr>
<pre>public abstract class <span class="typeNameLabel">AbstractClasspathEntry</span>
extends java.lang.Object
implements <a href="ClasspathEntry.html" title="interface in org.gradle.plugins.ide.eclipse.model">ClasspathEntry</a></pre>
<div class="block">Common superclass for all <a href="ClasspathEntry.html" title="interface in org.gradle.plugins.ide.eclipse.model"><code>ClasspathEntry</code></a> instances.</div>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- =========== FIELD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="field.summary">
<!--   -->
</a>
<h3>Field Summary</h3>
<table class="memberSummary" summary="Field Summary table, listing fields, and an explanation">
<caption><span>Fields</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colSecond" scope="col">Field</th>
<th class="colLast" scope="col">Description</th>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected java.util.Set&lt;<a href="AccessRule.html" title="class in org.gradle.plugins.ide.eclipse.model">AccessRule</a>&gt;</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#accessRules">accessRules</a></span></code></th>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#COMPONENT_DEPENDENCY_ATTRIBUTE">COMPONENT_DEPENDENCY_ATTRIBUTE</a></span></code></th>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#COMPONENT_NON_DEPENDENCY_ATTRIBUTE">COMPONENT_NON_DEPENDENCY_ATTRIBUTE</a></span></code></th>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected java.util.Map&lt;java.lang.String,&#8203;java.lang.Object&gt;</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#entryAttributes">entryAttributes</a></span></code></th>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected boolean</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#exported">exported</a></span></code></th>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected java.lang.String</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#path">path</a></span></code></th>
<td class="colLast">&nbsp;</td>
</tr>
</table>
</li>
</ul>
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.summary">
<!--   -->
</a>
<h3>Constructor Summary</h3>
<table class="memberSummary" summary="Constructor Summary table, listing constructors, and an explanation">
<caption><span>Constructors</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Constructor</th>
<th class="colLast" scope="col">Description</th>
</tr>
<tr class="altColor">
<th class="colConstructorName" scope="row"><code><span class="memberNameLink"><a href="#AbstractClasspathEntry-groovy.util.Node-">AbstractClasspathEntry</a></span>&#8203;(<a href="https://docs.groovy-lang.org/docs/groovy-3.0.17/html/gapi/groovy/util/Node.html?is-external=true" title="class or interface in groovy.util" class="externalLink">Node</a>&nbsp;node)</code></th>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="rowColor">
<th class="colConstructorName" scope="row"><code><span class="memberNameLink"><a href="#AbstractClasspathEntry-java.lang.String-">AbstractClasspathEntry</a></span>&#8203;(java.lang.String&nbsp;path)</code></th>
<td class="colLast">&nbsp;</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colSecond" scope="col">Method</th>
<th class="colLast" scope="col">Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>protected <a href="https://docs.groovy-lang.org/docs/groovy-3.0.17/html/gapi/groovy/util/Node.html?is-external=true" title="class or interface in groovy.util" class="externalLink">Node</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#addClasspathEntry-groovy.util.Node-java.util.Map-">addClasspathEntry</a></span>&#8203;(<a href="https://docs.groovy-lang.org/docs/groovy-3.0.17/html/gapi/groovy/util/Node.html?is-external=true" title="class or interface in groovy.util" class="externalLink">Node</a>&nbsp;node,
                 java.util.Map&lt;java.lang.String,&#8203;?&gt;&nbsp;attributes)</code></th>
<td class="colLast">&nbsp;</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#appendNode-groovy.util.Node-">appendNode</a></span>&#8203;(<a href="https://docs.groovy-lang.org/docs/groovy-3.0.17/html/gapi/groovy/util/Node.html?is-external=true" title="class or interface in groovy.util" class="externalLink">Node</a>&nbsp;node)</code></th>
<td class="colLast">&nbsp;</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#equals-java.lang.Object-">equals</a></span>&#8203;(java.lang.Object&nbsp;o)</code></th>
<td class="colLast">&nbsp;</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>java.util.Set&lt;<a href="AccessRule.html" title="class in org.gradle.plugins.ide.eclipse.model">AccessRule</a>&gt;</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getAccessRules--">getAccessRules</a></span>()</code></th>
<td class="colLast">&nbsp;</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code>java.util.Map&lt;java.lang.String,&#8203;java.lang.Object&gt;</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getEntryAttributes--">getEntryAttributes</a></span>()</code></th>
<td class="colLast">&nbsp;</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code>java.lang.String</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getNativeLibraryLocation--">getNativeLibraryLocation</a></span>()</code></th>
<td class="colLast">&nbsp;</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code>java.lang.String</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getPath--">getPath</a></span>()</code></th>
<td class="colLast">&nbsp;</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code>int</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#hashCode--">hashCode</a></span>()</code></th>
<td class="colLast">&nbsp;</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#isExported--">isExported</a></span>()</code></th>
<td class="colLast">&nbsp;</td>
</tr>
<tr id="i9" class="rowColor">
<td class="colFirst"><code>protected java.lang.String</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#normalizePath-java.lang.String-">normalizePath</a></span>&#8203;(java.lang.String&nbsp;path)</code></th>
<td class="colLast">&nbsp;</td>
</tr>
<tr id="i10" class="altColor">
<td class="colFirst"><code>void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#setAccessRules-java.util.Set-">setAccessRules</a></span>&#8203;(java.util.Set&lt;<a href="AccessRule.html" title="class in org.gradle.plugins.ide.eclipse.model">AccessRule</a>&gt;&nbsp;accessRules)</code></th>
<td class="colLast">&nbsp;</td>
</tr>
<tr id="i11" class="rowColor">
<td class="colFirst"><code>void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#setExported-boolean-">setExported</a></span>&#8203;(boolean&nbsp;exported)</code></th>
<td class="colLast">&nbsp;</td>
</tr>
<tr id="i12" class="altColor">
<td class="colFirst"><code>void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#setNativeLibraryLocation-java.lang.String-">setNativeLibraryLocation</a></span>&#8203;(java.lang.String&nbsp;location)</code></th>
<td class="colLast">&nbsp;</td>
</tr>
<tr id="i13" class="rowColor">
<td class="colFirst"><code>void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#setPath-java.lang.String-">setPath</a></span>&#8203;(java.lang.String&nbsp;path)</code></th>
<td class="colLast">&nbsp;</td>
</tr>
<tr id="i14" class="altColor">
<td class="colFirst"><code>java.lang.String</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#toString--">toString</a></span>()</code></th>
<td class="colLast">&nbsp;</td>
</tr>
<tr id="i15" class="rowColor">
<td class="colFirst"><code>void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#writeEntryAttributes-groovy.util.Node-">writeEntryAttributes</a></span>&#8203;(<a href="https://docs.groovy-lang.org/docs/groovy-3.0.17/html/gapi/groovy/util/Node.html?is-external=true" title="class or interface in groovy.util" class="externalLink">Node</a>&nbsp;node)</code></th>
<td class="colLast">&nbsp;</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.Object</h3>
<code>clone, finalize, getClass, notify, notifyAll, wait, wait, wait</code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.org.gradle.plugins.ide.eclipse.model.ClasspathEntry">
<!--   -->
</a>
<h3>Methods inherited from interface&nbsp;org.gradle.plugins.ide.eclipse.model.<a href="ClasspathEntry.html" title="interface in org.gradle.plugins.ide.eclipse.model">ClasspathEntry</a></h3>
<code><a href="ClasspathEntry.html#getKind--">getKind</a></code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ FIELD DETAIL =========== -->
<ul class="blockList">
<li class="blockList"><a name="field.detail">
<!--   -->
</a>
<h3>Field Detail</h3>
<a name="COMPONENT_NON_DEPENDENCY_ATTRIBUTE">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>COMPONENT_NON_DEPENDENCY_ATTRIBUTE</h4>
<pre>public static final&nbsp;java.lang.String COMPONENT_NON_DEPENDENCY_ATTRIBUTE</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../../../../constant-values.html#org.gradle.plugins.ide.eclipse.model.AbstractClasspathEntry.COMPONENT_NON_DEPENDENCY_ATTRIBUTE">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="COMPONENT_DEPENDENCY_ATTRIBUTE">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>COMPONENT_DEPENDENCY_ATTRIBUTE</h4>
<pre>public static final&nbsp;java.lang.String COMPONENT_DEPENDENCY_ATTRIBUTE</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../../../../constant-values.html#org.gradle.plugins.ide.eclipse.model.AbstractClasspathEntry.COMPONENT_DEPENDENCY_ATTRIBUTE">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="path">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>path</h4>
<pre>protected&nbsp;java.lang.String path</pre>
</li>
</ul>
<a name="exported">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>exported</h4>
<pre>protected&nbsp;boolean exported</pre>
</li>
</ul>
<a name="accessRules">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>accessRules</h4>
<pre>protected&nbsp;java.util.Set&lt;<a href="AccessRule.html" title="class in org.gradle.plugins.ide.eclipse.model">AccessRule</a>&gt; accessRules</pre>
</li>
</ul>
<a name="entryAttributes">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>entryAttributes</h4>
<pre>protected final&nbsp;java.util.Map&lt;java.lang.String,&#8203;java.lang.Object&gt; entryAttributes</pre>
</li>
</ul>
</li>
</ul>
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.detail">
<!--   -->
</a>
<h3>Constructor Detail</h3>
<a name="AbstractClasspathEntry-groovy.util.Node-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>AbstractClasspathEntry</h4>
<pre>public&nbsp;AbstractClasspathEntry&#8203;(<a href="https://docs.groovy-lang.org/docs/groovy-3.0.17/html/gapi/groovy/util/Node.html?is-external=true" title="class or interface in groovy.util" class="externalLink">Node</a>&nbsp;node)</pre>
</li>
</ul>
<a name="AbstractClasspathEntry-java.lang.String-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>AbstractClasspathEntry</h4>
<pre>public&nbsp;AbstractClasspathEntry&#8203;(java.lang.String&nbsp;path)</pre>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="getPath--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getPath</h4>
<pre class="methodSignature">public&nbsp;java.lang.String&nbsp;getPath()</pre>
</li>
</ul>
<a name="setPath-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setPath</h4>
<pre class="methodSignature">public&nbsp;void&nbsp;setPath&#8203;(java.lang.String&nbsp;path)</pre>
</li>
</ul>
<a name="isExported--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isExported</h4>
<pre class="methodSignature">public&nbsp;boolean&nbsp;isExported()</pre>
</li>
</ul>
<a name="setExported-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setExported</h4>
<pre class="methodSignature">public&nbsp;void&nbsp;setExported&#8203;(boolean&nbsp;exported)</pre>
</li>
</ul>
<a name="getAccessRules--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getAccessRules</h4>
<pre class="methodSignature">public&nbsp;java.util.Set&lt;<a href="AccessRule.html" title="class in org.gradle.plugins.ide.eclipse.model">AccessRule</a>&gt;&nbsp;getAccessRules()</pre>
</li>
</ul>
<a name="setAccessRules-java.util.Set-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setAccessRules</h4>
<pre class="methodSignature">public&nbsp;void&nbsp;setAccessRules&#8203;(java.util.Set&lt;<a href="AccessRule.html" title="class in org.gradle.plugins.ide.eclipse.model">AccessRule</a>&gt;&nbsp;accessRules)</pre>
</li>
</ul>
<a name="getEntryAttributes--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getEntryAttributes</h4>
<pre class="methodSignature">public&nbsp;java.util.Map&lt;java.lang.String,&#8203;java.lang.Object&gt;&nbsp;getEntryAttributes()</pre>
</li>
</ul>
<a name="getNativeLibraryLocation--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getNativeLibraryLocation</h4>
<pre class="methodSignature">public&nbsp;java.lang.String&nbsp;getNativeLibraryLocation()</pre>
</li>
</ul>
<a name="setNativeLibraryLocation-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setNativeLibraryLocation</h4>
<pre class="methodSignature">public&nbsp;void&nbsp;setNativeLibraryLocation&#8203;(java.lang.String&nbsp;location)</pre>
</li>
</ul>
<a name="appendNode-groovy.util.Node-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>appendNode</h4>
<pre class="methodSignature">public&nbsp;void&nbsp;appendNode&#8203;(<a href="https://docs.groovy-lang.org/docs/groovy-3.0.17/html/gapi/groovy/util/Node.html?is-external=true" title="class or interface in groovy.util" class="externalLink">Node</a>&nbsp;node)</pre>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="ClasspathEntry.html#appendNode-groovy.util.Node-">appendNode</a></code>&nbsp;in interface&nbsp;<code><a href="ClasspathEntry.html" title="interface in org.gradle.plugins.ide.eclipse.model">ClasspathEntry</a></code></dd>
</dl>
</li>
</ul>
<a name="addClasspathEntry-groovy.util.Node-java.util.Map-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>addClasspathEntry</h4>
<pre class="methodSignature">protected&nbsp;<a href="https://docs.groovy-lang.org/docs/groovy-3.0.17/html/gapi/groovy/util/Node.html?is-external=true" title="class or interface in groovy.util" class="externalLink">Node</a>&nbsp;addClasspathEntry&#8203;(<a href="https://docs.groovy-lang.org/docs/groovy-3.0.17/html/gapi/groovy/util/Node.html?is-external=true" title="class or interface in groovy.util" class="externalLink">Node</a>&nbsp;node,
                                 java.util.Map&lt;java.lang.String,&#8203;?&gt;&nbsp;attributes)</pre>
</li>
</ul>
<a name="normalizePath-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>normalizePath</h4>
<pre class="methodSignature">protected&nbsp;java.lang.String&nbsp;normalizePath&#8203;(java.lang.String&nbsp;path)</pre>
</li>
</ul>
<a name="writeEntryAttributes-groovy.util.Node-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>writeEntryAttributes</h4>
<pre class="methodSignature">public&nbsp;void&nbsp;writeEntryAttributes&#8203;(<a href="https://docs.groovy-lang.org/docs/groovy-3.0.17/html/gapi/groovy/util/Node.html?is-external=true" title="class or interface in groovy.util" class="externalLink">Node</a>&nbsp;node)</pre>
</li>
</ul>
<a name="equals-java.lang.Object-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>equals</h4>
<pre class="methodSignature">public&nbsp;boolean&nbsp;equals&#8203;(java.lang.Object&nbsp;o)</pre>
<dl>
<dt><span class="overrideSpecifyLabel">Overrides:</span></dt>
<dd><code>equals</code>&nbsp;in class&nbsp;<code>java.lang.Object</code></dd>
</dl>
</li>
</ul>
<a name="hashCode--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>hashCode</h4>
<pre class="methodSignature">public&nbsp;int&nbsp;hashCode()</pre>
<dl>
<dt><span class="overrideSpecifyLabel">Overrides:</span></dt>
<dd><code>hashCode</code>&nbsp;in class&nbsp;<code>java.lang.Object</code></dd>
</dl>
</li>
</ul>
<a name="toString--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>toString</h4>
<pre class="methodSignature">public&nbsp;java.lang.String&nbsp;toString()</pre>
<dl>
<dt><span class="overrideSpecifyLabel">Overrides:</span></dt>
<dd><code>toString</code>&nbsp;in class&nbsp;<code>java.lang.Object</code></dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../../index-all.html">Index</a></li>
<li><a href="../../../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../../../allclasses.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li><a href="#field.summary">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li><a href="#field.detail">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
