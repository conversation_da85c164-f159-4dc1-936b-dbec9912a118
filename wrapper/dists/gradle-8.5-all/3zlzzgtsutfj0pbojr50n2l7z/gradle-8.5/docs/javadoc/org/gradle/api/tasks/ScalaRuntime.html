<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc -->
<title>ScalaRuntime (Gradle API 8.5)</title>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<link rel="stylesheet" type="text/css" href="../../../../javadoc.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../jquery/jquery-ui.min.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../jquery-ui.overrides.css" title="Style">
<script type="text/javascript" src="../../../../script.js"></script>
<script type="text/javascript" src="../../../../jquery/jszip/dist/jszip.min.js"></script>
<script type="text/javascript" src="../../../../jquery/jszip-utils/dist/jszip-utils.min.js"></script>
<!--[if IE]>
<script type="text/javascript" src="../../../../jquery/jszip-utils/dist/jszip-utils-ie.min.js"></script>
<![endif]-->
<script type="text/javascript" src="../../../../jquery/jquery-3.6.1.min.js"></script>
<script type="text/javascript" src="../../../../jquery/jquery-ui.min.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="ScalaRuntime (Gradle API 8.5)";
        }
    }
    catch(err) {
    }
//-->
var data = {"i0":10,"i1":10,"i2":10};
var tabs = {65535:["t0","All Methods"],2:["t2","Instance Methods"],8:["t4","Concrete Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
var pathtoroot = "../../../../";
var useModuleDirectories = false;
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<div class="fixedNav">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../allclasses.html">All&nbsp;Classes</a></li>
</ul>
<ul class="navListSearch">
<li><label for="search">SEARCH:</label>
<input type="text" id="search" value="search" disabled="disabled">
<input type="reset" id="reset" value="reset" disabled="disabled">
</li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
</div>
<div class="navPadding">&nbsp;</div>
<script type="text/javascript"><!--
$('.navPadding').css('padding-top', $('.fixedNav').css("height"));
//-->
</script>
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle"><span class="packageLabelInType">Package</span>&nbsp;<a href="package-summary.html">org.gradle.api.tasks</a></div>
<h2 title="Class ScalaRuntime" class="title">Class ScalaRuntime</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li>org.gradle.api.tasks.ScalaRuntime</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<hr>
<pre>public abstract class <span class="typeNameLabel">ScalaRuntime</span>
extends java.lang.Object</pre>
<div class="block">Provides information related to the Scala runtime(s) used in a project. Added by the
 <code>org.gradle.api.plugins.scala.ScalaBasePlugin</code> as a project extension named <code>scalaRuntime</code>.

 <p>Example usage:

 <pre class='autoTested'>
     plugins {
         id 'scala'
     }

     repositories {
         mavenCentral()
     }

     dependencies {
         implementation "org.scala-lang:scala-library:2.10.1"
     }

     def scalaClasspath = scalaRuntime.inferScalaClasspath(configurations.compileClasspath)
     // The returned class path can be used to configure the 'scalaClasspath' property of tasks
     // such as 'ScalaCompile' or 'ScalaDoc', or to execute these and other Scala tools directly.
 </pre></div>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.summary">
<!--   -->
</a>
<h3>Constructor Summary</h3>
<table class="memberSummary" summary="Constructor Summary table, listing constructors, and an explanation">
<caption><span>Constructors</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Constructor</th>
<th class="colLast" scope="col">Description</th>
</tr>
<tr class="altColor">
<th class="colConstructorName" scope="row"><code><span class="memberNameLink"><a href="#ScalaRuntime-org.gradle.api.Project-">ScalaRuntime</a></span>&#8203;(<a href="../Project.html" title="interface in org.gradle.api">Project</a>&nbsp;project)</code></th>
<td class="colLast">&nbsp;</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colSecond" scope="col">Method</th>
<th class="colLast" scope="col">Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>java.io.File</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#findScalaJar-java.lang.Iterable-java.lang.String-">findScalaJar</a></span>&#8203;(java.lang.Iterable&lt;java.io.File&gt;&nbsp;classpath,
            java.lang.String&nbsp;appendix)</code></th>
<td class="colLast">
<div class="block">Searches the specified class path for a Scala Jar file (scala-compiler, scala-library,
 scala-jdbc, etc.) with the specified appendix (compiler, library, jdbc, etc.).</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>java.lang.String</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getScalaVersion-java.io.File-">getScalaVersion</a></span>&#8203;(java.io.File&nbsp;scalaJar)</code></th>
<td class="colLast">
<div class="block">Determines the version of a Scala Jar file (scala-compiler, scala-library,
 scala-jdbc, etc.).</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code><a href="../file/FileCollection.html" title="interface in org.gradle.api.file">FileCollection</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#inferScalaClasspath-java.lang.Iterable-">inferScalaClasspath</a></span>&#8203;(java.lang.Iterable&lt;java.io.File&gt;&nbsp;classpath)</code></th>
<td class="colLast">
<div class="block">Searches the specified class path for a 'scala-library' Jar, and returns a class path
 containing a corresponding (same version) 'scala-compiler' Jar and its dependencies.</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.Object</h3>
<code>clone, equals, finalize, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.detail">
<!--   -->
</a>
<h3>Constructor Detail</h3>
<a name="ScalaRuntime-org.gradle.api.Project-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>ScalaRuntime</h4>
<pre>public&nbsp;ScalaRuntime&#8203;(<a href="../Project.html" title="interface in org.gradle.api">Project</a>&nbsp;project)</pre>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="inferScalaClasspath-java.lang.Iterable-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>inferScalaClasspath</h4>
<pre class="methodSignature">public&nbsp;<a href="../file/FileCollection.html" title="interface in org.gradle.api.file">FileCollection</a>&nbsp;inferScalaClasspath&#8203;(java.lang.Iterable&lt;java.io.File&gt;&nbsp;classpath)</pre>
<div class="block">Searches the specified class path for a 'scala-library' Jar, and returns a class path
 containing a corresponding (same version) 'scala-compiler' Jar and its dependencies.

 <p>The returned class path may be empty, or may fail to resolve when asked for its contents.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>classpath</code> - a class path containing a 'scala-library' Jar</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>a class path containing a corresponding 'scala-compiler' Jar and its dependencies</dd>
</dl>
</li>
</ul>
<a name="findScalaJar-java.lang.Iterable-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>findScalaJar</h4>
<pre class="methodSignature">@Nullable
public&nbsp;java.io.File&nbsp;findScalaJar&#8203;(java.lang.Iterable&lt;java.io.File&gt;&nbsp;classpath,
                                 java.lang.String&nbsp;appendix)</pre>
<div class="block">Searches the specified class path for a Scala Jar file (scala-compiler, scala-library,
 scala-jdbc, etc.) with the specified appendix (compiler, library, jdbc, etc.).
 If no such file is found, <code>null</code> is returned.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>classpath</code> - the class path to search</dd>
<dd><code>appendix</code> - the appendix to search for</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>a Scala Jar file with the specified appendix</dd>
</dl>
</li>
</ul>
<a name="getScalaVersion-java.io.File-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>getScalaVersion</h4>
<pre class="methodSignature">@Nullable
public&nbsp;java.lang.String&nbsp;getScalaVersion&#8203;(java.io.File&nbsp;scalaJar)</pre>
<div class="block">Determines the version of a Scala Jar file (scala-compiler, scala-library,
 scala-jdbc, etc.). If the version cannot be determined, or the file is not a Scala
 Jar file, <code>null</code> is returned.

 <p>Implementation note: The version is determined by parsing the file name, which
 is expected to match the pattern 'scala-[component]-[version].jar'.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>scalaJar</code> - a Scala Jar file</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>the version of the Scala Jar file</dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../allclasses.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
