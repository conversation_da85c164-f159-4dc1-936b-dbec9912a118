<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc -->
<title>ResolutionStrategy (Gradle API 8.5)</title>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<link rel="stylesheet" type="text/css" href="../../../../javadoc.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../jquery/jquery-ui.min.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../jquery-ui.overrides.css" title="Style">
<script type="text/javascript" src="../../../../script.js"></script>
<script type="text/javascript" src="../../../../jquery/jszip/dist/jszip.min.js"></script>
<script type="text/javascript" src="../../../../jquery/jszip-utils/dist/jszip-utils.min.js"></script>
<!--[if IE]>
<script type="text/javascript" src="../../../../jquery/jszip-utils/dist/jszip-utils-ie.min.js"></script>
<![endif]-->
<script type="text/javascript" src="../../../../jquery/jquery-3.6.1.min.js"></script>
<script type="text/javascript" src="../../../../jquery/jquery-ui.min.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="ResolutionStrategy (Gradle API 8.5)";
        }
    }
    catch(err) {
    }
//-->
var data = {"i0":6,"i1":6,"i2":6,"i3":6,"i4":6,"i5":6,"i6":6,"i7":6,"i8":6,"i9":6,"i10":6,"i11":6,"i12":6,"i13":6,"i14":6,"i15":6,"i16":6,"i17":6,"i18":6,"i19":6,"i20":6,"i21":6,"i22":6,"i23":6,"i24":6};
var tabs = {65535:["t0","All Methods"],2:["t2","Instance Methods"],4:["t3","Abstract Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
var pathtoroot = "../../../../";
var useModuleDirectories = false;
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<div class="fixedNav">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../allclasses.html">All&nbsp;Classes</a></li>
</ul>
<ul class="navListSearch">
<li><label for="search">SEARCH:</label>
<input type="text" id="search" value="search" disabled="disabled">
<input type="reset" id="reset" value="reset" disabled="disabled">
</li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li><a href="#nested.class.summary">Nested</a>&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
</div>
<div class="navPadding">&nbsp;</div>
<script type="text/javascript"><!--
$('.navPadding').css('padding-top', $('.fixedNav').css("height"));
//-->
</script>
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle"><span class="packageLabelInType">Package</span>&nbsp;<a href="package-summary.html">org.gradle.api.artifacts</a></div>
<h2 title="Interface ResolutionStrategy" class="title">Interface ResolutionStrategy</h2>
</div>
<div class="contentContainer">
<div class="description">
<ul class="blockList">
<li class="blockList">
<hr>
<pre>public interface <span class="typeNameLabel">ResolutionStrategy</span></pre>
<div class="block">Defines the strategies around dependency resolution.
 For example, forcing certain dependency versions, substitutions, conflict resolutions or snapshot timeouts.
 <p>
 Examples:
 <pre class='autoTested'>
 plugins {
     id 'java' // so that there are some configurations
 }

 configurations.all {
   resolutionStrategy {
     // fail eagerly on version conflict (includes transitive dependencies)
     // e.g. multiple different versions of the same dependency (group and name are equal)
     failOnVersionConflict()

     // prefer modules that are part of this build (multi-project or composite build) over external modules
     preferProjectModules()

     // force certain versions of dependencies (including transitive)
     //  *append new forced modules:
     force 'asm:asm-all:3.3.1', 'commons-io:commons-io:1.4'
     //  *replace existing forced modules with new ones:
     forcedModules = ['asm:asm-all:3.3.1']

     // add dependency substitution rules
     dependencySubstitution {
       substitute module('org.gradle:api') using project(':api')
       substitute project(':util') using module('org.gradle:util:3.0')
     }

     // cache dynamic versions for 10 minutes
     cacheDynamicVersionsFor 10*60, 'seconds'
     // don't cache changing modules at all
     cacheChangingModulesFor 0, 'seconds'
   }
 }
 </pre></div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>1.0-milestone-6</dd>
</dl>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ======== NESTED CLASS SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="nested.class.summary">
<!--   -->
</a>
<h3>Nested Class Summary</h3>
<table class="memberSummary" summary="Nested Class Summary table, listing nested classes, and an explanation">
<caption><span>Nested Classes</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colSecond" scope="col">Interface</th>
<th class="colLast" scope="col">Description</th>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static class&nbsp;</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="ResolutionStrategy.SortOrder.html" title="enum in org.gradle.api.artifacts">ResolutionStrategy.SortOrder</a></span></code></th>
<td class="colLast">
<div class="block">Defines the sort order for components and artifacts produced by the configuration.</div>
</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t3" class="tableTab"><span><a href="javascript:show(4);">Abstract Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colSecond" scope="col">Method</th>
<th class="colLast" scope="col">Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code><a href="ResolutionStrategy.html" title="interface in org.gradle.api.artifacts">ResolutionStrategy</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#activateDependencyLocking--">activateDependencyLocking</a></span>()</code></th>
<td class="colLast">
<div class="block">Activates dependency locking support in Gradle.</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#cacheChangingModulesFor-int-java.lang.String-">cacheChangingModulesFor</a></span>&#8203;(int&nbsp;value,
                       java.lang.String&nbsp;units)</code></th>
<td class="colLast">
<div class="block">Sets the length of time that changing modules will be cached, with units expressed as a String.</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#cacheChangingModulesFor-int-java.util.concurrent.TimeUnit-">cacheChangingModulesFor</a></span>&#8203;(int&nbsp;value,
                       java.util.concurrent.TimeUnit&nbsp;units)</code></th>
<td class="colLast">
<div class="block">Sets the length of time that changing modules will be cached.</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#cacheDynamicVersionsFor-int-java.lang.String-">cacheDynamicVersionsFor</a></span>&#8203;(int&nbsp;value,
                       java.lang.String&nbsp;units)</code></th>
<td class="colLast">
<div class="block">Sets the length of time that dynamic versions will be cached, with units expressed as a String.</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code>void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#cacheDynamicVersionsFor-int-java.util.concurrent.TimeUnit-">cacheDynamicVersionsFor</a></span>&#8203;(int&nbsp;value,
                       java.util.concurrent.TimeUnit&nbsp;units)</code></th>
<td class="colLast">
<div class="block">Sets the length of time that dynamic versions will be cached.</div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code><a href="ResolutionStrategy.html" title="interface in org.gradle.api.artifacts">ResolutionStrategy</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#capabilitiesResolution-org.gradle.api.Action-">capabilitiesResolution</a></span>&#8203;(<a href="../Action.html" title="interface in org.gradle.api">Action</a>&lt;? super <a href="CapabilitiesResolution.html" title="interface in org.gradle.api.artifacts">CapabilitiesResolution</a>&gt;&nbsp;action)</code></th>
<td class="colLast">
<div class="block">Configures the capabilities resolution strategy.</div>
</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code><a href="ResolutionStrategy.html" title="interface in org.gradle.api.artifacts">ResolutionStrategy</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#componentSelection-org.gradle.api.Action-">componentSelection</a></span>&#8203;(<a href="../Action.html" title="interface in org.gradle.api">Action</a>&lt;? super <a href="ComponentSelectionRules.html" title="interface in org.gradle.api.artifacts">ComponentSelectionRules</a>&gt;&nbsp;action)</code></th>
<td class="colLast">
<div class="block">The componentSelection block provides rules to filter or prevent certain components from appearing in the resolution result.</div>
</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code><a href="ResolutionStrategy.html" title="interface in org.gradle.api.artifacts">ResolutionStrategy</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#deactivateDependencyLocking--">deactivateDependencyLocking</a></span>()</code></th>
<td class="colLast">
<div class="block">Deactivates dependency locking support in Gradle.</div>
</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code><a href="ResolutionStrategy.html" title="interface in org.gradle.api.artifacts">ResolutionStrategy</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#dependencySubstitution-org.gradle.api.Action-">dependencySubstitution</a></span>&#8203;(<a href="../Action.html" title="interface in org.gradle.api">Action</a>&lt;? super <a href="DependencySubstitutions.html" title="interface in org.gradle.api.artifacts">DependencySubstitutions</a>&gt;&nbsp;action)</code></th>
<td class="colLast">
<div class="block">Configures the set of dependency substitution rules for this configuration.</div>
</td>
</tr>
<tr id="i9" class="rowColor">
<td class="colFirst"><code><a href="ResolutionStrategy.html" title="interface in org.gradle.api.artifacts">ResolutionStrategy</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#disableDependencyVerification--">disableDependencyVerification</a></span>()</code></th>
<td class="colLast">
<div class="block">Deactivates dependency verification for this configuration.</div>
</td>
</tr>
<tr id="i10" class="altColor">
<td class="colFirst"><code><a href="ResolutionStrategy.html" title="interface in org.gradle.api.artifacts">ResolutionStrategy</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#eachDependency-org.gradle.api.Action-">eachDependency</a></span>&#8203;(<a href="../Action.html" title="interface in org.gradle.api">Action</a>&lt;? super <a href="DependencyResolveDetails.html" title="interface in org.gradle.api.artifacts">DependencyResolveDetails</a>&gt;&nbsp;rule)</code></th>
<td class="colLast">
<div class="block">Adds a dependency substitution rule that is triggered for every dependency (including transitive)
 when the configuration is being resolved.</div>
</td>
</tr>
<tr id="i11" class="rowColor">
<td class="colFirst"><code><a href="ResolutionStrategy.html" title="interface in org.gradle.api.artifacts">ResolutionStrategy</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#enableDependencyVerification--">enableDependencyVerification</a></span>()</code></th>
<td class="colLast">
<div class="block">Enabled dependency verification for this configuration.</div>
</td>
</tr>
<tr id="i12" class="altColor">
<td class="colFirst"><code><a href="ResolutionStrategy.html" title="interface in org.gradle.api.artifacts">ResolutionStrategy</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#failOnChangingVersions--">failOnChangingVersions</a></span>()</code></th>
<td class="colLast">
<div class="block">If this method is called, Gradle will make sure that no changing version participates in resolution.</div>
</td>
</tr>
<tr id="i13" class="rowColor">
<td class="colFirst"><code><a href="ResolutionStrategy.html" title="interface in org.gradle.api.artifacts">ResolutionStrategy</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#failOnDynamicVersions--">failOnDynamicVersions</a></span>()</code></th>
<td class="colLast">
<div class="block">If this method is called, Gradle will make sure that no dynamic version was used in the resulting dependency graph.</div>
</td>
</tr>
<tr id="i14" class="altColor">
<td class="colFirst"><code><a href="ResolutionStrategy.html" title="interface in org.gradle.api.artifacts">ResolutionStrategy</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#failOnNonReproducibleResolution--">failOnNonReproducibleResolution</a></span>()</code></th>
<td class="colLast">
<div class="block">Configures Gradle to fail the build is the resolution result is expected to be unstable, that is to say that
 it includes dynamic versions or changing versions and therefore the result may change depending
 on when the build is executed.</div>
</td>
</tr>
<tr id="i15" class="rowColor">
<td class="colFirst"><code><a href="ResolutionStrategy.html" title="interface in org.gradle.api.artifacts">ResolutionStrategy</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#failOnVersionConflict--">failOnVersionConflict</a></span>()</code></th>
<td class="colLast">
<div class="block">In case of conflict, Gradle by default uses the newest of conflicting versions.</div>
</td>
</tr>
<tr id="i16" class="altColor">
<td class="colFirst"><code><a href="ResolutionStrategy.html" title="interface in org.gradle.api.artifacts">ResolutionStrategy</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#force-java.lang.Object...-">force</a></span>&#8203;(java.lang.Object...&nbsp;moduleVersionSelectorNotations)</code></th>
<td class="colLast">
<div class="block">Allows forcing certain versions of dependencies, including transitive dependencies.</div>
</td>
</tr>
<tr id="i17" class="rowColor">
<td class="colFirst"><code><a href="CapabilitiesResolution.html" title="interface in org.gradle.api.artifacts">CapabilitiesResolution</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getCapabilitiesResolution--">getCapabilitiesResolution</a></span>()</code></th>
<td class="colLast">
<div class="block">Returns the capabilities resolution strategy.</div>
</td>
</tr>
<tr id="i18" class="altColor">
<td class="colFirst"><code><a href="ComponentSelectionRules.html" title="interface in org.gradle.api.artifacts">ComponentSelectionRules</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getComponentSelection--">getComponentSelection</a></span>()</code></th>
<td class="colLast">
<div class="block">Returns the currently configured version selection rules object.</div>
</td>
</tr>
<tr id="i19" class="rowColor">
<td class="colFirst"><code><a href="DependencySubstitutions.html" title="interface in org.gradle.api.artifacts">DependencySubstitutions</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getDependencySubstitution--">getDependencySubstitution</a></span>()</code></th>
<td class="colLast">
<div class="block">Returns the set of dependency substitution rules that are set for this configuration.</div>
</td>
</tr>
<tr id="i20" class="altColor">
<td class="colFirst"><code>java.util.Set&lt;<a href="ModuleVersionSelector.html" title="interface in org.gradle.api.artifacts">ModuleVersionSelector</a>&gt;</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getForcedModules--">getForcedModules</a></span>()</code></th>
<td class="colLast">
<div class="block">Returns currently configured forced modules.</div>
</td>
</tr>
<tr id="i21" class="rowColor">
<td class="colFirst"><code><a href="../provider/Property.html" title="interface in org.gradle.api.provider">Property</a>&lt;java.lang.Boolean&gt;</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getUseGlobalDependencySubstitutionRules--">getUseGlobalDependencySubstitutionRules</a></span>()</code></th>
<td class="colLast">
<div class="block">Gradle implicitly registers dependency substitution rules for all configurations in the whole build
 tree to find projects in other included builds.</div>
</td>
</tr>
<tr id="i22" class="altColor">
<td class="colFirst"><code>void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#preferProjectModules--">preferProjectModules</a></span>()</code></th>
<td class="colLast">
<div class="block">Gradle can resolve conflicts purely by version number or prioritize project dependencies over binary.</div>
</td>
</tr>
<tr id="i23" class="rowColor">
<td class="colFirst"><code><a href="ResolutionStrategy.html" title="interface in org.gradle.api.artifacts">ResolutionStrategy</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#setForcedModules-java.lang.Object...-">setForcedModules</a></span>&#8203;(java.lang.Object...&nbsp;moduleVersionSelectorNotations)</code></th>
<td class="colLast">
<div class="block">Allows forcing certain versions of dependencies, including transitive dependencies.</div>
</td>
</tr>
<tr id="i24" class="altColor">
<td class="colFirst"><code>void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#sortArtifacts-org.gradle.api.artifacts.ResolutionStrategy.SortOrder-">sortArtifacts</a></span>&#8203;(<a href="ResolutionStrategy.SortOrder.html" title="enum in org.gradle.api.artifacts">ResolutionStrategy.SortOrder</a>&nbsp;sortOrder)</code></th>
<td class="colLast">
<div class="block">Specifies the ordering for resolved artifacts.</div>
</td>
</tr>
</table>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="failOnVersionConflict--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>failOnVersionConflict</h4>
<pre class="methodSignature"><a href="ResolutionStrategy.html" title="interface in org.gradle.api.artifacts">ResolutionStrategy</a>&nbsp;failOnVersionConflict()</pre>
<div class="block">In case of conflict, Gradle by default uses the newest of conflicting versions.
 However, you can change this behavior. Use this method to configure the resolution to fail eagerly on any version conflict, e.g.
 multiple different versions of the same dependency (group and name are equal) in the same <a href="Configuration.html" title="interface in org.gradle.api.artifacts"><code>Configuration</code></a>.
 The check includes both first level and transitive dependencies. See example below:

 <pre class='autoTested'>
 plugins {
     id 'java' // so that there are some configurations
 }

 configurations.all {
   resolutionStrategy.failOnVersionConflict()
 }
 </pre></div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>this resolution strategy instance</dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>1.0-milestone-6</dd>
</dl>
</li>
</ul>
<a name="failOnDynamicVersions--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>failOnDynamicVersions</h4>
<pre class="methodSignature"><a href="ResolutionStrategy.html" title="interface in org.gradle.api.artifacts">ResolutionStrategy</a>&nbsp;failOnDynamicVersions()</pre>
<div class="block">If this method is called, Gradle will make sure that no dynamic version was used in the resulting dependency graph.
 In practice, it means that if the resolved dependency graph contains a module and that the versions participating
 in the selection of that module contain at least one dynamic version, then resolution will fail if the resolution
 result can change because of this version selector.

 This can be used in cases you want to make sure your build is reproducible, <i>without</i> relying on
 dependency locking.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>this resolution strategy</dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>6.1</dd>
</dl>
</li>
</ul>
<a name="failOnChangingVersions--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>failOnChangingVersions</h4>
<pre class="methodSignature"><a href="ResolutionStrategy.html" title="interface in org.gradle.api.artifacts">ResolutionStrategy</a>&nbsp;failOnChangingVersions()</pre>
<div class="block">If this method is called, Gradle will make sure that no changing version participates in resolution.

 This can be used in cases you want to make sure your build is reproducible, <i>without</i> relying on
 dependency locking.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>this resolution strategy</dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>6.1</dd>
</dl>
</li>
</ul>
<a name="failOnNonReproducibleResolution--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>failOnNonReproducibleResolution</h4>
<pre class="methodSignature"><a href="ResolutionStrategy.html" title="interface in org.gradle.api.artifacts">ResolutionStrategy</a>&nbsp;failOnNonReproducibleResolution()</pre>
<div class="block">Configures Gradle to fail the build is the resolution result is expected to be unstable, that is to say that
 it includes dynamic versions or changing versions and therefore the result may change depending
 on when the build is executed.

 This method is equivalent to calling both <a href="#failOnDynamicVersions--"><code>failOnDynamicVersions()</code></a> and
 <a href="#failOnChangingVersions--"><code>failOnChangingVersions()</code></a>.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>this resolution strategy</dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>6.1</dd>
</dl>
</li>
</ul>
<a name="preferProjectModules--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>preferProjectModules</h4>
<pre class="methodSignature">void&nbsp;preferProjectModules()</pre>
<div class="block">Gradle can resolve conflicts purely by version number or prioritize project dependencies over binary.
 The default is <b>by version number</b>.<p>
 This applies to both first level and transitive dependencies. See example below:

 <pre class='autoTested'>
 plugins {
     id 'java' // so that there are some configurations
 }

 configurations.all {
   resolutionStrategy.preferProjectModules()
 }
 </pre></div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>3.2</dd>
</dl>
</li>
</ul>
<a name="activateDependencyLocking--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>activateDependencyLocking</h4>
<pre class="methodSignature"><a href="ResolutionStrategy.html" title="interface in org.gradle.api.artifacts">ResolutionStrategy</a>&nbsp;activateDependencyLocking()</pre>
<div class="block">Activates dependency locking support in Gradle.
 Once turned on on a configuration, resolution result can be saved and then reused for subsequent builds.
 This enables reproducible builds when using dynamic versions.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>this resolution strategy instance</dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>4.8</dd>
</dl>
</li>
</ul>
<a name="deactivateDependencyLocking--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>deactivateDependencyLocking</h4>
<pre class="methodSignature"><a href="ResolutionStrategy.html" title="interface in org.gradle.api.artifacts">ResolutionStrategy</a>&nbsp;deactivateDependencyLocking()</pre>
<div class="block">Deactivates dependency locking support in Gradle.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>this resolution strategy instance</dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>6.0</dd>
</dl>
</li>
</ul>
<a name="disableDependencyVerification--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>disableDependencyVerification</h4>
<pre class="methodSignature"><a href="ResolutionStrategy.html" title="interface in org.gradle.api.artifacts">ResolutionStrategy</a>&nbsp;disableDependencyVerification()</pre>
<div class="block">Deactivates dependency verification for this configuration.
 You should always be careful when disabling verification, and in particular avoid
 disabling it for verification of plugins, because a plugin could use this to disable
 verification itself.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>6.2</dd>
</dl>
</li>
</ul>
<a name="enableDependencyVerification--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>enableDependencyVerification</h4>
<pre class="methodSignature"><a href="ResolutionStrategy.html" title="interface in org.gradle.api.artifacts">ResolutionStrategy</a>&nbsp;enableDependencyVerification()</pre>
<div class="block">Enabled dependency verification for this configuration.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>6.2</dd>
</dl>
</li>
</ul>
<a name="force-java.lang.Object...-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>force</h4>
<pre class="methodSignature"><a href="ResolutionStrategy.html" title="interface in org.gradle.api.artifacts">ResolutionStrategy</a>&nbsp;force&#8203;(java.lang.Object...&nbsp;moduleVersionSelectorNotations)</pre>
<div class="block">Allows forcing certain versions of dependencies, including transitive dependencies.
 <b>Appends</b> new forced modules to be considered when resolving dependencies.
 <p>
 It accepts following notations:
 <ul>
   <li>String in a format of: 'group:name:version', for example: 'org.gradle:gradle-core:1.0'</li>
   <li>instance of <a href="ModuleVersionSelector.html" title="interface in org.gradle.api.artifacts"><code>ModuleVersionSelector</code></a></li>
   <li>any collection or array of above will be automatically flattened</li>
 </ul>
 Example:
 <pre class='autoTested'>
 plugins {
     id 'java' // so that there are some configurations
 }

 configurations.all {
   resolutionStrategy.force 'asm:asm-all:3.3.1', 'commons-io:commons-io:1.4'
 }
 </pre></div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>moduleVersionSelectorNotations</code> - typically group:name:version notations to append</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>this ResolutionStrategy instance</dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>1.0-milestone-7</dd>
</dl>
</li>
</ul>
<a name="setForcedModules-java.lang.Object...-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setForcedModules</h4>
<pre class="methodSignature"><a href="ResolutionStrategy.html" title="interface in org.gradle.api.artifacts">ResolutionStrategy</a>&nbsp;setForcedModules&#8203;(java.lang.Object...&nbsp;moduleVersionSelectorNotations)</pre>
<div class="block">Allows forcing certain versions of dependencies, including transitive dependencies.
 <b>Replaces</b> existing forced modules with the input.
 <p>
 For information on notations see <a href="#force-java.lang.Object...-"><code>force(Object...)</code></a>
 <p>
 Example:
 <pre class='autoTested'>
 plugins {
     id 'java' // so that there are some configurations
 }

 configurations.all {
   resolutionStrategy.forcedModules = ['asm:asm-all:3.3.1', 'commons-io:commons-io:1.4']
 }
 </pre></div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>moduleVersionSelectorNotations</code> - typically group:name:version notations to set</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>this ResolutionStrategy instance</dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>1.0-milestone-7</dd>
</dl>
</li>
</ul>
<a name="getForcedModules--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getForcedModules</h4>
<pre class="methodSignature">java.util.Set&lt;<a href="ModuleVersionSelector.html" title="interface in org.gradle.api.artifacts">ModuleVersionSelector</a>&gt;&nbsp;getForcedModules()</pre>
<div class="block">Returns currently configured forced modules. For more information on forcing versions see <a href="#force-java.lang.Object...-"><code>force(Object...)</code></a></div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>forced modules</dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>1.0-milestone-7</dd>
</dl>
</li>
</ul>
<a name="eachDependency-org.gradle.api.Action-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>eachDependency</h4>
<pre class="methodSignature"><a href="ResolutionStrategy.html" title="interface in org.gradle.api.artifacts">ResolutionStrategy</a>&nbsp;eachDependency&#8203;(<a href="../Action.html" title="interface in org.gradle.api">Action</a>&lt;? super <a href="DependencyResolveDetails.html" title="interface in org.gradle.api.artifacts">DependencyResolveDetails</a>&gt;&nbsp;rule)</pre>
<div class="block">Adds a dependency substitution rule that is triggered for every dependency (including transitive)
 when the configuration is being resolved. The action receives an instance of <a href="DependencyResolveDetails.html" title="interface in org.gradle.api.artifacts"><code>DependencyResolveDetails</code></a>
 that can be used to find out what dependency is being resolved and to influence the resolution process.
 Example:
 <pre class='autoTested'>
 configurations {
   compileClasspath.resolutionStrategy {
     eachDependency { DependencyResolveDetails details -&gt;
       //specifying a fixed version for all libraries with 'org.gradle' group
       if (details.requested.group == 'org.gradle') {
         details.useVersion '1.4'
       }
     }
     eachDependency { details -&gt;
       //multiple actions can be specified
       if (details.requested.name == 'groovy-all') {
          //changing the name:
          details.useTarget group: details.requested.group, name: 'groovy', version: details.requested.version
       }
     }
   }
 }
 </pre>

 The rules are evaluated in order they are declared. Rules are evaluated after forced modules are applied (see <a href="#force-java.lang.Object...-"><code>force(Object...)</code></a></div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>this</dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>1.4</dd>
</dl>
</li>
</ul>
<a name="cacheDynamicVersionsFor-int-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>cacheDynamicVersionsFor</h4>
<pre class="methodSignature">void&nbsp;cacheDynamicVersionsFor&#8203;(int&nbsp;value,
                             java.lang.String&nbsp;units)</pre>
<div class="block">Sets the length of time that dynamic versions will be cached, with units expressed as a String.

 <p>A convenience method for <a href="#cacheDynamicVersionsFor-int-java.util.concurrent.TimeUnit-"><code>cacheDynamicVersionsFor(int, java.util.concurrent.TimeUnit)</code></a> with units expressed as a String.
 Units are resolved by calling the <code>valueOf(String)</code> method of <code>TimeUnit</code> with the upper-cased string value.</p></div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - The number of time units</dd>
<dd><code>units</code> - The units</dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>1.0-milestone-6</dd>
</dl>
</li>
</ul>
<a name="cacheDynamicVersionsFor-int-java.util.concurrent.TimeUnit-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>cacheDynamicVersionsFor</h4>
<pre class="methodSignature">void&nbsp;cacheDynamicVersionsFor&#8203;(int&nbsp;value,
                             java.util.concurrent.TimeUnit&nbsp;units)</pre>
<div class="block">Sets the length of time that dynamic versions will be cached.

 <p>Gradle keeps a cache of dynamic version =&gt; resolved version (ie 2.+ =&gt; 2.3). By default, these cached values are kept for 24 hours, after which the cached entry is expired
 and the dynamic version is resolved again.</p>
 <p>Use this method to provide a custom expiry time after which the cached value for any dynamic version will be expired.</p></div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - The number of time units</dd>
<dd><code>units</code> - The units</dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>1.0-milestone-6</dd>
</dl>
</li>
</ul>
<a name="cacheChangingModulesFor-int-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>cacheChangingModulesFor</h4>
<pre class="methodSignature">void&nbsp;cacheChangingModulesFor&#8203;(int&nbsp;value,
                             java.lang.String&nbsp;units)</pre>
<div class="block">Sets the length of time that changing modules will be cached, with units expressed as a String.

 <p>A convenience method for <a href="#cacheChangingModulesFor-int-java.util.concurrent.TimeUnit-"><code>cacheChangingModulesFor(int, java.util.concurrent.TimeUnit)</code></a> with units expressed as a String.
 Units are resolved by calling the <code>valueOf(String)</code> method of <code>TimeUnit</code> with the upper-cased string value.</p></div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - The number of time units</dd>
<dd><code>units</code> - The units</dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>1.0-milestone-6</dd>
</dl>
</li>
</ul>
<a name="cacheChangingModulesFor-int-java.util.concurrent.TimeUnit-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>cacheChangingModulesFor</h4>
<pre class="methodSignature">void&nbsp;cacheChangingModulesFor&#8203;(int&nbsp;value,
                             java.util.concurrent.TimeUnit&nbsp;units)</pre>
<div class="block">Sets the length of time that changing modules will be cached.

 <p>Gradle caches the contents and artifacts of changing modules. By default, these cached values are kept for 24 hours,
 after which the cached entry is expired and the module is resolved again.</p>
 <p>Use this method to provide a custom expiry time after which the cached entries for any changing module will be expired.</p></div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - The number of time units</dd>
<dd><code>units</code> - The units</dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>1.0-milestone-6</dd>
</dl>
</li>
</ul>
<a name="getComponentSelection--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getComponentSelection</h4>
<pre class="methodSignature"><a href="ComponentSelectionRules.html" title="interface in org.gradle.api.artifacts">ComponentSelectionRules</a>&nbsp;getComponentSelection()</pre>
<div class="block">Returns the currently configured version selection rules object.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>the version selection rules</dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>2.2</dd>
</dl>
</li>
</ul>
<a name="componentSelection-org.gradle.api.Action-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>componentSelection</h4>
<pre class="methodSignature"><a href="ResolutionStrategy.html" title="interface in org.gradle.api.artifacts">ResolutionStrategy</a>&nbsp;componentSelection&#8203;(<a href="../Action.html" title="interface in org.gradle.api">Action</a>&lt;? super <a href="ComponentSelectionRules.html" title="interface in org.gradle.api.artifacts">ComponentSelectionRules</a>&gt;&nbsp;action)</pre>
<div class="block">The componentSelection block provides rules to filter or prevent certain components from appearing in the resolution result.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>action</code> - Action to be applied to the <a href="ComponentSelectionRules.html" title="interface in org.gradle.api.artifacts"><code>ComponentSelectionRules</code></a></dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>this ResolutionStrategy instance</dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>2.2</dd>
</dl>
</li>
</ul>
<a name="getDependencySubstitution--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getDependencySubstitution</h4>
<pre class="methodSignature"><a href="DependencySubstitutions.html" title="interface in org.gradle.api.artifacts">DependencySubstitutions</a>&nbsp;getDependencySubstitution()</pre>
<div class="block">Returns the set of dependency substitution rules that are set for this configuration.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>2.5</dd>
</dl>
</li>
</ul>
<a name="dependencySubstitution-org.gradle.api.Action-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>dependencySubstitution</h4>
<pre class="methodSignature"><a href="ResolutionStrategy.html" title="interface in org.gradle.api.artifacts">ResolutionStrategy</a>&nbsp;dependencySubstitution&#8203;(<a href="../Action.html" title="interface in org.gradle.api">Action</a>&lt;? super <a href="DependencySubstitutions.html" title="interface in org.gradle.api.artifacts">DependencySubstitutions</a>&gt;&nbsp;action)</pre>
<div class="block">Configures the set of dependency substitution rules for this configuration.  The action receives an instance of <a href="DependencySubstitutions.html" title="interface in org.gradle.api.artifacts"><code>DependencySubstitutions</code></a> which
 can then be configured with substitution rules.
 <p>Examples:</p>
 <pre class='autoTested'>
 // add dependency substitution rules
 configurations.all {
   resolutionStrategy.dependencySubstitution {
     // Substitute project and module dependencies
     substitute module('org.gradle:api') using project(':api')
     substitute project(':util') using module('org.gradle:util:3.0')

     // Substitute one module dependency for another
     substitute module('org.gradle:api:2.0') using module('org.gradle:api:2.1')
   }
 }
 </pre></div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>this ResolutionStrategy instance</dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>2.5</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="DependencySubstitutions.html" title="interface in org.gradle.api.artifacts"><code>DependencySubstitutions</code></a></dd>
</dl>
</li>
</ul>
<a name="getUseGlobalDependencySubstitutionRules--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getUseGlobalDependencySubstitutionRules</h4>
<pre class="methodSignature"><a href="../provider/Property.html" title="interface in org.gradle.api.provider">Property</a>&lt;java.lang.Boolean&gt;&nbsp;getUseGlobalDependencySubstitutionRules()</pre>
<div class="block">Gradle implicitly registers dependency substitution rules for all configurations in the whole build
 tree to find projects in other included builds. These rules are always active by default.

 There are however cases, where a certain configuration should not apply these rules when resolving.
 For example, if a binary version of a module should be discovered that is also represented by
 a project in another build.

 This property may be used to deactivate these global substitution rules.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>7.4</dd>
</dl>
</li>
</ul>
<a name="sortArtifacts-org.gradle.api.artifacts.ResolutionStrategy.SortOrder-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>sortArtifacts</h4>
<pre class="methodSignature">void&nbsp;sortArtifacts&#8203;(<a href="ResolutionStrategy.SortOrder.html" title="enum in org.gradle.api.artifacts">ResolutionStrategy.SortOrder</a>&nbsp;sortOrder)</pre>
<div class="block">Specifies the ordering for resolved artifacts. Options are:
 <ul>
 <li><a href="ResolutionStrategy.SortOrder.html#DEFAULT"><code>ResolutionStrategy.SortOrder.DEFAULT</code></a> : Don't specify the sort order. Gradle will provide artifacts in the default order.</li>
 <li><a href="ResolutionStrategy.SortOrder.html#CONSUMER_FIRST"><code>ResolutionStrategy.SortOrder.CONSUMER_FIRST</code></a> : Artifacts for a consuming component should appear <em>before</em> artifacts for its dependencies.</li>
 <li><a href="ResolutionStrategy.SortOrder.html#DEPENDENCY_FIRST"><code>ResolutionStrategy.SortOrder.DEPENDENCY_FIRST</code></a> : Artifacts for a consuming component should appear <em>after</em> artifacts for its dependencies.</li>
 </ul>
 A best attempt will be made to sort artifacts according the supplied <a href="ResolutionStrategy.SortOrder.html" title="enum in org.gradle.api.artifacts"><code>ResolutionStrategy.SortOrder</code></a>, but no guarantees will be made in the presence of dependency cycles.

 NOTE: For a particular Gradle version, artifact ordering will be consistent. Multiple resolves for the same inputs will result in the
 same outputs in the same order.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>3.5</dd>
</dl>
</li>
</ul>
<a name="capabilitiesResolution-org.gradle.api.Action-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>capabilitiesResolution</h4>
<pre class="methodSignature"><a href="ResolutionStrategy.html" title="interface in org.gradle.api.artifacts">ResolutionStrategy</a>&nbsp;capabilitiesResolution&#8203;(<a href="../Action.html" title="interface in org.gradle.api">Action</a>&lt;? super <a href="CapabilitiesResolution.html" title="interface in org.gradle.api.artifacts">CapabilitiesResolution</a>&gt;&nbsp;action)</pre>
<div class="block">Configures the capabilities resolution strategy.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>action</code> - the configuration action.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>this resolution strategy</dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>5.6</dd>
</dl>
</li>
</ul>
<a name="getCapabilitiesResolution--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>getCapabilitiesResolution</h4>
<pre class="methodSignature"><a href="CapabilitiesResolution.html" title="interface in org.gradle.api.artifacts">CapabilitiesResolution</a>&nbsp;getCapabilitiesResolution()</pre>
<div class="block">Returns the capabilities resolution strategy.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>5.6</dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../allclasses.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li><a href="#nested.class.summary">Nested</a>&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
