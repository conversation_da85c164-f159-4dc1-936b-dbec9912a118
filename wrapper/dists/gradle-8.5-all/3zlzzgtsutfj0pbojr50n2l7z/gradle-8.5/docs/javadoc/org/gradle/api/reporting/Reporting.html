<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc -->
<title>Reporting (Gradle API 8.5)</title>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<link rel="stylesheet" type="text/css" href="../../../../javadoc.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../jquery/jquery-ui.min.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../jquery-ui.overrides.css" title="Style">
<script type="text/javascript" src="../../../../script.js"></script>
<script type="text/javascript" src="../../../../jquery/jszip/dist/jszip.min.js"></script>
<script type="text/javascript" src="../../../../jquery/jszip-utils/dist/jszip-utils.min.js"></script>
<!--[if IE]>
<script type="text/javascript" src="../../../../jquery/jszip-utils/dist/jszip-utils-ie.min.js"></script>
<![endif]-->
<script type="text/javascript" src="../../../../jquery/jquery-3.6.1.min.js"></script>
<script type="text/javascript" src="../../../../jquery/jquery-ui.min.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="Reporting (Gradle API 8.5)";
        }
    }
    catch(err) {
    }
//-->
var data = {"i0":6,"i1":6,"i2":6};
var tabs = {65535:["t0","All Methods"],2:["t2","Instance Methods"],4:["t3","Abstract Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
var pathtoroot = "../../../../";
var useModuleDirectories = false;
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<div class="fixedNav">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../allclasses.html">All&nbsp;Classes</a></li>
</ul>
<ul class="navListSearch">
<li><label for="search">SEARCH:</label>
<input type="text" id="search" value="search" disabled="disabled">
<input type="reset" id="reset" value="reset" disabled="disabled">
</li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
</div>
<div class="navPadding">&nbsp;</div>
<script type="text/javascript"><!--
$('.navPadding').css('padding-top', $('.fixedNav').css("height"));
//-->
</script>
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle"><span class="packageLabelInType">Package</span>&nbsp;<a href="package-summary.html">org.gradle.api.reporting</a></div>
<h2 title="Interface Reporting" class="title">Interface Reporting&lt;T extends <a href="ReportContainer.html" title="interface in org.gradle.api.reporting">ReportContainer</a>&gt;</h2>
</div>
<div class="contentContainer">
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt><span class="paramLabel">Type Parameters:</span></dt>
<dd><code>T</code> - The base type of the report container</dd>
</dl>
<dl>
<dt>All Known Implementing Classes:</dt>
<dd><code><a href="../tasks/diagnostics/AbstractConfigurationReportTask.html" title="class in org.gradle.api.tasks.diagnostics">AbstractConfigurationReportTask</a></code>, <code><a href="../tasks/testing/AbstractTestTask.html" title="class in org.gradle.api.tasks.testing">AbstractTestTask</a></code>, <code><a href="../plugins/quality/Checkstyle.html" title="class in org.gradle.api.plugins.quality">Checkstyle</a></code>, <code><a href="../plugins/quality/CodeNarc.html" title="class in org.gradle.api.plugins.quality">CodeNarc</a></code>, <code><a href="GenerateBuildDashboard.html" title="class in org.gradle.api.reporting">GenerateBuildDashboard</a></code>, <code><a href="dependencies/HtmlDependencyReportTask.html" title="class in org.gradle.api.reporting.dependencies">HtmlDependencyReportTask</a></code>, <code><a href="../../testing/jacoco/tasks/JacocoReport.html" title="class in org.gradle.testing.jacoco.tasks">JacocoReport</a></code>, <code><a href="../tasks/diagnostics/OutgoingVariantsReportTask.html" title="class in org.gradle.api.tasks.diagnostics">OutgoingVariantsReportTask</a></code>, <code><a href="../plugins/quality/Pmd.html" title="class in org.gradle.api.plugins.quality">Pmd</a></code>, <code><a href="../tasks/diagnostics/ResolvableConfigurationsReportTask.html" title="class in org.gradle.api.tasks.diagnostics">ResolvableConfigurationsReportTask</a></code>, <code><a href="../tasks/testing/Test.html" title="class in org.gradle.api.tasks.testing">Test</a></code>, <code><a href="../../nativeplatform/test/xctest/tasks/XCTest.html" title="class in org.gradle.nativeplatform.test.xctest.tasks">XCTest</a></code></dd>
</dl>
<hr>
<pre>public interface <span class="typeNameLabel">Reporting&lt;T extends <a href="ReportContainer.html" title="interface in org.gradle.api.reporting">ReportContainer</a>&gt;</span></pre>
<div class="block">An object that provides reporting options.
 <p>
 Tasks that produce reports as part of their execution expose configuration options of those reports via these methods.
 The <code>Reporting</code> interface is parameterized, where the parameter denotes the specific type of reporting container
 that is exposed. The specific type of the reporting container denotes the different types of reports available.
 <p>
 For example, given a task such as:
 </p>
 <pre>
 class MyTask implements Reporting&lt;MyReportContainer&gt; {
     // implementation
 }

 interface MyReportContainer extends ReportContainer&lt;Report&gt; {
     Report getHtml();
     Report getCsv();
 }
 </pre>
 <p>
 The reporting aspects of such a task can be configured as such:
 </p>
 <pre>
 task my(type: MyTask) {
     reports {
         html.required = true
         csv.required = false
     }
 }
 </pre>
 <p>
 See the documentation for the specific <code>ReportContainer</code> type for the task for information on report types and options.
 </p></div>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t3" class="tableTab"><span><a href="javascript:show(4);">Abstract Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colSecond" scope="col">Method</th>
<th class="colLast" scope="col">Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code><a href="Reporting.html" title="type parameter in Reporting">T</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getReports--">getReports</a></span>()</code></th>
<td class="colLast">
<div class="block">A <a href="ReportContainer.html" title="interface in org.gradle.api.reporting"><code>ReportContainer</code></a> instance.</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code><a href="Reporting.html" title="type parameter in Reporting">T</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#reports-groovy.lang.Closure-">reports</a></span>&#8203;(<a href="https://docs.groovy-lang.org/docs/groovy-3.0.17/html/gapi/groovy/lang/Closure.html?is-external=true" title="class or interface in groovy.lang" class="externalLink">Closure</a>&nbsp;closure)</code></th>
<td class="colLast">
<div class="block">Allow configuration of the report container by closure.</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code><a href="Reporting.html" title="type parameter in Reporting">T</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#reports-org.gradle.api.Action-">reports</a></span>&#8203;(<a href="../Action.html" title="interface in org.gradle.api">Action</a>&lt;? super <a href="Reporting.html" title="type parameter in Reporting">T</a>&gt;&nbsp;configureAction)</code></th>
<td class="colLast">
<div class="block">Allow configuration of the report container by closure.</div>
</td>
</tr>
</table>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="getReports--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getReports</h4>
<pre class="methodSignature"><a href="Reporting.html" title="type parameter in Reporting">T</a>&nbsp;getReports()</pre>
<div class="block">A <a href="ReportContainer.html" title="interface in org.gradle.api.reporting"><code>ReportContainer</code></a> instance.
 <p>
 Implementers specify a specific implementation of <a href="ReportContainer.html" title="interface in org.gradle.api.reporting"><code>ReportContainer</code></a> that describes the types of reports that
 are available.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>The report container</dd>
</dl>
</li>
</ul>
<a name="reports-groovy.lang.Closure-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>reports</h4>
<pre class="methodSignature"><a href="Reporting.html" title="type parameter in Reporting">T</a>&nbsp;reports&#8203;(<a href="https://docs.groovy-lang.org/docs/groovy-3.0.17/html/gapi/groovy/lang/DelegatesTo.html?is-external=true" title="class or interface in groovy.lang" class="externalLink">@DelegatesTo</a>(<a href="https://docs.groovy-lang.org/docs/groovy-3.0.17/html/gapi/groovy/lang/DelegatesTo.html?is-external=true#type--" title="class or interface in groovy.lang" class="externalLink">type</a>="T",<a href="https://docs.groovy-lang.org/docs/groovy-3.0.17/html/gapi/groovy/lang/DelegatesTo.html?is-external=true#strategy--" title="class or interface in groovy.lang" class="externalLink">strategy</a>=1)
          <a href="https://docs.groovy-lang.org/docs/groovy-3.0.17/html/gapi/groovy/lang/Closure.html?is-external=true" title="class or interface in groovy.lang" class="externalLink">Closure</a>&nbsp;closure)</pre>
<div class="block">Allow configuration of the report container by closure.

 <pre>
 reports {
   html {
     required false
   }
   xml.outputLocation = "build/reports/myReport.xml"
 }
 </pre></div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>closure</code> - The configuration</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>The report container</dd>
</dl>
</li>
</ul>
<a name="reports-org.gradle.api.Action-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>reports</h4>
<pre class="methodSignature"><a href="Reporting.html" title="type parameter in Reporting">T</a>&nbsp;reports&#8203;(<a href="../Action.html" title="interface in org.gradle.api">Action</a>&lt;? super <a href="Reporting.html" title="type parameter in Reporting">T</a>&gt;&nbsp;configureAction)</pre>
<div class="block">Allow configuration of the report container by closure.

 <pre>
 reports {
   html {
     required false
   }
   xml.outputLocation = "build/reports/myReport.xml"
 }
 </pre></div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>configureAction</code> - The configuration</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>The report container</dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../allclasses.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
