<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc -->
<title>NamedDomainObjectCollection (Gradle API 8.5)</title>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<link rel="stylesheet" type="text/css" href="../../../javadoc.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../jquery/jquery-ui.min.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../jquery-ui.overrides.css" title="Style">
<script type="text/javascript" src="../../../script.js"></script>
<script type="text/javascript" src="../../../jquery/jszip/dist/jszip.min.js"></script>
<script type="text/javascript" src="../../../jquery/jszip-utils/dist/jszip-utils.min.js"></script>
<!--[if IE]>
<script type="text/javascript" src="../../../jquery/jszip-utils/dist/jszip-utils-ie.min.js"></script>
<![endif]-->
<script type="text/javascript" src="../../../jquery/jquery-3.6.1.min.js"></script>
<script type="text/javascript" src="../../../jquery/jquery-ui.min.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="NamedDomainObjectCollection (Gradle API 8.5)";
        }
    }
    catch(err) {
    }
//-->
var data = {"i0":6,"i1":6,"i2":6,"i3":6,"i4":6,"i5":6,"i6":6,"i7":6,"i8":6,"i9":6,"i10":6,"i11":6,"i12":6,"i13":6,"i14":6,"i15":6,"i16":6,"i17":6,"i18":6,"i19":6,"i20":6,"i21":6};
var tabs = {65535:["t0","All Methods"],2:["t2","Instance Methods"],4:["t3","Abstract Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
var pathtoroot = "../../../";
var useModuleDirectories = false;
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<div class="fixedNav">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../index-all.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../allclasses.html">All&nbsp;Classes</a></li>
</ul>
<ul class="navListSearch">
<li><label for="search">SEARCH:</label>
<input type="text" id="search" value="search" disabled="disabled">
<input type="reset" id="reset" value="reset" disabled="disabled">
</li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
</div>
<div class="navPadding">&nbsp;</div>
<script type="text/javascript"><!--
$('.navPadding').css('padding-top', $('.fixedNav').css("height"));
//-->
</script>
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle"><span class="packageLabelInType">Package</span>&nbsp;<a href="package-summary.html">org.gradle.api</a></div>
<h2 title="Interface NamedDomainObjectCollection" class="title">Interface NamedDomainObjectCollection&lt;T&gt;</h2>
</div>
<div class="contentContainer">
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt><span class="paramLabel">Type Parameters:</span></dt>
<dd><code>T</code> - The type of objects in this collection.</dd>
</dl>
<dl>
<dt>All Superinterfaces:</dt>
<dd><code>java.util.Collection&lt;T&gt;</code>, <code><a href="DomainObjectCollection.html" title="interface in org.gradle.api">DomainObjectCollection</a>&lt;T&gt;</code>, <code>java.lang.Iterable&lt;T&gt;</code></dd>
</dl>
<dl>
<dt>All Known Subinterfaces:</dt>
<dd><code><a href="artifacts/ArtifactRepositoryContainer.html" title="interface in org.gradle.api.artifacts">ArtifactRepositoryContainer</a></code>, <code><a href="artifacts/type/ArtifactTypeContainer.html" title="interface in org.gradle.api.artifacts.type">ArtifactTypeContainer</a></code>, <code><a href="artifacts/repositories/AuthenticationContainer.html" title="interface in org.gradle.api.artifacts.repositories">AuthenticationContainer</a></code>, <code><a href="reporting/BuildDashboardReports.html" title="interface in org.gradle.api.reporting">BuildDashboardReports</a></code>, <code><a href="../nativeplatform/BuildTypeContainer.html" title="interface in org.gradle.nativeplatform">BuildTypeContainer</a></code>, <code><a href="plugins/quality/CheckstyleReports.html" title="interface in org.gradle.api.plugins.quality">CheckstyleReports</a></code>, <code><a href="plugins/quality/CodeNarcReports.html" title="interface in org.gradle.api.plugins.quality">CodeNarcReports</a></code>, <code><a href="artifacts/ConfigurationContainer.html" title="interface in org.gradle.api.artifacts">ConfigurationContainer</a></code>, <code><a href="tasks/diagnostics/configurations/ConfigurationReports.html" title="interface in org.gradle.api.tasks.diagnostics.configurations">ConfigurationReports</a></code>, <code><a href="reporting/dependencies/DependencyReportContainer.html" title="interface in org.gradle.api.reporting.dependencies">DependencyReportContainer</a></code>, <code><a href="distribution/DistributionContainer.html" title="interface in org.gradle.api.distribution">DistributionContainer</a></code>, <code><a href="ExtensiblePolymorphicDomainObjectContainer.html" title="interface in org.gradle.api">ExtensiblePolymorphicDomainObjectContainer</a>&lt;T&gt;</code>, <code><a href="../nativeplatform/FlavorContainer.html" title="interface in org.gradle.nativeplatform">FlavorContainer</a></code>, <code><a href="publish/ivy/IvyConfigurationContainer.html" title="interface in org.gradle.api.publish.ivy">IvyConfigurationContainer</a></code>, <code><a href="../testing/jacoco/tasks/JacocoReportsContainer.html" title="interface in org.gradle.testing.jacoco.tasks">JacocoReportsContainer</a></code>, <code><a href="initialization/resolve/MutableVersionCatalogContainer.html" title="interface in org.gradle.api.initialization.resolve">MutableVersionCatalogContainer</a></code>, <code><a href="NamedDomainObjectContainer.html" title="interface in org.gradle.api">NamedDomainObjectContainer</a>&lt;T&gt;</code>, <code><a href="NamedDomainObjectList.html" title="interface in org.gradle.api">NamedDomainObjectList</a>&lt;T&gt;</code>, <code><a href="NamedDomainObjectSet.html" title="interface in org.gradle.api">NamedDomainObjectSet</a>&lt;T&gt;</code>, <code><a href="../nativeplatform/toolchain/NativeToolChainRegistry.html" title="interface in org.gradle.nativeplatform.toolchain">NativeToolChainRegistry</a></code>, <code><a href="../platform/base/PlatformContainer.html" title="interface in org.gradle.platform.base">PlatformContainer</a></code>, <code><a href="plugins/quality/PmdReports.html" title="interface in org.gradle.api.plugins.quality">PmdReports</a></code>, <code><a href="PolymorphicDomainObjectContainer.html" title="interface in org.gradle.api">PolymorphicDomainObjectContainer</a>&lt;T&gt;</code>, <code><a href="../nativeplatform/PrebuiltLibraries.html" title="interface in org.gradle.nativeplatform">PrebuiltLibraries</a></code>, <code><a href="publish/PublicationContainer.html" title="interface in org.gradle.api.publish">PublicationContainer</a></code>, <code><a href="reporting/ReportContainer.html" title="interface in org.gradle.api.reporting">ReportContainer</a>&lt;T&gt;</code>, <code><a href="../nativeplatform/Repositories.html" title="interface in org.gradle.nativeplatform">Repositories</a></code>, <code><a href="artifacts/dsl/RepositoryHandler.html" title="interface in org.gradle.api.artifacts.dsl">RepositoryHandler</a></code>, <code><a href="component/SoftwareComponentContainer.html" title="interface in org.gradle.api.component">SoftwareComponentContainer</a></code>, <code><a href="tasks/SourceSetContainer.html" title="interface in org.gradle.api.tasks">SourceSetContainer</a></code>, <code><a href="tasks/TaskCollection.html" title="interface in org.gradle.api.tasks">TaskCollection</a>&lt;T&gt;</code>, <code><a href="tasks/TaskContainer.html" title="interface in org.gradle.api.tasks">TaskContainer</a></code>, <code><a href="tasks/testing/TestTaskReports.html" title="interface in org.gradle.api.tasks.testing">TestTaskReports</a></code></dd>
</dl>
<hr>
<pre>public interface <span class="typeNameLabel">NamedDomainObjectCollection&lt;T&gt;</span>
extends <a href="DomainObjectCollection.html" title="interface in org.gradle.api">DomainObjectCollection</a>&lt;T&gt;</pre>
<div class="block"><p>A <code>NamedDomainObjectCollection</code> represents a collection of objects that have an inherent, constant, name.</p>

 <p>Objects to be added to a named domain object collection must implement <code>equals()</code> in such a way that no two objects
 with different names are considered equal. That is, all equality tests <strong>must</strong> consider the name as an
 equality key. Behavior is undefined if two objects with different names are considered equal by their <code>equals()</code> implementation.</p>

 <p>All implementations <strong>must</strong> guarantee that all elements in the collection are uniquely named. That is,
 an attempt to add an object with a name equal to the name of any existing object in the collection will fail.
 Implementations may choose to simply return false from <code>add(T)</code> or to throw an exception.</p>

 <p>Objects in the collection are accessible as read-only properties, using the name of the object
 as the property name. For example (assuming the 'name' property provides the object name):</p>

 <pre>
 books.add(new Book(name: "gradle", title: null))
 books.gradle.title = "Gradle in Action"
 </pre>

 <p>A dynamic method is added for each object which takes a configuration closure. This is equivalent to calling
 <a href="#getByName-java.lang.String-groovy.lang.Closure-"><code>getByName(String, groovy.lang.Closure)</code></a>. For example:</p>

 <pre>
 books.add(new Book(name: "gradle", title: null))
 books.gradle {
   title = "Gradle in Action"
 }
 </pre>

 <p>You can also use the <code>[]</code> operator to access the objects of a collection by name. For example:</p>

 <pre>
 books.add(new Book(name: "gradle", title: null))
 books['gradle'].title = "Gradle in Action"
 </pre>

 <p><a href="Rule.html" title="interface in org.gradle.api"><code>Rule</code></a> objects can be attached to the collection in order to respond to requests for objects by name
 where no object with name exists in the collection. This mechanism can be used to create objects on demand.
 For example: </p>

 <pre>
 books.addRule('create any') { books.add(new Book(name: "gradle", title: null)) }
 books.gradle.name == "gradle"
 </pre></div>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t3" class="tableTab"><span><a href="javascript:show(4);">Abstract Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colSecond" scope="col">Method</th>
<th class="colLast" scope="col">Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#add-T-">add</a></span>&#8203;(<a href="NamedDomainObjectCollection.html" title="type parameter in NamedDomainObjectCollection">T</a>&nbsp;e)</code></th>
<td class="colLast">
<div class="block">Adds an object to the collection, if there is no existing object in the collection with the same name.</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#addAll-java.util.Collection-">addAll</a></span>&#8203;(java.util.Collection&lt;? extends <a href="NamedDomainObjectCollection.html" title="type parameter in NamedDomainObjectCollection">T</a>&gt;&nbsp;c)</code></th>
<td class="colLast">
<div class="block">Adds any of the given objects to the collection that do not have the same name as any existing element.</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code><a href="Rule.html" title="interface in org.gradle.api">Rule</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#addRule-java.lang.String-groovy.lang.Closure-">addRule</a></span>&#8203;(java.lang.String&nbsp;description,
       <a href="https://docs.groovy-lang.org/docs/groovy-3.0.17/html/gapi/groovy/lang/Closure.html?is-external=true" title="class or interface in groovy.lang" class="externalLink">Closure</a>&nbsp;ruleAction)</code></th>
<td class="colLast">
<div class="block">Adds a rule to this collection.</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code><a href="Rule.html" title="interface in org.gradle.api">Rule</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#addRule-java.lang.String-org.gradle.api.Action-">addRule</a></span>&#8203;(java.lang.String&nbsp;description,
       <a href="Action.html" title="interface in org.gradle.api">Action</a>&lt;java.lang.String&gt;&nbsp;ruleAction)</code></th>
<td class="colLast">
<div class="block">Adds a rule to this collection.</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code><a href="Rule.html" title="interface in org.gradle.api">Rule</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#addRule-org.gradle.api.Rule-">addRule</a></span>&#8203;(<a href="Rule.html" title="interface in org.gradle.api">Rule</a>&nbsp;rule)</code></th>
<td class="colLast">
<div class="block">Adds a rule to this collection.</div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code><a href="NamedDomainObjectCollection.html" title="type parameter in NamedDomainObjectCollection">T</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#findByName-java.lang.String-">findByName</a></span>&#8203;(java.lang.String&nbsp;name)</code></th>
<td class="colLast">
<div class="block">Locates an object by name, returning null if there is no such object.</div>
</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code>java.util.SortedMap&lt;java.lang.String,&#8203;<a href="NamedDomainObjectCollection.html" title="type parameter in NamedDomainObjectCollection">T</a>&gt;</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getAsMap--">getAsMap</a></span>()</code></th>
<td class="colLast">
<div class="block">Returns the objects in this collection, as a map from object name to object instance.</div>
</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code><a href="NamedDomainObjectCollection.html" title="type parameter in NamedDomainObjectCollection">T</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getAt-java.lang.String-">getAt</a></span>&#8203;(java.lang.String&nbsp;name)</code></th>
<td class="colLast">
<div class="block">Locates an object by name, failing if there is no such object.</div>
</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code><a href="NamedDomainObjectCollection.html" title="type parameter in NamedDomainObjectCollection">T</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getByName-java.lang.String-">getByName</a></span>&#8203;(java.lang.String&nbsp;name)</code></th>
<td class="colLast">
<div class="block">Locates an object by name, failing if there is no such object.</div>
</td>
</tr>
<tr id="i9" class="rowColor">
<td class="colFirst"><code><a href="NamedDomainObjectCollection.html" title="type parameter in NamedDomainObjectCollection">T</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getByName-java.lang.String-groovy.lang.Closure-">getByName</a></span>&#8203;(java.lang.String&nbsp;name,
         <a href="https://docs.groovy-lang.org/docs/groovy-3.0.17/html/gapi/groovy/lang/Closure.html?is-external=true" title="class or interface in groovy.lang" class="externalLink">Closure</a>&nbsp;configureClosure)</code></th>
<td class="colLast">
<div class="block">Locates an object by name, failing if there is no such object.</div>
</td>
</tr>
<tr id="i10" class="altColor">
<td class="colFirst"><code><a href="NamedDomainObjectCollection.html" title="type parameter in NamedDomainObjectCollection">T</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getByName-java.lang.String-org.gradle.api.Action-">getByName</a></span>&#8203;(java.lang.String&nbsp;name,
         <a href="Action.html" title="interface in org.gradle.api">Action</a>&lt;? super <a href="NamedDomainObjectCollection.html" title="type parameter in NamedDomainObjectCollection">T</a>&gt;&nbsp;configureAction)</code></th>
<td class="colLast">
<div class="block">Locates an object by name, failing if there is no such object.</div>
</td>
</tr>
<tr id="i11" class="rowColor">
<td class="colFirst"><code><a href="NamedDomainObjectCollectionSchema.html" title="interface in org.gradle.api">NamedDomainObjectCollectionSchema</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getCollectionSchema--">getCollectionSchema</a></span>()</code></th>
<td class="colLast">
<div class="block">Provides access to the schema of all created or registered named domain objects in this collection.</div>
</td>
</tr>
<tr id="i12" class="altColor">
<td class="colFirst"><code><a href="Namer.html" title="interface in org.gradle.api">Namer</a>&lt;<a href="NamedDomainObjectCollection.html" title="type parameter in NamedDomainObjectCollection">T</a>&gt;</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getNamer--">getNamer</a></span>()</code></th>
<td class="colLast">
<div class="block">An object that represents the naming strategy used to name objects of this collection.</div>
</td>
</tr>
<tr id="i13" class="rowColor">
<td class="colFirst"><code>java.util.SortedSet&lt;java.lang.String&gt;</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getNames--">getNames</a></span>()</code></th>
<td class="colLast">
<div class="block">Returns the names of the objects in this collection as a Set of Strings.</div>
</td>
</tr>
<tr id="i14" class="altColor">
<td class="colFirst"><code>java.util.List&lt;<a href="Rule.html" title="interface in org.gradle.api">Rule</a>&gt;</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getRules--">getRules</a></span>()</code></th>
<td class="colLast">
<div class="block">Returns the rules used by this collection.</div>
</td>
</tr>
<tr id="i15" class="rowColor">
<td class="colFirst"><code><a href="NamedDomainObjectCollection.html" title="interface in org.gradle.api">NamedDomainObjectCollection</a>&lt;<a href="NamedDomainObjectCollection.html" title="type parameter in NamedDomainObjectCollection">T</a>&gt;</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#matching-groovy.lang.Closure-">matching</a></span>&#8203;(<a href="https://docs.groovy-lang.org/docs/groovy-3.0.17/html/gapi/groovy/lang/Closure.html?is-external=true" title="class or interface in groovy.lang" class="externalLink">Closure</a>&nbsp;spec)</code></th>
<td class="colLast">
<div class="block">Returns a collection which contains the objects in this collection which meet the given closure specification.</div>
</td>
</tr>
<tr id="i16" class="altColor">
<td class="colFirst"><code><a href="NamedDomainObjectCollection.html" title="interface in org.gradle.api">NamedDomainObjectCollection</a>&lt;<a href="NamedDomainObjectCollection.html" title="type parameter in NamedDomainObjectCollection">T</a>&gt;</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#matching-org.gradle.api.specs.Spec-">matching</a></span>&#8203;(<a href="specs/Spec.html" title="interface in org.gradle.api.specs">Spec</a>&lt;? super <a href="NamedDomainObjectCollection.html" title="type parameter in NamedDomainObjectCollection">T</a>&gt;&nbsp;spec)</code></th>
<td class="colLast">
<div class="block">Returns a collection which contains the objects in this collection which meet the given specification.</div>
</td>
</tr>
<tr id="i17" class="rowColor">
<td class="colFirst"><code><a href="NamedDomainObjectProvider.html" title="interface in org.gradle.api">NamedDomainObjectProvider</a>&lt;<a href="NamedDomainObjectCollection.html" title="type parameter in NamedDomainObjectCollection">T</a>&gt;</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#named-java.lang.String-">named</a></span>&#8203;(java.lang.String&nbsp;name)</code></th>
<td class="colLast">
<div class="block">Locates a object by name, without triggering its creation or configuration, failing if there is no such object.</div>
</td>
</tr>
<tr id="i18" class="altColor">
<td class="colFirst"><code>&lt;S extends <a href="NamedDomainObjectCollection.html" title="type parameter in NamedDomainObjectCollection">T</a>&gt;<br><a href="NamedDomainObjectProvider.html" title="interface in org.gradle.api">NamedDomainObjectProvider</a>&lt;S&gt;</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#named-java.lang.String-java.lang.Class-">named</a></span>&#8203;(java.lang.String&nbsp;name,
     java.lang.Class&lt;S&gt;&nbsp;type)</code></th>
<td class="colLast">
<div class="block">Locates a object by name and type, without triggering its creation or configuration, failing if there is no such object.</div>
</td>
</tr>
<tr id="i19" class="rowColor">
<td class="colFirst"><code>&lt;S extends <a href="NamedDomainObjectCollection.html" title="type parameter in NamedDomainObjectCollection">T</a>&gt;<br><a href="NamedDomainObjectProvider.html" title="interface in org.gradle.api">NamedDomainObjectProvider</a>&lt;S&gt;</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#named-java.lang.String-java.lang.Class-org.gradle.api.Action-">named</a></span>&#8203;(java.lang.String&nbsp;name,
     java.lang.Class&lt;S&gt;&nbsp;type,
     <a href="Action.html" title="interface in org.gradle.api">Action</a>&lt;? super S&gt;&nbsp;configurationAction)</code></th>
<td class="colLast">
<div class="block">Locates a object by name and type, without triggering its creation or configuration, failing if there is no such object.</div>
</td>
</tr>
<tr id="i20" class="altColor">
<td class="colFirst"><code><a href="NamedDomainObjectProvider.html" title="interface in org.gradle.api">NamedDomainObjectProvider</a>&lt;<a href="NamedDomainObjectCollection.html" title="type parameter in NamedDomainObjectCollection">T</a>&gt;</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#named-java.lang.String-org.gradle.api.Action-">named</a></span>&#8203;(java.lang.String&nbsp;name,
     <a href="Action.html" title="interface in org.gradle.api">Action</a>&lt;? super <a href="NamedDomainObjectCollection.html" title="type parameter in NamedDomainObjectCollection">T</a>&gt;&nbsp;configurationAction)</code></th>
<td class="colLast">
<div class="block">Locates a object by name, without triggering its creation or configuration, failing if there is no such object.</div>
</td>
</tr>
<tr id="i21" class="rowColor">
<td class="colFirst"><code>&lt;S extends <a href="NamedDomainObjectCollection.html" title="type parameter in NamedDomainObjectCollection">T</a>&gt;<br><a href="NamedDomainObjectCollection.html" title="interface in org.gradle.api">NamedDomainObjectCollection</a>&lt;S&gt;</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#withType-java.lang.Class-">withType</a></span>&#8203;(java.lang.Class&lt;S&gt;&nbsp;type)</code></th>
<td class="colLast">
<div class="block">Returns a collection containing the objects in this collection of the given type.</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.util.Collection">
<!--   -->
</a>
<h3>Methods inherited from interface&nbsp;java.util.Collection</h3>
<code>clear, contains, containsAll, equals, hashCode, isEmpty, iterator, parallelStream, remove, removeAll, removeIf, retainAll, size, spliterator, stream, toArray, toArray, toArray</code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.org.gradle.api.DomainObjectCollection">
<!--   -->
</a>
<h3>Methods inherited from interface&nbsp;org.gradle.api.<a href="DomainObjectCollection.html" title="interface in org.gradle.api">DomainObjectCollection</a></h3>
<code><a href="DomainObjectCollection.html#addAllLater-org.gradle.api.provider.Provider-">addAllLater</a>, <a href="DomainObjectCollection.html#addLater-org.gradle.api.provider.Provider-">addLater</a>, <a href="DomainObjectCollection.html#all-groovy.lang.Closure-">all</a>, <a href="DomainObjectCollection.html#all-org.gradle.api.Action-">all</a>, <a href="DomainObjectCollection.html#configureEach-org.gradle.api.Action-">configureEach</a>, <a href="DomainObjectCollection.html#findAll-groovy.lang.Closure-">findAll</a>, <a href="DomainObjectCollection.html#whenObjectAdded-groovy.lang.Closure-">whenObjectAdded</a>, <a href="DomainObjectCollection.html#whenObjectAdded-org.gradle.api.Action-">whenObjectAdded</a>, <a href="DomainObjectCollection.html#whenObjectRemoved-groovy.lang.Closure-">whenObjectRemoved</a>, <a href="DomainObjectCollection.html#whenObjectRemoved-org.gradle.api.Action-">whenObjectRemoved</a>, <a href="DomainObjectCollection.html#withType-java.lang.Class-groovy.lang.Closure-">withType</a>, <a href="DomainObjectCollection.html#withType-java.lang.Class-org.gradle.api.Action-">withType</a></code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Iterable">
<!--   -->
</a>
<h3>Methods inherited from interface&nbsp;java.lang.Iterable</h3>
<code>forEach</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="add-java.lang.Object-">
<!--   -->
</a><a name="add-T-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>add</h4>
<pre class="methodSignature">boolean&nbsp;add&#8203;(<a href="NamedDomainObjectCollection.html" title="type parameter in NamedDomainObjectCollection">T</a>&nbsp;e)</pre>
<div class="block">Adds an object to the collection, if there is no existing object in the collection with the same name.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code>add</code>&nbsp;in interface&nbsp;<code>java.util.Collection&lt;<a href="NamedDomainObjectCollection.html" title="type parameter in NamedDomainObjectCollection">T</a>&gt;</code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>e</code> - the item to add to the collection</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd><code>true</code> if the item was added, or  false if an item with the same name already exists.</dd>
</dl>
</li>
</ul>
<a name="addAll-java.util.Collection-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>addAll</h4>
<pre class="methodSignature">boolean&nbsp;addAll&#8203;(java.util.Collection&lt;? extends <a href="NamedDomainObjectCollection.html" title="type parameter in NamedDomainObjectCollection">T</a>&gt;&nbsp;c)</pre>
<div class="block">Adds any of the given objects to the collection that do not have the same name as any existing element.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code>addAll</code>&nbsp;in interface&nbsp;<code>java.util.Collection&lt;<a href="NamedDomainObjectCollection.html" title="type parameter in NamedDomainObjectCollection">T</a>&gt;</code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>c</code> - the items to add to the collection</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd><code>true</code> if any item was added, or  false if all items have non unique names within this collection.</dd>
</dl>
</li>
</ul>
<a name="getNamer--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getNamer</h4>
<pre class="methodSignature"><a href="Namer.html" title="interface in org.gradle.api">Namer</a>&lt;<a href="NamedDomainObjectCollection.html" title="type parameter in NamedDomainObjectCollection">T</a>&gt;&nbsp;getNamer()</pre>
<div class="block">An object that represents the naming strategy used to name objects of this collection.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>Object representing the naming strategy.</dd>
</dl>
</li>
</ul>
<a name="getAsMap--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getAsMap</h4>
<pre class="methodSignature">java.util.SortedMap&lt;java.lang.String,&#8203;<a href="NamedDomainObjectCollection.html" title="type parameter in NamedDomainObjectCollection">T</a>&gt;&nbsp;getAsMap()</pre>
<div class="block"><p>Returns the objects in this collection, as a map from object name to object instance.</p>

 <p>The map is ordered by the <em>natural ordering</em> of the object names (i.e. keys).</p></div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>The objects. Returns an empty map if this collection is empty.</dd>
</dl>
</li>
</ul>
<a name="getNames--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getNames</h4>
<pre class="methodSignature">java.util.SortedSet&lt;java.lang.String&gt;&nbsp;getNames()</pre>
<div class="block"><p>Returns the names of the objects in this collection as a Set of Strings.</p>

 <p>The set of names is in <em>natural ordering</em>.</p></div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>The names. Returns an empty set if this collection is empty.</dd>
</dl>
</li>
</ul>
<a name="findByName-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>findByName</h4>
<pre class="methodSignature">@Nullable
<a href="NamedDomainObjectCollection.html" title="type parameter in NamedDomainObjectCollection">T</a>&nbsp;findByName&#8203;(java.lang.String&nbsp;name)</pre>
<div class="block">Locates an object by name, returning null if there is no such object.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>name</code> - The object name</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>The object with the given name, or null if there is no such object in this collection.</dd>
</dl>
</li>
</ul>
<a name="getByName-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getByName</h4>
<pre class="methodSignature"><a href="NamedDomainObjectCollection.html" title="type parameter in NamedDomainObjectCollection">T</a>&nbsp;getByName&#8203;(java.lang.String&nbsp;name)
     throws <a href="UnknownDomainObjectException.html" title="class in org.gradle.api">UnknownDomainObjectException</a></pre>
<div class="block">Locates an object by name, failing if there is no such object.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>name</code> - The object name</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>The object with the given name. Never returns null.</dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code><a href="UnknownDomainObjectException.html" title="class in org.gradle.api">UnknownDomainObjectException</a></code> - when there is no such object in this collection.</dd>
</dl>
</li>
</ul>
<a name="getByName-java.lang.String-groovy.lang.Closure-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getByName</h4>
<pre class="methodSignature"><a href="NamedDomainObjectCollection.html" title="type parameter in NamedDomainObjectCollection">T</a>&nbsp;getByName&#8203;(java.lang.String&nbsp;name,
            <a href="https://docs.groovy-lang.org/docs/groovy-3.0.17/html/gapi/groovy/lang/Closure.html?is-external=true" title="class or interface in groovy.lang" class="externalLink">Closure</a>&nbsp;configureClosure)
     throws <a href="UnknownDomainObjectException.html" title="class in org.gradle.api">UnknownDomainObjectException</a></pre>
<div class="block">Locates an object by name, failing if there is no such object. The given configure closure is executed against
 the object before it is returned from this method. The object is passed to the closure as its delegate.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>name</code> - The object name</dd>
<dd><code>configureClosure</code> - The closure to use to configure the object.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>The object with the given name, after the configure closure has been applied to it. Never returns null.</dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code><a href="UnknownDomainObjectException.html" title="class in org.gradle.api">UnknownDomainObjectException</a></code> - when there is no such object in this collection.</dd>
</dl>
</li>
</ul>
<a name="getByName-java.lang.String-org.gradle.api.Action-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getByName</h4>
<pre class="methodSignature"><a href="NamedDomainObjectCollection.html" title="type parameter in NamedDomainObjectCollection">T</a>&nbsp;getByName&#8203;(java.lang.String&nbsp;name,
            <a href="Action.html" title="interface in org.gradle.api">Action</a>&lt;? super <a href="NamedDomainObjectCollection.html" title="type parameter in NamedDomainObjectCollection">T</a>&gt;&nbsp;configureAction)
     throws <a href="UnknownDomainObjectException.html" title="class in org.gradle.api">UnknownDomainObjectException</a></pre>
<div class="block">Locates an object by name, failing if there is no such object. The given configure action is executed against
 the object before it is returned from this method.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>name</code> - The object name</dd>
<dd><code>configureAction</code> - The action to use to configure the object.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>The object with the given name, after the configure action has been applied to it. Never returns null.</dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code><a href="UnknownDomainObjectException.html" title="class in org.gradle.api">UnknownDomainObjectException</a></code> - when there is no such object in this collection.</dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>3.1</dd>
</dl>
</li>
</ul>
<a name="getAt-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getAt</h4>
<pre class="methodSignature"><a href="NamedDomainObjectCollection.html" title="type parameter in NamedDomainObjectCollection">T</a>&nbsp;getAt&#8203;(java.lang.String&nbsp;name)
 throws <a href="UnknownDomainObjectException.html" title="class in org.gradle.api">UnknownDomainObjectException</a></pre>
<div class="block">Locates an object by name, failing if there is no such object. This method is identical to <a href="#getByName-java.lang.String-"><code>getByName(String)</code></a>. You can call this method in your build script by using the groovy <code>[]</code> operator.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>name</code> - The object name</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>The object with the given name. Never returns null.</dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code><a href="UnknownDomainObjectException.html" title="class in org.gradle.api">UnknownDomainObjectException</a></code> - when there is no such object in this collection.</dd>
</dl>
</li>
</ul>
<a name="addRule-org.gradle.api.Rule-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>addRule</h4>
<pre class="methodSignature"><a href="Rule.html" title="interface in org.gradle.api">Rule</a>&nbsp;addRule&#8203;(<a href="Rule.html" title="interface in org.gradle.api">Rule</a>&nbsp;rule)</pre>
<div class="block">Adds a rule to this collection. The given rule is invoked when an unknown object is requested by name.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>rule</code> - The rule to add.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>The added rule.</dd>
</dl>
</li>
</ul>
<a name="addRule-java.lang.String-groovy.lang.Closure-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>addRule</h4>
<pre class="methodSignature"><a href="Rule.html" title="interface in org.gradle.api">Rule</a>&nbsp;addRule&#8203;(java.lang.String&nbsp;description,
             <a href="https://docs.groovy-lang.org/docs/groovy-3.0.17/html/gapi/groovy/lang/Closure.html?is-external=true" title="class or interface in groovy.lang" class="externalLink">Closure</a>&nbsp;ruleAction)</pre>
<div class="block">Adds a rule to this collection. The given closure is executed when an unknown object is requested by name. The
 requested name is passed to the closure as a parameter.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>description</code> - The description of the rule.</dd>
<dd><code>ruleAction</code> - The closure to execute to apply the rule.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>The added rule.</dd>
</dl>
</li>
</ul>
<a name="addRule-java.lang.String-org.gradle.api.Action-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>addRule</h4>
<pre class="methodSignature"><a href="Rule.html" title="interface in org.gradle.api">Rule</a>&nbsp;addRule&#8203;(java.lang.String&nbsp;description,
             <a href="Action.html" title="interface in org.gradle.api">Action</a>&lt;java.lang.String&gt;&nbsp;ruleAction)</pre>
<div class="block">Adds a rule to this collection. The given action is executed when an unknown object is requested by name. The
 requested name is passed to the action.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>description</code> - The description of the rule.</dd>
<dd><code>ruleAction</code> - The action to execute to apply the rule.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>The added rule.</dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>3.3</dd>
</dl>
</li>
</ul>
<a name="getRules--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getRules</h4>
<pre class="methodSignature">java.util.List&lt;<a href="Rule.html" title="interface in org.gradle.api">Rule</a>&gt;&nbsp;getRules()</pre>
<div class="block">Returns the rules used by this collection.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>The rules, in the order they will be applied.</dd>
</dl>
</li>
</ul>
<a name="withType-java.lang.Class-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>withType</h4>
<pre class="methodSignature">&lt;S extends <a href="NamedDomainObjectCollection.html" title="type parameter in NamedDomainObjectCollection">T</a>&gt;&nbsp;<a href="NamedDomainObjectCollection.html" title="interface in org.gradle.api">NamedDomainObjectCollection</a>&lt;S&gt;&nbsp;withType&#8203;(java.lang.Class&lt;S&gt;&nbsp;type)</pre>
<div class="block">Returns a collection containing the objects in this collection of the given type.  The returned collection is
 live, so that when matching objects are later added to this collection, they are also visible in the filtered
 collection.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="DomainObjectCollection.html#withType-java.lang.Class-">withType</a></code>&nbsp;in interface&nbsp;<code><a href="DomainObjectCollection.html" title="interface in org.gradle.api">DomainObjectCollection</a>&lt;<a href="NamedDomainObjectCollection.html" title="type parameter in NamedDomainObjectCollection">T</a>&gt;</code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>type</code> - The type of objects to find.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>The matching objects. Returns an empty collection if there are no such objects in this collection.</dd>
</dl>
</li>
</ul>
<a name="matching-org.gradle.api.specs.Spec-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>matching</h4>
<pre class="methodSignature"><a href="NamedDomainObjectCollection.html" title="interface in org.gradle.api">NamedDomainObjectCollection</a>&lt;<a href="NamedDomainObjectCollection.html" title="type parameter in NamedDomainObjectCollection">T</a>&gt;&nbsp;matching&#8203;(<a href="specs/Spec.html" title="interface in org.gradle.api.specs">Spec</a>&lt;? super <a href="NamedDomainObjectCollection.html" title="type parameter in NamedDomainObjectCollection">T</a>&gt;&nbsp;spec)</pre>
<div class="block">Returns a collection which contains the objects in this collection which meet the given specification. The
 returned collection is live, so that when matching objects are added to this collection, they are also visible in
 the filtered collection.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="DomainObjectCollection.html#matching-org.gradle.api.specs.Spec-">matching</a></code>&nbsp;in interface&nbsp;<code><a href="DomainObjectCollection.html" title="interface in org.gradle.api">DomainObjectCollection</a>&lt;<a href="NamedDomainObjectCollection.html" title="type parameter in NamedDomainObjectCollection">T</a>&gt;</code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>spec</code> - The specification to use.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>The collection of matching objects. Returns an empty collection if there are no such objects in this
         collection.</dd>
</dl>
</li>
</ul>
<a name="matching-groovy.lang.Closure-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>matching</h4>
<pre class="methodSignature"><a href="NamedDomainObjectCollection.html" title="interface in org.gradle.api">NamedDomainObjectCollection</a>&lt;<a href="NamedDomainObjectCollection.html" title="type parameter in NamedDomainObjectCollection">T</a>&gt;&nbsp;matching&#8203;(<a href="https://docs.groovy-lang.org/docs/groovy-3.0.17/html/gapi/groovy/lang/Closure.html?is-external=true" title="class or interface in groovy.lang" class="externalLink">Closure</a>&nbsp;spec)</pre>
<div class="block">Returns a collection which contains the objects in this collection which meet the given closure specification. The
 returned collection is live, so that when matching objects are added to this collection, they are also visible in
 the filtered collection.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="DomainObjectCollection.html#matching-groovy.lang.Closure-">matching</a></code>&nbsp;in interface&nbsp;<code><a href="DomainObjectCollection.html" title="interface in org.gradle.api">DomainObjectCollection</a>&lt;<a href="NamedDomainObjectCollection.html" title="type parameter in NamedDomainObjectCollection">T</a>&gt;</code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>spec</code> - The specification to use. The closure gets a collection element as an argument.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>The collection of matching objects. Returns an empty collection if there are no such objects in this
         collection.</dd>
</dl>
</li>
</ul>
<a name="named-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>named</h4>
<pre class="methodSignature"><a href="NamedDomainObjectProvider.html" title="interface in org.gradle.api">NamedDomainObjectProvider</a>&lt;<a href="NamedDomainObjectCollection.html" title="type parameter in NamedDomainObjectCollection">T</a>&gt;&nbsp;named&#8203;(java.lang.String&nbsp;name)
                            throws <a href="UnknownDomainObjectException.html" title="class in org.gradle.api">UnknownDomainObjectException</a></pre>
<div class="block">Locates a object by name, without triggering its creation or configuration, failing if there is no such object.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>name</code> - The object's name</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>A <a href="provider/Provider.html" title="interface in org.gradle.api.provider"><code>Provider</code></a> that will return the object when queried. The object may be created and configured at this point, if not already.</dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code><a href="UnknownDomainObjectException.html" title="class in org.gradle.api">UnknownDomainObjectException</a></code> - If a object with the given name is not defined.</dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>4.10</dd>
</dl>
</li>
</ul>
<a name="named-java.lang.String-org.gradle.api.Action-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>named</h4>
<pre class="methodSignature"><a href="NamedDomainObjectProvider.html" title="interface in org.gradle.api">NamedDomainObjectProvider</a>&lt;<a href="NamedDomainObjectCollection.html" title="type parameter in NamedDomainObjectCollection">T</a>&gt;&nbsp;named&#8203;(java.lang.String&nbsp;name,
                                   <a href="Action.html" title="interface in org.gradle.api">Action</a>&lt;? super <a href="NamedDomainObjectCollection.html" title="type parameter in NamedDomainObjectCollection">T</a>&gt;&nbsp;configurationAction)
                            throws <a href="UnknownDomainObjectException.html" title="class in org.gradle.api">UnknownDomainObjectException</a></pre>
<div class="block">Locates a object by name, without triggering its creation or configuration, failing if there is no such object.
 The given configure action is executed against the object before it is returned from the provider.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>name</code> - The object's name</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>A <a href="provider/Provider.html" title="interface in org.gradle.api.provider"><code>Provider</code></a> that will return the object when queried. The object may be created and configured at this point, if not already.</dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code><a href="UnknownDomainObjectException.html" title="class in org.gradle.api">UnknownDomainObjectException</a></code> - If an object with the given name is not defined.</dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>5.0</dd>
</dl>
</li>
</ul>
<a name="named-java.lang.String-java.lang.Class-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>named</h4>
<pre class="methodSignature">&lt;S extends <a href="NamedDomainObjectCollection.html" title="type parameter in NamedDomainObjectCollection">T</a>&gt;&nbsp;<a href="NamedDomainObjectProvider.html" title="interface in org.gradle.api">NamedDomainObjectProvider</a>&lt;S&gt;&nbsp;named&#8203;(java.lang.String&nbsp;name,
                                                 java.lang.Class&lt;S&gt;&nbsp;type)
                                          throws <a href="UnknownDomainObjectException.html" title="class in org.gradle.api">UnknownDomainObjectException</a></pre>
<div class="block">Locates a object by name and type, without triggering its creation or configuration, failing if there is no such object.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>name</code> - The object's name</dd>
<dd><code>type</code> - The object's type</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>A <a href="provider/Provider.html" title="interface in org.gradle.api.provider"><code>Provider</code></a> that will return the object when queried. The object may be created and configured at this point, if not already.</dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code><a href="UnknownDomainObjectException.html" title="class in org.gradle.api">UnknownDomainObjectException</a></code> - If an object with the given name is not defined.</dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>5.0</dd>
</dl>
</li>
</ul>
<a name="named-java.lang.String-java.lang.Class-org.gradle.api.Action-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>named</h4>
<pre class="methodSignature">&lt;S extends <a href="NamedDomainObjectCollection.html" title="type parameter in NamedDomainObjectCollection">T</a>&gt;&nbsp;<a href="NamedDomainObjectProvider.html" title="interface in org.gradle.api">NamedDomainObjectProvider</a>&lt;S&gt;&nbsp;named&#8203;(java.lang.String&nbsp;name,
                                                 java.lang.Class&lt;S&gt;&nbsp;type,
                                                 <a href="Action.html" title="interface in org.gradle.api">Action</a>&lt;? super S&gt;&nbsp;configurationAction)
                                          throws <a href="UnknownDomainObjectException.html" title="class in org.gradle.api">UnknownDomainObjectException</a></pre>
<div class="block">Locates a object by name and type, without triggering its creation or configuration, failing if there is no such object.
 The given configure action is executed against the object before it is returned from the provider.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>name</code> - The object's name</dd>
<dd><code>type</code> - The object's type</dd>
<dd><code>configurationAction</code> - The action to use to configure the object.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>A <a href="provider/Provider.html" title="interface in org.gradle.api.provider"><code>Provider</code></a> that will return the object when queried. The object may be created and configured at this point, if not already.</dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code><a href="UnknownDomainObjectException.html" title="class in org.gradle.api">UnknownDomainObjectException</a></code> - If an object with the given name is not defined.</dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>5.0</dd>
</dl>
</li>
</ul>
<a name="getCollectionSchema--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>getCollectionSchema</h4>
<pre class="methodSignature"><a href="tasks/Internal.html" title="annotation in org.gradle.api.tasks">@Internal</a>
<a href="NamedDomainObjectCollectionSchema.html" title="interface in org.gradle.api">NamedDomainObjectCollectionSchema</a>&nbsp;getCollectionSchema()</pre>
<div class="block">Provides access to the schema of all created or registered named domain objects in this collection.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>4.10</dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../index-all.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../allclasses.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
