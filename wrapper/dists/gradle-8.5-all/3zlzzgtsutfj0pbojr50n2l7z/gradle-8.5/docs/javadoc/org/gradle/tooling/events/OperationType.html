<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc -->
<title>OperationType (Gradle API 8.5)</title>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<link rel="stylesheet" type="text/css" href="../../../../javadoc.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../jquery/jquery-ui.min.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../jquery-ui.overrides.css" title="Style">
<script type="text/javascript" src="../../../../script.js"></script>
<script type="text/javascript" src="../../../../jquery/jszip/dist/jszip.min.js"></script>
<script type="text/javascript" src="../../../../jquery/jszip-utils/dist/jszip-utils.min.js"></script>
<!--[if IE]>
<script type="text/javascript" src="../../../../jquery/jszip-utils/dist/jszip-utils-ie.min.js"></script>
<![endif]-->
<script type="text/javascript" src="../../../../jquery/jquery-3.6.1.min.js"></script>
<script type="text/javascript" src="../../../../jquery/jquery-ui.min.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="OperationType (Gradle API 8.5)";
        }
    }
    catch(err) {
    }
//-->
var data = {"i0":9,"i1":9};
var tabs = {65535:["t0","All Methods"],1:["t1","Static Methods"],8:["t4","Concrete Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
var pathtoroot = "../../../../";
var useModuleDirectories = false;
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<div class="fixedNav">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../allclasses.html">All&nbsp;Classes</a></li>
</ul>
<ul class="navListSearch">
<li><label for="search">SEARCH:</label>
<input type="text" id="search" value="search" disabled="disabled">
<input type="reset" id="reset" value="reset" disabled="disabled">
</li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li><a href="#enum.constant.summary">Enum Constants</a>&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li><a href="#enum.constant.detail">Enum Constants</a>&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
</div>
<div class="navPadding">&nbsp;</div>
<script type="text/javascript"><!--
$('.navPadding').css('padding-top', $('.fixedNav').css("height"));
//-->
</script>
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle"><span class="packageLabelInType">Package</span>&nbsp;<a href="package-summary.html">org.gradle.tooling.events</a></div>
<h2 title="Enum OperationType" class="title">Enum OperationType</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li>java.lang.Enum&lt;<a href="OperationType.html" title="enum in org.gradle.tooling.events">OperationType</a>&gt;</li>
<li>
<ul class="inheritance">
<li>org.gradle.tooling.events.OperationType</li>
</ul>
</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt>All Implemented Interfaces:</dt>
<dd><code>java.io.Serializable</code>, <code>java.lang.Comparable&lt;<a href="OperationType.html" title="enum in org.gradle.tooling.events">OperationType</a>&gt;</code></dd>
</dl>
<hr>
<pre>public enum <span class="typeNameLabel">OperationType</span>
extends java.lang.Enum&lt;<a href="OperationType.html" title="enum in org.gradle.tooling.events">OperationType</a>&gt;</pre>
<div class="block">Enumerates the different types of operations for which progress events can be received.</div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../LongRunningOperation.html#addProgressListener-org.gradle.tooling.events.ProgressListener-java.util.Set-"><code>LongRunningOperation.addProgressListener(ProgressListener, java.util.Set)</code></a></dd>
</dl>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- =========== ENUM CONSTANT SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="enum.constant.summary">
<!--   -->
</a>
<h3>Enum Constant Summary</h3>
<table class="memberSummary" summary="Enum Constant Summary table, listing enum constants, and an explanation">
<caption><span>Enum Constants</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Enum Constant</th>
<th class="colLast" scope="col">Description</th>
</tr>
<tr class="altColor">
<th class="colFirst" scope="row"><code><span class="memberNameLink"><a href="#BUILD_PHASE">BUILD_PHASE</a></span></code></th>
<td class="colLast">
<div class="block">Flag for build phase events.</div>
</td>
</tr>
<tr class="rowColor">
<th class="colFirst" scope="row"><code><span class="memberNameLink"><a href="#FILE_DOWNLOAD">FILE_DOWNLOAD</a></span></code></th>
<td class="colLast">
<div class="block">Flag for file download progress events.</div>
</td>
</tr>
<tr class="altColor">
<th class="colFirst" scope="row"><code><span class="memberNameLink"><a href="#GENERIC">GENERIC</a></span></code></th>
<td class="colLast">
<div class="block">Flag for operations with no specific type.</div>
</td>
</tr>
<tr class="rowColor">
<th class="colFirst" scope="row"><code><span class="memberNameLink"><a href="#PROBLEMS">PROBLEMS</a></span></code></th>
<td class="colLast">
<div class="block">Flag for problem events.</div>
</td>
</tr>
<tr class="altColor">
<th class="colFirst" scope="row"><code><span class="memberNameLink"><a href="#PROJECT_CONFIGURATION">PROJECT_CONFIGURATION</a></span></code></th>
<td class="colLast">
<div class="block">Flag for project configuration operation progress events.</div>
</td>
</tr>
<tr class="rowColor">
<th class="colFirst" scope="row"><code><span class="memberNameLink"><a href="#TASK">TASK</a></span></code></th>
<td class="colLast">
<div class="block">Flag for task operation progress events.</div>
</td>
</tr>
<tr class="altColor">
<th class="colFirst" scope="row"><code><span class="memberNameLink"><a href="#TEST">TEST</a></span></code></th>
<td class="colLast">
<div class="block">Flag for test operation progress events.</div>
</td>
</tr>
<tr class="rowColor">
<th class="colFirst" scope="row"><code><span class="memberNameLink"><a href="#TEST_OUTPUT">TEST_OUTPUT</a></span></code></th>
<td class="colLast">
<div class="block">Flag for test output operation progress events.</div>
</td>
</tr>
<tr class="altColor">
<th class="colFirst" scope="row"><code><span class="memberNameLink"><a href="#TRANSFORM">TRANSFORM</a></span></code></th>
<td class="colLast">
<div class="block">Flag for transform operation progress events.</div>
</td>
</tr>
<tr class="rowColor">
<th class="colFirst" scope="row"><code><span class="memberNameLink"><a href="#WORK_ITEM">WORK_ITEM</a></span></code></th>
<td class="colLast">
<div class="block">Flag for work item operation progress events.</div>
</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t1" class="tableTab"><span><a href="javascript:show(1);">Static Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colSecond" scope="col">Method</th>
<th class="colLast" scope="col">Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>static <a href="OperationType.html" title="enum in org.gradle.tooling.events">OperationType</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#valueOf-java.lang.String-">valueOf</a></span>&#8203;(java.lang.String&nbsp;name)</code></th>
<td class="colLast">
<div class="block">Returns the enum constant of this type with the specified name.</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>static <a href="OperationType.html" title="enum in org.gradle.tooling.events">OperationType</a>[]</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#values--">values</a></span>()</code></th>
<td class="colLast">
<div class="block">Returns an array containing the constants of this enum type, in
the order they are declared.</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Enum">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.Enum</h3>
<code>clone, compareTo, equals, finalize, getDeclaringClass, hashCode, name, ordinal, toString, valueOf</code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.Object</h3>
<code>getClass, notify, notifyAll, wait, wait, wait</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ ENUM CONSTANT DETAIL =========== -->
<ul class="blockList">
<li class="blockList"><a name="enum.constant.detail">
<!--   -->
</a>
<h3>Enum Constant Detail</h3>
<a name="TEST">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>TEST</h4>
<pre>public static final&nbsp;<a href="OperationType.html" title="enum in org.gradle.tooling.events">OperationType</a> TEST</pre>
<div class="block">Flag for test operation progress events.

 <p>
 The following events are currently issued for this operation type.
 <ul>
     <li><a href="test/TestStartEvent.html" title="interface in org.gradle.tooling.events.test"><code>TestStartEvent</code></a></li>
     <li><a href="test/TestFinishEvent.html" title="interface in org.gradle.tooling.events.test"><code>TestFinishEvent</code></a></li>
 </ul></div>
</li>
</ul>
<a name="TASK">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>TASK</h4>
<pre>public static final&nbsp;<a href="OperationType.html" title="enum in org.gradle.tooling.events">OperationType</a> TASK</pre>
<div class="block">Flag for task operation progress events.

 <p>
 The following events are currently issued for this operation type.
 <ul>
     <li><a href="task/TaskStartEvent.html" title="interface in org.gradle.tooling.events.task"><code>TaskStartEvent</code></a></li>
     <li><a href="task/TaskFinishEvent.html" title="interface in org.gradle.tooling.events.task"><code>TaskFinishEvent</code></a></li>
 </ul></div>
</li>
</ul>
<a name="GENERIC">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>GENERIC</h4>
<pre>public static final&nbsp;<a href="OperationType.html" title="enum in org.gradle.tooling.events">OperationType</a> GENERIC</pre>
<div class="block">Flag for operations with no specific type.

 <p>
 The following events are currently issued for this operation type.
 <ul>
     <li><a href="StartEvent.html" title="interface in org.gradle.tooling.events"><code>StartEvent</code></a></li>
     <li><a href="FinishEvent.html" title="interface in org.gradle.tooling.events"><code>FinishEvent</code></a></li>
     <li><a href="StatusEvent.html" title="interface in org.gradle.tooling.events"><code>StatusEvent</code></a></li>
 </ul></div>
</li>
</ul>
<a name="WORK_ITEM">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>WORK_ITEM</h4>
<pre>public static final&nbsp;<a href="OperationType.html" title="enum in org.gradle.tooling.events">OperationType</a> WORK_ITEM</pre>
<div class="block">Flag for work item operation progress events.

 <p>
 The following events are currently issued for this operation type.
 <ul>
     <li><a href="work/WorkItemStartEvent.html" title="interface in org.gradle.tooling.events.work"><code>WorkItemStartEvent</code></a></li>
     <li><a href="work/WorkItemFinishEvent.html" title="interface in org.gradle.tooling.events.work"><code>WorkItemFinishEvent</code></a></li>
 </ul></div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>5.1</dd>
</dl>
</li>
</ul>
<a name="PROJECT_CONFIGURATION">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>PROJECT_CONFIGURATION</h4>
<pre>public static final&nbsp;<a href="OperationType.html" title="enum in org.gradle.tooling.events">OperationType</a> PROJECT_CONFIGURATION</pre>
<div class="block">Flag for project configuration operation progress events.

 <p>
 The following events are currently issued for this operation type.
 <ul>
     <li><a href="configuration/ProjectConfigurationStartEvent.html" title="interface in org.gradle.tooling.events.configuration"><code>ProjectConfigurationStartEvent</code></a></li>
     <li><a href="configuration/ProjectConfigurationFinishEvent.html" title="interface in org.gradle.tooling.events.configuration"><code>ProjectConfigurationFinishEvent</code></a></li>
 </ul></div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>5.1</dd>
</dl>
</li>
</ul>
<a name="TRANSFORM">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>TRANSFORM</h4>
<pre>public static final&nbsp;<a href="OperationType.html" title="enum in org.gradle.tooling.events">OperationType</a> TRANSFORM</pre>
<div class="block">Flag for transform operation progress events.

 <p>
 The following events are currently issued for this operation type.
 <ul>
     <li><a href="transform/TransformStartEvent.html" title="interface in org.gradle.tooling.events.transform"><code>TransformStartEvent</code></a></li>
     <li><a href="transform/TransformFinishEvent.html" title="interface in org.gradle.tooling.events.transform"><code>TransformFinishEvent</code></a></li>
 </ul></div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>5.1</dd>
</dl>
</li>
</ul>
<a name="TEST_OUTPUT">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>TEST_OUTPUT</h4>
<pre>public static final&nbsp;<a href="OperationType.html" title="enum in org.gradle.tooling.events">OperationType</a> TEST_OUTPUT</pre>
<div class="block">Flag for test output operation progress events.
 <p>
 Clients must subscribe to <a href="#TEST"><code>TEST</code></a> events too if they want to receive test output events.
 </p>

 The following events are currently issued for this operation type.
 <ul>
     <li><a href="test/TestOutputEvent.html" title="interface in org.gradle.tooling.events.test"><code>TestOutputEvent</code></a></li>
 </ul></div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>6.0</dd>
</dl>
</li>
</ul>
<a name="FILE_DOWNLOAD">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>FILE_DOWNLOAD</h4>
<pre>public static final&nbsp;<a href="OperationType.html" title="enum in org.gradle.tooling.events">OperationType</a> FILE_DOWNLOAD</pre>
<div class="block">Flag for file download progress events. This includes various types of files, for example files downloaded during dependency resolution,
 Gradle distribution downloads, and Java toolchain downloads.

 <p>
 The following events are currently issued for this operation type.
 <ul>
     <li><a href="download/FileDownloadStartEvent.html" title="interface in org.gradle.tooling.events.download"><code>FileDownloadStartEvent</code></a></li>
     <li><a href="StatusEvent.html" title="interface in org.gradle.tooling.events"><code>StatusEvent</code></a></li>
     <li><a href="download/FileDownloadFinishEvent.html" title="interface in org.gradle.tooling.events.download"><code>FileDownloadFinishEvent</code></a></li>
 </ul></div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>7.3</dd>
</dl>
</li>
</ul>
<a name="BUILD_PHASE">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>BUILD_PHASE</h4>
<pre><a href="../../api/Incubating.html" title="annotation in org.gradle.api">@Incubating</a>
public static final&nbsp;<a href="OperationType.html" title="enum in org.gradle.tooling.events">OperationType</a> BUILD_PHASE</pre>
<div class="block">Flag for build phase events.

 <p>
 The following events are currently issued for this operation type.
 <ul>
     <li><a href="lifecycle/BuildPhaseStartEvent.html" title="interface in org.gradle.tooling.events.lifecycle"><code>BuildPhaseStartEvent</code></a></li>
     <li><a href="lifecycle/BuildPhaseFinishEvent.html" title="interface in org.gradle.tooling.events.lifecycle"><code>BuildPhaseFinishEvent</code></a></li>
 </ul></div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>7.6</dd>
</dl>
</li>
</ul>
<a name="PROBLEMS">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>PROBLEMS</h4>
<pre><a href="../../api/Incubating.html" title="annotation in org.gradle.api">@Incubating</a>
public static final&nbsp;<a href="OperationType.html" title="enum in org.gradle.tooling.events">OperationType</a> PROBLEMS</pre>
<div class="block">Flag for problem events.

 <p>
 The following events are currently issued for this operation type.
 <ul>
     <li><a href="problems/ProblemEvent.html" title="interface in org.gradle.tooling.events.problems"><code>ProblemEvent</code></a></li>
 </ul></div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>8.4</dd>
</dl>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="values--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>values</h4>
<pre class="methodSignature">public static&nbsp;<a href="OperationType.html" title="enum in org.gradle.tooling.events">OperationType</a>[]&nbsp;values()</pre>
<div class="block">Returns an array containing the constants of this enum type, in
the order they are declared. This method may be used to iterate
over the constants as follows:
<pre>
for (OperationType c : OperationType.values())
&nbsp;   System.out.println(c);
</pre></div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>an array containing the constants of this enum type, in the order they are declared</dd>
</dl>
</li>
</ul>
<a name="valueOf-java.lang.String-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>valueOf</h4>
<pre class="methodSignature">public static&nbsp;<a href="OperationType.html" title="enum in org.gradle.tooling.events">OperationType</a>&nbsp;valueOf&#8203;(java.lang.String&nbsp;name)</pre>
<div class="block">Returns the enum constant of this type with the specified name.
The string must match <i>exactly</i> an identifier used to declare an
enum constant in this type.  (Extraneous whitespace characters are 
not permitted.)</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>name</code> - the name of the enum constant to be returned.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>the enum constant with the specified name</dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code>java.lang.IllegalArgumentException</code> - if this enum type has no constant with the specified name</dd>
<dd><code>java.lang.NullPointerException</code> - if the argument is null</dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../allclasses.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li><a href="#enum.constant.summary">Enum Constants</a>&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li><a href="#enum.constant.detail">Enum Constants</a>&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
