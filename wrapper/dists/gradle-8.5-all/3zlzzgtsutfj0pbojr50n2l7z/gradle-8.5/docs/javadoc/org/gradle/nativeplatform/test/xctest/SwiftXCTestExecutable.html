<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc -->
<title>SwiftXCTestExecutable (Gradle API 8.5)</title>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<link rel="stylesheet" type="text/css" href="../../../../../javadoc.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../../jquery/jquery-ui.min.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../../jquery-ui.overrides.css" title="Style">
<script type="text/javascript" src="../../../../../script.js"></script>
<script type="text/javascript" src="../../../../../jquery/jszip/dist/jszip.min.js"></script>
<script type="text/javascript" src="../../../../../jquery/jszip-utils/dist/jszip-utils.min.js"></script>
<!--[if IE]>
<script type="text/javascript" src="../../../../../jquery/jszip-utils/dist/jszip-utils-ie.min.js"></script>
<![endif]-->
<script type="text/javascript" src="../../../../../jquery/jquery-3.6.1.min.js"></script>
<script type="text/javascript" src="../../../../../jquery/jquery-ui.min.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="SwiftXCTestExecutable (Gradle API 8.5)";
        }
    }
    catch(err) {
    }
//-->
var pathtoroot = "../../../../../";
var useModuleDirectories = false;
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<div class="fixedNav">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../index-all.html">Index</a></li>
<li><a href="../../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../../allclasses.html">All&nbsp;Classes</a></li>
</ul>
<ul class="navListSearch">
<li><label for="search">SEARCH:</label>
<input type="text" id="search" value="search" disabled="disabled">
<input type="reset" id="reset" value="reset" disabled="disabled">
</li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li><a href="#nested.class.summary">Nested</a>&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li>Method</li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
</div>
<div class="navPadding">&nbsp;</div>
<script type="text/javascript"><!--
$('.navPadding').css('padding-top', $('.fixedNav').css("height"));
//-->
</script>
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle"><span class="packageLabelInType">Package</span>&nbsp;<a href="package-summary.html">org.gradle.nativeplatform.test.xctest</a></div>
<h2 title="Interface SwiftXCTestExecutable" class="title">Interface SwiftXCTestExecutable</h2>
</div>
<div class="contentContainer">
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt>All Superinterfaces:</dt>
<dd><code><a href="../../../language/ComponentWithDependencies.html" title="interface in org.gradle.language">ComponentWithDependencies</a></code>, <code><a href="../../../language/nativeplatform/ComponentWithExecutable.html" title="interface in org.gradle.language.nativeplatform">ComponentWithExecutable</a></code>, <code><a href="../../../language/nativeplatform/ComponentWithInstallation.html" title="interface in org.gradle.language.nativeplatform">ComponentWithInstallation</a></code>, <code><a href="../../../language/nativeplatform/ComponentWithNativeRuntime.html" title="interface in org.gradle.language.nativeplatform">ComponentWithNativeRuntime</a></code>, <code><a href="../../../language/nativeplatform/ComponentWithObjectFiles.html" title="interface in org.gradle.language.nativeplatform">ComponentWithObjectFiles</a></code>, <code><a href="../../../api/Named.html" title="interface in org.gradle.api">Named</a></code>, <code><a href="../../../api/component/SoftwareComponent.html" title="interface in org.gradle.api.component">SoftwareComponent</a></code>, <code><a href="../../../language/swift/SwiftBinary.html" title="interface in org.gradle.language.swift">SwiftBinary</a></code>, <code><a href="SwiftXCTestBinary.html" title="interface in org.gradle.nativeplatform.test.xctest">SwiftXCTestBinary</a></code>, <code><a href="../TestComponent.html" title="interface in org.gradle.nativeplatform.test">TestComponent</a></code></dd>
</dl>
<hr>
<pre>public interface <span class="typeNameLabel">SwiftXCTestExecutable</span>
extends <a href="SwiftXCTestBinary.html" title="interface in org.gradle.nativeplatform.test.xctest">SwiftXCTestBinary</a>, <a href="../../../language/nativeplatform/ComponentWithExecutable.html" title="interface in org.gradle.language.nativeplatform">ComponentWithExecutable</a>, <a href="../../../language/nativeplatform/ComponentWithInstallation.html" title="interface in org.gradle.language.nativeplatform">ComponentWithInstallation</a></pre>
<div class="block">An XCTest executable for tests implemented in Swift.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>4.5</dd>
</dl>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ======== NESTED CLASS SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="nested.class.summary">
<!--   -->
</a>
<h3>Nested Class Summary</h3>
<ul class="blockList">
<li class="blockList"><a name="nested.classes.inherited.from.class.org.gradle.api.Named">
<!--   -->
</a>
<h3>Nested classes/interfaces inherited from interface&nbsp;org.gradle.api.<a href="../../../api/Named.html" title="interface in org.gradle.api">Named</a></h3>
<code><a href="../../../api/Named.Namer.html" title="class in org.gradle.api">Named.Namer</a></code></li>
</ul>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.org.gradle.language.ComponentWithDependencies">
<!--   -->
</a>
<h3>Methods inherited from interface&nbsp;org.gradle.language.<a href="../../../language/ComponentWithDependencies.html" title="interface in org.gradle.language">ComponentWithDependencies</a></h3>
<code><a href="../../../language/ComponentWithDependencies.html#getDependencies--">getDependencies</a></code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.org.gradle.language.nativeplatform.ComponentWithExecutable">
<!--   -->
</a>
<h3>Methods inherited from interface&nbsp;org.gradle.language.nativeplatform.<a href="../../../language/nativeplatform/ComponentWithExecutable.html" title="interface in org.gradle.language.nativeplatform">ComponentWithExecutable</a></h3>
<code><a href="../../../language/nativeplatform/ComponentWithExecutable.html#getExecutableFile--">getExecutableFile</a>, <a href="../../../language/nativeplatform/ComponentWithExecutable.html#getExecutableFileProducer--">getExecutableFileProducer</a>, <a href="../../../language/nativeplatform/ComponentWithExecutable.html#getLinkLibraries--">getLinkLibraries</a>, <a href="../../../language/nativeplatform/ComponentWithExecutable.html#getLinkTask--">getLinkTask</a></code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.org.gradle.language.nativeplatform.ComponentWithInstallation">
<!--   -->
</a>
<h3>Methods inherited from interface&nbsp;org.gradle.language.nativeplatform.<a href="../../../language/nativeplatform/ComponentWithInstallation.html" title="interface in org.gradle.language.nativeplatform">ComponentWithInstallation</a></h3>
<code><a href="../../../language/nativeplatform/ComponentWithInstallation.html#getInstallDirectory--">getInstallDirectory</a>, <a href="../../../language/nativeplatform/ComponentWithInstallation.html#getInstallTask--">getInstallTask</a>, <a href="../../../language/nativeplatform/ComponentWithInstallation.html#getRuntimeLibraries--">getRuntimeLibraries</a></code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.org.gradle.language.nativeplatform.ComponentWithNativeRuntime">
<!--   -->
</a>
<h3>Methods inherited from interface&nbsp;org.gradle.language.nativeplatform.<a href="../../../language/nativeplatform/ComponentWithNativeRuntime.html" title="interface in org.gradle.language.nativeplatform">ComponentWithNativeRuntime</a></h3>
<code><a href="../../../language/nativeplatform/ComponentWithNativeRuntime.html#getBaseName--">getBaseName</a>, <a href="../../../language/nativeplatform/ComponentWithNativeRuntime.html#getTargetMachine--">getTargetMachine</a>, <a href="../../../language/nativeplatform/ComponentWithNativeRuntime.html#getToolChain--">getToolChain</a>, <a href="../../../language/nativeplatform/ComponentWithNativeRuntime.html#isDebuggable--">isDebuggable</a>, <a href="../../../language/nativeplatform/ComponentWithNativeRuntime.html#isOptimized--">isOptimized</a></code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.org.gradle.language.nativeplatform.ComponentWithObjectFiles">
<!--   -->
</a>
<h3>Methods inherited from interface&nbsp;org.gradle.language.nativeplatform.<a href="../../../language/nativeplatform/ComponentWithObjectFiles.html" title="interface in org.gradle.language.nativeplatform">ComponentWithObjectFiles</a></h3>
<code><a href="../../../language/nativeplatform/ComponentWithObjectFiles.html#getObjects--">getObjects</a></code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.org.gradle.api.Named">
<!--   -->
</a>
<h3>Methods inherited from interface&nbsp;org.gradle.api.<a href="../../../api/Named.html" title="interface in org.gradle.api">Named</a></h3>
<code><a href="../../../api/Named.html#getName--">getName</a></code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.org.gradle.language.swift.SwiftBinary">
<!--   -->
</a>
<h3>Methods inherited from interface&nbsp;org.gradle.language.swift.<a href="../../../language/swift/SwiftBinary.html" title="interface in org.gradle.language.swift">SwiftBinary</a></h3>
<code><a href="../../../language/swift/SwiftBinary.html#getCompileModules--">getCompileModules</a>, <a href="../../../language/swift/SwiftBinary.html#getCompileTask--">getCompileTask</a>, <a href="../../../language/swift/SwiftBinary.html#getLinkLibraries--">getLinkLibraries</a>, <a href="../../../language/swift/SwiftBinary.html#getModule--">getModule</a>, <a href="../../../language/swift/SwiftBinary.html#getModuleFile--">getModuleFile</a>, <a href="../../../language/swift/SwiftBinary.html#getRuntimeLibraries--">getRuntimeLibraries</a>, <a href="../../../language/swift/SwiftBinary.html#getSwiftSource--">getSwiftSource</a>, <a href="../../../language/swift/SwiftBinary.html#getTargetPlatform--">getTargetPlatform</a>, <a href="../../../language/swift/SwiftBinary.html#isTestable--">isTestable</a></code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.org.gradle.nativeplatform.test.xctest.SwiftXCTestBinary">
<!--   -->
</a>
<h3>Methods inherited from interface&nbsp;org.gradle.nativeplatform.test.xctest.<a href="SwiftXCTestBinary.html" title="interface in org.gradle.nativeplatform.test.xctest">SwiftXCTestBinary</a></h3>
<code><a href="SwiftXCTestBinary.html#getExecutableFile--">getExecutableFile</a>, <a href="SwiftXCTestBinary.html#getInstallDirectory--">getInstallDirectory</a>, <a href="SwiftXCTestBinary.html#getLinkTask--">getLinkTask</a>, <a href="SwiftXCTestBinary.html#getRunScriptFile--">getRunScriptFile</a>, <a href="SwiftXCTestBinary.html#getRunTask--">getRunTask</a></code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../index-all.html">Index</a></li>
<li><a href="../../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../../allclasses.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li><a href="#nested.class.summary">Nested</a>&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li>Method</li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
