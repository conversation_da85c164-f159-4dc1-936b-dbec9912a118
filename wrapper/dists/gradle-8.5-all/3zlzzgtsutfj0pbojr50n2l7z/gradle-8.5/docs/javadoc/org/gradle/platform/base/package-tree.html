<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc -->
<title>org.gradle.platform.base Class Hierarchy (Gradle API 8.5)</title>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<link rel="stylesheet" type="text/css" href="../../../../javadoc.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../jquery/jquery-ui.min.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../jquery-ui.overrides.css" title="Style">
<script type="text/javascript" src="../../../../script.js"></script>
<script type="text/javascript" src="../../../../jquery/jszip/dist/jszip.min.js"></script>
<script type="text/javascript" src="../../../../jquery/jszip-utils/dist/jszip-utils.min.js"></script>
<!--[if IE]>
<script type="text/javascript" src="../../../../jquery/jszip-utils/dist/jszip-utils-ie.min.js"></script>
<![endif]-->
<script type="text/javascript" src="../../../../jquery/jquery-3.6.1.min.js"></script>
<script type="text/javascript" src="../../../../jquery/jquery-ui.min.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="org.gradle.platform.base Class Hierarchy (Gradle API 8.5)";
        }
    }
    catch(err) {
    }
//-->
var pathtoroot = "../../../../";
var useModuleDirectories = false;
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<div class="fixedNav">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li>Class</li>
<li class="navBarCell1Rev">Tree</li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../allclasses.html">All&nbsp;Classes</a></li>
</ul>
<ul class="navListSearch">
<li><label for="search">SEARCH:</label>
<input type="text" id="search" value="search" disabled="disabled">
<input type="reset" id="reset" value="reset" disabled="disabled">
</li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
</div>
<div class="navPadding">&nbsp;</div>
<script type="text/javascript"><!--
$('.navPadding').css('padding-top', $('.fixedNav').css("height"));
//-->
</script>
<div class="header">
<h1 class="title">Hierarchy For Package org.gradle.platform.base</h1>
<span class="packageHierarchyLabel">Package Hierarchies:</span>
<ul class="horizontal">
<li><a href="../../../../overview-tree.html">All Packages</a></li>
</ul>
</div>
<div class="contentContainer">
<h2 title="Class Hierarchy">Class Hierarchy</h2>
<ul>
<li class="circle">java.lang.Object
<ul>
<li class="circle">java.lang.Throwable (implements java.io.Serializable)
<ul>
<li class="circle">java.lang.Exception
<ul>
<li class="circle">java.lang.RuntimeException
<ul>
<li class="circle">org.gradle.api.<a href="../../api/GradleException.html" title="class in org.gradle.api"><span class="typeNameLink">GradleException</span></a>
<ul>
<li class="circle">org.gradle.platform.base.<a href="InvalidModelException.html" title="class in org.gradle.platform.base"><span class="typeNameLink">InvalidModelException</span></a></li>
<li class="circle">org.gradle.platform.base.<a href="ModelInstantiationException.html" title="class in org.gradle.platform.base"><span class="typeNameLink">ModelInstantiationException</span></a></li>
</ul>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</li>
</ul>
<h2 title="Interface Hierarchy">Interface Hierarchy</h2>
<ul>
<li class="circle">org.gradle.platform.base.<a href="Binary.html" title="interface in org.gradle.platform.base"><span class="typeNameLink">Binary</span></a>
<ul>
<li class="circle">org.gradle.platform.base.<a href="BinarySpec.html" title="interface in org.gradle.platform.base"><span class="typeNameLink">BinarySpec</span></a> (also extends org.gradle.api.<a href="../../api/BuildableComponentSpec.html" title="interface in org.gradle.api">BuildableComponentSpec</a>, org.gradle.api.<a href="../../api/CheckableComponentSpec.html" title="interface in org.gradle.api">CheckableComponentSpec</a>)
<ul>
<li class="circle">org.gradle.platform.base.<a href="ApplicationBinarySpec.html" title="interface in org.gradle.platform.base"><span class="typeNameLink">ApplicationBinarySpec</span></a></li>
<li class="circle">org.gradle.platform.base.<a href="LibraryBinarySpec.html" title="interface in org.gradle.platform.base"><span class="typeNameLink">LibraryBinarySpec</span></a></li>
</ul>
</li>
</ul>
</li>
<li class="circle">org.gradle.api.<a href="../../api/Buildable.html" title="interface in org.gradle.api"><span class="typeNameLink">Buildable</span></a>
<ul>
<li class="circle">org.gradle.api.<a href="../../api/BuildableComponentSpec.html" title="interface in org.gradle.api"><span class="typeNameLink">BuildableComponentSpec</span></a> (also extends org.gradle.platform.base.<a href="ComponentSpec.html" title="interface in org.gradle.platform.base">ComponentSpec</a>)
<ul>
<li class="circle">org.gradle.platform.base.<a href="BinarySpec.html" title="interface in org.gradle.platform.base"><span class="typeNameLink">BinarySpec</span></a> (also extends org.gradle.platform.base.<a href="Binary.html" title="interface in org.gradle.platform.base">Binary</a>, org.gradle.api.<a href="../../api/CheckableComponentSpec.html" title="interface in org.gradle.api">CheckableComponentSpec</a>)
<ul>
<li class="circle">org.gradle.platform.base.<a href="ApplicationBinarySpec.html" title="interface in org.gradle.platform.base"><span class="typeNameLink">ApplicationBinarySpec</span></a></li>
<li class="circle">org.gradle.platform.base.<a href="LibraryBinarySpec.html" title="interface in org.gradle.platform.base"><span class="typeNameLink">LibraryBinarySpec</span></a></li>
</ul>
</li>
</ul>
</li>
</ul>
</li>
<li class="circle">org.gradle.api.component.<a href="../../api/component/Component.html" title="interface in org.gradle.api.component"><span class="typeNameLink">Component</span></a>
<ul>
<li class="circle">org.gradle.platform.base.<a href="Application.html" title="interface in org.gradle.platform.base"><span class="typeNameLink">Application</span></a></li>
<li class="circle">org.gradle.platform.base.<a href="Library.html" title="interface in org.gradle.platform.base"><span class="typeNameLink">Library</span></a></li>
</ul>
</li>
<li class="circle">org.gradle.util.<a href="../../util/Configurable.html" title="interface in org.gradle.util"><span class="typeNameLink">Configurable</span></a>&lt;T&gt;
<ul>
<li class="circle">org.gradle.api.<a href="../../api/NamedDomainObjectContainer.html" title="interface in org.gradle.api"><span class="typeNameLink">NamedDomainObjectContainer</span></a>&lt;T&gt; (also extends org.gradle.api.<a href="../../api/NamedDomainObjectSet.html" title="interface in org.gradle.api">NamedDomainObjectSet</a>&lt;T&gt;)
<ul>
<li class="circle">org.gradle.api.<a href="../../api/PolymorphicDomainObjectContainer.html" title="interface in org.gradle.api"><span class="typeNameLink">PolymorphicDomainObjectContainer</span></a>&lt;T&gt;
<ul>
<li class="circle">org.gradle.api.<a href="../../api/ExtensiblePolymorphicDomainObjectContainer.html" title="interface in org.gradle.api"><span class="typeNameLink">ExtensiblePolymorphicDomainObjectContainer</span></a>&lt;T&gt; (also extends org.gradle.api.internal.rules.NamedDomainObjectFactoryRegistry&lt;T&gt;)
<ul>
<li class="circle">org.gradle.platform.base.<a href="PlatformContainer.html" title="interface in org.gradle.platform.base"><span class="typeNameLink">PlatformContainer</span></a></li>
</ul>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</li>
<li class="circle">org.gradle.platform.base.<a href="DependencySpec.html" title="interface in org.gradle.platform.base"><span class="typeNameLink">DependencySpec</span></a>
<ul>
<li class="circle">org.gradle.platform.base.<a href="LibraryBinaryDependencySpec.html" title="interface in org.gradle.platform.base"><span class="typeNameLink">LibraryBinaryDependencySpec</span></a></li>
<li class="circle">org.gradle.platform.base.<a href="ModuleDependencySpec.html" title="interface in org.gradle.platform.base"><span class="typeNameLink">ModuleDependencySpec</span></a></li>
<li class="circle">org.gradle.platform.base.<a href="ProjectDependencySpec.html" title="interface in org.gradle.platform.base"><span class="typeNameLink">ProjectDependencySpec</span></a></li>
</ul>
</li>
<li class="circle">org.gradle.platform.base.<a href="DependencySpecBuilder.html" title="interface in org.gradle.platform.base"><span class="typeNameLink">DependencySpecBuilder</span></a>
<ul>
<li class="circle">org.gradle.platform.base.<a href="ModuleDependencySpecBuilder.html" title="interface in org.gradle.platform.base"><span class="typeNameLink">ModuleDependencySpecBuilder</span></a></li>
<li class="circle">org.gradle.platform.base.<a href="ProjectDependencySpecBuilder.html" title="interface in org.gradle.platform.base"><span class="typeNameLink">ProjectDependencySpecBuilder</span></a></li>
</ul>
</li>
<li class="circle">org.gradle.platform.base.<a href="DependencySpecContainer.html" title="interface in org.gradle.platform.base"><span class="typeNameLink">DependencySpecContainer</span></a></li>
<li class="circle">java.lang.Iterable&lt;T&gt;
<ul>
<li class="circle">java.util.Collection&lt;E&gt;
<ul>
<li class="circle">org.gradle.api.<a href="../../api/DomainObjectCollection.html" title="interface in org.gradle.api"><span class="typeNameLink">DomainObjectCollection</span></a>&lt;T&gt;
<ul>
<li class="circle">org.gradle.api.<a href="../../api/DomainObjectSet.html" title="interface in org.gradle.api"><span class="typeNameLink">DomainObjectSet</span></a>&lt;T&gt; (also extends java.util.Set&lt;E&gt;)
<ul>
<li class="circle">org.gradle.platform.base.<a href="BinaryTasksCollection.html" title="interface in org.gradle.platform.base"><span class="typeNameLink">BinaryTasksCollection</span></a></li>
<li class="circle">org.gradle.api.<a href="../../api/NamedDomainObjectSet.html" title="interface in org.gradle.api"><span class="typeNameLink">NamedDomainObjectSet</span></a>&lt;T&gt; (also extends org.gradle.api.<a href="../../api/NamedDomainObjectCollection.html" title="interface in org.gradle.api">NamedDomainObjectCollection</a>&lt;T&gt;)
<ul>
<li class="circle">org.gradle.api.<a href="../../api/NamedDomainObjectContainer.html" title="interface in org.gradle.api"><span class="typeNameLink">NamedDomainObjectContainer</span></a>&lt;T&gt; (also extends org.gradle.util.<a href="../../util/Configurable.html" title="interface in org.gradle.util">Configurable</a>&lt;T&gt;)
<ul>
<li class="circle">org.gradle.api.<a href="../../api/PolymorphicDomainObjectContainer.html" title="interface in org.gradle.api"><span class="typeNameLink">PolymorphicDomainObjectContainer</span></a>&lt;T&gt;
<ul>
<li class="circle">org.gradle.api.<a href="../../api/ExtensiblePolymorphicDomainObjectContainer.html" title="interface in org.gradle.api"><span class="typeNameLink">ExtensiblePolymorphicDomainObjectContainer</span></a>&lt;T&gt; (also extends org.gradle.api.internal.rules.NamedDomainObjectFactoryRegistry&lt;T&gt;)
<ul>
<li class="circle">org.gradle.platform.base.<a href="PlatformContainer.html" title="interface in org.gradle.platform.base"><span class="typeNameLink">PlatformContainer</span></a></li>
</ul>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</li>
<li class="circle">org.gradle.api.<a href="../../api/NamedDomainObjectCollection.html" title="interface in org.gradle.api"><span class="typeNameLink">NamedDomainObjectCollection</span></a>&lt;T&gt;
<ul>
<li class="circle">org.gradle.api.<a href="../../api/NamedDomainObjectSet.html" title="interface in org.gradle.api"><span class="typeNameLink">NamedDomainObjectSet</span></a>&lt;T&gt; (also extends org.gradle.api.<a href="../../api/DomainObjectSet.html" title="interface in org.gradle.api">DomainObjectSet</a>&lt;T&gt;)
<ul>
<li class="circle">org.gradle.api.<a href="../../api/NamedDomainObjectContainer.html" title="interface in org.gradle.api"><span class="typeNameLink">NamedDomainObjectContainer</span></a>&lt;T&gt; (also extends org.gradle.util.<a href="../../util/Configurable.html" title="interface in org.gradle.util">Configurable</a>&lt;T&gt;)
<ul>
<li class="circle">org.gradle.api.<a href="../../api/PolymorphicDomainObjectContainer.html" title="interface in org.gradle.api"><span class="typeNameLink">PolymorphicDomainObjectContainer</span></a>&lt;T&gt;
<ul>
<li class="circle">org.gradle.api.<a href="../../api/ExtensiblePolymorphicDomainObjectContainer.html" title="interface in org.gradle.api"><span class="typeNameLink">ExtensiblePolymorphicDomainObjectContainer</span></a>&lt;T&gt; (also extends org.gradle.api.internal.rules.NamedDomainObjectFactoryRegistry&lt;T&gt;)
<ul>
<li class="circle">org.gradle.platform.base.<a href="PlatformContainer.html" title="interface in org.gradle.platform.base"><span class="typeNameLink">PlatformContainer</span></a></li>
</ul>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</li>
<li class="circle">java.util.Set&lt;E&gt;
<ul>
<li class="circle">org.gradle.api.<a href="../../api/DomainObjectSet.html" title="interface in org.gradle.api"><span class="typeNameLink">DomainObjectSet</span></a>&lt;T&gt; (also extends org.gradle.api.<a href="../../api/DomainObjectCollection.html" title="interface in org.gradle.api">DomainObjectCollection</a>&lt;T&gt;)
<ul>
<li class="circle">org.gradle.platform.base.<a href="BinaryTasksCollection.html" title="interface in org.gradle.platform.base"><span class="typeNameLink">BinaryTasksCollection</span></a></li>
<li class="circle">org.gradle.api.<a href="../../api/NamedDomainObjectSet.html" title="interface in org.gradle.api"><span class="typeNameLink">NamedDomainObjectSet</span></a>&lt;T&gt; (also extends org.gradle.api.<a href="../../api/NamedDomainObjectCollection.html" title="interface in org.gradle.api">NamedDomainObjectCollection</a>&lt;T&gt;)
<ul>
<li class="circle">org.gradle.api.<a href="../../api/NamedDomainObjectContainer.html" title="interface in org.gradle.api"><span class="typeNameLink">NamedDomainObjectContainer</span></a>&lt;T&gt; (also extends org.gradle.util.<a href="../../util/Configurable.html" title="interface in org.gradle.util">Configurable</a>&lt;T&gt;)
<ul>
<li class="circle">org.gradle.api.<a href="../../api/PolymorphicDomainObjectContainer.html" title="interface in org.gradle.api"><span class="typeNameLink">PolymorphicDomainObjectContainer</span></a>&lt;T&gt;
<ul>
<li class="circle">org.gradle.api.<a href="../../api/ExtensiblePolymorphicDomainObjectContainer.html" title="interface in org.gradle.api"><span class="typeNameLink">ExtensiblePolymorphicDomainObjectContainer</span></a>&lt;T&gt; (also extends org.gradle.api.internal.rules.NamedDomainObjectFactoryRegistry&lt;T&gt;)
<ul>
<li class="circle">org.gradle.platform.base.<a href="PlatformContainer.html" title="interface in org.gradle.platform.base"><span class="typeNameLink">PlatformContainer</span></a></li>
</ul>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</li>
<li class="circle">org.gradle.model.<a href="../../model/ModelMap.html" title="interface in org.gradle.model"><span class="typeNameLink">ModelMap</span></a>&lt;T&gt; (also extends org.gradle.model.<a href="../../model/ModelElement.html" title="interface in org.gradle.model">ModelElement</a>)
<ul>
<li class="circle">org.gradle.platform.base.<a href="BinaryContainer.html" title="interface in org.gradle.platform.base"><span class="typeNameLink">BinaryContainer</span></a></li>
<li class="circle">org.gradle.platform.base.<a href="ComponentSpecContainer.html" title="interface in org.gradle.platform.base"><span class="typeNameLink">ComponentSpecContainer</span></a></li>
</ul>
</li>
</ul>
</li>
<li class="circle">org.gradle.api.<a href="../../api/Named.html" title="interface in org.gradle.api"><span class="typeNameLink">Named</span></a>
<ul>
<li class="circle">org.gradle.model.<a href="../../model/ModelElement.html" title="interface in org.gradle.model"><span class="typeNameLink">ModelElement</span></a>
<ul>
<li class="circle">org.gradle.platform.base.<a href="ComponentSpec.html" title="interface in org.gradle.platform.base"><span class="typeNameLink">ComponentSpec</span></a>
<ul>
<li class="circle">org.gradle.api.<a href="../../api/BuildableComponentSpec.html" title="interface in org.gradle.api"><span class="typeNameLink">BuildableComponentSpec</span></a> (also extends org.gradle.api.<a href="../../api/Buildable.html" title="interface in org.gradle.api">Buildable</a>)
<ul>
<li class="circle">org.gradle.platform.base.<a href="BinarySpec.html" title="interface in org.gradle.platform.base"><span class="typeNameLink">BinarySpec</span></a> (also extends org.gradle.platform.base.<a href="Binary.html" title="interface in org.gradle.platform.base">Binary</a>, org.gradle.api.<a href="../../api/CheckableComponentSpec.html" title="interface in org.gradle.api">CheckableComponentSpec</a>)
<ul>
<li class="circle">org.gradle.platform.base.<a href="ApplicationBinarySpec.html" title="interface in org.gradle.platform.base"><span class="typeNameLink">ApplicationBinarySpec</span></a></li>
<li class="circle">org.gradle.platform.base.<a href="LibraryBinarySpec.html" title="interface in org.gradle.platform.base"><span class="typeNameLink">LibraryBinarySpec</span></a></li>
</ul>
</li>
</ul>
</li>
<li class="circle">org.gradle.api.<a href="../../api/CheckableComponentSpec.html" title="interface in org.gradle.api"><span class="typeNameLink">CheckableComponentSpec</span></a>
<ul>
<li class="circle">org.gradle.platform.base.<a href="BinarySpec.html" title="interface in org.gradle.platform.base"><span class="typeNameLink">BinarySpec</span></a> (also extends org.gradle.platform.base.<a href="Binary.html" title="interface in org.gradle.platform.base">Binary</a>, org.gradle.api.<a href="../../api/BuildableComponentSpec.html" title="interface in org.gradle.api">BuildableComponentSpec</a>)
<ul>
<li class="circle">org.gradle.platform.base.<a href="ApplicationBinarySpec.html" title="interface in org.gradle.platform.base"><span class="typeNameLink">ApplicationBinarySpec</span></a></li>
<li class="circle">org.gradle.platform.base.<a href="LibraryBinarySpec.html" title="interface in org.gradle.platform.base"><span class="typeNameLink">LibraryBinarySpec</span></a></li>
</ul>
</li>
</ul>
</li>
<li class="circle">org.gradle.platform.base.<a href="GeneralComponentSpec.html" title="interface in org.gradle.platform.base"><span class="typeNameLink">GeneralComponentSpec</span></a> (also extends org.gradle.platform.base.<a href="SourceComponentSpec.html" title="interface in org.gradle.platform.base">SourceComponentSpec</a>, org.gradle.platform.base.<a href="VariantComponentSpec.html" title="interface in org.gradle.platform.base">VariantComponentSpec</a>)
<ul>
<li class="circle">org.gradle.platform.base.<a href="ApplicationSpec.html" title="interface in org.gradle.platform.base"><span class="typeNameLink">ApplicationSpec</span></a></li>
<li class="circle">org.gradle.platform.base.<a href="LibrarySpec.html" title="interface in org.gradle.platform.base"><span class="typeNameLink">LibrarySpec</span></a></li>
</ul>
</li>
<li class="circle">org.gradle.platform.base.<a href="SourceComponentSpec.html" title="interface in org.gradle.platform.base"><span class="typeNameLink">SourceComponentSpec</span></a>
<ul>
<li class="circle">org.gradle.platform.base.<a href="GeneralComponentSpec.html" title="interface in org.gradle.platform.base"><span class="typeNameLink">GeneralComponentSpec</span></a> (also extends org.gradle.platform.base.<a href="ComponentSpec.html" title="interface in org.gradle.platform.base">ComponentSpec</a>, org.gradle.platform.base.<a href="VariantComponentSpec.html" title="interface in org.gradle.platform.base">VariantComponentSpec</a>)
<ul>
<li class="circle">org.gradle.platform.base.<a href="ApplicationSpec.html" title="interface in org.gradle.platform.base"><span class="typeNameLink">ApplicationSpec</span></a></li>
<li class="circle">org.gradle.platform.base.<a href="LibrarySpec.html" title="interface in org.gradle.platform.base"><span class="typeNameLink">LibrarySpec</span></a></li>
</ul>
</li>
</ul>
</li>
<li class="circle">org.gradle.platform.base.<a href="VariantComponentSpec.html" title="interface in org.gradle.platform.base"><span class="typeNameLink">VariantComponentSpec</span></a> (also extends org.gradle.platform.base.<a href="VariantComponent.html" title="interface in org.gradle.platform.base">VariantComponent</a>)
<ul>
<li class="circle">org.gradle.platform.base.<a href="GeneralComponentSpec.html" title="interface in org.gradle.platform.base"><span class="typeNameLink">GeneralComponentSpec</span></a> (also extends org.gradle.platform.base.<a href="ComponentSpec.html" title="interface in org.gradle.platform.base">ComponentSpec</a>, org.gradle.platform.base.<a href="SourceComponentSpec.html" title="interface in org.gradle.platform.base">SourceComponentSpec</a>)
<ul>
<li class="circle">org.gradle.platform.base.<a href="ApplicationSpec.html" title="interface in org.gradle.platform.base"><span class="typeNameLink">ApplicationSpec</span></a></li>
<li class="circle">org.gradle.platform.base.<a href="LibrarySpec.html" title="interface in org.gradle.platform.base"><span class="typeNameLink">LibrarySpec</span></a></li>
</ul>
</li>
<li class="circle">org.gradle.platform.base.<a href="PlatformAwareComponentSpec.html" title="interface in org.gradle.platform.base"><span class="typeNameLink">PlatformAwareComponentSpec</span></a></li>
</ul>
</li>
</ul>
</li>
<li class="circle">org.gradle.model.<a href="../../model/ModelMap.html" title="interface in org.gradle.model"><span class="typeNameLink">ModelMap</span></a>&lt;T&gt; (also extends java.lang.Iterable&lt;T&gt;)
<ul>
<li class="circle">org.gradle.platform.base.<a href="BinaryContainer.html" title="interface in org.gradle.platform.base"><span class="typeNameLink">BinaryContainer</span></a></li>
<li class="circle">org.gradle.platform.base.<a href="ComponentSpecContainer.html" title="interface in org.gradle.platform.base"><span class="typeNameLink">ComponentSpecContainer</span></a></li>
</ul>
</li>
<li class="circle">org.gradle.platform.base.<a href="VariantComponent.html" title="interface in org.gradle.platform.base"><span class="typeNameLink">VariantComponent</span></a>
<ul>
<li class="circle">org.gradle.platform.base.<a href="VariantComponentSpec.html" title="interface in org.gradle.platform.base"><span class="typeNameLink">VariantComponentSpec</span></a> (also extends org.gradle.platform.base.<a href="ComponentSpec.html" title="interface in org.gradle.platform.base">ComponentSpec</a>)
<ul>
<li class="circle">org.gradle.platform.base.<a href="GeneralComponentSpec.html" title="interface in org.gradle.platform.base"><span class="typeNameLink">GeneralComponentSpec</span></a> (also extends org.gradle.platform.base.<a href="ComponentSpec.html" title="interface in org.gradle.platform.base">ComponentSpec</a>, org.gradle.platform.base.<a href="SourceComponentSpec.html" title="interface in org.gradle.platform.base">SourceComponentSpec</a>)
<ul>
<li class="circle">org.gradle.platform.base.<a href="ApplicationSpec.html" title="interface in org.gradle.platform.base"><span class="typeNameLink">ApplicationSpec</span></a></li>
<li class="circle">org.gradle.platform.base.<a href="LibrarySpec.html" title="interface in org.gradle.platform.base"><span class="typeNameLink">LibrarySpec</span></a></li>
</ul>
</li>
<li class="circle">org.gradle.platform.base.<a href="PlatformAwareComponentSpec.html" title="interface in org.gradle.platform.base"><span class="typeNameLink">PlatformAwareComponentSpec</span></a></li>
</ul>
</li>
</ul>
</li>
</ul>
</li>
<li class="circle">org.gradle.platform.base.<a href="Platform.html" title="interface in org.gradle.platform.base"><span class="typeNameLink">Platform</span></a></li>
<li class="circle">org.gradle.platform.base.<a href="ToolChain.html" title="interface in org.gradle.platform.base"><span class="typeNameLink">ToolChain</span></a></li>
</ul>
</li>
<li class="circle">org.gradle.api.internal.rules.NamedDomainObjectFactoryRegistry&lt;T&gt;
<ul>
<li class="circle">org.gradle.api.<a href="../../api/ExtensiblePolymorphicDomainObjectContainer.html" title="interface in org.gradle.api"><span class="typeNameLink">ExtensiblePolymorphicDomainObjectContainer</span></a>&lt;T&gt; (also extends org.gradle.api.<a href="../../api/PolymorphicDomainObjectContainer.html" title="interface in org.gradle.api">PolymorphicDomainObjectContainer</a>&lt;T&gt;)
<ul>
<li class="circle">org.gradle.platform.base.<a href="PlatformContainer.html" title="interface in org.gradle.platform.base"><span class="typeNameLink">PlatformContainer</span></a></li>
</ul>
</li>
</ul>
</li>
<li class="circle">org.gradle.platform.base.<a href="ToolChainRegistry.html" title="interface in org.gradle.platform.base"><span class="typeNameLink">ToolChainRegistry</span></a>&lt;P,&#8203;T&gt;</li>
<li class="circle">org.gradle.platform.base.<a href="TransformationFileType.html" title="interface in org.gradle.platform.base"><span class="typeNameLink">TransformationFileType</span></a></li>
<li class="circle">org.gradle.platform.base.<a href="TypeBuilder.html" title="interface in org.gradle.platform.base"><span class="typeNameLink">TypeBuilder</span></a>&lt;T&gt;</li>
</ul>
<h2 title="Annotation Type Hierarchy">Annotation Type Hierarchy</h2>
<ul>
<li class="circle">org.gradle.platform.base.<a href="BinaryTasks.html" title="annotation in org.gradle.platform.base"><span class="typeNameLink">BinaryTasks</span></a> (implements java.lang.annotation.Annotation)</li>
<li class="circle">org.gradle.platform.base.<a href="ComponentBinaries.html" title="annotation in org.gradle.platform.base"><span class="typeNameLink">ComponentBinaries</span></a> (implements java.lang.annotation.Annotation)</li>
<li class="circle">org.gradle.platform.base.<a href="ComponentType.html" title="annotation in org.gradle.platform.base"><span class="typeNameLink">ComponentType</span></a> (implements java.lang.annotation.Annotation)</li>
<li class="circle">org.gradle.platform.base.<a href="Variant.html" title="annotation in org.gradle.platform.base"><span class="typeNameLink">Variant</span></a> (implements java.lang.annotation.Annotation)</li>
</ul>
</div>
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li>Class</li>
<li class="navBarCell1Rev">Tree</li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../allclasses.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
