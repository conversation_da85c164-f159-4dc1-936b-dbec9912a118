<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc -->
<title>Exec (Gradle API 8.5)</title>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<link rel="stylesheet" type="text/css" href="../../../../javadoc.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../jquery/jquery-ui.min.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../jquery-ui.overrides.css" title="Style">
<script type="text/javascript" src="../../../../script.js"></script>
<script type="text/javascript" src="../../../../jquery/jszip/dist/jszip.min.js"></script>
<script type="text/javascript" src="../../../../jquery/jszip-utils/dist/jszip-utils.min.js"></script>
<!--[if IE]>
<script type="text/javascript" src="../../../../jquery/jszip-utils/dist/jszip-utils-ie.min.js"></script>
<![endif]-->
<script type="text/javascript" src="../../../../jquery/jquery-3.6.1.min.js"></script>
<script type="text/javascript" src="../../../../jquery/jquery-ui.min.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="Exec (Gradle API 8.5)";
        }
    }
    catch(err) {
    }
//-->
var pathtoroot = "../../../../";
var useModuleDirectories = false;
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<div class="fixedNav">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../allclasses.html">All&nbsp;Classes</a></li>
</ul>
<ul class="navListSearch">
<li><label for="search">SEARCH:</label>
<input type="text" id="search" value="search" disabled="disabled">
<input type="reset" id="reset" value="reset" disabled="disabled">
</li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li><a href="#nested.class.summary">Nested</a>&nbsp;|&nbsp;</li>
<li><a href="#field.summary">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li>Method</li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
</div>
<div class="navPadding">&nbsp;</div>
<script type="text/javascript"><!--
$('.navPadding').css('padding-top', $('.fixedNav').css("height"));
//-->
</script>
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle"><span class="packageLabelInType">Package</span>&nbsp;<a href="package-summary.html">org.gradle.api.tasks</a></div>
<h2 title="Class Exec" class="title">Class Exec</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li>org.gradle.api.internal.AbstractTask</li>
<li>
<ul class="inheritance">
<li><a href="../DefaultTask.html" title="class in org.gradle.api">org.gradle.api.DefaultTask</a></li>
<li>
<ul class="inheritance">
<li>org.gradle.api.internal.ConventionTask</li>
<li>
<ul class="inheritance">
<li><a href="AbstractExecTask.html" title="class in org.gradle.api.tasks">org.gradle.api.tasks.AbstractExecTask</a>&lt;<a href="Exec.html" title="class in org.gradle.api.tasks">Exec</a>&gt;</li>
<li>
<ul class="inheritance">
<li>org.gradle.api.tasks.Exec</li>
</ul>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt>All Implemented Interfaces:</dt>
<dd><code>java.lang.Comparable&lt;<a href="../Task.html" title="interface in org.gradle.api">Task</a>&gt;</code>, <code>org.gradle.api.internal.DynamicObjectAware</code>, <code>org.gradle.api.internal.IConventionAware</code>, <code>org.gradle.api.internal.TaskInternal</code>, <code><a href="../plugins/ExtensionAware.html" title="interface in org.gradle.api.plugins">ExtensionAware</a></code>, <code><a href="../Task.html" title="interface in org.gradle.api">Task</a></code>, <code><a href="../../process/BaseExecSpec.html" title="interface in org.gradle.process">BaseExecSpec</a></code>, <code><a href="../../process/ExecSpec.html" title="interface in org.gradle.process">ExecSpec</a></code>, <code><a href="../../process/ProcessForkOptions.html" title="interface in org.gradle.process">ProcessForkOptions</a></code>, <code><a href="../../util/Configurable.html" title="interface in org.gradle.util">Configurable</a>&lt;<a href="../Task.html" title="interface in org.gradle.api">Task</a>&gt;</code></dd>
</dl>
<hr>
<pre><a href="../../work/DisableCachingByDefault.html" title="annotation in org.gradle.work">@DisableCachingByDefault</a>(<a href="../../work/DisableCachingByDefault.html#because--">because</a>="Gradle would require more information to cache this task")
public abstract class <span class="typeNameLabel">Exec</span>
extends <a href="AbstractExecTask.html" title="class in org.gradle.api.tasks">AbstractExecTask</a>&lt;<a href="Exec.html" title="class in org.gradle.api.tasks">Exec</a>&gt;</pre>
<div class="block">Executes a command line process. Example:
 <pre class='autoTested'>
 task stopTomcat(type:Exec) {
   workingDir '../tomcat/bin'

   //on windows:
   commandLine 'cmd', '/c', 'stop.bat'

   //on linux
   commandLine './stop.sh'

   //store the output instead of printing to the console:
   standardOutput = new ByteArrayOutputStream()

   //extension method stopTomcat.output() can be used to obtain the output:
   ext.output = {
     return standardOutput.toString()
   }
 }
 </pre></div>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ======== NESTED CLASS SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="nested.class.summary">
<!--   -->
</a>
<h3>Nested Class Summary</h3>
<ul class="blockList">
<li class="blockList"><a name="nested.classes.inherited.from.class.org.gradle.api.Task">
<!--   -->
</a>
<h3>Nested classes/interfaces inherited from interface&nbsp;org.gradle.api.<a href="../Task.html" title="interface in org.gradle.api">Task</a></h3>
<code><a href="../Task.Namer.html" title="class in org.gradle.api">Task.Namer</a></code></li>
</ul>
</li>
</ul>
<!-- =========== FIELD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="field.summary">
<!--   -->
</a>
<h3>Field Summary</h3>
<ul class="blockList">
<li class="blockList"><a name="fields.inherited.from.class.org.gradle.api.Task">
<!--   -->
</a>
<h3>Fields inherited from interface&nbsp;org.gradle.api.<a href="../Task.html" title="interface in org.gradle.api">Task</a></h3>
<code><a href="../Task.html#TASK_ACTION">TASK_ACTION</a>, <a href="../Task.html#TASK_CONSTRUCTOR_ARGS">TASK_CONSTRUCTOR_ARGS</a>, <a href="../Task.html#TASK_DEPENDS_ON">TASK_DEPENDS_ON</a>, <a href="../Task.html#TASK_DESCRIPTION">TASK_DESCRIPTION</a>, <a href="../Task.html#TASK_GROUP">TASK_GROUP</a>, <a href="../Task.html#TASK_NAME">TASK_NAME</a>, <a href="../Task.html#TASK_OVERWRITE">TASK_OVERWRITE</a>, <a href="../Task.html#TASK_TYPE">TASK_TYPE</a></code></li>
</ul>
</li>
</ul>
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.summary">
<!--   -->
</a>
<h3>Constructor Summary</h3>
<table class="memberSummary" summary="Constructor Summary table, listing constructors, and an explanation">
<caption><span>Constructors</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Constructor</th>
<th class="colLast" scope="col">Description</th>
</tr>
<tr class="altColor">
<th class="colConstructorName" scope="row"><code><span class="memberNameLink"><a href="#Exec--">Exec</a></span>()</code></th>
<td class="colLast">&nbsp;</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.org.gradle.api.tasks.AbstractExecTask">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;org.gradle.api.tasks.<a href="AbstractExecTask.html" title="class in org.gradle.api.tasks">AbstractExecTask</a></h3>
<code><a href="AbstractExecTask.html#args-java.lang.Iterable-">args</a>, <a href="AbstractExecTask.html#args-java.lang.Object...-">args</a>, <a href="AbstractExecTask.html#commandLine-java.lang.Iterable-">commandLine</a>, <a href="AbstractExecTask.html#commandLine-java.lang.Object...-">commandLine</a>, <a href="AbstractExecTask.html#copyTo-org.gradle.process.ProcessForkOptions-">copyTo</a>, <a href="AbstractExecTask.html#environment-java.lang.String-java.lang.Object-">environment</a>, <a href="AbstractExecTask.html#environment-java.util.Map-">environment</a>, <a href="AbstractExecTask.html#exec--">exec</a>, <a href="AbstractExecTask.html#executable-java.lang.Object-">executable</a>, <a href="AbstractExecTask.html#getArgs--">getArgs</a>, <a href="AbstractExecTask.html#getArgumentProviders--">getArgumentProviders</a>, <a href="AbstractExecTask.html#getCommandLine--">getCommandLine</a>, <a href="AbstractExecTask.html#getEnvironment--">getEnvironment</a>, <a href="AbstractExecTask.html#getErrorOutput--">getErrorOutput</a>, <a href="AbstractExecTask.html#getExecActionFactory--">getExecActionFactory</a>, <a href="AbstractExecTask.html#getExecutable--">getExecutable</a>, <a href="AbstractExecTask.html#getExecutionResult--">getExecutionResult</a>, <a href="AbstractExecTask.html#getObjectFactory--">getObjectFactory</a>, <a href="AbstractExecTask.html#getStandardInput--">getStandardInput</a>, <a href="AbstractExecTask.html#getStandardOutput--">getStandardOutput</a>, <a href="AbstractExecTask.html#getWorkingDir--">getWorkingDir</a>, <a href="AbstractExecTask.html#isIgnoreExitValue--">isIgnoreExitValue</a>, <a href="AbstractExecTask.html#setArgs-java.lang.Iterable-">setArgs</a>, <a href="AbstractExecTask.html#setArgs-java.util.List-">setArgs</a>, <a href="AbstractExecTask.html#setCommandLine-java.lang.Iterable-">setCommandLine</a>, <a href="AbstractExecTask.html#setCommandLine-java.lang.Object...-">setCommandLine</a>, <a href="AbstractExecTask.html#setCommandLine-java.util.List-">setCommandLine</a>, <a href="AbstractExecTask.html#setEnvironment-java.util.Map-">setEnvironment</a>, <a href="AbstractExecTask.html#setErrorOutput-java.io.OutputStream-">setErrorOutput</a>, <a href="AbstractExecTask.html#setExecutable-java.lang.Object-">setExecutable</a>, <a href="AbstractExecTask.html#setExecutable-java.lang.String-">setExecutable</a>, <a href="AbstractExecTask.html#setIgnoreExitValue-boolean-">setIgnoreExitValue</a>, <a href="AbstractExecTask.html#setStandardInput-java.io.InputStream-">setStandardInput</a>, <a href="AbstractExecTask.html#setStandardOutput-java.io.OutputStream-">setStandardOutput</a>, <a href="AbstractExecTask.html#setWorkingDir-java.io.File-">setWorkingDir</a>, <a href="AbstractExecTask.html#setWorkingDir-java.lang.Object-">setWorkingDir</a>, <a href="AbstractExecTask.html#workingDir-java.lang.Object-">workingDir</a></code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.org.gradle.api.internal.ConventionTask">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;org.gradle.api.internal.ConventionTask</h3>
<code>conventionMapping, conventionMapping, getConventionMapping</code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.org.gradle.api.DefaultTask">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;org.gradle.api.<a href="../DefaultTask.html" title="class in org.gradle.api">DefaultTask</a></h3>
<code><a href="../DefaultTask.html#compareTo-org.gradle.api.Task-">compareTo</a>, <a href="../DefaultTask.html#configure-groovy.lang.Closure-">configure</a>, <a href="../DefaultTask.html#dependsOn-java.lang.Object...-">dependsOn</a>, <a href="../DefaultTask.html#doFirst-groovy.lang.Closure-">doFirst</a>, <a href="../DefaultTask.html#doFirst-java.lang.String-org.gradle.api.Action-">doFirst</a>, <a href="../DefaultTask.html#doFirst-org.gradle.api.Action-">doFirst</a>, <a href="../DefaultTask.html#doLast-groovy.lang.Closure-">doLast</a>, <a href="../DefaultTask.html#doLast-java.lang.String-org.gradle.api.Action-">doLast</a>, <a href="../DefaultTask.html#doLast-org.gradle.api.Action-">doLast</a>, <a href="../DefaultTask.html#finalizedBy-java.lang.Object...-">finalizedBy</a>, <a href="../DefaultTask.html#getActions--">getActions</a>, <a href="../DefaultTask.html#getAnt--">getAnt</a>, <a href="../DefaultTask.html#getDependsOn--">getDependsOn</a>, <a href="../DefaultTask.html#getDescription--">getDescription</a>, <a href="../DefaultTask.html#getDestroyables--">getDestroyables</a>, <a href="../DefaultTask.html#getDidWork--">getDidWork</a>, <a href="../DefaultTask.html#getEnabled--">getEnabled</a>, <a href="../DefaultTask.html#getExtensions--">getExtensions</a>, <a href="../DefaultTask.html#getFinalizedBy--">getFinalizedBy</a>, <a href="../DefaultTask.html#getGroup--">getGroup</a>, <a href="../DefaultTask.html#getInputs--">getInputs</a>, <a href="../DefaultTask.html#getLocalState--">getLocalState</a>, <a href="../DefaultTask.html#getLogger--">getLogger</a>, <a href="../DefaultTask.html#getLogging--">getLogging</a>, <a href="../DefaultTask.html#getMustRunAfter--">getMustRunAfter</a>, <a href="../DefaultTask.html#getName--">getName</a>, <a href="../DefaultTask.html#getOutputs--">getOutputs</a>, <a href="../DefaultTask.html#getPath--">getPath</a>, <a href="../DefaultTask.html#getProject--">getProject</a>, <a href="../DefaultTask.html#getShouldRunAfter--">getShouldRunAfter</a>, <a href="../DefaultTask.html#getState--">getState</a>, <a href="../DefaultTask.html#getTaskDependencies--">getTaskDependencies</a>, <a href="../DefaultTask.html#getTemporaryDir--">getTemporaryDir</a>, <a href="../DefaultTask.html#getTimeout--">getTimeout</a>, <a href="../DefaultTask.html#hasProperty-java.lang.String-">hasProperty</a>, <a href="../DefaultTask.html#mustRunAfter-java.lang.Object...-">mustRunAfter</a>, <a href="../DefaultTask.html#onlyIf-groovy.lang.Closure-">onlyIf</a>, <a href="../DefaultTask.html#onlyIf-java.lang.String-org.gradle.api.specs.Spec-">onlyIf</a>, <a href="../DefaultTask.html#onlyIf-org.gradle.api.specs.Spec-">onlyIf</a>, <a href="../DefaultTask.html#property-java.lang.String-">property</a>, <a href="../DefaultTask.html#setActions-java.util.List-">setActions</a>, <a href="../DefaultTask.html#setDependsOn-java.lang.Iterable-">setDependsOn</a>, <a href="../DefaultTask.html#setDescription-java.lang.String-">setDescription</a>, <a href="../DefaultTask.html#setDidWork-boolean-">setDidWork</a>, <a href="../DefaultTask.html#setEnabled-boolean-">setEnabled</a>, <a href="../DefaultTask.html#setFinalizedBy-java.lang.Iterable-">setFinalizedBy</a>, <a href="../DefaultTask.html#setGroup-java.lang.String-">setGroup</a>, <a href="../DefaultTask.html#setMustRunAfter-java.lang.Iterable-">setMustRunAfter</a>, <a href="../DefaultTask.html#setOnlyIf-groovy.lang.Closure-">setOnlyIf</a>, <a href="../DefaultTask.html#setOnlyIf-java.lang.String-org.gradle.api.specs.Spec-">setOnlyIf</a>, <a href="../DefaultTask.html#setOnlyIf-org.gradle.api.specs.Spec-">setOnlyIf</a>, <a href="../DefaultTask.html#setProperty-java.lang.String-java.lang.Object-">setProperty</a>, <a href="../DefaultTask.html#setShouldRunAfter-java.lang.Iterable-">setShouldRunAfter</a>, <a href="../DefaultTask.html#shouldRunAfter-java.lang.Object...-">shouldRunAfter</a>, <a href="../DefaultTask.html#usesService-org.gradle.api.provider.Provider-">usesService</a></code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.org.gradle.api.internal.AbstractTask">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;org.gradle.api.internal.AbstractTask</h3>
<code>acceptServiceReferences, appendParallelSafeAction, doNotTrackState, getAsDynamicObject, getConvention, getIdentityPath, getImpliesSubProjects, getLifecycleDependencies, getOnlyIf, getReasonNotToTrackState, getReasonTaskIsIncompatibleWithConfigurationCache, getRequiredServices, getServices, getSharedResources, getStandardOutputCapture, getTaskActions, getTaskIdentity, getTemporaryDirFactory, hasTaskActions, injectIntoNewInstance, isCompatibleWithConfigurationCache, isEnabled, isHasCustomActions, notCompatibleWithConfigurationCache, prependParallelSafeAction, setImpliesSubProjects</code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.Object</h3>
<code>clone, equals, finalize, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.org.gradle.api.Task">
<!--   -->
</a>
<h3>Methods inherited from interface&nbsp;org.gradle.api.<a href="../Task.html" title="interface in org.gradle.api">Task</a></h3>
<code><a href="../Task.html#doNotTrackState-java.lang.String-">doNotTrackState</a>, <a href="../Task.html#getConvention--">getConvention</a>, <a href="../Task.html#notCompatibleWithConfigurationCache-java.lang.String-">notCompatibleWithConfigurationCache</a></code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.detail">
<!--   -->
</a>
<h3>Constructor Detail</h3>
<a name="Exec--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>Exec</h4>
<pre>public&nbsp;Exec()</pre>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../allclasses.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li><a href="#nested.class.summary">Nested</a>&nbsp;|&nbsp;</li>
<li><a href="#field.summary">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li>Method</li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
