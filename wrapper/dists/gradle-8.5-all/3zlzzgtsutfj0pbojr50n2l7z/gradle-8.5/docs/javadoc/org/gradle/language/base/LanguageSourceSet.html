<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc -->
<title>LanguageSourceSet (Gradle API 8.5)</title>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<link rel="stylesheet" type="text/css" href="../../../../javadoc.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../jquery/jquery-ui.min.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../jquery-ui.overrides.css" title="Style">
<script type="text/javascript" src="../../../../script.js"></script>
<script type="text/javascript" src="../../../../jquery/jszip/dist/jszip.min.js"></script>
<script type="text/javascript" src="../../../../jquery/jszip-utils/dist/jszip-utils.min.js"></script>
<!--[if IE]>
<script type="text/javascript" src="../../../../jquery/jszip-utils/dist/jszip-utils-ie.min.js"></script>
<![endif]-->
<script type="text/javascript" src="../../../../jquery/jquery-3.6.1.min.js"></script>
<script type="text/javascript" src="../../../../jquery/jquery-ui.min.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="LanguageSourceSet (Gradle API 8.5)";
        }
    }
    catch(err) {
    }
//-->
var data = {"i0":6,"i1":6,"i2":6};
var tabs = {65535:["t0","All Methods"],2:["t2","Instance Methods"],4:["t3","Abstract Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
var pathtoroot = "../../../../";
var useModuleDirectories = false;
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<div class="fixedNav">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../allclasses.html">All&nbsp;Classes</a></li>
</ul>
<ul class="navListSearch">
<li><label for="search">SEARCH:</label>
<input type="text" id="search" value="search" disabled="disabled">
<input type="reset" id="reset" value="reset" disabled="disabled">
</li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li><a href="#nested.class.summary">Nested</a>&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
</div>
<div class="navPadding">&nbsp;</div>
<script type="text/javascript"><!--
$('.navPadding').css('padding-top', $('.fixedNav').css("height"));
//-->
</script>
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle"><span class="packageLabelInType">Package</span>&nbsp;<a href="package-summary.html">org.gradle.language.base</a></div>
<h2 title="Interface LanguageSourceSet" class="title">Interface LanguageSourceSet</h2>
</div>
<div class="contentContainer">
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt>All Superinterfaces:</dt>
<dd><code><a href="../../api/Buildable.html" title="interface in org.gradle.api">Buildable</a></code>, <code><a href="../../api/BuildableComponentSpec.html" title="interface in org.gradle.api">BuildableComponentSpec</a></code>, <code><a href="../../platform/base/ComponentSpec.html" title="interface in org.gradle.platform.base">ComponentSpec</a></code>, <code><a href="../../model/ModelElement.html" title="interface in org.gradle.model">ModelElement</a></code>, <code><a href="../../api/Named.html" title="interface in org.gradle.api">Named</a></code></dd>
</dl>
<dl>
<dt>All Known Subinterfaces:</dt>
<dd><code><a href="../assembler/AssemblerSourceSet.html" title="interface in org.gradle.language.assembler">AssemblerSourceSet</a></code>, <code><a href="../cpp/CppSourceSet.html" title="interface in org.gradle.language.cpp">CppSourceSet</a></code>, <code><a href="../c/CSourceSet.html" title="interface in org.gradle.language.c">CSourceSet</a></code>, <code><a href="DependentSourceSet.html" title="interface in org.gradle.language.base">DependentSourceSet</a></code>, <code><a href="../nativeplatform/DependentSourceSet.html" title="interface in org.gradle.language.nativeplatform">DependentSourceSet</a></code>, <code><a href="../nativeplatform/HeaderExportingSourceSet.html" title="interface in org.gradle.language.nativeplatform">HeaderExportingSourceSet</a></code>, <code><a href="../nativeplatform/NativeResourceSet.html" title="interface in org.gradle.language.nativeplatform">NativeResourceSet</a></code>, <code><a href="../objectivecpp/ObjectiveCppSourceSet.html" title="interface in org.gradle.language.objectivecpp">ObjectiveCppSourceSet</a></code>, <code><a href="../objectivec/ObjectiveCSourceSet.html" title="interface in org.gradle.language.objectivec">ObjectiveCSourceSet</a></code>, <code><a href="../rc/WindowsResourceSet.html" title="interface in org.gradle.language.rc">WindowsResourceSet</a></code></dd>
</dl>
<dl>
<dt>All Known Implementing Classes:</dt>
<dd><code>org.gradle.language.base.internal.AbstractLanguageSourceSet</code>, <code><a href="sources/BaseLanguageSourceSet.html" title="class in org.gradle.language.base.sources">BaseLanguageSourceSet</a></code></dd>
</dl>
<hr>
<pre><a href="../../api/Incubating.html" title="annotation in org.gradle.api">@Incubating</a>
public interface <span class="typeNameLabel">LanguageSourceSet</span>
extends <a href="../../api/BuildableComponentSpec.html" title="interface in org.gradle.api">BuildableComponentSpec</a></pre>
<div class="block">A set of sources for a programming language.</div>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ======== NESTED CLASS SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="nested.class.summary">
<!--   -->
</a>
<h3>Nested Class Summary</h3>
<ul class="blockList">
<li class="blockList"><a name="nested.classes.inherited.from.class.org.gradle.api.Named">
<!--   -->
</a>
<h3>Nested classes/interfaces inherited from interface&nbsp;org.gradle.api.<a href="../../api/Named.html" title="interface in org.gradle.api">Named</a></h3>
<code><a href="../../api/Named.Namer.html" title="class in org.gradle.api">Named.Namer</a></code></li>
</ul>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t3" class="tableTab"><span><a href="javascript:show(4);">Abstract Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colSecond" scope="col">Method</th>
<th class="colLast" scope="col">Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#generatedBy-org.gradle.api.Task-">generatedBy</a></span>&#8203;(<a href="../../api/Task.html" title="interface in org.gradle.api">Task</a>&nbsp;generatorTask)</code></th>
<td class="colLast">&nbsp;</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>java.lang.String</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getParentName--">getParentName</a></span>()</code></th>
<td class="colLast">&nbsp;</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code><a href="../../api/file/SourceDirectorySet.html" title="interface in org.gradle.api.file">SourceDirectorySet</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getSource--">getSource</a></span>()</code></th>
<td class="colLast">
<div class="block">The source files.</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.org.gradle.api.Buildable">
<!--   -->
</a>
<h3>Methods inherited from interface&nbsp;org.gradle.api.<a href="../../api/Buildable.html" title="interface in org.gradle.api">Buildable</a></h3>
<code><a href="../../api/Buildable.html#getBuildDependencies--">getBuildDependencies</a></code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.org.gradle.api.BuildableComponentSpec">
<!--   -->
</a>
<h3>Methods inherited from interface&nbsp;org.gradle.api.<a href="../../api/BuildableComponentSpec.html" title="interface in org.gradle.api">BuildableComponentSpec</a></h3>
<code><a href="../../api/BuildableComponentSpec.html#builtBy-java.lang.Object...-">builtBy</a>, <a href="../../api/BuildableComponentSpec.html#getBuildTask--">getBuildTask</a>, <a href="../../api/BuildableComponentSpec.html#hasBuildDependencies--">hasBuildDependencies</a>, <a href="../../api/BuildableComponentSpec.html#setBuildTask-org.gradle.api.Task-">setBuildTask</a></code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.org.gradle.platform.base.ComponentSpec">
<!--   -->
</a>
<h3>Methods inherited from interface&nbsp;org.gradle.platform.base.<a href="../../platform/base/ComponentSpec.html" title="interface in org.gradle.platform.base">ComponentSpec</a></h3>
<code><a href="../../platform/base/ComponentSpec.html#getProjectPath--">getProjectPath</a></code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.org.gradle.model.ModelElement">
<!--   -->
</a>
<h3>Methods inherited from interface&nbsp;org.gradle.model.<a href="../../model/ModelElement.html" title="interface in org.gradle.model">ModelElement</a></h3>
<code><a href="../../model/ModelElement.html#getDisplayName--">getDisplayName</a>, <a href="../../model/ModelElement.html#getName--">getName</a></code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="getSource--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getSource</h4>
<pre class="methodSignature"><a href="../../api/file/SourceDirectorySet.html" title="interface in org.gradle.api.file">SourceDirectorySet</a>&nbsp;getSource()</pre>
<div class="block">The source files.</div>
</li>
</ul>
<a name="generatedBy-org.gradle.api.Task-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>generatedBy</h4>
<pre class="methodSignature">void&nbsp;generatedBy&#8203;(<a href="../../api/Task.html" title="interface in org.gradle.api">Task</a>&nbsp;generatorTask)</pre>
</li>
</ul>
<a name="getParentName--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>getParentName</h4>
<pre class="methodSignature">@Nullable
java.lang.String&nbsp;getParentName()</pre>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../allclasses.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li><a href="#nested.class.summary">Nested</a>&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
