<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc -->
<title>JavaToolchainService (Gradle API 8.5)</title>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<link rel="stylesheet" type="text/css" href="../../../../javadoc.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../jquery/jquery-ui.min.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../jquery-ui.overrides.css" title="Style">
<script type="text/javascript" src="../../../../script.js"></script>
<script type="text/javascript" src="../../../../jquery/jszip/dist/jszip.min.js"></script>
<script type="text/javascript" src="../../../../jquery/jszip-utils/dist/jszip-utils.min.js"></script>
<!--[if IE]>
<script type="text/javascript" src="../../../../jquery/jszip-utils/dist/jszip-utils-ie.min.js"></script>
<![endif]-->
<script type="text/javascript" src="../../../../jquery/jquery-3.6.1.min.js"></script>
<script type="text/javascript" src="../../../../jquery/jquery-ui.min.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="JavaToolchainService (Gradle API 8.5)";
        }
    }
    catch(err) {
    }
//-->
var data = {"i0":6,"i1":6,"i2":6,"i3":6,"i4":6,"i5":6};
var tabs = {65535:["t0","All Methods"],2:["t2","Instance Methods"],4:["t3","Abstract Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
var pathtoroot = "../../../../";
var useModuleDirectories = false;
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<div class="fixedNav">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../allclasses.html">All&nbsp;Classes</a></li>
</ul>
<ul class="navListSearch">
<li><label for="search">SEARCH:</label>
<input type="text" id="search" value="search" disabled="disabled">
<input type="reset" id="reset" value="reset" disabled="disabled">
</li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
</div>
<div class="navPadding">&nbsp;</div>
<script type="text/javascript"><!--
$('.navPadding').css('padding-top', $('.fixedNav').css("height"));
//-->
</script>
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle"><span class="packageLabelInType">Package</span>&nbsp;<a href="package-summary.html">org.gradle.jvm.toolchain</a></div>
<h2 title="Interface JavaToolchainService" class="title">Interface JavaToolchainService</h2>
</div>
<div class="contentContainer">
<div class="description">
<ul class="blockList">
<li class="blockList">
<hr>
<pre>public interface <span class="typeNameLabel">JavaToolchainService</span></pre>
<div class="block">Allows to query for toolchain managed tools, like <a href="JavaCompiler.html" title="interface in org.gradle.jvm.toolchain"><code>JavaCompiler</code></a>, <a href="JavaLauncher.html" title="interface in org.gradle.jvm.toolchain"><code>JavaLauncher</code></a> and <a href="JavadocTool.html" title="interface in org.gradle.jvm.toolchain"><code>JavadocTool</code></a>.
 <p>
 An instance of this service is available for injection into tasks, plugins and other types.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>6.7</dd>
</dl>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t3" class="tableTab"><span><a href="javascript:show(4);">Abstract Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colSecond" scope="col">Method</th>
<th class="colLast" scope="col">Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code><a href="../../api/provider/Provider.html" title="interface in org.gradle.api.provider">Provider</a>&lt;<a href="JavaCompiler.html" title="interface in org.gradle.jvm.toolchain">JavaCompiler</a>&gt;</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#compilerFor-org.gradle.api.Action-">compilerFor</a></span>&#8203;(<a href="../../api/Action.html" title="interface in org.gradle.api">Action</a>&lt;? super <a href="JavaToolchainSpec.html" title="interface in org.gradle.jvm.toolchain">JavaToolchainSpec</a>&gt;&nbsp;config)</code></th>
<td class="colLast">
<div class="block">Obtain a <a href="JavaCompiler.html" title="interface in org.gradle.jvm.toolchain"><code>JavaCompiler</code></a> matching the <a href="JavaToolchainSpec.html" title="interface in org.gradle.jvm.toolchain"><code>JavaToolchainSpec</code></a>, as configured by the provided action.</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code><a href="../../api/provider/Provider.html" title="interface in org.gradle.api.provider">Provider</a>&lt;<a href="JavaCompiler.html" title="interface in org.gradle.jvm.toolchain">JavaCompiler</a>&gt;</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#compilerFor-org.gradle.jvm.toolchain.JavaToolchainSpec-">compilerFor</a></span>&#8203;(<a href="JavaToolchainSpec.html" title="interface in org.gradle.jvm.toolchain">JavaToolchainSpec</a>&nbsp;spec)</code></th>
<td class="colLast">
<div class="block">Obtain a <a href="JavaCompiler.html" title="interface in org.gradle.jvm.toolchain"><code>JavaCompiler</code></a> matching the <a href="JavaToolchainSpec.html" title="interface in org.gradle.jvm.toolchain"><code>JavaToolchainSpec</code></a>.</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code><a href="../../api/provider/Provider.html" title="interface in org.gradle.api.provider">Provider</a>&lt;<a href="JavadocTool.html" title="interface in org.gradle.jvm.toolchain">JavadocTool</a>&gt;</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#javadocToolFor-org.gradle.api.Action-">javadocToolFor</a></span>&#8203;(<a href="../../api/Action.html" title="interface in org.gradle.api">Action</a>&lt;? super <a href="JavaToolchainSpec.html" title="interface in org.gradle.jvm.toolchain">JavaToolchainSpec</a>&gt;&nbsp;config)</code></th>
<td class="colLast">
<div class="block">Obtain a <a href="JavadocTool.html" title="interface in org.gradle.jvm.toolchain"><code>JavadocTool</code></a> matching the <a href="JavaToolchainSpec.html" title="interface in org.gradle.jvm.toolchain"><code>JavaToolchainSpec</code></a>, as configured by the provided action.</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code><a href="../../api/provider/Provider.html" title="interface in org.gradle.api.provider">Provider</a>&lt;<a href="JavadocTool.html" title="interface in org.gradle.jvm.toolchain">JavadocTool</a>&gt;</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#javadocToolFor-org.gradle.jvm.toolchain.JavaToolchainSpec-">javadocToolFor</a></span>&#8203;(<a href="JavaToolchainSpec.html" title="interface in org.gradle.jvm.toolchain">JavaToolchainSpec</a>&nbsp;spec)</code></th>
<td class="colLast">
<div class="block">Obtain a <a href="JavadocTool.html" title="interface in org.gradle.jvm.toolchain"><code>JavadocTool</code></a> matching the <a href="JavaToolchainSpec.html" title="interface in org.gradle.jvm.toolchain"><code>JavaToolchainSpec</code></a>.</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code><a href="../../api/provider/Provider.html" title="interface in org.gradle.api.provider">Provider</a>&lt;<a href="JavaLauncher.html" title="interface in org.gradle.jvm.toolchain">JavaLauncher</a>&gt;</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#launcherFor-org.gradle.api.Action-">launcherFor</a></span>&#8203;(<a href="../../api/Action.html" title="interface in org.gradle.api">Action</a>&lt;? super <a href="JavaToolchainSpec.html" title="interface in org.gradle.jvm.toolchain">JavaToolchainSpec</a>&gt;&nbsp;config)</code></th>
<td class="colLast">
<div class="block">Obtain a <a href="JavaLauncher.html" title="interface in org.gradle.jvm.toolchain"><code>JavaLauncher</code></a> matching the <a href="JavaToolchainSpec.html" title="interface in org.gradle.jvm.toolchain"><code>JavaToolchainSpec</code></a>, as configured by the provided action.</div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code><a href="../../api/provider/Provider.html" title="interface in org.gradle.api.provider">Provider</a>&lt;<a href="JavaLauncher.html" title="interface in org.gradle.jvm.toolchain">JavaLauncher</a>&gt;</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#launcherFor-org.gradle.jvm.toolchain.JavaToolchainSpec-">launcherFor</a></span>&#8203;(<a href="JavaToolchainSpec.html" title="interface in org.gradle.jvm.toolchain">JavaToolchainSpec</a>&nbsp;spec)</code></th>
<td class="colLast">
<div class="block">Obtain a <a href="JavaLauncher.html" title="interface in org.gradle.jvm.toolchain"><code>JavaLauncher</code></a> matching the <a href="JavaToolchainSpec.html" title="interface in org.gradle.jvm.toolchain"><code>JavaToolchainSpec</code></a>.</div>
</td>
</tr>
</table>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="compilerFor-org.gradle.api.Action-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>compilerFor</h4>
<pre class="methodSignature"><a href="../../api/provider/Provider.html" title="interface in org.gradle.api.provider">Provider</a>&lt;<a href="JavaCompiler.html" title="interface in org.gradle.jvm.toolchain">JavaCompiler</a>&gt;&nbsp;compilerFor&#8203;(<a href="../../api/Action.html" title="interface in org.gradle.api">Action</a>&lt;? super <a href="JavaToolchainSpec.html" title="interface in org.gradle.jvm.toolchain">JavaToolchainSpec</a>&gt;&nbsp;config)</pre>
<div class="block">Obtain a <a href="JavaCompiler.html" title="interface in org.gradle.jvm.toolchain"><code>JavaCompiler</code></a> matching the <a href="JavaToolchainSpec.html" title="interface in org.gradle.jvm.toolchain"><code>JavaToolchainSpec</code></a>, as configured by the provided action.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>config</code> - The configuration of the <code>JavaToolchainSpec</code></dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>A <code>Provider&lt;JavaCompiler&gt;</code></dd>
</dl>
</li>
</ul>
<a name="compilerFor-org.gradle.jvm.toolchain.JavaToolchainSpec-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>compilerFor</h4>
<pre class="methodSignature"><a href="../../api/provider/Provider.html" title="interface in org.gradle.api.provider">Provider</a>&lt;<a href="JavaCompiler.html" title="interface in org.gradle.jvm.toolchain">JavaCompiler</a>&gt;&nbsp;compilerFor&#8203;(<a href="JavaToolchainSpec.html" title="interface in org.gradle.jvm.toolchain">JavaToolchainSpec</a>&nbsp;spec)</pre>
<div class="block">Obtain a <a href="JavaCompiler.html" title="interface in org.gradle.jvm.toolchain"><code>JavaCompiler</code></a> matching the <a href="JavaToolchainSpec.html" title="interface in org.gradle.jvm.toolchain"><code>JavaToolchainSpec</code></a>.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>spec</code> - The <code>JavaToolchainSpec</code></dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>A <code>Provider&lt;JavaCompiler&gt;</code></dd>
</dl>
</li>
</ul>
<a name="launcherFor-org.gradle.api.Action-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>launcherFor</h4>
<pre class="methodSignature"><a href="../../api/provider/Provider.html" title="interface in org.gradle.api.provider">Provider</a>&lt;<a href="JavaLauncher.html" title="interface in org.gradle.jvm.toolchain">JavaLauncher</a>&gt;&nbsp;launcherFor&#8203;(<a href="../../api/Action.html" title="interface in org.gradle.api">Action</a>&lt;? super <a href="JavaToolchainSpec.html" title="interface in org.gradle.jvm.toolchain">JavaToolchainSpec</a>&gt;&nbsp;config)</pre>
<div class="block">Obtain a <a href="JavaLauncher.html" title="interface in org.gradle.jvm.toolchain"><code>JavaLauncher</code></a> matching the <a href="JavaToolchainSpec.html" title="interface in org.gradle.jvm.toolchain"><code>JavaToolchainSpec</code></a>, as configured by the provided action.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>config</code> - The configuration of the <code>JavaToolchainSpec</code></dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>A <code>Provider&lt;JavaLauncher&gt;</code></dd>
</dl>
</li>
</ul>
<a name="launcherFor-org.gradle.jvm.toolchain.JavaToolchainSpec-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>launcherFor</h4>
<pre class="methodSignature"><a href="../../api/provider/Provider.html" title="interface in org.gradle.api.provider">Provider</a>&lt;<a href="JavaLauncher.html" title="interface in org.gradle.jvm.toolchain">JavaLauncher</a>&gt;&nbsp;launcherFor&#8203;(<a href="JavaToolchainSpec.html" title="interface in org.gradle.jvm.toolchain">JavaToolchainSpec</a>&nbsp;spec)</pre>
<div class="block">Obtain a <a href="JavaLauncher.html" title="interface in org.gradle.jvm.toolchain"><code>JavaLauncher</code></a> matching the <a href="JavaToolchainSpec.html" title="interface in org.gradle.jvm.toolchain"><code>JavaToolchainSpec</code></a>.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>spec</code> - The <code>JavaToolchainSpec</code></dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>A <code>Provider&lt;JavaLauncher&gt;</code></dd>
</dl>
</li>
</ul>
<a name="javadocToolFor-org.gradle.api.Action-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>javadocToolFor</h4>
<pre class="methodSignature"><a href="../../api/provider/Provider.html" title="interface in org.gradle.api.provider">Provider</a>&lt;<a href="JavadocTool.html" title="interface in org.gradle.jvm.toolchain">JavadocTool</a>&gt;&nbsp;javadocToolFor&#8203;(<a href="../../api/Action.html" title="interface in org.gradle.api">Action</a>&lt;? super <a href="JavaToolchainSpec.html" title="interface in org.gradle.jvm.toolchain">JavaToolchainSpec</a>&gt;&nbsp;config)</pre>
<div class="block">Obtain a <a href="JavadocTool.html" title="interface in org.gradle.jvm.toolchain"><code>JavadocTool</code></a> matching the <a href="JavaToolchainSpec.html" title="interface in org.gradle.jvm.toolchain"><code>JavaToolchainSpec</code></a>, as configured by the provided action.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>config</code> - The configuration of the <code>JavaToolchainSpec</code></dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>A <code>Provider&lt;JavadocTool&gt;</code></dd>
</dl>
</li>
</ul>
<a name="javadocToolFor-org.gradle.jvm.toolchain.JavaToolchainSpec-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>javadocToolFor</h4>
<pre class="methodSignature"><a href="../../api/provider/Provider.html" title="interface in org.gradle.api.provider">Provider</a>&lt;<a href="JavadocTool.html" title="interface in org.gradle.jvm.toolchain">JavadocTool</a>&gt;&nbsp;javadocToolFor&#8203;(<a href="JavaToolchainSpec.html" title="interface in org.gradle.jvm.toolchain">JavaToolchainSpec</a>&nbsp;spec)</pre>
<div class="block">Obtain a <a href="JavadocTool.html" title="interface in org.gradle.jvm.toolchain"><code>JavadocTool</code></a> matching the <a href="JavaToolchainSpec.html" title="interface in org.gradle.jvm.toolchain"><code>JavaToolchainSpec</code></a>.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>spec</code> - The <code>JavaToolchainSpec</code></dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>A <code>Provider&lt;JavadocTool&gt;</code></dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../allclasses.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
